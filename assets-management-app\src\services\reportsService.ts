import { apiClient, API_CONFIG } from '../lib/api'
import { Asset } from '../types/api'
import { AuditLog } from './auditLogsService'

export interface ReportFilters {
  startDate?: string
  endDate?: string
  categoryId?: string
  locationId?: string
  status?: string
  condition?: string
  assignedUserId?: string
}

export interface AssetReport {
  totalAssets: number
  totalValue: number
  statusBreakdown: { status: string; count: number; percentage: number }[]
  conditionBreakdown: { condition: string; count: number; percentage: number }[]
  categoryBreakdown: { category: string; count: number; percentage: number }[]
  locationBreakdown: { location: string; count: number; percentage: number }[]
  assets: Asset[]
}

export interface UtilizationReport {
  totalAssets: number
  inUseAssets: number
  availableAssets: number
  utilizationRate: number
  categoryUtilization: { category: string; total: number; inUse: number; rate: number }[]
  locationUtilization: { location: string; total: number; inUse: number; rate: number }[]
}

export interface MaintenanceReport {
  assetsInMaintenance: number
  assetsDamaged: number
  assetsRetired: number
  maintenanceByCategory: { category: string; count: number }[]
  maintenanceByLocation: { location: string; count: number }[]
  maintenanceSchedule: Asset[]
}

export interface ActivityReport {
  totalActivities: number
  recentActivities: AuditLog[]
  activityByAction: { action: string; count: number }[]
  activityByUser: { user: string; count: number }[]
  activityTrend: { date: string; count: number }[]
}

export interface SummaryReport {
  assets: AssetReport
  utilization: UtilizationReport
  maintenance: MaintenanceReport
  activity: ActivityReport
  generatedAt: string
}

class ReportsService {
  async generateAssetReport(filters: ReportFilters = {}): Promise<AssetReport> {
    return apiClient.get<AssetReport>(API_CONFIG.ENDPOINTS.REPORTS.ASSETS, filters)
  }

  async generateUtilizationReport(): Promise<UtilizationReport> {
    return apiClient.get<UtilizationReport>(API_CONFIG.ENDPOINTS.REPORTS.UTILIZATION)
  }

  async generateMaintenanceReport(): Promise<MaintenanceReport> {
    return apiClient.get<MaintenanceReport>(API_CONFIG.ENDPOINTS.REPORTS.MAINTENANCE)
  }

  async generateActivityReport(filters: Pick<ReportFilters, 'startDate' | 'endDate'> = {}): Promise<ActivityReport> {
    return apiClient.get<ActivityReport>(API_CONFIG.ENDPOINTS.REPORTS.ACTIVITY, filters)
  }

  async generateSummaryReport(): Promise<SummaryReport> {
    return apiClient.get<SummaryReport>(API_CONFIG.ENDPOINTS.REPORTS.SUMMARY)
  }

  // Helper methods for formatting and exporting
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-LK', {
      style: 'currency',
      currency: 'LKR'
    }).format(amount)
  }

  formatPercentage(value: number): string {
    return `${value}%`
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString()
  }

  formatDateTime(date: string): string {
    return new Date(date).toLocaleString()
  }

  exportToCSV(data: any[], filename: string): void {
    if (!data || data.length === 0) {
      return
    }

    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row =>
        headers
          .map(header => {
            const value = row[header]
            if (value === null || value === undefined) return ''
            if (typeof value === 'string' && value.includes(',')) {
              return `"${value}"`
            }
            return value
          })
          .join(',')
      )
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `${filename}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  exportAssetReportToCSV(report: AssetReport): void {
    const data = report.assets.map(asset => ({
      'Asset Number': asset.assetNumber,
      Name: asset.name,
      Category: asset.category?.name || 'Unknown',
      Location: asset.location?.name || 'Unknown',
      Status: asset.status?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      Condition: asset.condition?.charAt(0).toUpperCase() + asset.condition?.slice(1),
      'Unit Price (LKR)': asset.unitPrice || 0,
      Manufacturer: asset.manufacturer || '',
      Model: asset.model || '',
      'Serial Number': asset.serialNumber || '',
      'Assigned To': asset.assignedTo?.fullName || 'Unassigned',
      'Purchase Date': asset.purchaseDate ? this.formatDate(asset.purchaseDate) : '',
      'Warranty Expiry': asset.warrantyExpiry ? this.formatDate(asset.warrantyExpiry) : '',
      'Created At': this.formatDateTime(asset.createdAt || '')
    }))

    this.exportToCSV(data, `asset-report-${new Date().toISOString().split('T')[0]}`)
  }

  exportUtilizationReportToCSV(report: UtilizationReport): void {
    const data = [
      {
        Metric: 'Total Assets',
        Value: report.totalAssets
      },
      {
        Metric: 'In Use Assets',
        Value: report.inUseAssets
      },
      {
        Metric: 'Available Assets',
        Value: report.availableAssets
      },
      {
        Metric: 'Utilization Rate (%)',
        Value: report.utilizationRate
      },
      ...report.categoryUtilization.map(item => ({
        Category: item.category,
        Total: item.total,
        'In Use': item.inUse,
        'Utilization Rate (%)': item.rate
      }))
    ]

    this.exportToCSV(data, `utilization-report-${new Date().toISOString().split('T')[0]}`)
  }

  exportMaintenanceReportToCSV(report: MaintenanceReport): void {
    const data = [
      {
        Metric: 'Assets in Maintenance',
        Value: report.assetsInMaintenance
      },
      {
        Metric: 'Assets Damaged',
        Value: report.assetsDamaged
      },
      {
        Metric: 'Assets Retired',
        Value: report.assetsRetired
      },
      ...report.maintenanceByCategory.map(item => ({
        Category: item.category,
        'Maintenance Count': item.count
      }))
    ]

    this.exportToCSV(data, `maintenance-report-${new Date().toISOString().split('T')[0]}`)
  }

  exportActivityReportToCSV(report: ActivityReport): void {
    const data = report.recentActivities.map(activity => ({
      Date: this.formatDateTime(activity.createdAt),
      User: activity.user?.fullName || 'Unknown',
      Action: activity.action?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      'Entity Type': activity.entityType?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      'Entity Name': activity.entityName || '',
      Description: activity.description || ''
    }))

    this.exportToCSV(data, `activity-report-${new Date().toISOString().split('T')[0]}`)
  }
}

export const reportsService = new ReportsService()
