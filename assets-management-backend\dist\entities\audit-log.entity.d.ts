import { User } from './user.entity';
export declare enum AuditAction {
    CREATE = "CREATE",
    UPDATE = "UPDATE",
    DELETE = "DELETE",
    LOGIN = "LOGIN",
    LOGOUT = "LOGOUT",
    EXPORT = "EXPORT",
    PRINT = "PRINT",
    ASSIGN = "ASSIGN",
    UNASSIGN = "UNASSIGN",
    STATUS_CHANGE = "STATUS_CHANGE",
    CONDITION_CHANGE = "CONDITION_CHANGE"
}
export declare enum EntityType {
    ASSET = "ASSET",
    ASSET_ITEM = "ASSET_ITEM",
    CATEGORY = "CATEGORY",
    LOCATION = "LOCATION",
    USER = "USER",
    SYSTEM = "SYSTEM"
}
export declare class AuditLog {
    id: string;
    action: AuditAction;
    entityType: EntityType;
    entityId: string;
    entityName: string;
    description: string;
    oldValues: any;
    newValues: any;
    metadata: any;
    ipAddress: string;
    userAgent: string;
    user: User;
    userId: string;
    createdAt: Date;
}
