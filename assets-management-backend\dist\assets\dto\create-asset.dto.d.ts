import { AssetStatus, AssetCondition } from '../../entities/asset.entity';
export declare class CreateAssetDto {
    name: string;
    description?: string;
    model?: string;
    manufacturer?: string;
    serialNumber?: string;
    unitPrice: number;
    currentValue?: number;
    quantity: number;
    status?: AssetStatus;
    condition?: AssetCondition;
    purchaseDate?: string;
    warrantyExpiry?: string;
    supplier?: string;
    invoiceNumber?: string;
    notes?: string;
    imageUrl?: string;
    categoryId: string;
    locationId: string;
    assignedToId?: string;
    isActive?: boolean;
}
