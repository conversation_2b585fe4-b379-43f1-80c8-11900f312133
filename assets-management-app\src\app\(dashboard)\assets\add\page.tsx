'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Grid,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Alert,
  InputAdornment,
  Paper,
  Chip
} from '@mui/material'
import { Save as SaveIcon, Cancel as CancelIcon, Upload as UploadIcon, QrCode as QrCodeIcon } from '@mui/icons-material'
import TreeSelect from '@/components/ui/TreeSelect'
import { categoriesService } from '@/lib/api/categories'
import { locationsService } from '@/lib/api/locations'

interface Category {
  id: string
  name: string
  code: string
  children?: Category[]
}

interface Location {
  id: string
  name: string
  type: string
  children?: Location[]
}

const users = [
  { id: '1', name: '<PERSON>', email: '<EMAIL>' },
  { id: '2', name: '<PERSON>', email: '<EMAIL>' },
  { id: '3', name: '<PERSON>', email: '<EMAIL>' },
  { id: '4', name: '<PERSON>', email: '<EMAIL>' }
]

export default function AddAssetPage() {
  const router = useRouter()
  const [categories, setCategories] = useState<Category[]>([])
  const [locations, setLocations] = useState<Location[]>([])
  const [loading, setLoading] = useState(true)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    model: '',
    manufacturer: '',
    serialNumber: '',
    unitPrice: '',
    quantity: '1',
    status: 'In Stock',
    condition: 'Good',
    categoryId: '',
    locationId: '',
    assignedToId: '',
    purchaseDate: '',
    warrantyExpiry: '',
    supplier: '',
    invoiceNumber: '',
    notes: ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [previewAssetNumber, setPreviewAssetNumber] = useState('AST-202412-XXXX')

  // Load categories and locations on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)
        const [categoriesData, locationsData] = await Promise.all([
          categoriesService.getCategoryHierarchy(),
          locationsService.getLocationHierarchy()
        ])
        setCategories(categoriesData)
        setLocations(locationsData)
      } catch (error) {
        console.error('Failed to load categories and locations:', error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  const handleInputChange = (field: string) => (event: any) => {
    const value = event.target.value
    setFormData(prev => ({ ...prev, [field]: value }))

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }

    // Generate preview asset number based on current date
    if (field === 'name' && value) {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const random = Math.floor(Math.random() * 9999)
        .toString()
        .padStart(4, '0')
      setPreviewAssetNumber(`AST-${year}${month}-${random}`)
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) newErrors.name = 'Asset name is required'
    if (!formData.categoryId) newErrors.categoryId = 'Category is required'
    if (!formData.locationId) newErrors.locationId = 'Location is required'
    if (!formData.unitPrice || parseFloat(formData.unitPrice) <= 0) {
      newErrors.unitPrice = 'Valid unit price is required'
    }
    if (!formData.quantity || parseInt(formData.quantity) <= 0) {
      newErrors.quantity = 'Valid quantity is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault()

    if (validateForm()) {
      // Here you would typically call your API
      console.log('Form submitted:', formData)

      // Show success message and redirect
      router.push('/assets')
    }
  }

  const handleCancel = () => {
    router.push('/assets')
  }

  const calculateTotalValue = () => {
    const price = parseFloat(formData.unitPrice) || 0
    const qty = parseInt(formData.quantity) || 0
    return price * qty
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant='h4' component='h1'>
          Add New Asset
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button variant='outlined' startIcon={<CancelIcon />} onClick={handleCancel}>
            Cancel
          </Button>
          <Button variant='contained' startIcon={<SaveIcon />} onClick={handleSubmit} color='primary'>
            Save Asset
          </Button>
        </Box>
      </Box>

      {/* Asset Number Preview */}
      <Alert severity='info' sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <QrCodeIcon />
          <Box>
            <Typography variant='subtitle2'>Auto-Generated Asset Number</Typography>
            <Typography variant='h6' color='primary'>
              {previewAssetNumber}
            </Typography>
          </Box>
        </Box>
      </Alert>

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant='h6' gutterBottom>
                  Basic Information
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label='Asset Name'
                      value={formData.name}
                      onChange={handleInputChange('name')}
                      error={!!errors.name}
                      helperText={errors.name}
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TreeSelect
                      label='Category'
                      value={formData.categoryId}
                      onChange={value => setFormData(prev => ({ ...prev, categoryId: value }))}
                      options={categories}
                      error={!!errors.categoryId}
                      helperText={errors.categoryId}
                      required
                      disabled={loading}
                      placeholder='Select a category'
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label='Description'
                      value={formData.description}
                      onChange={handleInputChange('description')}
                      multiline
                      rows={3}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label='Manufacturer'
                      value={formData.manufacturer}
                      onChange={handleInputChange('manufacturer')}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField fullWidth label='Model' value={formData.model} onChange={handleInputChange('model')} />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label='Serial Number'
                      value={formData.serialNumber}
                      onChange={handleInputChange('serialNumber')}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Financial Information */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant='h6' gutterBottom>
                  Financial Information
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label='Unit Price'
                      type='number'
                      value={formData.unitPrice}
                      onChange={handleInputChange('unitPrice')}
                      error={!!errors.unitPrice}
                      helperText={errors.unitPrice}
                      InputProps={{
                        startAdornment: <InputAdornment position='start'>$</InputAdornment>
                      }}
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label='Quantity'
                      type='number'
                      value={formData.quantity}
                      onChange={handleInputChange('quantity')}
                      error={!!errors.quantity}
                      helperText={errors.quantity}
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                      <Typography variant='subtitle2' color='text.secondary'>
                        Total Value
                      </Typography>
                      <Typography variant='h6' color='primary'>
                        ${calculateTotalValue().toLocaleString()}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label='Purchase Date'
                      type='date'
                      value={formData.purchaseDate}
                      onChange={handleInputChange('purchaseDate')}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label='Warranty Expiry'
                      type='date'
                      value={formData.warrantyExpiry}
                      onChange={handleInputChange('warrantyExpiry')}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label='Supplier'
                      value={formData.supplier}
                      onChange={handleInputChange('supplier')}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label='Invoice Number'
                      value={formData.invoiceNumber}
                      onChange={handleInputChange('invoiceNumber')}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Location and Assignment */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant='h6' gutterBottom>
                  Location & Assignment
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TreeSelect
                      label='Location'
                      value={formData.locationId}
                      onChange={value => setFormData(prev => ({ ...prev, locationId: value }))}
                      options={locations}
                      error={!!errors.locationId}
                      helperText={errors.locationId}
                      required
                      disabled={loading}
                      placeholder='Select a location'
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Assign To (Optional)</InputLabel>
                      <Select
                        value={formData.assignedToId}
                        label='Assign To (Optional)'
                        onChange={handleInputChange('assignedToId')}
                      >
                        <MenuItem value=''>Unassigned</MenuItem>
                        {users.map(user => (
                          <MenuItem key={user.id} value={user.id}>
                            {user.name} ({user.email})
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Status</InputLabel>
                      <Select value={formData.status} label='Status' onChange={handleInputChange('status')}>
                        <MenuItem value='In Stock'>In Stock</MenuItem>
                        <MenuItem value='In Use'>In Use</MenuItem>
                        <MenuItem value='Maintenance'>Maintenance</MenuItem>
                        <MenuItem value='Retired'>Retired</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Condition</InputLabel>
                      <Select value={formData.condition} label='Condition' onChange={handleInputChange('condition')}>
                        <MenuItem value='Excellent'>Excellent</MenuItem>
                        <MenuItem value='Good'>Good</MenuItem>
                        <MenuItem value='Fair'>Fair</MenuItem>
                        <MenuItem value='Poor'>Poor</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Additional Notes */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant='h6' gutterBottom>
                  Additional Information
                </Typography>
                <TextField
                  fullWidth
                  label='Notes'
                  value={formData.notes}
                  onChange={handleInputChange('notes')}
                  multiline
                  rows={4}
                  placeholder='Any additional notes about this asset...'
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </form>
    </Box>
  )
}
