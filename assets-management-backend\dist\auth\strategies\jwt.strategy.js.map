{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../src/auth/strategies/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAmE;AACnE,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAC/C,6CAAmD;AACnD,qCAAqC;AACrC,4DAA8D;AAWvD,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IAE/C;IAEA;IAHV,YACU,aAA4B,EAE5B,cAAgC;QAExC,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,IAAI,iBAAiB;SAC1E,CAAC,CAAC;QARK,kBAAa,GAAb,aAAa,CAAe;QAE5B,mBAAc,GAAd,cAAc,CAAkB;IAO1C,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAmB;QAChC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,GAAG,EAAE;SAC3B,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAU,CAAC,MAAM,EAAE,CAAC;YAC/C,MAAM,IAAI,8BAAqB,CAAC,4BAA4B,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AAxBY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;IAIR,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;qCADA,sBAAa;QAEZ,oBAAU;GAJzB,WAAW,CAwBvB"}