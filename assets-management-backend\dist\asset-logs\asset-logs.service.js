"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetLogsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const asset_log_schema_1 = require("../schemas/asset-log.schema");
let AssetLogsService = class AssetLogsService {
    assetLogModel;
    constructor(assetLogModel) {
        this.assetLogModel = assetLogModel;
    }
    async createLog(options) {
        const log = new this.assetLogModel({
            ...options,
            timestamp: new Date(),
        });
        return log.save();
    }
    async logAssetCreated(asset, user, ipAddress, userAgent) {
        return this.createLog({
            assetId: asset.id,
            assetNumber: asset.assetNumber,
            action: asset_log_schema_1.LogAction.CREATED,
            performedBy: user.id,
            performedByName: user.fullName,
            newData: this.sanitizeAssetData(asset),
            description: `Asset ${asset.assetNumber} created`,
            ipAddress,
            userAgent,
        });
    }
    async logAssetUpdated(asset, previousData, user, ipAddress, userAgent) {
        return this.createLog({
            assetId: asset.id,
            assetNumber: asset.assetNumber,
            action: asset_log_schema_1.LogAction.UPDATED,
            performedBy: user.id,
            performedByName: user.fullName,
            previousData: this.sanitizeAssetData(previousData),
            newData: this.sanitizeAssetData(asset),
            description: `Asset ${asset.assetNumber} updated`,
            ipAddress,
            userAgent,
        });
    }
    async logAssetDeleted(asset, user, ipAddress, userAgent) {
        return this.createLog({
            assetId: asset.id,
            assetNumber: asset.assetNumber,
            action: asset_log_schema_1.LogAction.DELETED,
            performedBy: user.id,
            performedByName: user.fullName,
            previousData: this.sanitizeAssetData(asset),
            description: `Asset ${asset.assetNumber} deleted`,
            ipAddress,
            userAgent,
        });
    }
    async logAssetAssigned(asset, assignedTo, performedBy, ipAddress, userAgent) {
        return this.createLog({
            assetId: asset.id,
            assetNumber: asset.assetNumber,
            action: asset_log_schema_1.LogAction.ASSIGNED,
            performedBy: performedBy.id,
            performedByName: performedBy.fullName,
            newData: {
                assignedTo: {
                    id: assignedTo.id,
                    name: assignedTo.fullName,
                    email: assignedTo.email,
                },
                assignedAt: new Date(),
            },
            description: `Asset ${asset.assetNumber} assigned to ${assignedTo.fullName}`,
            ipAddress,
            userAgent,
        });
    }
    async logAssetUnassigned(asset, previousAssignee, performedBy, ipAddress, userAgent) {
        return this.createLog({
            assetId: asset.id,
            assetNumber: asset.assetNumber,
            action: asset_log_schema_1.LogAction.UNASSIGNED,
            performedBy: performedBy.id,
            performedByName: performedBy.fullName,
            previousData: {
                assignedTo: {
                    id: previousAssignee.id,
                    name: previousAssignee.fullName,
                    email: previousAssignee.email,
                },
            },
            description: `Asset ${asset.assetNumber} unassigned from ${previousAssignee.fullName}`,
            ipAddress,
            userAgent,
        });
    }
    async logStatusChange(asset, previousStatus, newStatus, user, ipAddress, userAgent) {
        return this.createLog({
            assetId: asset.id,
            assetNumber: asset.assetNumber,
            action: asset_log_schema_1.LogAction.STATUS_CHANGED,
            performedBy: user.id,
            performedByName: user.fullName,
            previousData: { status: previousStatus },
            newData: { status: newStatus },
            description: `Asset ${asset.assetNumber} status changed from ${previousStatus} to ${newStatus}`,
            ipAddress,
            userAgent,
        });
    }
    async logLocationChange(asset, previousLocation, newLocation, user, ipAddress, userAgent) {
        return this.createLog({
            assetId: asset.id,
            assetNumber: asset.assetNumber,
            action: asset_log_schema_1.LogAction.LOCATION_CHANGED,
            performedBy: user.id,
            performedByName: user.fullName,
            previousData: { location: previousLocation },
            newData: { location: newLocation },
            description: `Asset ${asset.assetNumber} location changed from ${previousLocation} to ${newLocation}`,
            ipAddress,
            userAgent,
        });
    }
    async getAssetLogs(assetId, limit = 50, skip = 0) {
        return this.assetLogModel
            .find({ assetId })
            .sort({ timestamp: -1 })
            .limit(limit)
            .skip(skip)
            .exec();
    }
    async getUserActivityLogs(userId, limit = 50, skip = 0) {
        return this.assetLogModel
            .find({ performedBy: userId })
            .sort({ timestamp: -1 })
            .limit(limit)
            .skip(skip)
            .exec();
    }
    async getRecentLogs(limit = 100) {
        return this.assetLogModel
            .find()
            .sort({ timestamp: -1 })
            .limit(limit)
            .exec();
    }
    async getLogsByDateRange(startDate, endDate, limit = 100) {
        return this.assetLogModel
            .find({
            timestamp: {
                $gte: startDate,
                $lte: endDate,
            },
        })
            .sort({ timestamp: -1 })
            .limit(limit)
            .exec();
    }
    async getLogsByAction(action, limit = 100) {
        return this.assetLogModel
            .find({ action })
            .sort({ timestamp: -1 })
            .limit(limit)
            .exec();
    }
    sanitizeAssetData(data) {
        if (!data)
            return {};
        const sanitized = { ...data };
        delete sanitized.password;
        delete sanitized.createdAt;
        delete sanitized.updatedAt;
        Object.keys(sanitized).forEach(key => {
            if (sanitized[key] instanceof Date) {
                sanitized[key] = sanitized[key].toISOString();
            }
        });
        return sanitized;
    }
};
exports.AssetLogsService = AssetLogsService;
exports.AssetLogsService = AssetLogsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(asset_log_schema_1.AssetLog.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], AssetLogsService);
//# sourceMappingURL=asset-logs.service.js.map