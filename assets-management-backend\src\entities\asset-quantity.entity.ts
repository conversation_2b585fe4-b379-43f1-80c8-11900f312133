import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Unique,
} from 'typeorm';
import { Asset, AssetStatus } from './asset.entity';
import { User } from './user.entity';

@Entity('asset_quantities')
@Unique(['assetId', 'status'])
export class AssetQuantity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Asset, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'assetId' })
  asset: Asset;

  @Column()
  assetId: string;

  @Column({
    type: 'enum',
    enum: AssetStatus,
  })
  status: AssetStatus;

  @Column({ type: 'int', default: 0 })
  quantity: number;

  @Column({ nullable: true })
  notes: string;

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'lastModifiedBy' })
  lastModifiedBy: User;

  @Column({ nullable: true })
  lastModifiedById: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
