import {
  IsNotEmpty,
  <PERSON>String,
  <PERSON><PERSON>ptional,
  IsUUID,
  IsBoolean,
  IsEnum,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { LocationType } from '../../entities/location.entity';

export class CreateLocationDto {
  @ApiProperty({
    description: 'Location name',
    example: 'Main Office',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Location description',
    example: 'Main office building in downtown',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Location code',
    example: 'INV',
    required: false,
  })
  @IsString()
  @IsOptional()
  code?: string;

  @ApiProperty({
    description: 'Location type',
    enum: LocationType,
    example: LocationType.BUILDING,
  })
  @IsEnum(LocationType)
  @IsNotEmpty()
  type: LocationType;

  @ApiProperty({
    description: 'Location address',
    example: '123 Main St, City, State 12345',
    required: false,
  })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty({
    description: 'Location coordinates (JSON string)',
    example: '{"lat": 40.7128, "lng": -74.0060}',
    required: false,
  })
  @IsString()
  @IsOptional()
  coordinates?: string;

  @ApiProperty({
    description: 'Parent location ID for nested locations',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  parentId?: string;

  @ApiProperty({
    description: 'Whether the location is active',
    example: true,
    default: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
