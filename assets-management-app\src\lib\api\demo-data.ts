import { Asset, Category, Location, User } from '@/types/api'

// Demo data for when backend is not available
export const demoAssets: Asset[] = [
  {
    id: '1',
    assetNumber: 'AST-202412-0001',
    name: 'Dell Laptop XPS 13',
    description: 'High-performance laptop for development work',
    categoryId: '1',
    locationId: '1',
    status: 'In Use',
    condition: 'Good',
    assignedToId: '1',
    unitPrice: 1299.99,
    quantity: 1,
    totalValue: 1299.99,
    purchaseDate: '2024-01-15',
    warrantyExpiry: '2027-01-15',
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z'
  },
  {
    id: '2',
    assetNumber: 'AST-202412-0002',
    name: 'Office Chair Ergonomic',
    description: 'Comfortable ergonomic office chair',
    categoryId: '2',
    locationId: '2',
    status: 'In Stock',
    condition: 'Excellent',
    assignedToId: null,
    unitPrice: 299.99,
    quantity: 5,
    totalValue: 1499.95,
    purchaseDate: '2024-02-01',
    warrantyExpiry: '2026-02-01',
    createdAt: '2024-02-01T00:00:00Z',
    updatedAt: '2024-02-01T00:00:00Z'
  },
  {
    id: '3',
    assetNumber: 'AST-202412-0003',
    name: 'HP Printer LaserJet Pro',
    description: 'Professional laser printer',
    categoryId: '1',
    locationId: '1',
    status: 'Maintenance',
    condition: 'Fair',
    assignedToId: null,
    unitPrice: 399.99,
    quantity: 1,
    totalValue: 399.99,
    purchaseDate: '2023-06-10',
    warrantyExpiry: '2025-06-10',
    createdAt: '2023-06-10T00:00:00Z',
    updatedAt: '2024-12-01T00:00:00Z'
  }
]

export const demoCategories: Category[] = [
  {
    id: '1',
    name: 'IT Equipment',
    code: 'IT',
    description: 'Information Technology equipment and devices',
    parentId: null,
    isActive: true,
    children: [],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: 'Furniture',
    code: 'FURN',
    description: 'Office furniture and fixtures',
    parentId: null,
    isActive: true,
    children: [],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '3',
    name: 'Vehicles',
    code: 'VEH',
    description: 'Company vehicles and transportation',
    parentId: null,
    isActive: true,
    children: [],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
]

export const demoLocations: Location[] = [
  {
    id: '1',
    name: 'Main Office',
    code: 'MAIN',
    description: 'Main office building',
    type: 'Building',
    parentId: null,
    isActive: true,
    children: [],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: 'IT Department',
    code: 'IT-DEPT',
    description: 'Information Technology department',
    type: 'Department',
    parentId: '1',
    isActive: true,
    children: [],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '3',
    name: 'Conference Room A',
    code: 'CONF-A',
    description: 'Main conference room',
    type: 'Room',
    parentId: '1',
    isActive: true,
    children: [],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
]

export const demoUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    fullName: 'John Doe',
    role: 'Admin',
    department: 'IT',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    email: '<EMAIL>',
    fullName: 'Jane Smith',
    role: 'Manager',
    department: 'Operations',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '3',
    email: '<EMAIL>',
    fullName: 'Bob Johnson',
    role: 'User',
    department: 'Sales',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
]

// Demo mode check
export const isDemoMode = (): boolean => {
  if (typeof window === 'undefined') return false
  const demoAuth = localStorage.getItem('demo_auth')
  const authToken = localStorage.getItem('auth_token')
  console.log('Demo mode check:', { demoAuth, authToken })
  return demoAuth === 'demo-authenticated' || authToken === 'demo-jwt-token'
}

// Demo API responses
export const getDemoAssets = () => ({
  assets: demoAssets,
  total: demoAssets.length,
  page: 1,
  limit: 100
})

export const getDemoCategories = () => demoCategories
export const getDemoLocations = () => demoLocations
export const getDemoUsers = () => demoUsers
