"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const assets_service_1 = require("./assets.service");
const create_asset_dto_1 = require("./dto/create-asset.dto");
const update_asset_dto_1 = require("./dto/update-asset.dto");
const assign_asset_dto_1 = require("./dto/assign-asset.dto");
const transfer_quantity_dto_1 = require("./dto/transfer-quantity.dto");
const asset_quantity_service_1 = require("./asset-quantity.service");
const asset_item_service_1 = require("./asset-item.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const current_user_decorator_1 = require("../auth/decorators/current-user.decorator");
const user_entity_1 = require("../entities/user.entity");
const asset_entity_1 = require("../entities/asset.entity");
let AssetsController = class AssetsController {
    assetsService;
    assetQuantityService;
    assetItemService;
    constructor(assetsService, assetQuantityService, assetItemService) {
        this.assetsService = assetsService;
        this.assetQuantityService = assetQuantityService;
        this.assetItemService = assetItemService;
    }
    async create(createAssetDto, user) {
        const asset = await this.assetsService.create(createAssetDto);
        if (asset.quantity > 0) {
            try {
                await this.assetItemService.createMultipleAssetItems(asset.id, asset.quantity, user.id);
            }
            catch (error) {
                console.error('Failed to create asset items:', error);
            }
        }
        return asset;
    }
    findAll(query) {
        return this.assetsService.findAll(query);
    }
    getStats() {
        return this.assetsService.getAssetStats();
    }
    async findByAssetNumber(assetNumber) {
        return this.assetItemService.findByAssetNumber(assetNumber);
    }
    findOne(id) {
        return this.assetsService.findOne(id);
    }
    update(id, updateAssetDto) {
        return this.assetsService.update(id, updateAssetDto);
    }
    remove(id) {
        return this.assetsService.remove(id);
    }
    assignAsset(id, assignAssetDto) {
        return this.assetsService.assignAsset(id, assignAssetDto);
    }
    unassignAsset(id) {
        return this.assetsService.unassignAsset(id);
    }
    getAssetQuantities(id) {
        return this.assetQuantityService.getAssetQuantitiesByStatus(id);
    }
    transferQuantity(transferDto, user) {
        return this.assetQuantityService.transferQuantity(transferDto, user.id);
    }
    bulkTransferQuantity(bulkTransferDto, user) {
        return this.assetQuantityService.bulkTransferQuantity(bulkTransferDto, user.id);
    }
    setAssetQuantities(setQuantityDto, user) {
        return this.assetQuantityService.setAssetQuantities(setQuantityDto, user.id);
    }
    getTransferHistory(id) {
        return this.assetQuantityService.getTransferHistory(id);
    }
    getAllAssetQuantities() {
        return this.assetQuantityService.getAllAssetQuantities();
    }
    createAssetItems(assetId, createDto, user) {
        return this.assetItemService.createMultipleAssetItems(assetId, createDto.quantity, user.id);
    }
    getAssetItems(assetId) {
        return this.assetItemService.getAssetItemsByAssetId(assetId);
    }
    getAssetItem(itemId) {
        return this.assetItemService.getAssetItemById(itemId);
    }
    updateAssetItem(itemId, updateDto, user) {
        return this.assetItemService.updateAssetItem(itemId, updateDto, user.id);
    }
    transferAssetItem(transferDto, user) {
        return this.assetItemService.transferAssetItem(transferDto, user.id);
    }
    getAssetItemQuantities(assetId) {
        return this.assetItemService.getAssetQuantitiesByStatus(assetId);
    }
    getAllAssetItemQuantities() {
        return this.assetItemService.getAllAssetQuantities();
    }
    deleteAssetItem(itemId) {
        return this.assetItemService.deleteAssetItem(itemId);
    }
};
exports.AssetsController = AssetsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new asset (Admin/Manager only)' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Asset successfully created',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Category or Location not found',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_asset_dto_1.CreateAssetDto, Object]),
    __metadata("design:returntype", Promise)
], AssetsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all assets with filtering and pagination' }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        description: 'Search term for name, description, asset number, serial number, manufacturer, model',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'categoryId',
        required: false,
        description: 'Filter by category ID',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'locationId',
        required: false,
        description: 'Filter by location ID',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'status',
        required: false,
        enum: asset_entity_1.AssetStatus,
        description: 'Filter by status',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'condition',
        required: false,
        description: 'Filter by condition (excellent, good, fair, poor)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'assignedToId',
        required: false,
        description: 'Filter by assigned user ID',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'manufacturer',
        required: false,
        description: 'Filter by manufacturer',
    }),
    (0, swagger_1.ApiQuery)({ name: 'model', required: false, description: 'Filter by model' }),
    (0, swagger_1.ApiQuery)({
        name: 'minValue',
        required: false,
        type: Number,
        description: 'Minimum total value',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'maxValue',
        required: false,
        type: Number,
        description: 'Maximum total value',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'purchaseDateFrom',
        required: false,
        description: 'Purchase date from (YYYY-MM-DD)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'purchaseDateTo',
        required: false,
        description: 'Purchase date to (YYYY-MM-DD)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number (default: 1)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Items per page (default: 10)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortBy',
        required: false,
        description: 'Sort field (default: createdAt)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortOrder',
        required: false,
        enum: ['ASC', 'DESC'],
        description: 'Sort order (default: DESC)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Assets retrieved successfully',
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({ summary: 'Get asset statistics (Admin/Manager only)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset statistics retrieved successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('by-asset-number/:assetNumber'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get asset item by asset number (Public - No Auth Required)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset item found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Asset item not found',
    }),
    __param(0, (0, common_1.Param)('assetNumber')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AssetsController.prototype, "findByAssetNumber", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get asset by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset retrieved successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Asset not found',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({ summary: 'Update asset (Admin/Manager only)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset updated successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Asset not found',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_asset_dto_1.UpdateAssetDto]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({ summary: 'Delete asset (Admin/Manager only)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Asset not found',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/assign'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({ summary: 'Assign asset to user (Admin/Manager only)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset assigned successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Asset or User not found',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, assign_asset_dto_1.AssignAssetDto]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "assignAsset", null);
__decorate([
    (0, common_1.Post)(':id/unassign'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({ summary: 'Unassign asset from user (Admin/Manager only)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset unassigned successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Asset not found',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "unassignAsset", null);
__decorate([
    (0, common_1.Get)(':id/quantities'),
    (0, swagger_1.ApiOperation)({ summary: 'Get asset quantities by status' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset quantities retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "getAssetQuantities", null);
__decorate([
    (0, common_1.Post)('quantities/transfer'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({
        summary: 'Transfer asset quantities between statuses (Admin/Manager only)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Quantity transfer completed successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Insufficient quantity or invalid transfer',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [transfer_quantity_dto_1.TransferQuantityDto, Object]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "transferQuantity", null);
__decorate([
    (0, common_1.Post)('quantities/bulk-transfer'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({
        summary: 'Bulk transfer asset quantities (Admin/Manager only)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Bulk quantity transfer completed successfully',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [transfer_quantity_dto_1.BulkTransferQuantityDto, Object]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "bulkTransferQuantity", null);
__decorate([
    (0, common_1.Post)('quantities/set'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({
        summary: 'Set asset quantities by status (Admin/Manager only)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset quantities set successfully',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [transfer_quantity_dto_1.SetAssetQuantityDto, Object]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "setAssetQuantities", null);
__decorate([
    (0, common_1.Get)(':id/transfer-history'),
    (0, swagger_1.ApiOperation)({ summary: 'Get asset quantity transfer history' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Transfer history retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "getTransferHistory", null);
__decorate([
    (0, common_1.Get)('quantities/all'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all asset quantities overview' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'All asset quantities retrieved successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "getAllAssetQuantities", null);
__decorate([
    (0, common_1.Post)(':id/items'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({
        summary: 'Create asset items for an asset (Admin/Manager only)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Asset items created successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "createAssetItems", null);
__decorate([
    (0, common_1.Get)(':id/items'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all asset items for an asset' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset items retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "getAssetItems", null);
__decorate([
    (0, common_1.Get)('items/:itemId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get asset item by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset item retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('itemId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "getAssetItem", null);
__decorate([
    (0, common_1.Patch)('items/:itemId'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({ summary: 'Update asset item (Admin/Manager only)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset item updated successfully',
    }),
    __param(0, (0, common_1.Param)('itemId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "updateAssetItem", null);
__decorate([
    (0, common_1.Post)('items/transfer'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({ summary: 'Transfer asset item status (Admin/Manager only)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset item transferred successfully',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "transferAssetItem", null);
__decorate([
    (0, common_1.Get)(':id/items/quantities'),
    (0, swagger_1.ApiOperation)({ summary: 'Get asset item quantities by status' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset item quantities retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "getAssetItemQuantities", null);
__decorate([
    (0, common_1.Get)('items/quantities/all'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all asset item quantities overview' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'All asset item quantities retrieved successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "getAllAssetItemQuantities", null);
__decorate([
    (0, common_1.Delete)('items/:itemId'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete asset item (Admin only)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset item deleted successfully',
    }),
    __param(0, (0, common_1.Param)('itemId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssetsController.prototype, "deleteAssetItem", null);
exports.AssetsController = AssetsController = __decorate([
    (0, swagger_1.ApiTags)('Assets'),
    (0, common_1.Controller)('assets'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [assets_service_1.AssetsService,
        asset_quantity_service_1.AssetQuantityService,
        asset_item_service_1.AssetItemService])
], AssetsController);
//# sourceMappingURL=assets.controller.js.map