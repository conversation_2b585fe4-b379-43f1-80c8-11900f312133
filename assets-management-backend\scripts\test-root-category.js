const axios = require('axios');

const API_BASE = 'http://localhost:3001';

// First, login to get a token
async function login() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    return response.data.token;
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testRootCategoryAssetNumber() {
  try {
    console.log('🔐 Logging in...');
    const token = await login();
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    console.log('📋 Testing with root-level category...');
    
    // Get categories and find a root-level one
    const categoriesResponse = await axios.get(`${API_BASE}/categories`, { headers });
    const categories = categoriesResponse.data;
    
    // Find "Chair" which is a root category with code "CH"
    const chairCategory = categories.find(cat => cat.name === 'Chair');
    
    // Get locations and find our test warehouse
    const locationsResponse = await axios.get(`${API_BASE}/locations`, { headers });
    const locations = locationsResponse.data;
    const testWarehouse = locations.find(loc => loc.name === 'Test Warehouse');

    console.log(`Using root category: ${chairCategory.name} (Code: ${chairCategory.code})`);
    console.log(`Using location: ${testWarehouse.name} (Code: ${testWarehouse.code})`);
    console.log(`Expected format: ${testWarehouse.code}-${chairCategory.code}-XXXX`);

    // Create a test asset with root category
    console.log('\n💺 Creating asset with root category...');
    const assetResponse = await axios.post(`${API_BASE}/assets`, {
      name: 'Test Office Chair',
      description: 'Testing asset number with root category',
      categoryId: chairCategory.id,
      locationId: testWarehouse.id,
      unitPrice: 200,
      quantity: 1
    }, { headers });

    const asset = assetResponse.data;
    console.log(`✅ Asset created successfully!`);
    console.log(`🎯 Generated Asset Number: ${asset.assetNumber}`);
    
    // Verify the format
    const expectedPattern = new RegExp(`^${testWarehouse.code}-${chairCategory.code}-\\d{4}$`);
    
    if (expectedPattern.test(asset.assetNumber)) {
      console.log('✅ Root category asset number format is CORRECT!');
      console.log(`   Expected: ${testWarehouse.code}-${chairCategory.code}-XXXX`);
      console.log(`   Actual: ${asset.assetNumber}`);
    } else {
      console.log('❌ Root category asset number format is INCORRECT!');
      console.log(`   Expected: ${testWarehouse.code}-${chairCategory.code}-XXXX`);
      console.log(`   Actual: ${asset.assetNumber}`);
    }

    console.log('\n🎉 Root category test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testRootCategoryAssetNumber();
