import { useState, useEffect, useContext, createContext, ReactNode } from 'react';
import { authService } from '@/lib/api';
import { User, LoginRequest, RegisterRequest } from '@/types/api';
import { useAsyncApi } from './useApi';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loginMutation = useAsyncApi(authService.login.bind(authService), {
    onSuccess: (response) => {
      setUser(response.user);
      setError(null);
    },
    onError: (error) => {
      setError(error);
      setUser(null);
    },
  });

  const registerMutation = useAsyncApi(authService.register.bind(authService), {
    onSuccess: (response) => {
      setUser(response.user);
      setError(null);
    },
    onError: (error) => {
      setError(error);
      setUser(null);
    },
  });

  const refreshUser = async () => {
    if (!authService.isAuthenticated()) {
      setUser(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const userData = await authService.getProfile();
      setUser(userData);
      setError(null);
    } catch (error) {
      console.error('Failed to refresh user:', error);
      setUser(null);
      setError(error instanceof Error ? error.message : 'Failed to refresh user');
      authService.logout();
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials: LoginRequest) => {
    await loginMutation.execute(credentials);
  };

  const register = async (userData: RegisterRequest) => {
    await registerMutation.execute(userData);
  };

  const logout = () => {
    authService.logout();
    setUser(null);
    setError(null);
  };

  useEffect(() => {
    refreshUser();
  }, []);

  const value: AuthContextType = {
    user,
    loading: loading || loginMutation.loading || registerMutation.loading,
    error: error || loginMutation.error || registerMutation.error,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Hook for checking if user has specific role
export function useRole() {
  const { user } = useAuth();
  
  const hasRole = (role: string | string[]) => {
    if (!user) return false;
    if (Array.isArray(role)) {
      return role.includes(user.role);
    }
    return user.role === role;
  };

  const isAdmin = () => hasRole('admin');
  const isManager = () => hasRole(['admin', 'manager']);
  const isViewer = () => hasRole('viewer');

  return {
    hasRole,
    isAdmin,
    isManager,
    isViewer,
    currentRole: user?.role,
  };
}
