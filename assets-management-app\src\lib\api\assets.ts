import { apiClient } from './client';
import { API_CONFIG } from './config';
import {
  Asset,
  CreateAssetRequest,
  UpdateAssetRequest,
  AssignAssetRequest,
  AssetSearchParams,
  AssetStats,
} from '@/types/api';

export interface AssetListResponse {
  assets: Asset[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class AssetsService {
  async getAssets(params?: AssetSearchParams): Promise<AssetListResponse> {
    return apiClient.get<AssetListResponse>(
      API_CONFIG.ENDPOINTS.ASSETS.BASE,
      params
    );
  }

  async getAssetById(id: string): Promise<Asset> {
    return apiClient.get<Asset>(API_CONFIG.ENDPOINTS.ASSETS.BY_ID(id));
  }

  async getAssetByNumber(assetNumber: string): Promise<Asset> {
    return apiClient.get<Asset>(
      API_CONFIG.ENDPOINTS.ASSETS.BY_ASSET_NUMBER(assetNumber)
    );
  }

  async createAsset(data: CreateAssetRequest): Promise<Asset> {
    return apiClient.post<Asset>(API_CONFIG.ENDPOINTS.ASSETS.BASE, data);
  }

  async updateAsset(id: string, data: UpdateAssetRequest): Promise<Asset> {
    return apiClient.patch<Asset>(API_CONFIG.ENDPOINTS.ASSETS.BY_ID(id), data);
  }

  async deleteAsset(id: string): Promise<void> {
    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.ASSETS.BY_ID(id));
  }

  async assignAsset(id: string, data: AssignAssetRequest): Promise<Asset> {
    return apiClient.post<Asset>(
      API_CONFIG.ENDPOINTS.ASSETS.ASSIGN(id),
      data
    );
  }

  async unassignAsset(id: string): Promise<Asset> {
    return apiClient.post<Asset>(API_CONFIG.ENDPOINTS.ASSETS.UNASSIGN(id));
  }

  async getAssetStats(): Promise<AssetStats> {
    return apiClient.get<AssetStats>(API_CONFIG.ENDPOINTS.ASSETS.STATS);
  }

  // Search assets with debouncing support
  async searchAssets(
    searchTerm: string,
    filters?: Omit<AssetSearchParams, 'search'>
  ): Promise<AssetListResponse> {
    return this.getAssets({
      search: searchTerm,
      ...filters,
    });
  }

  // Get assets by category
  async getAssetsByCategory(categoryId: string): Promise<AssetListResponse> {
    return this.getAssets({ categoryId });
  }

  // Get assets by location
  async getAssetsByLocation(locationId: string): Promise<AssetListResponse> {
    return this.getAssets({ locationId });
  }

  // Get assets assigned to a user
  async getAssetsByUser(userId: string): Promise<AssetListResponse> {
    return this.getAssets({ assignedToId: userId });
  }

  // Upload asset image
  async uploadAssetImage(assetId: string, file: File): Promise<{ imageUrl: string }> {
    return apiClient.uploadFile<{ imageUrl: string }>(
      `/assets/${assetId}/upload-image`,
      file
    );
  }
}

export const assetsService = new AssetsService();
