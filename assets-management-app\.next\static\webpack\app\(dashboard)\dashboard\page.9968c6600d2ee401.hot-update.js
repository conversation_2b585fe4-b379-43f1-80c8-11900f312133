"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx":
/*!************************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AssetDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Inventory.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Category.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Build.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Warning.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst statusColors = {\n    in_use: \"primary\",\n    in_stock: \"success\",\n    maintenance: \"warning\",\n    retired: \"secondary\",\n    lost: \"error\",\n    damaged: \"error\"\n};\nconst conditionColors = {\n    excellent: \"success\",\n    good: \"info\",\n    fair: \"warning\",\n    poor: \"error\"\n};\nfunction AssetDashboard() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assets, setAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load dashboard data\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Load all data in parallel\n            const [assetsResponse, categoriesData, locationsData, usersData] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.assetsService.getAssets({\n                    limit: 1000\n                }),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.getCategories(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.locationsService.getLocations(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.usersService.getUsers()\n            ]);\n            const allAssets = assetsResponse.assets;\n            setAssets(allAssets);\n            setCategories(categoriesData);\n            setLocations(locationsData);\n            setUsers(usersData);\n            // Calculate statistics\n            const totalAssets = allAssets.length;\n            const statusCounts = allAssets.reduce((acc, asset)=>{\n                acc[asset.status] = (acc[asset.status] || 0) + 1;\n                return acc;\n            }, {});\n            const conditionCounts = allAssets.reduce((acc, asset)=>{\n                acc[asset.condition] = (acc[asset.condition] || 0) + 1;\n                return acc;\n            }, {});\n            const categoryCounts = allAssets.reduce((acc, asset)=>{\n                var _asset_category;\n                const categoryName = ((_asset_category = asset.category) === null || _asset_category === void 0 ? void 0 : _asset_category.name) || \"Unknown\";\n                acc[categoryName] = (acc[categoryName] || 0) + 1;\n                return acc;\n            }, {});\n            const locationCounts = allAssets.reduce((acc, asset)=>{\n                var _asset_location;\n                const locationName = ((_asset_location = asset.location) === null || _asset_location === void 0 ? void 0 : _asset_location.name) || \"Unknown\";\n                acc[locationName] = (acc[locationName] || 0) + 1;\n                return acc;\n            }, {});\n            // Create breakdown arrays\n            const categoryBreakdown = Object.entries(categoryCounts).map((param)=>{\n                let [name, count] = param;\n                return {\n                    name,\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const locationBreakdown = Object.entries(locationCounts).map((param)=>{\n                let [name, count] = param;\n                return {\n                    name,\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const statusBreakdown = Object.entries(statusCounts).map((param)=>{\n                let [status, count] = param;\n                return {\n                    status: status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const conditionBreakdown = Object.entries(conditionCounts).map((param)=>{\n                let [condition, count] = param;\n                return {\n                    condition: condition.charAt(0).toUpperCase() + condition.slice(1),\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            // Get recent assets (last 5)\n            const recentAssets = allAssets.sort((a, b)=>new Date(b.createdAt || \"\").getTime() - new Date(a.createdAt || \"\").getTime()).slice(0, 5);\n            const dashboardStats = {\n                totalAssets,\n                inUse: statusCounts.in_use || 0,\n                inStock: statusCounts.in_stock || 0,\n                maintenance: statusCounts.maintenance || 0,\n                retired: statusCounts.retired || 0,\n                lost: statusCounts.lost || 0,\n                damaged: statusCounts.damaged || 0,\n                totalCategories: categoriesData.length,\n                totalLocations: locationsData.length,\n                totalUsers: usersData.length,\n                recentAssets,\n                categoryBreakdown,\n                locationBreakdown,\n                statusBreakdown,\n                conditionBreakdown\n            };\n            setStats(dashboardStats);\n        } catch (err) {\n            console.error(\"Failed to load dashboard data:\", err);\n            setError(\"Failed to load dashboard data. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const StatCard = (param)=>{\n        let { title, value, icon, color, subtitle } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            sx: {\n                background: \"linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)\",\n                backdropFilter: \"blur(10px)\",\n                border: \"1px solid rgba(255,255,255,0.2)\",\n                borderRadius: 3,\n                transition: \"all 0.3s ease-in-out\",\n                \"&:hover\": {\n                    transform: \"translateY(-4px)\",\n                    boxShadow: \"0 8px 25px rgba(0,0,0,0.15)\"\n                }\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    p: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    color: \"text.secondary\",\n                                    gutterBottom: true,\n                                    variant: \"body2\",\n                                    sx: {\n                                        fontWeight: 500\n                                    },\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"h3\",\n                                    component: \"div\",\n                                    color: color,\n                                    sx: {\n                                        fontWeight: 700,\n                                        mb: 0.5\n                                    },\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        fontSize: \"0.875rem\"\n                                    },\n                                    children: subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                bgcolor: \"\".concat(color, \".main\"),\n                                width: 64,\n                                height: 64,\n                                boxShadow: \"0 4px 14px rgba(0,0,0,0.15)\"\n                            },\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 212,\n            columnNumber: 5\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3,\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"60vh\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                size: 60\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 257,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    severity: \"error\",\n                    sx: {\n                        mb: 3\n                    },\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    variant: \"contained\",\n                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 48\n                    }, void 0),\n                    onClick: loadDashboardData,\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 265,\n            columnNumber: 7\n        }, this);\n    }\n    if (!stats) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                severity: \"info\",\n                children: \"No data available\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 278,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        sx: {\n            p: 4,\n            backgroundColor: \"grey.50\",\n            minHeight: \"100vh\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"h1\",\n                                gutterBottom: true,\n                                sx: {\n                                    fontWeight: 700,\n                                    color: \"text.primary\"\n                                },\n                                children: \"Asset Management Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"body1\",\n                                color: \"text.secondary\",\n                                children: \"Comprehensive overview of your organization's assets\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        variant: \"outlined\",\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 22\n                        }, void 0),\n                        onClick: loadDashboardData,\n                        sx: {\n                            borderRadius: 2,\n                            textTransform: \"none\",\n                            fontWeight: 600\n                        },\n                        children: \"Refresh Data\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Total Assets\",\n                            value: stats.totalAssets.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"primary\",\n                            subtitle: \"All registered assets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Categories\",\n                            value: stats.totalCategories.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"info\",\n                            subtitle: \"Asset categories\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Locations\",\n                            value: stats.totalLocations.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"warning\",\n                            subtitle: \"Storage locations\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Users\",\n                            value: stats.totalUsers.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"success\",\n                            subtitle: \"System users\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"In Use\",\n                            value: stats.inUse.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"primary\",\n                            subtitle: \"Currently assigned\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Available\",\n                            value: stats.inStock.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"success\",\n                            subtitle: \"Ready for assignment\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Maintenance\",\n                            value: stats.maintenance.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"warning\",\n                            subtitle: \"Under maintenance\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Retired\",\n                            value: stats.retired.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"secondary\",\n                            subtitle: \"End of life\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Lost\",\n                            value: stats.lost.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"error\",\n                            subtitle: \"Missing assets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Damaged\",\n                            value: stats.damaged.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"error\",\n                            subtitle: \"Damaged assets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Asset Status Distribution\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            mt: 2\n                                        },\n                                        children: stats.statusBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    mb: index === stats.statusBreakdown.length - 1 ? 0 : 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"space-between\",\n                                                            mb: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 500\n                                                                },\n                                                                children: item.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: [\n                                                                    item.count,\n                                                                    \" (\",\n                                                                    item.percentage,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"determinate\",\n                                                        value: item.percentage,\n                                                        sx: {\n                                                            height: 8,\n                                                            borderRadius: 4,\n                                                            backgroundColor: \"grey.200\",\n                                                            \"& .MuiLinearProgress-bar\": {\n                                                                borderRadius: 4\n                                                            }\n                                                        },\n                                                        color: item.status.toLowerCase().includes(\"use\") ? \"primary\" : item.status.toLowerCase().includes(\"stock\") ? \"success\" : item.status.toLowerCase().includes(\"maintenance\") ? \"warning\" : \"secondary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.status, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Assets by Category\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            size: \"small\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Count\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Percentage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    children: stats.categoryBreakdown.slice(0, 8).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 492,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            category.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 491,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    sx: {\n                                                                        fontWeight: 500\n                                                                    },\n                                                                    children: category.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        label: \"\".concat(category.percentage, \"%\"),\n                                                                        size: \"small\",\n                                                                        color: category.percentage > 20 ? \"primary\" : \"default\",\n                                                                        variant: category.percentage > 20 ? \"filled\" : \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 500,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 499,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, category.name, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Assets by Location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            size: \"small\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Count\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Percentage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    children: stats.locationBreakdown.slice(0, 8).map((location)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"warning\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 547,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            location.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 546,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    sx: {\n                                                                        fontWeight: 500\n                                                                    },\n                                                                    children: location.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 551,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        label: \"\".concat(location.percentage, \"%\"),\n                                                                        size: \"small\",\n                                                                        color: location.percentage > 15 ? \"warning\" : \"default\",\n                                                                        variant: location.percentage > 15 ? \"filled\" : \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 555,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, location.name, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Asset Condition Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            mt: 2\n                                        },\n                                        children: stats.conditionBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    mb: index === stats.conditionBreakdown.length - 1 ? 0 : 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"space-between\",\n                                                            mb: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 500\n                                                                },\n                                                                children: item.condition\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: [\n                                                                    item.count,\n                                                                    \" (\",\n                                                                    item.percentage,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"determinate\",\n                                                        value: item.percentage,\n                                                        sx: {\n                                                            height: 8,\n                                                            borderRadius: 4,\n                                                            backgroundColor: \"grey.200\",\n                                                            \"& .MuiLinearProgress-bar\": {\n                                                                borderRadius: 4\n                                                            }\n                                                        },\n                                                        color: item.condition.toLowerCase() === \"excellent\" ? \"success\" : item.condition.toLowerCase() === \"good\" ? \"info\" : item.condition.toLowerCase() === \"fair\" ? \"warning\" : \"error\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.condition, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 573,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 572,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Recently Added Assets\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Asset Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Condition\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 645,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Assigned To\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Date Added\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 647,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    children: stats.recentAssets.map((asset)=>{\n                                                        var _asset_category, _asset_location, _asset_assignedTo;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        fontWeight: \"medium\",\n                                                                        color: \"primary.main\",\n                                                                        children: asset.assetNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        fontWeight: \"medium\",\n                                                                        children: asset.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 659,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 665,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_category = asset.category) === null || _asset_category === void 0 ? void 0 : _asset_category.name) || \"Unknown\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 666,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 664,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 663,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"warning\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 671,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_location = asset.location) === null || _asset_location === void 0 ? void 0 : _asset_location.name) || \"Unknown\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 672,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 670,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        label: asset.status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                                                                        color: statusColors[asset.status],\n                                                                        size: \"small\",\n                                                                        variant: \"filled\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 676,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        label: asset.condition.charAt(0).toUpperCase() + asset.condition.slice(1),\n                                                                        color: conditionColors[asset.condition],\n                                                                        size: \"small\",\n                                                                        variant: \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"action\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 693,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_assignedTo = asset.assignedTo) === null || _asset_assignedTo === void 0 ? void 0 : _asset_assignedTo.fullName) || \"Unassigned\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 694,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        color: \"text.secondary\",\n                                                                        children: asset.createdAt ? new Date(asset.createdAt).toLocaleDateString() : \"Unknown\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 698,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, asset.id, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 15\n                                    }, this),\n                                    stats.recentAssets.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            textAlign: \"center\",\n                                            py: 4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"No recent assets found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 625,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 624,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 408,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 285,\n        columnNumber: 5\n    }, this);\n}\n_s(AssetDashboard, \"XHXyvutMjVwFwI//GM0yYOuxtd0=\");\n_c = AssetDashboard;\nvar _c;\n$RefreshReg$(_c, \"AssetDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx\n"));

/***/ })

});