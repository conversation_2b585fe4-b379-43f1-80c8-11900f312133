import { Category } from './category.entity';
import { Location } from './location.entity';
export declare enum AssetStatus {
    IN_STOCK = "in_stock",
    IN_USE = "in_use",
    MAINTENANCE = "maintenance",
    RETIRED = "retired",
    LOST = "lost",
    DAMAGED = "damaged"
}
export declare enum AssetCondition {
    EXCELLENT = "excellent",
    GOOD = "good",
    FAIR = "fair",
    POOR = "poor"
}
export declare class Asset {
    id: string;
    assetNumber: string;
    name: string;
    description: string;
    model: string;
    manufacturer: string;
    unitPrice: number;
    currentValue: number;
    quantity: number;
    totalValue: number;
    supplier: string;
    invoiceNumber: string;
    notes: string;
    imageUrl: string;
    isActive: boolean;
    category: Category;
    categoryId: string;
    location: Location;
    locationId: string;
    createdAt: Date;
    updatedAt: Date;
    calculateTotalValue(): void;
}
