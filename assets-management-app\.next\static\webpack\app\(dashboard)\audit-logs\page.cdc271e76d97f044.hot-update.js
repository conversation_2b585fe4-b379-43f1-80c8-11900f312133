"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/audit-logs/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/audit-logs/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/(dashboard)/audit-logs/page.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AuditLogsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TablePagination/TablePagination.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TablePagination,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_ExpandMore_Refresh_Search_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ExpandMore,Refresh,Search,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_ExpandMore_Refresh_Search_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ExpandMore,Refresh,Search,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _barrel_optimize_names_ExpandMore_Refresh_Search_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ExpandMore,Refresh,Search,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Visibility.js\");\n/* harmony import */ var _barrel_optimize_names_ExpandMore_Refresh_Search_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=ExpandMore,Refresh,Search,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var _services_auditLogsService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../services/auditLogsService */ \"(app-pages-browser)/./src/services/auditLogsService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AuditLogsPage() {\n    var _selectedLog_user, _selectedLog_user1;\n    _s();\n    const [auditLogs, setAuditLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [total, setTotal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [rowsPerPage, setRowsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedLog, setSelectedLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [detailsOpen, setDetailsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const loadAuditLogs = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await _services_auditLogsService__WEBPACK_IMPORTED_MODULE_2__.auditLogsService.getAuditLogs({\n                ...filters,\n                page: page + 1,\n                limit: rowsPerPage\n            });\n            setAuditLogs(response.auditLogs);\n            setTotal(response.total);\n        } catch (err) {\n            console.error(\"Failed to load audit logs:\", err);\n            setError(\"Failed to load audit logs. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAuditLogs();\n    }, [\n        page,\n        rowsPerPage,\n        filters\n    ]);\n    const handleFilterChange = (field, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        setPage(0) // Reset to first page when filtering\n        ;\n    };\n    const handleClearFilters = ()=>{\n        setFilters({});\n        setPage(0);\n    };\n    const handleViewDetails = (log)=>{\n        setSelectedLog(log);\n        setDetailsOpen(true);\n    };\n    const formatDateTime = (dateString)=>{\n        return new Date(dateString).toLocaleString();\n    };\n    const renderValue = (value)=>{\n        if (value === null || value === undefined) return \"N/A\";\n        if (typeof value === \"object\") return JSON.stringify(value, null, 2);\n        return String(value);\n    };\n    if (loading && auditLogs.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            sx: {\n                p: 3,\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"60vh\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 60\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        sx: {\n            p: 4,\n            backgroundColor: \"grey.50\",\n            minHeight: \"100vh\"\n        },\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mb: 3\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"h1\",\n                                gutterBottom: true,\n                                sx: {\n                                    fontWeight: 700,\n                                    color: \"text.primary\"\n                                },\n                                children: \"Audit Logs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"body1\",\n                                color: \"text.secondary\",\n                                children: \"Track all system activities and changes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"outlined\",\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExpandMore_Refresh_Search_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 22\n                        }, void 0),\n                        onClick: loadAuditLogs,\n                        sx: {\n                            borderRadius: 2,\n                            textTransform: \"none\",\n                            fontWeight: 600\n                        },\n                        children: \"Refresh\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                sx: {\n                    mb: 3,\n                    boxShadow: \"none\",\n                    border: \"1px solid\",\n                    borderColor: \"divider\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        alignItems: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    fullWidth: true,\n                                    label: \"Search\",\n                                    placeholder: \"Search logs...\",\n                                    value: filters.search || \"\",\n                                    onChange: (e)=>handleFilterChange(\"search\", e.target.value),\n                                    InputProps: {\n                                        startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExpandMore_Refresh_Search_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            sx: {\n                                                mr: 1,\n                                                color: \"text.secondary\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 35\n                                        }, void 0)\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    fullWidth: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            children: \"Action\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            value: filters.action || \"\",\n                                            label: \"Action\",\n                                            onChange: (e)=>handleFilterChange(\"action\", e.target.value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"\",\n                                                    children: \"All Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"CREATE\",\n                                                    children: \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"UPDATE\",\n                                                    children: \"Update\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"DELETE\",\n                                                    children: \"Delete\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"LOGIN\",\n                                                    children: \"Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"LOGOUT\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"EXPORT\",\n                                                    children: \"Export\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"ASSIGN\",\n                                                    children: \"Assign\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"UNASSIGN\",\n                                                    children: \"Unassign\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    fullWidth: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            children: \"Entity Type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            value: filters.entityType || \"\",\n                                            label: \"Entity Type\",\n                                            onChange: (e)=>handleFilterChange(\"entityType\", e.target.value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"\",\n                                                    children: \"All Types\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"ASSET\",\n                                                    children: \"Asset\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"ASSET_ITEM\",\n                                                    children: \"Asset Item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"CATEGORY\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"LOCATION\",\n                                                    children: \"Location\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"USER\",\n                                                    children: \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    value: \"SYSTEM\",\n                                                    children: \"System\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    fullWidth: true,\n                                    type: \"date\",\n                                    label: \"Start Date\",\n                                    value: filters.startDate || \"\",\n                                    onChange: (e)=>handleFilterChange(\"startDate\", e.target.value),\n                                    InputLabelProps: {\n                                        shrink: true\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    fullWidth: true,\n                                    type: \"date\",\n                                    label: \"End Date\",\n                                    value: filters.endDate || \"\",\n                                    onChange: (e)=>handleFilterChange(\"endDate\", e.target.value),\n                                    InputLabelProps: {\n                                        shrink: true\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 1,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"outlined\",\n                                    onClick: handleClearFilters,\n                                    sx: {\n                                        height: \"56px\",\n                                        minWidth: \"auto\"\n                                    },\n                                    children: \"Clear\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                sx: {\n                    boxShadow: \"none\",\n                    border: \"1px solid\",\n                    borderColor: \"divider\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    sx: {\n                        p: 0\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    sx: {\n                                                        fontWeight: 600\n                                                    },\n                                                    children: \"Date & Time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    sx: {\n                                                        fontWeight: 600\n                                                    },\n                                                    children: \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    sx: {\n                                                        fontWeight: 600\n                                                    },\n                                                    children: \"Action\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    sx: {\n                                                        fontWeight: 600\n                                                    },\n                                                    children: \"Entity Type\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    sx: {\n                                                        fontWeight: 600\n                                                    },\n                                                    children: \"Entity Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    sx: {\n                                                        fontWeight: 600\n                                                    },\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    align: \"center\",\n                                                    sx: {\n                                                        fontWeight: 600\n                                                    },\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                colSpan: 7,\n                                                align: \"center\",\n                                                sx: {\n                                                    py: 4\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    size: 40\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 19\n                                        }, this) : auditLogs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                colSpan: 7,\n                                                align: \"center\",\n                                                sx: {\n                                                    py: 4\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"text.secondary\",\n                                                    children: \"No audit logs found\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, this) : auditLogs.map((log)=>{\n                                            var _log_user;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                hover: true,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: \"body2\",\n                                                            sx: {\n                                                                fontWeight: 500\n                                                            },\n                                                            children: formatDateTime(log.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: \"body2\",\n                                                            children: ((_log_user = log.user) === null || _log_user === void 0 ? void 0 : _log_user.fullName) || \"System\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            label: _services_auditLogsService__WEBPACK_IMPORTED_MODULE_2__.auditLogsService.formatAction(log.action),\n                                                            color: _services_auditLogsService__WEBPACK_IMPORTED_MODULE_2__.auditLogsService.getActionColor(log.action),\n                                                            size: \"small\",\n                                                            variant: \"filled\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            label: _services_auditLogsService__WEBPACK_IMPORTED_MODULE_2__.auditLogsService.formatEntityType(log.entityType),\n                                                            color: _services_auditLogsService__WEBPACK_IMPORTED_MODULE_2__.auditLogsService.getEntityTypeColor(log.entityType),\n                                                            size: \"small\",\n                                                            variant: \"outlined\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: \"body2\",\n                                                            children: log.entityName || \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            variant: \"body2\",\n                                                            sx: {\n                                                                maxWidth: 200,\n                                                                overflow: \"hidden\",\n                                                                textOverflow: \"ellipsis\",\n                                                                whiteSpace: \"nowrap\"\n                                                            },\n                                                            children: log.description || \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        align: \"center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            title: \"View Details\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                size: \"small\",\n                                                                onClick: ()=>handleViewDetails(log),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExpandMore_Refresh_Search_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    fontSize: \"small\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, log.id, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                            component: \"div\",\n                            count: total,\n                            page: page,\n                            onPageChange: (_, newPage)=>setPage(newPage),\n                            rowsPerPage: rowsPerPage,\n                            onRowsPerPageChange: (e)=>{\n                                setRowsPerPage(parseInt(e.target.value, 10));\n                                setPage(0);\n                            },\n                            rowsPerPageOptions: [\n                                10,\n                                25,\n                                50,\n                                100\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                open: detailsOpen,\n                onClose: ()=>setDetailsOpen(false),\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                        children: \"Audit Log Details\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                        children: selectedLog && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                mt: 2\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                container: true,\n                                spacing: 3,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        md: 6,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                variant: \"subtitle2\",\n                                                gutterBottom: true,\n                                                children: \"Date & Time\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                variant: \"body2\",\n                                                sx: {\n                                                    mb: 2\n                                                },\n                                                children: formatDateTime(selectedLog.createdAt)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        md: 6,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                variant: \"subtitle2\",\n                                                gutterBottom: true,\n                                                children: \"User\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                variant: \"body2\",\n                                                sx: {\n                                                    mb: 2\n                                                },\n                                                children: [\n                                                    ((_selectedLog_user = selectedLog.user) === null || _selectedLog_user === void 0 ? void 0 : _selectedLog_user.fullName) || \"System\",\n                                                    \" (\",\n                                                    ((_selectedLog_user1 = selectedLog.user) === null || _selectedLog_user1 === void 0 ? void 0 : _selectedLog_user1.email) || \"N/A\",\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        md: 6,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                variant: \"subtitle2\",\n                                                gutterBottom: true,\n                                                children: \"Action\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                label: _services_auditLogsService__WEBPACK_IMPORTED_MODULE_2__.auditLogsService.formatAction(selectedLog.action),\n                                                color: _services_auditLogsService__WEBPACK_IMPORTED_MODULE_2__.auditLogsService.getActionColor(selectedLog.action),\n                                                size: \"small\",\n                                                sx: {\n                                                    mb: 2\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        md: 6,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                variant: \"subtitle2\",\n                                                gutterBottom: true,\n                                                children: \"Entity Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                label: _services_auditLogsService__WEBPACK_IMPORTED_MODULE_2__.auditLogsService.formatEntityType(selectedLog.entityType),\n                                                color: _services_auditLogsService__WEBPACK_IMPORTED_MODULE_2__.auditLogsService.getEntityTypeColor(selectedLog.entityType),\n                                                size: \"small\",\n                                                variant: \"outlined\",\n                                                sx: {\n                                                    mb: 2\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                variant: \"subtitle2\",\n                                                gutterBottom: true,\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                variant: \"body2\",\n                                                sx: {\n                                                    mb: 2\n                                                },\n                                                children: selectedLog.description || \"No description available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 17\n                                    }, this),\n                                    selectedLog.oldValues && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExpandMore_Refresh_Search_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 53\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        variant: \"subtitle2\",\n                                                        children: \"Old Values\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                        sx: {\n                                                            p: 2,\n                                                            backgroundColor: \"grey.50\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            style: {\n                                                                margin: 0,\n                                                                fontSize: \"0.875rem\",\n                                                                whiteSpace: \"pre-wrap\"\n                                                            },\n                                                            children: renderValue(selectedLog.oldValues)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 19\n                                    }, this),\n                                    selectedLog.newValues && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExpandMore_Refresh_Search_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 53\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        variant: \"subtitle2\",\n                                                        children: \"New Values\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                        sx: {\n                                                            p: 2,\n                                                            backgroundColor: \"grey.50\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            style: {\n                                                                margin: 0,\n                                                                fontSize: \"0.875rem\",\n                                                                whiteSpace: \"pre-wrap\"\n                                                            },\n                                                            children: renderValue(selectedLog.newValues)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 19\n                                    }, this),\n                                    selectedLog.metadata && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                    expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExpandMore_Refresh_Search_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 53\n                                                    }, void 0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        variant: \"subtitle2\",\n                                                        children: \"Metadata\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                        sx: {\n                                                            p: 2,\n                                                            backgroundColor: \"grey.50\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            style: {\n                                                                margin: 0,\n                                                                fontSize: \"0.875rem\",\n                                                                whiteSpace: \"pre-wrap\"\n                                                            },\n                                                            children: renderValue(selectedLog.metadata)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TablePagination_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            onClick: ()=>setDetailsOpen(false),\n                            children: \"Close\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\audit-logs\\\\page.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n_s(AuditLogsPage, \"sG9qJ7fqFcE7Dd41hoJCVQqXsDs=\");\n_c = AuditLogsPage;\nvar _c;\n$RefreshReg$(_c, \"AuditLogsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/audit-logs/page.tsx\n"));

/***/ })

});