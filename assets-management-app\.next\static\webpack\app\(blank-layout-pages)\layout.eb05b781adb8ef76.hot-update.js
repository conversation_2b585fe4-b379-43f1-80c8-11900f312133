"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(blank-layout-pages)/layout",{

/***/ "(app-pages-browser)/./src/lib/api/assetItemService.ts":
/*!*****************************************!*\
  !*** ./src/lib/api/assetItemService.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assetItemService: function() { return /* binding */ assetItemService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\nconst assetItemService = {\n    // Create multiple asset items for an asset\n    async createAssetItems (assetId, data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/\".concat(assetId, \"/items\"), data);\n        return response;\n    },\n    // Get all asset items for a specific asset\n    async getAssetItems (assetId) {\n        console.log(\"AssetItemService: Getting asset items for asset ID:\", assetId);\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/\".concat(assetId, \"/items\"));\n        console.log(\"AssetItemService: Response received:\", response);\n        return response;\n    },\n    // Get asset item by ID\n    async getAssetItem (itemId) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/items/\".concat(itemId));\n        return response;\n    },\n    // Update asset item\n    async updateAssetItem (itemId, data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(\"/assets/items/\".concat(itemId), data);\n        return response;\n    },\n    // Transfer asset item status\n    async transferAssetItem (data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/items/transfer\", data);\n        return response;\n    },\n    // Get asset item quantities by status for a specific asset\n    async getAssetItemQuantities (assetId) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/\".concat(assetId, \"/items/quantities\"));\n        return response;\n    },\n    // Get all asset item quantities overview\n    async getAllAssetItemQuantities () {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/items/quantities/all\");\n        return response;\n    },\n    // Get asset item by asset number (Authenticated)\n    async getAssetItemByAssetNumber (assetNumber) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/by-asset-number/\".concat(assetNumber));\n        return response;\n    },\n    // Delete asset item\n    async deleteAssetItem (itemId) {\n        await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/assets/items/\".concat(itemId));\n    },\n    // Upload image for asset item\n    async uploadAssetItemImage (itemId, file) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        const token = localStorage.getItem(\"token\");\n        const headers = {};\n        if (token) {\n            headers.Authorization = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"\".concat(\"http://192.168.1.80:3001\", \"/assets/items/\").concat(itemId, \"/upload-image\"), {\n            method: \"POST\",\n            headers,\n            body: formData\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to upload image\");\n        }\n        return response.json();\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/assetItemService.ts\n"));

/***/ })

});