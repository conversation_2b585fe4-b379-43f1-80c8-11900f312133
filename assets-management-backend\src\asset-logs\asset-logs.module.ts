import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AssetLogsService } from './asset-logs.service';
import { AssetLogsController } from './asset-logs.controller';
import { AssetLog, AssetLogSchema } from '../schemas/asset-log.schema';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: AssetLog.name, schema: AssetLogSchema }]),
  ],
  controllers: [AssetLogsController],
  providers: [AssetLogsService],
  exports: [AssetLogsService],
})
export class AssetLogsModule {}
