import { apiClient } from './client';
import { API_CONFIG } from './config';
import {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  ChangePasswordRequest,
  UserRole,
} from '@/types/api';

export class UsersService {
  async getUsers(): Promise<User[]> {
    return apiClient.get<User[]>(API_CONFIG.ENDPOINTS.USERS.BASE);
  }

  async getUserById(id: string): Promise<User> {
    return apiClient.get<User>(API_CONFIG.ENDPOINTS.USERS.BY_ID(id));
  }

  async createUser(data: CreateUserRequest): Promise<User> {
    return apiClient.post<User>(API_CONFIG.ENDPOINTS.USERS.BASE, data);
  }

  async updateUser(id: string, data: UpdateUserRequest): Promise<User> {
    return apiClient.patch<User>(API_CONFIG.ENDPOINTS.USERS.BY_ID(id), data);
  }

  async deleteUser(id: string): Promise<void> {
    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.USERS.BY_ID(id));
  }

  async changePassword(id: string, data: ChangePasswordRequest): Promise<void> {
    return apiClient.patch<void>(
      API_CONFIG.ENDPOINTS.USERS.CHANGE_PASSWORD(id),
      data
    );
  }

  // Helper method to get users as options for forms
  async getUserOptions(): Promise<Array<{ value: string; label: string }>> {
    const users = await this.getUsers();
    return users.map(user => ({
      value: user.id,
      label: user.fullName,
    }));
  }

  // Get users by role
  async getUsersByRole(role: UserRole): Promise<User[]> {
    const users = await this.getUsers();
    return users.filter(user => user.role === role);
  }

  // Get active users only
  async getActiveUsers(): Promise<User[]> {
    const users = await this.getUsers();
    return users.filter(user => user.status === 'active');
  }

  // Get users by department
  async getUsersByDepartment(department: string): Promise<User[]> {
    const users = await this.getUsers();
    return users.filter(user => user.department === department);
  }

  // Search users by name or email
  async searchUsers(searchTerm: string): Promise<User[]> {
    const users = await this.getUsers();
    const term = searchTerm.toLowerCase();
    
    return users.filter(user => 
      user.fullName.toLowerCase().includes(term) ||
      user.email.toLowerCase().includes(term) ||
      user.firstName.toLowerCase().includes(term) ||
      user.lastName.toLowerCase().includes(term)
    );
  }
}

export const usersService = new UsersService();
