"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const assets_service_1 = require("./assets.service");
const assets_controller_1 = require("./assets.controller");
const asset_number_service_1 = require("./asset-number.service");
const asset_quantity_service_1 = require("./asset-quantity.service");
const asset_item_service_1 = require("./asset-item.service");
const asset_entity_1 = require("../entities/asset.entity");
const asset_item_entity_1 = require("../entities/asset-item.entity");
const asset_quantity_entity_1 = require("../entities/asset-quantity.entity");
const quantity_transfer_entity_1 = require("../entities/quantity-transfer.entity");
const category_entity_1 = require("../entities/category.entity");
const location_entity_1 = require("../entities/location.entity");
const user_entity_1 = require("../entities/user.entity");
let AssetsModule = class AssetsModule {
};
exports.AssetsModule = AssetsModule;
exports.AssetsModule = AssetsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                asset_entity_1.Asset,
                asset_item_entity_1.AssetItem,
                asset_quantity_entity_1.AssetQuantity,
                quantity_transfer_entity_1.QuantityTransfer,
                category_entity_1.Category,
                location_entity_1.Location,
                user_entity_1.User,
            ]),
        ],
        controllers: [assets_controller_1.AssetsController],
        providers: [
            assets_service_1.AssetsService,
            asset_number_service_1.AssetNumberService,
            asset_quantity_service_1.AssetQuantityService,
            asset_item_service_1.AssetItemService,
        ],
        exports: [
            assets_service_1.AssetsService,
            asset_number_service_1.AssetNumberService,
            asset_quantity_service_1.AssetQuantityService,
            asset_item_service_1.AssetItemService,
        ],
    })
], AssetsModule);
//# sourceMappingURL=assets.module.js.map