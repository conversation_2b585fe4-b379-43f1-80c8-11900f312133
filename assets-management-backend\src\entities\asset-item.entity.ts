import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Asset, AssetStatus, AssetCondition } from './asset.entity';
import { User } from './user.entity';

@Entity('asset_items')
@Index(['assetNumber'], { unique: true })
export class AssetItem {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Asset, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'assetId' })
  asset: Asset;

  @Column()
  assetId: string;

  @Column({ unique: true })
  assetNumber: string; // Unique number for each individual asset item (e.g., AST-2024-001-001)

  @Column({
    type: 'enum',
    enum: AssetStatus,
    default: AssetStatus.IN_STOCK,
  })
  status: AssetStatus;

  @Column({
    type: 'enum',
    enum: AssetCondition,
    default: AssetCondition.EXCELLENT,
  })
  condition: AssetCondition;

  @Column({ nullable: true })
  serialNumber: string;

  @Column({ type: 'date', nullable: true })
  purchaseDate: Date;

  @Column({ type: 'date', nullable: true })
  warrantyExpiry: Date;

  @Column({ nullable: true })
  notes: string;

  @Column({ nullable: true })
  imageUrl: string; // Image URL for individual asset item

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'assignedToId' })
  assignedTo: User;

  @Column({ nullable: true })
  assignedToId: string;

  @Column({ type: 'date', nullable: true })
  assignedDate: Date;

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'lastModifiedBy' })
  lastModifiedBy: User;

  @Column({ nullable: true })
  lastModifiedById: string;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
