"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/asset-details/[assetNumber]/page",{

/***/ "(app-pages-browser)/./src/app/asset-details/[assetNumber]/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/asset-details/[assetNumber]/page.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AssetDetailsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/QrCode.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Info.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Category.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CalendarToday.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Print.js\");\n/* harmony import */ var _lib_api_assetItemService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/assetItemService */ \"(app-pages-browser)/./src/lib/api/assetItemService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AssetDetailsPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const assetNumber = params.assetNumber;\n    const [assetItem, setAssetItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Authentication hooks\n    const { user, isAuthenticated, isLoading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Role checking\n    const isAdmin = ()=>(user === null || user === void 0 ? void 0 : user.role) === \"admin\";\n    // Check authentication first\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!authLoading) {\n            if (!isAuthenticated || !isAdmin()) {\n                // Redirect to login with return URL\n                const currentUrl = \"/asset-details/\".concat(assetNumber);\n                const returnUrl = encodeURIComponent(currentUrl);\n                router.push(\"/login?returnUrl=\".concat(returnUrl));\n                return;\n            }\n        }\n    }, [\n        authLoading,\n        isAuthenticated,\n        assetNumber,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAssetDetails = async ()=>{\n            // Don't load data if not authenticated\n            if (!isAuthenticated || !isAdmin()) {\n                return;\n            }\n            try {\n                setLoading(true);\n                setError(null);\n                console.log(\"Loading asset details for asset number:\", assetNumber);\n                // Use the new direct API endpoint\n                const assetItemData = await _lib_api_assetItemService__WEBPACK_IMPORTED_MODULE_4__.assetItemService.getAssetItemByAssetNumber(assetNumber);\n                console.log(\"Loaded asset item data:\", assetItemData);\n                setAssetItem(assetItemData);\n            } catch (err) {\n                console.error(\"Failed to load asset details:\", err);\n                setError(\"Failed to load asset details: \".concat(err instanceof Error ? err.message : \"Unknown error\"));\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (assetNumber && !authLoading && isAuthenticated && isAdmin()) {\n            loadAssetDetails();\n        }\n    }, [\n        assetNumber,\n        authLoading,\n        isAuthenticated\n    ]);\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case \"in_stock\":\n                return \"success\";\n            case \"in_use\":\n                return \"primary\";\n            case \"maintenance\":\n                return \"warning\";\n            case \"retired\":\n                return \"error\";\n            default:\n                return \"default\";\n        }\n    };\n    const getConditionColor = (condition)=>{\n        switch(condition.toLowerCase()){\n            case \"excellent\":\n                return \"success\";\n            case \"good\":\n                return \"info\";\n            case \"fair\":\n                return \"warning\";\n            case \"poor\":\n                return \"error\";\n            default:\n                return \"default\";\n        }\n    };\n    const formatStatus = (status)=>{\n        return status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase());\n    };\n    const formatCondition = (condition)=>{\n        return condition.charAt(0).toUpperCase() + condition.slice(1).toLowerCase();\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        }).format(amount);\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"Not set\";\n        return new Date(dateString).toLocaleDateString();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            maxWidth: \"md\",\n            sx: {\n                py: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"50vh\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !assetItem) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            maxWidth: \"md\",\n            sx: {\n                py: 4\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    severity: \"error\",\n                    sx: {\n                        mb: 2\n                    },\n                    children: error || \"Asset not found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    variant: \"contained\",\n                    href: \"/assets\",\n                    children: \"Back to Assets\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        maxWidth: \"md\",\n        sx: {\n            py: 4\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    mb: 4,\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        sx: {\n                            mx: \"auto\",\n                            mb: 2,\n                            bgcolor: \"primary.main\",\n                            width: 64,\n                            height: 64\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            sx: {\n                                fontSize: 32\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        variant: \"h4\",\n                        component: \"h1\",\n                        gutterBottom: true,\n                        children: assetItem.asset.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        variant: \"h6\",\n                        color: \"text.secondary\",\n                        gutterBottom: true,\n                        children: [\n                            \"Asset Number: \",\n                            assetItem.assetNumber\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            gap: 1,\n                            mb: 2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                label: formatStatus(assetItem.status),\n                                color: getStatusColor(assetItem.status),\n                                variant: \"filled\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                label: formatCondition(assetItem.condition),\n                                color: getConditionColor(assetItem.condition),\n                                variant: \"outlined\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    mb: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"h6\",\n                            gutterBottom: true,\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    color: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                \"Asset Information\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.asset.description || \"No description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Unit Price\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: formatCurrency(assetItem.asset.unitPrice)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Manufacturer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.asset.manufacturer || \"Not specified\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Model\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.asset.model || \"Not specified\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                container: true,\n                spacing: 2,\n                sx: {\n                    mb: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                color: \"primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Category\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"body1\",\n                                        children: assetItem.asset.category.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                color: \"primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Location\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"body1\",\n                                        children: assetItem.asset.location.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"body2\",\n                                        color: \"text.secondary\",\n                                        children: [\n                                            \"Type: \",\n                                            assetItem.asset.location.type\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    mb: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"h6\",\n                            gutterBottom: true,\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    color: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                \"Item Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Serial Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.serialNumber || \"Not set\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Purchase Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: formatDate(assetItem.purchaseDate)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Warranty Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: formatDate(assetItem.warrantyDate)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this),\n                                assetItem.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Notes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.notes\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this),\n            assetItem.assignedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    mb: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"h6\",\n                            gutterBottom: true,\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    color: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this),\n                                \"Assigned To\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    sx: {\n                                        bgcolor: \"primary.main\"\n                                    },\n                                    children: assetItem.assignedUser.name.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.assignedUser.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: assetItem.assignedUser.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    textAlign: \"center\",\n                    mt: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        variant: \"contained\",\n                        href: \"/assets\",\n                        sx: {\n                            mr: 2\n                        },\n                        children: \"Back to Assets\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        variant: \"outlined\",\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 47\n                        }, void 0),\n                        onClick: ()=>window.print(),\n                        children: \"Print Details\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n        lineNumber: 198,\n        columnNumber: 5\n    }, this);\n}\n_s(AssetDetailsPage, \"81/RgpDyeDsJ0Vl8HgLWOdUea0k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = AssetDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"AssetDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/asset-details/[assetNumber]/page.tsx\n"));

/***/ })

});