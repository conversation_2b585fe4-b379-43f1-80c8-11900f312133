"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/layout",{

/***/ "(app-pages-browser)/./src/components/layout/vertical/Navigation.tsx":
/*!*******************************************************!*\
  !*** ./src/components/layout/vertical/Navigation.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/styles */ \"(app-pages-browser)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/styles */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _menu_vertical_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @menu/vertical-menu */ \"(app-pages-browser)/./src/@menu/vertical-menu/index.tsx\");\n/* harmony import */ var _VerticalMenu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./VerticalMenu */ \"(app-pages-browser)/./src/components/layout/vertical/VerticalMenu.tsx\");\n/* harmony import */ var _components_layout_shared_Logo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @components/layout/shared/Logo */ \"(app-pages-browser)/./src/components/layout/shared/Logo.tsx\");\n/* harmony import */ var _menu_hooks_useVerticalNav__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @menu/hooks/useVerticalNav */ \"(app-pages-browser)/./src/@menu/hooks/useVerticalNav.tsx\");\n/* harmony import */ var _core_styles_vertical_navigationCustomStyles__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/styles/vertical/navigationCustomStyles */ \"(app-pages-browser)/./src/@core/styles/vertical/navigationCustomStyles.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// React Imports\n\n// Next Imports\n\n// MUI Imports\n\n// Component Imports\n\n\n\n// Hook Imports\n\n// Style Imports\n\nconst StyledBoxForShadow = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(\"div\")((param)=>{\n    let { theme } = param;\n    return {\n        top: 60,\n        left: -8,\n        zIndex: 2,\n        opacity: 0,\n        position: \"absolute\",\n        pointerEvents: \"none\",\n        width: \"calc(100% + 15px)\",\n        height: theme.mixins.toolbar.minHeight,\n        transition: \"opacity .15s ease-in-out\",\n        background: \"linear-gradient(var(--mui-palette-background-default) 5%, rgb(var(--mui-palette-background-defaultChannel) / 0.85) 30%, rgb(var(--mui-palette-background-defaultChannel) / 0.5) 65%, rgb(var(--mui-palette-background-defaultChannel) / 0.3) 75%, transparent)\",\n        \"&.scrolled\": {\n            opacity: 1\n        }\n    };\n});\n_c = StyledBoxForShadow;\nconst Navigation = ()=>{\n    _s();\n    // Hooks\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_9__[\"default\"])();\n    const { isBreakpointReached, toggleVerticalNav } = (0,_menu_hooks_useVerticalNav__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    // Refs\n    const shadowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollMenu = (container, isPerfectScrollbar)=>{\n        container = isBreakpointReached || !isPerfectScrollbar ? container.target : container;\n        if (shadowRef && container.scrollTop > 0) {\n            // @ts-ignore\n            if (!shadowRef.current.classList.contains(\"scrolled\")) {\n                // @ts-ignore\n                shadowRef.current.classList.add(\"scrolled\");\n            }\n        } else {\n            // @ts-ignore\n            shadowRef.current.classList.remove(\"scrolled\");\n        }\n    };\n    return(// eslint-disable-next-line lines-around-comment\n    // Sidebar Vertical Menu\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_vertical_menu__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        customStyles: (0,_core_styles_vertical_navigationCustomStyles__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(theme),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_vertical_menu__WEBPACK_IMPORTED_MODULE_3__.NavHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_shared_Logo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            color: theme.palette.text.primary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\Navigation.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\Navigation.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined),\n                    isBreakpointReached && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"ri-close-line text-xl\",\n                        onClick: ()=>toggleVerticalNav(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\Navigation.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 33\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\Navigation.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StyledBoxForShadow, {\n                ref: shadowRef\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\Navigation.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VerticalMenu__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                scrollMenu: scrollMenu\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\Navigation.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\Navigation.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined));\n};\n_s(Navigation, \"2Gsf9/BRkPfIdbjbG7betSKcpgU=\", false, function() {\n    return [\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        _menu_hooks_useVerticalNav__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c1 = Navigation;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Navigation);\nvar _c, _c1;\n$RefreshReg$(_c, \"StyledBoxForShadow\");\n$RefreshReg$(_c1, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/vertical/Navigation.tsx\n"));

/***/ })

});