"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx":
/*!************************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AssetDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Inventory.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Category.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Build.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Warning.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst statusColors = {\n    in_use: \"primary\",\n    in_stock: \"success\",\n    maintenance: \"warning\",\n    retired: \"secondary\",\n    lost: \"error\",\n    damaged: \"error\",\n    unknown: \"default\"\n};\nconst conditionColors = {\n    excellent: \"success\",\n    good: \"info\",\n    fair: \"warning\",\n    poor: \"error\",\n    unknown: \"default\"\n};\nfunction AssetDashboard() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assets, setAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load dashboard data\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Load all data in parallel\n            const [assetsResponse, categoriesData, locationsData, usersData] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.assetsService.getAssets({\n                    limit: 1000\n                }),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.getCategories(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.locationsService.getLocations(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.usersService.getUsers()\n            ]);\n            const allAssets = assetsResponse.assets;\n            setAssets(allAssets);\n            setCategories(categoriesData);\n            setLocations(locationsData);\n            setUsers(usersData);\n            // Calculate statistics\n            const totalAssets = allAssets.length;\n            const statusCounts = allAssets.reduce((acc, asset)=>{\n                const status = asset.status || \"unknown\";\n                acc[status] = (acc[status] || 0) + 1;\n                return acc;\n            }, {});\n            const conditionCounts = allAssets.reduce((acc, asset)=>{\n                const condition = asset.condition || \"unknown\";\n                acc[condition] = (acc[condition] || 0) + 1;\n                return acc;\n            }, {});\n            const categoryCounts = allAssets.reduce((acc, asset)=>{\n                var _asset_category;\n                const categoryName = ((_asset_category = asset.category) === null || _asset_category === void 0 ? void 0 : _asset_category.name) || \"Unknown\";\n                acc[categoryName] = (acc[categoryName] || 0) + 1;\n                return acc;\n            }, {});\n            const locationCounts = allAssets.reduce((acc, asset)=>{\n                var _asset_location;\n                const locationName = ((_asset_location = asset.location) === null || _asset_location === void 0 ? void 0 : _asset_location.name) || \"Unknown\";\n                acc[locationName] = (acc[locationName] || 0) + 1;\n                return acc;\n            }, {});\n            // Create breakdown arrays\n            const categoryBreakdown = Object.entries(categoryCounts).map((param)=>{\n                let [name, count] = param;\n                return {\n                    name,\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const locationBreakdown = Object.entries(locationCounts).map((param)=>{\n                let [name, count] = param;\n                return {\n                    name,\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const statusBreakdown = Object.entries(statusCounts).map((param)=>{\n                let [status, count] = param;\n                return {\n                    status: status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const conditionBreakdown = Object.entries(conditionCounts).map((param)=>{\n                let [condition, count] = param;\n                return {\n                    condition: condition.charAt(0).toUpperCase() + condition.slice(1),\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            // Get recent assets (last 5)\n            const recentAssets = allAssets.sort((a, b)=>new Date(b.createdAt || \"\").getTime() - new Date(a.createdAt || \"\").getTime()).slice(0, 5);\n            const dashboardStats = {\n                totalAssets,\n                inUse: statusCounts.in_use || 0,\n                inStock: statusCounts.in_stock || 0,\n                maintenance: statusCounts.maintenance || 0,\n                retired: statusCounts.retired || 0,\n                lost: statusCounts.lost || 0,\n                damaged: statusCounts.damaged || 0,\n                totalCategories: categoriesData.length,\n                totalLocations: locationsData.length,\n                totalUsers: usersData.length,\n                recentAssets,\n                categoryBreakdown,\n                locationBreakdown,\n                statusBreakdown,\n                conditionBreakdown\n            };\n            setStats(dashboardStats);\n        } catch (err) {\n            console.error(\"Failed to load dashboard data:\", err);\n            setError(\"Failed to load dashboard data. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const StatCard = (param)=>{\n        let { title, value, icon, color, subtitle } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            sx: {\n                background: \"linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)\",\n                backdropFilter: \"blur(10px)\",\n                border: \"1px solid rgba(255,255,255,0.2)\",\n                transition: \"all 0.3s ease-in-out\",\n                \"&:hover\": {\n                    transform: \"translateY(-4px)\",\n                    boxShadow: \"0 8px 25px rgba(0,0,0,0.15)\"\n                }\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    p: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    color: \"text.secondary\",\n                                    gutterBottom: true,\n                                    variant: \"body2\",\n                                    sx: {\n                                        fontWeight: 500\n                                    },\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"h3\",\n                                    component: \"div\",\n                                    color: color,\n                                    sx: {\n                                        fontWeight: 700,\n                                        mb: 0.5\n                                    },\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        fontSize: \"0.875rem\"\n                                    },\n                                    children: subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                bgcolor: \"\".concat(color, \".main\"),\n                                width: 64,\n                                height: 64,\n                                boxShadow: \"0 4px 14px rgba(0,0,0,0.15)\"\n                            },\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 216,\n            columnNumber: 5\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3,\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"60vh\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                size: 60\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 260,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    severity: \"error\",\n                    sx: {\n                        mb: 3\n                    },\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    variant: \"contained\",\n                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 48\n                    }, void 0),\n                    onClick: loadDashboardData,\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 268,\n            columnNumber: 7\n        }, this);\n    }\n    if (!stats) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                severity: \"info\",\n                children: \"No data available\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 282,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 281,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        sx: {\n            p: 4,\n            backgroundColor: \"grey.50\",\n            minHeight: \"100vh\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"h1\",\n                                gutterBottom: true,\n                                sx: {\n                                    fontWeight: 700,\n                                    color: \"text.primary\"\n                                },\n                                children: \"Asset Management Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"body1\",\n                                color: \"text.secondary\",\n                                children: \"Comprehensive overview of your organization's assets\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        variant: \"outlined\",\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 22\n                        }, void 0),\n                        onClick: loadDashboardData,\n                        sx: {\n                            borderRadius: 2,\n                            textTransform: \"none\",\n                            fontWeight: 600\n                        },\n                        children: \"Refresh Data\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Total Assets\",\n                            value: stats.totalAssets.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"primary\",\n                            subtitle: \"All registered assets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Categories\",\n                            value: stats.totalCategories.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"info\",\n                            subtitle: \"Asset categories\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Locations\",\n                            value: stats.totalLocations.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"warning\",\n                            subtitle: \"Storage locations\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Users\",\n                            value: stats.totalUsers.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"success\",\n                            subtitle: \"System users\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"In Use\",\n                            value: stats.inUse.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"primary\",\n                            subtitle: \"Currently assigned\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Available\",\n                            value: stats.inStock.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"success\",\n                            subtitle: \"Ready for assignment\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Maintenance\",\n                            value: stats.maintenance.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"warning\",\n                            subtitle: \"Under maintenance\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Retired\",\n                            value: stats.retired.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"secondary\",\n                            subtitle: \"End of life\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Lost\",\n                            value: stats.lost.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"error\",\n                            subtitle: \"Missing assets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Damaged\",\n                            value: stats.damaged.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"error\",\n                            subtitle: \"Damaged assets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Asset Status Distribution\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            mt: 2\n                                        },\n                                        children: stats.statusBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    mb: index === stats.statusBreakdown.length - 1 ? 0 : 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"space-between\",\n                                                            mb: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 500\n                                                                },\n                                                                children: item.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: [\n                                                                    item.count,\n                                                                    \" (\",\n                                                                    item.percentage,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"determinate\",\n                                                        value: item.percentage,\n                                                        sx: {\n                                                            height: 8,\n                                                            borderRadius: 4,\n                                                            backgroundColor: \"grey.200\",\n                                                            \"& .MuiLinearProgress-bar\": {\n                                                                borderRadius: 4\n                                                            }\n                                                        },\n                                                        color: item.status.toLowerCase().includes(\"use\") ? \"primary\" : item.status.toLowerCase().includes(\"stock\") ? \"success\" : item.status.toLowerCase().includes(\"maintenance\") ? \"warning\" : \"secondary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.status, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Assets by Category\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            size: \"small\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Count\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Percentage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    children: stats.categoryBreakdown.slice(0, 8).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 495,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            category.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 494,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    sx: {\n                                                                        fontWeight: 500\n                                                                    },\n                                                                    children: category.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 499,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        label: \"\".concat(category.percentage, \"%\"),\n                                                                        size: \"small\",\n                                                                        color: category.percentage > 20 ? \"primary\" : \"default\",\n                                                                        variant: category.percentage > 20 ? \"filled\" : \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, category.name, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Assets by Location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            size: \"small\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Count\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Percentage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 540,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    children: stats.locationBreakdown.slice(0, 8).map((location)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"warning\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 550,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            location.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    sx: {\n                                                                        fontWeight: 500\n                                                                    },\n                                                                    children: location.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        label: \"\".concat(location.percentage, \"%\"),\n                                                                        size: \"small\",\n                                                                        color: location.percentage > 15 ? \"warning\" : \"default\",\n                                                                        variant: location.percentage > 15 ? \"filled\" : \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, location.name, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Asset Condition Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            mt: 2\n                                        },\n                                        children: stats.conditionBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    mb: index === stats.conditionBreakdown.length - 1 ? 0 : 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"space-between\",\n                                                            mb: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 500\n                                                                },\n                                                                children: item.condition\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: [\n                                                                    item.count,\n                                                                    \" (\",\n                                                                    item.percentage,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"determinate\",\n                                                        value: item.percentage,\n                                                        sx: {\n                                                            height: 8,\n                                                            borderRadius: 4,\n                                                            backgroundColor: \"grey.200\",\n                                                            \"& .MuiLinearProgress-bar\": {\n                                                                borderRadius: 4\n                                                            }\n                                                        },\n                                                        color: item.condition.toLowerCase() === \"excellent\" ? \"success\" : item.condition.toLowerCase() === \"good\" ? \"info\" : item.condition.toLowerCase() === \"fair\" ? \"warning\" : \"error\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.condition, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Recently Added Assets\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Asset Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 645,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 647,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Condition\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Assigned To\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 649,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Date Added\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    children: stats.recentAssets.map((asset)=>{\n                                                        var _asset_category, _asset_location, _asset_assignedTo;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        fontWeight: \"medium\",\n                                                                        color: \"primary.main\",\n                                                                        children: asset.assetNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 657,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        fontWeight: \"medium\",\n                                                                        children: asset.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 662,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 661,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 668,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_category = asset.category) === null || _asset_category === void 0 ? void 0 : _asset_category.name) || \"Unknown\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 669,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 667,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"warning\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 674,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_location = asset.location) === null || _asset_location === void 0 ? void 0 : _asset_location.name) || \"Unknown\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 675,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 673,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 672,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        label: asset.status ? asset.status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase()) : \"Unknown\",\n                                                                        color: asset.status ? statusColors[asset.status] : \"default\",\n                                                                        size: \"small\",\n                                                                        variant: \"filled\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 679,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        label: asset.condition ? asset.condition.charAt(0).toUpperCase() + asset.condition.slice(1) : \"Unknown\",\n                                                                        color: asset.condition ? conditionColors[asset.condition] : \"default\",\n                                                                        size: \"small\",\n                                                                        variant: \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"action\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 708,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_assignedTo = asset.assignedTo) === null || _asset_assignedTo === void 0 ? void 0 : _asset_assignedTo.fullName) || \"Unassigned\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 709,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        color: \"text.secondary\",\n                                                                        children: asset.createdAt ? new Date(asset.createdAt).toLocaleDateString() : \"Unknown\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 713,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, asset.id, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 15\n                                    }, this),\n                                    stats.recentAssets.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            textAlign: \"center\",\n                                            py: 4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"No recent assets found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 723,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 635,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 628,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 288,\n        columnNumber: 5\n    }, this);\n}\n_s(AssetDashboard, \"XHXyvutMjVwFwI//GM0yYOuxtd0=\");\n_c = AssetDashboard;\nvar _c;\n$RefreshReg$(_c, \"AssetDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx\n"));

/***/ })

});