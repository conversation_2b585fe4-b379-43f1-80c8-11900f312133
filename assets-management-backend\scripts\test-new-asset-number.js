const axios = require('axios');

const API_BASE = 'http://localhost:3001';

// First, login to get a token
async function login() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123',
    });
    return response.data.token;
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testNewAssetNumberGeneration() {
  try {
    console.log('🔐 Logging in...');
    const token = await login();

    const headers = {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    };

    console.log('📋 Fetching existing categories and locations...');

    // Get existing categories and locations
    const categoriesResponse = await axios.get(`${API_BASE}/categories`, {
      headers,
    });
    const locationsResponse = await axios.get(`${API_BASE}/locations`, {
      headers,
    });

    const categories = categoriesResponse.data;
    const locations = locationsResponse.data;

    console.log('Available categories:');
    categories.forEach((cat) => {
      console.log(`  - ${cat.name} (Code: ${cat.code || 'No code'})`);
    });

    console.log('Available locations:');
    locations.forEach((loc) => {
      console.log(`  - ${loc.name} (Code: ${loc.code || 'No code'})`);
    });

    // Find a category and location with codes
    let categoryWithCode = categories.find((cat) => cat.code);
    let locationWithCode = locations.find((loc) => loc.code);

    if (!categoryWithCode) {
      console.log('❌ No category with code found. Creating one...');
      const newCategory = await axios.post(
        `${API_BASE}/categories`,
        {
          name: 'Test Equipment',
          code: 'TST',
          description: 'Test equipment category',
        },
        { headers },
      );
      categoryWithCode = newCategory.data;
    }

    if (!locationWithCode) {
      console.log('❌ No location with code found. Creating one...');
      const newLocation = await axios.post(
        `${API_BASE}/locations`,
        {
          name: 'Test Warehouse',
          code: 'TWH',
          type: 'building',
          description: 'Test warehouse location',
        },
        { headers },
      );
      locationWithCode = newLocation.data;
    }

    console.log(`\n🧪 Testing asset number generation with:`);
    console.log(
      `   Category: ${categoryWithCode.name} (Code: ${categoryWithCode.code})`,
    );
    console.log(
      `   Location: ${locationWithCode.name} (Code: ${locationWithCode.code})`,
    );
    console.log(
      `   Expected format: ${locationWithCode.code}-${categoryWithCode.code}-XXXX`,
    );

    // Create a test asset
    console.log('\n💻 Creating test asset...');
    const assetResponse = await axios.post(
      `${API_BASE}/assets`,
      {
        name: 'Test Asset for Number Generation',
        description: 'Testing the new asset number format',
        categoryId: categoryWithCode.id,
        locationId: locationWithCode.id,
        unitPrice: 100,
        quantity: 1,
      },
      { headers },
    );

    const asset = assetResponse.data;
    console.log(`✅ Asset created successfully!`);
    console.log(`🎯 Generated Asset Number: ${asset.assetNumber}`);

    // Verify the format
    const expectedPattern = new RegExp(
      `^${locationWithCode.code}-${categoryWithCode.code}-\\d{4}$`,
    );

    if (expectedPattern.test(asset.assetNumber)) {
      console.log('✅ Asset number format is CORRECT!');
      console.log(
        `   Expected: ${locationWithCode.code}-${categoryWithCode.code}-XXXX`,
      );
      console.log(`   Actual: ${asset.assetNumber}`);
    } else {
      console.log('❌ Asset number format is INCORRECT!');
      console.log(
        `   Expected: ${locationWithCode.code}-${categoryWithCode.code}-XXXX`,
      );
      console.log(`   Actual: ${asset.assetNumber}`);
    }

    // Create another asset to test sequential numbering
    console.log('\n🔄 Creating second asset to test sequential numbering...');
    const asset2Response = await axios.post(
      `${API_BASE}/assets`,
      {
        name: 'Second Test Asset',
        description: 'Testing sequential numbering',
        categoryId: categoryWithCode.id,
        locationId: locationWithCode.id,
        unitPrice: 150,
        quantity: 1,
      },
      { headers },
    );

    const asset2 = asset2Response.data;
    console.log(`✅ Second asset created!`);
    console.log(`🎯 Generated Asset Number: ${asset2.assetNumber}`);

    // Check sequential numbering
    const firstNumber = parseInt(asset.assetNumber.split('-')[2]);
    const secondNumber = parseInt(asset2.assetNumber.split('-')[2]);

    if (secondNumber === firstNumber + 1) {
      console.log('✅ Sequential numbering is working correctly!');
    } else {
      console.log('❌ Sequential numbering issue detected!');
      console.log(`   First: ${firstNumber}, Second: ${secondNumber}`);
    }

    console.log('\n🎉 Asset number generation test completed!');
    console.log('Generated asset numbers:');
    console.log(`  1. ${asset.assetNumber}`);
    console.log(`  2. ${asset2.assetNumber}`);
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testNewAssetNumberGeneration();
