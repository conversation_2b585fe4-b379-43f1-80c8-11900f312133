import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { Location } from '../entities/location.entity';
import { CreateLocationDto } from './dto/create-location.dto';
import { UpdateLocationDto } from './dto/update-location.dto';

@Injectable()
export class LocationsService {
  constructor(
    @InjectRepository(Location)
    private locationRepository: Repository<Location>,
  ) {}

  async create(createLocationDto: CreateLocationDto): Promise<Location> {
    // Validate parent location exists if provided
    if (createLocationDto.parentId) {
      const parentLocation = await this.locationRepository.findOne({
        where: { id: createLocationDto.parentId },
      });

      if (!parentLocation) {
        throw new NotFoundException('Parent location not found');
      }
    }

    const location = this.locationRepository.create(createLocationDto);
    return this.locationRepository.save(location);
  }

  async findAll(): Promise<Location[]> {
    return this.locationRepository.find({
      relations: ['parent', 'children'],
      order: { name: 'ASC' },
    });
  }

  async findAllActive(): Promise<Location[]> {
    return this.locationRepository.find({
      where: { isActive: true },
      relations: ['parent', 'children'],
      order: { name: 'ASC' },
    });
  }

  async findOne(id: string): Promise<Location> {
    const location = await this.locationRepository.findOne({
      where: { id },
      relations: ['parent', 'children', 'assets'],
    });

    if (!location) {
      throw new NotFoundException('Location not found');
    }

    return location;
  }

  async update(
    id: string,
    updateLocationDto: UpdateLocationDto,
  ): Promise<Location> {
    const location = await this.locationRepository.findOne({ where: { id } });

    if (!location) {
      throw new NotFoundException('Location not found');
    }

    // Validate parent location exists if provided
    if (updateLocationDto.parentId) {
      if (updateLocationDto.parentId === id) {
        throw new BadRequestException('Location cannot be its own parent');
      }

      const parentLocation = await this.locationRepository.findOne({
        where: { id: updateLocationDto.parentId },
      });

      if (!parentLocation) {
        throw new NotFoundException('Parent location not found');
      }

      // Check for circular reference
      const isCircular = await this.checkCircularReference(
        id,
        updateLocationDto.parentId,
      );

      if (isCircular) {
        throw new BadRequestException('Circular reference detected');
      }
    }

    await this.locationRepository.update(id, updateLocationDto);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const location = await this.locationRepository.findOne({
      where: { id },
      relations: ['children', 'assets'],
    });

    if (!location) {
      throw new NotFoundException('Location not found');
    }

    if (location.children && location.children.length > 0) {
      throw new BadRequestException(
        'Cannot delete location with sub-locations',
      );
    }

    if (location.assets && location.assets.length > 0) {
      throw new BadRequestException('Cannot delete location with assets');
    }

    await this.locationRepository.remove(location);
  }

  async getHierarchy(): Promise<Location[]> {
    // Load all locations without relations to avoid TypeORM limitations
    const allLocations = await this.locationRepository.find({
      order: { name: 'ASC' },
    });

    // Build complete hierarchy manually with all nested levels
    const locationMap = new Map<string, any>();

    // First, create all location objects with empty children arrays
    allLocations.forEach((location) => {
      locationMap.set(location.id, {
        ...location,
        children: [],
      });
    });

    // Second, build parent-child relationships recursively
    const rootLocations: any[] = [];
    allLocations.forEach((location) => {
      const locationNode = locationMap.get(location.id);

      if (location.parentId) {
        // This is a child - add it to its parent's children array
        const parent = locationMap.get(location.parentId);
        if (parent) {
          parent.children.push(locationNode);
        }
      } else {
        // This is a root location
        rootLocations.push(locationNode);
      }
    });

    // Sort recursively
    this.sortLocationTree(rootLocations);
    return rootLocations;
  }

  private buildLocationHierarchy(allLocations: Location[]): Location[] {
    const locationMap = new Map<string, Location>();
    const rootLocations: Location[] = [];

    // Initialize all locations with empty children arrays
    allLocations.forEach((location) => {
      locationMap.set(location.id, { ...location, children: [] });
    });

    // Build the hierarchy
    allLocations.forEach((location) => {
      const locationWithChildren = locationMap.get(location.id)!;
      if (location.parentId) {
        const parent = locationMap.get(location.parentId);
        if (parent) {
          parent.children = parent.children || [];
          parent.children.push(locationWithChildren);
        }
      } else {
        rootLocations.push(locationWithChildren);
      }
    });

    // Sort children recursively
    this.sortLocationTree(rootLocations);
    return rootLocations;
  }

  private sortLocationTree(locations: Location[]): void {
    locations.sort((a, b) => a.name.localeCompare(b.name));
    locations.forEach((location) => {
      if (location.children && location.children.length > 0) {
        this.sortLocationTree(location.children);
      }
    });
  }

  // Keep the old buildLocationTree method for backward compatibility if needed
  private async buildLocationTree(locations: Location[]): Promise<Location[]> {
    for (const location of locations) {
      if (location.children && location.children.length > 0) {
        location.children = await this.buildLocationTree(location.children);
      }
    }
    return locations;
  }

  private async checkCircularReference(
    locationId: string,
    parentId: string,
  ): Promise<boolean> {
    let currentParentId: string | undefined = parentId;

    while (currentParentId) {
      if (currentParentId === locationId) {
        return true;
      }

      const parent = await this.locationRepository.findOne({
        where: { id: currentParentId },
        relations: ['parent'],
      });

      currentParentId = parent?.parentId;
    }

    return false;
  }
}
