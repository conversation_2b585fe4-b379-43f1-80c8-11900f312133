"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/categories/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/categories/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/(dashboard)/categories/page.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CategoriesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControlLabel/FormControlLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Switch/Switch.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Edit.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Delete.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ChevronRight.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Folder.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Category.js\");\n/* harmony import */ var _mui_x_tree_view_SimpleTreeView__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/x-tree-view/SimpleTreeView */ \"(app-pages-browser)/./node_modules/@mui/x-tree-view/SimpleTreeView/SimpleTreeView.js\");\n/* harmony import */ var _mui_x_tree_view_TreeItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/x-tree-view/TreeItem */ \"(app-pages-browser)/./node_modules/@mui/x-tree-view/TreeItem/TreeItem.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Mock data for development (fallback)\nconst mockCategories = [\n    {\n        id: \"1\",\n        name: \"IT Equipment\",\n        code: \"IT\",\n        description: \"All IT related equipment and devices\",\n        isActive: true,\n        parentId: null,\n        children: [\n            {\n                id: \"2\",\n                name: \"Computers\",\n                code: \"COMP\",\n                description: \"Desktop and laptop computers\",\n                isActive: true,\n                parentId: \"1\",\n                children: []\n            },\n            {\n                id: \"3\",\n                name: \"Peripherals\",\n                code: \"PERI\",\n                description: \"Keyboards, mice, monitors, etc.\",\n                isActive: true,\n                parentId: \"1\",\n                children: []\n            }\n        ]\n    },\n    {\n        id: \"4\",\n        name: \"Furniture\",\n        code: \"FURN\",\n        description: \"Office furniture and fixtures\",\n        isActive: true,\n        parentId: null,\n        children: [\n            {\n                id: \"5\",\n                name: \"Chairs\",\n                code: \"CHAIR\",\n                description: \"Office chairs and seating\",\n                isActive: true,\n                parentId: \"4\",\n                children: []\n            },\n            {\n                id: \"6\",\n                name: \"Desks\",\n                code: \"DESK\",\n                description: \"Office desks and tables\",\n                isActive: true,\n                parentId: \"4\",\n                children: []\n            }\n        ]\n    },\n    {\n        id: \"7\",\n        name: \"Vehicles\",\n        code: \"VEH\",\n        description: \"Company vehicles\",\n        isActive: true,\n        parentId: null,\n        children: []\n    }\n];\nfunction CategoriesPage() {\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dialogOpen, setDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"tree\");\n    // Load categories from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadCategories = async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Use hierarchy endpoint for tree view to avoid duplicate IDs\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.getCategoryHierarchy();\n                setCategories(data);\n            } catch (err) {\n                var _err_message, _err_message1;\n                console.error(\"Failed to load categories:\", err);\n                if (((_err_message = err.message) === null || _err_message === void 0 ? void 0 : _err_message.includes(\"401\")) || ((_err_message1 = err.message) === null || _err_message1 === void 0 ? void 0 : _err_message1.includes(\"Unauthorized\"))) {\n                    setError(\"Authentication failed. Please log in again.\");\n                    setTimeout(()=>{\n                        window.location.href = \"/login\";\n                    }, 2000);\n                } else {\n                    setError(\"Failed to load categories. Please check if the backend server is running and try again.\");\n                }\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadCategories();\n    }, []);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        code: \"\",\n        description: \"\",\n        parentId: \"\",\n        isActive: true\n    });\n    const handleAddCategory = ()=>{\n        setFormData({\n            name: \"\",\n            code: \"\",\n            description: \"\",\n            parentId: \"\",\n            isActive: true\n        });\n        setIsEditing(false);\n        setDialogOpen(true);\n    };\n    const handleEditCategory = (category)=>{\n        setFormData({\n            name: category.name,\n            code: category.code,\n            description: category.description,\n            parentId: category.parentId || \"\",\n            isActive: category.isActive\n        });\n        setSelectedCategory(category);\n        setIsEditing(true);\n        setDialogOpen(true);\n    };\n    const handleDeleteCategory = (category)=>{\n        setSelectedCategory(category);\n        setDeleteDialogOpen(true);\n    };\n    const refreshCategories = async ()=>{\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.getCategoryHierarchy();\n            setCategories(data);\n        } catch (err) {\n            console.error(\"Failed to refresh categories:\", err);\n            setError(\"Failed to refresh categories. Please try again.\");\n        }\n    };\n    const handleSaveCategory = async ()=>{\n        try {\n            if (isEditing && selectedCategory) {\n                // Update existing category\n                await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.updateCategory(selectedCategory.id, {\n                    name: formData.name,\n                    code: formData.code,\n                    description: formData.description,\n                    parentId: formData.parentId || null,\n                    isActive: formData.isActive\n                });\n            } else {\n                // Create new category\n                await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.createCategory({\n                    name: formData.name,\n                    code: formData.code,\n                    description: formData.description,\n                    parentId: formData.parentId || null,\n                    isActive: formData.isActive\n                });\n            }\n            // Refresh the entire hierarchy\n            await refreshCategories();\n            setDialogOpen(false);\n            setSelectedCategory(null);\n        } catch (err) {\n            console.error(\"Failed to save category:\", err);\n            setError(\"Failed to save category. Please try again.\");\n        }\n    };\n    const confirmDelete = async ()=>{\n        if (!selectedCategory) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.deleteCategory(selectedCategory.id);\n            // Refresh the entire hierarchy\n            await refreshCategories();\n            setDeleteDialogOpen(false);\n            setSelectedCategory(null);\n        } catch (err) {\n            console.error(\"Failed to delete category:\", err);\n            setError(\"Failed to delete category. Please try again.\");\n        }\n    };\n    const getAllCategories = (cats)=>{\n        let result = [];\n        cats.forEach((cat)=>{\n            result.push(cat);\n            if (cat.children && cat.children.length > 0) {\n                result = result.concat(getAllCategories(cat.children));\n            }\n        });\n        return result;\n    };\n    // Helper function to build full path for a category\n    const getCategoryPath = (category, allCats)=>{\n        if (!category.parentId) {\n            return category.name;\n        }\n        const parent = allCats.find((cat)=>cat.id === category.parentId);\n        if (parent) {\n            return \"\".concat(getCategoryPath(parent, allCats), \" > \").concat(category.name);\n        }\n        return category.name;\n    };\n    const renderTreeItem = (category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_tree_view_TreeItem__WEBPACK_IMPORTED_MODULE_3__.TreeItem, {\n            itemId: category.id,\n            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    py: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            flex: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"body1\",\n                                sx: {\n                                    mr: 2\n                                },\n                                children: category.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                label: category.code,\n                                size: \"small\",\n                                variant: \"outlined\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, void 0),\n                            !category.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                label: \"Inactive\",\n                                size: \"small\",\n                                color: \"error\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 36\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            gap: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                title: \"Edit\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: \"small\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleEditCategory(category);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        fontSize: \"small\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                title: \"Delete\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: \"small\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleDeleteCategory(category);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        fontSize: \"small\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, void 0),\n            children: category.children && category.children.map((child)=>renderTreeItem(child))\n        }, category.id, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n            lineNumber: 275,\n            columnNumber: 5\n        }, this);\n    const flatCategories = getAllCategories(categories);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            sx: {\n                p: 3,\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"400px\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    sx: {\n                        ml: 2\n                    },\n                    children: \"Loading categories...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n            lineNumber: 323,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        sx: {\n            p: 4,\n            backgroundColor: \"grey.50\",\n            minHeight: \"100vh\"\n        },\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mb: 3\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 334,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    mb: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"h4\",\n                        component: \"h1\",\n                        children: \"Category Management\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            gap: 2,\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    checked: viewMode === \"tree\",\n                                    onChange: (e)=>setViewMode(e.target.checked ? \"tree\" : \"table\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, void 0),\n                                label: \"Tree View\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                variant: \"contained\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 50\n                                }, void 0),\n                                onClick: handleAddCategory,\n                                color: \"primary\",\n                                children: \"Add Category\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                sx: {\n                    border: \"1px solid\",\n                    borderColor: \"divider\",\n                    boxShadow: \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    children: viewMode === \"tree\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_tree_view_SimpleTreeView__WEBPACK_IMPORTED_MODULE_19__.SimpleTreeView, {\n                        defaultCollapseIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 36\n                        }, void 0),\n                        defaultExpandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 34\n                        }, void 0),\n                        defaultExpandedItems: getAllCategories(categories).map((cat)=>cat.id),\n                        children: categories.map((category)=>renderTreeItem(category))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                children: \"Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                children: \"Parent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                align: \"center\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                    children: flatCategories.map((category)=>{\n                                        var _flatCategories_find;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            hover: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\"\n                                                        },\n                                                        children: [\n                                                            category.children && category.children.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                sx: {\n                                                                    mr: 1\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                sx: {\n                                                                    mr: 1\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            category.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        label: category.code,\n                                                        size: \"small\",\n                                                        variant: \"outlined\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    children: category.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    children: category.parentId ? ((_flatCategories_find = flatCategories.find((c)=>c.id === category.parentId)) === null || _flatCategories_find === void 0 ? void 0 : _flatCategories_find.name) || \"-\" : \"Root\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        label: category.isActive ? \"Active\" : \"Inactive\",\n                                                        color: category.isActive ? \"success\" : \"error\",\n                                                        size: \"small\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    align: \"center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"center\",\n                                                            gap: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                title: \"Edit\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    size: \"small\",\n                                                                    onClick: ()=>handleEditCategory(category),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                title: \"Delete\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    size: \"small\",\n                                                                    onClick: ()=>handleDeleteCategory(category),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, category.id, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                open: dialogOpen,\n                onClose: ()=>setDialogOpen(false),\n                maxWidth: \"sm\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                        children: isEditing ? \"Edit Category\" : \"Add New Category\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                        fullWidth: true,\n                                        label: \"Category Name\",\n                                        value: formData.name,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                        fullWidth: true,\n                                        label: \"Code\",\n                                        value: formData.code,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    code: e.target.value.toUpperCase()\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                        fullWidth: true,\n                                        label: \"Description\",\n                                        value: formData.description,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                })),\n                                        multiline: true,\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                        fullWidth: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                children: \"Parent Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                value: formData.parentId,\n                                                label: \"Parent Category\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            parentId: e.target.value\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                        value: \"\",\n                                                        children: \"None (Root Category)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    flatCategories.filter((cat)=>!isEditing || cat.id !== (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id)).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                            value: category.id,\n                                                            children: [\n                                                                getCategoryPath(category, flatCategories),\n                                                                \" (\",\n                                                                category.code,\n                                                                \")\"\n                                                            ]\n                                                        }, category.id, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            checked: formData.isActive,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        isActive: e.target.checked\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        label: \"Active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                onClick: ()=>setDialogOpen(false),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                onClick: handleSaveCategory,\n                                variant: \"contained\",\n                                children: isEditing ? \"Update\" : \"Create\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                open: deleteDialogOpen,\n                onClose: ()=>setDeleteDialogOpen(false),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                        children: \"Confirm Delete\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: [\n                                'Are you sure you want to delete category \"',\n                                selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.name,\n                                '\"?',\n                                (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.children) && selectedCategory.children.length > 0 ? \" This will also delete all subcategories.\" : \"\",\n                                \"This action cannot be undone.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 513,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                onClick: ()=>setDeleteDialogOpen(false),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                onClick: confirmDelete,\n                                color: \"error\",\n                                variant: \"contained\",\n                                children: \"Delete\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 522,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 511,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n        lineNumber: 331,\n        columnNumber: 5\n    }, this);\n}\n_s(CategoriesPage, \"mNpKX9zesD67Le7y4yKTOAVbaKM=\");\n_c = CategoriesPage;\nvar _c;\n$RefreshReg$(_c, \"CategoriesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/categories/page.tsx\n"));

/***/ })

});