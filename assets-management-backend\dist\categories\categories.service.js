"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoriesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const category_entity_1 = require("../entities/category.entity");
let CategoriesService = class CategoriesService {
    categoryRepository;
    constructor(categoryRepository) {
        this.categoryRepository = categoryRepository;
    }
    async create(createCategoryDto) {
        const existingCategory = await this.categoryRepository.findOne({
            where: { name: createCategoryDto.name },
        });
        if (existingCategory) {
            throw new common_1.ConflictException('Category with this name already exists');
        }
        if (createCategoryDto.parentId) {
            const parentCategory = await this.categoryRepository.findOne({
                where: { id: createCategoryDto.parentId },
            });
            if (!parentCategory) {
                throw new common_1.NotFoundException('Parent category not found');
            }
        }
        const category = this.categoryRepository.create(createCategoryDto);
        return this.categoryRepository.save(category);
    }
    async findAll() {
        return this.categoryRepository.find({
            relations: ['parent', 'children'],
            order: { name: 'ASC' },
        });
    }
    async findAllActive() {
        return this.categoryRepository.find({
            where: { isActive: true },
            relations: ['parent', 'children'],
            order: { name: 'ASC' },
        });
    }
    async findOne(id) {
        const category = await this.categoryRepository.findOne({
            where: { id },
            relations: ['parent', 'children', 'assets'],
        });
        if (!category) {
            throw new common_1.NotFoundException('Category not found');
        }
        return category;
    }
    async update(id, updateCategoryDto) {
        const category = await this.categoryRepository.findOne({ where: { id } });
        if (!category) {
            throw new common_1.NotFoundException('Category not found');
        }
        if (updateCategoryDto.name && updateCategoryDto.name !== category.name) {
            const existingCategory = await this.categoryRepository.findOne({
                where: { name: updateCategoryDto.name },
            });
            if (existingCategory) {
                throw new common_1.ConflictException('Category with this name already exists');
            }
        }
        if (updateCategoryDto.parentId) {
            if (updateCategoryDto.parentId === id) {
                throw new common_1.BadRequestException('Category cannot be its own parent');
            }
            const parentCategory = await this.categoryRepository.findOne({
                where: { id: updateCategoryDto.parentId },
            });
            if (!parentCategory) {
                throw new common_1.NotFoundException('Parent category not found');
            }
            const isCircular = await this.checkCircularReference(id, updateCategoryDto.parentId);
            if (isCircular) {
                throw new common_1.BadRequestException('Circular reference detected');
            }
        }
        await this.categoryRepository.update(id, updateCategoryDto);
        return this.findOne(id);
    }
    async remove(id) {
        const category = await this.categoryRepository.findOne({
            where: { id },
            relations: ['children', 'assets'],
        });
        if (!category) {
            throw new common_1.NotFoundException('Category not found');
        }
        if (category.children && category.children.length > 0) {
            throw new common_1.BadRequestException('Cannot delete category with subcategories');
        }
        if (category.assets && category.assets.length > 0) {
            throw new common_1.BadRequestException('Cannot delete category with assets');
        }
        await this.categoryRepository.remove(category);
    }
    async getHierarchy() {
        const allCategories = await this.categoryRepository.find({
            order: { name: 'ASC' },
        });
        return this.buildCategoryHierarchy(allCategories);
    }
    buildCategoryHierarchy(allCategories) {
        const categoryMap = new Map();
        const rootCategories = [];
        allCategories.forEach((category) => {
            categoryMap.set(category.id, { ...category, children: [] });
        });
        allCategories.forEach((category) => {
            const categoryWithChildren = categoryMap.get(category.id);
            if (category.parentId) {
                const parent = categoryMap.get(category.parentId);
                if (parent) {
                    parent.children = parent.children || [];
                    parent.children.push(categoryWithChildren);
                }
            }
            else {
                rootCategories.push(categoryWithChildren);
            }
        });
        this.sortCategoryTree(rootCategories);
        return rootCategories;
    }
    sortCategoryTree(categories) {
        categories.sort((a, b) => a.name.localeCompare(b.name));
        categories.forEach((category) => {
            if (category.children && category.children.length > 0) {
                this.sortCategoryTree(category.children);
            }
        });
    }
    async buildCategoryTree(categories) {
        for (const category of categories) {
            if (category.children && category.children.length > 0) {
                category.children = await this.buildCategoryTree(category.children);
            }
        }
        return categories;
    }
    async checkCircularReference(categoryId, parentId) {
        let currentParentId = parentId;
        while (currentParentId) {
            if (currentParentId === categoryId) {
                return true;
            }
            const parent = await this.categoryRepository.findOne({
                where: { id: currentParentId },
                relations: ['parent'],
            });
            currentParentId = parent?.parentId;
        }
        return false;
    }
};
exports.CategoriesService = CategoriesService;
exports.CategoriesService = CategoriesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(category_entity_1.Category)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], CategoriesService);
//# sourceMappingURL=categories.service.js.map