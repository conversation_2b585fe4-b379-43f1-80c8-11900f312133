import { AssetStatus } from '../../entities/asset.entity';
import { TransferType } from '../../entities/quantity-transfer.entity';
export declare class TransferQuantityDto {
    assetId: string;
    fromStatus: AssetStatus;
    toStatus: AssetStatus;
    quantity: number;
    reason?: string;
    notes?: string;
    transferType?: TransferType;
}
export declare class BulkTransferQuantityDto {
    transfers: TransferQuantityDto[];
    batchReason?: string;
    batchNotes?: string;
}
export declare class SetAssetQuantityDto {
    assetId: string;
    quantities: Record<AssetStatus, number>;
    reason?: string;
}
