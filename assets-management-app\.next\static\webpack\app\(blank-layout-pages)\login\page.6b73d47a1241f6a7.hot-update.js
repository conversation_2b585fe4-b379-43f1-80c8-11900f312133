"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(blank-layout-pages)/login/page",{

/***/ "(app-pages-browser)/./src/app/(blank-layout-pages)/login/page.tsx":
/*!*****************************************************!*\
  !*** ./src/app/(blank-layout-pages)/login/page.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoginPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Checkbox,FormControlLabel,IconButton,InputAdornment,Link,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Checkbox,FormControlLabel,IconButton,InputAdornment,Link,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Checkbox,FormControlLabel,IconButton,InputAdornment,Link,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Checkbox,FormControlLabel,IconButton,InputAdornment,Link,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Checkbox,FormControlLabel,IconButton,InputAdornment,Link,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Checkbox,FormControlLabel,IconButton,InputAdornment,Link,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Checkbox,FormControlLabel,IconButton,InputAdornment,Link,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/InputAdornment/InputAdornment.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Checkbox,FormControlLabel,IconButton,InputAdornment,Link,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Checkbox,FormControlLabel,IconButton,InputAdornment,Link,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControlLabel/FormControlLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Checkbox,FormControlLabel,IconButton,InputAdornment,Link,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Checkbox/Checkbox.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Checkbox,FormControlLabel,IconButton,InputAdornment,Link,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Link/Link.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Checkbox,FormControlLabel,IconButton,InputAdornment,Link,TextField,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Business_Email_Lock_Visibility_VisibilityOff_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Business,Email,Lock,Visibility,VisibilityOff!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Business.js\");\n/* harmony import */ var _barrel_optimize_names_Business_Email_Lock_Visibility_VisibilityOff_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Business,Email,Lock,Visibility,VisibilityOff!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Email.js\");\n/* harmony import */ var _barrel_optimize_names_Business_Email_Lock_Visibility_VisibilityOff_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Business,Email,Lock,Visibility,VisibilityOff!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Lock.js\");\n/* harmony import */ var _barrel_optimize_names_Business_Email_Lock_Visibility_VisibilityOff_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Business,Email,Lock,Visibility,VisibilityOff!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/VisibilityOff.js\");\n/* harmony import */ var _barrel_optimize_names_Business_Email_Lock_Visibility_VisibilityOff_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Business,Email,Lock,Visibility,VisibilityOff!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Visibility.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { login, isLoading, error, clearError } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const returnUrl = searchParams.get(\"returnUrl\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\",\n        rememberMe: false\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (field)=>(event)=>{\n            const value = event.target.type === \"checkbox\" ? event.target.checked : event.target.value;\n            setFormData((prev)=>({\n                    ...prev,\n                    [field]: value\n                }));\n            if (error) clearError();\n        };\n    const handleSubmit = async (event)=>{\n        event.preventDefault();\n        try {\n            await login({\n                email: formData.email,\n                password: formData.password\n            });\n            // Redirect to return URL if provided, otherwise to dashboard\n            if (returnUrl) {\n                router.push(decodeURIComponent(returnUrl));\n            } else {\n                router.push(\"/dashboard\");\n            }\n        } catch (err) {\n            console.error(\"Login error:\", err);\n        // Error is handled by the AuthContext\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        sx: {\n            minHeight: \"100vh\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            bgcolor: \"grey.50\",\n            p: 3\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                maxWidth: 400,\n                width: \"100%\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    p: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        sx: {\n                            textAlign: \"center\",\n                            mb: 4\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Business_Email_Lock_Visibility_VisibilityOff_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                sx: {\n                                    fontSize: 48,\n                                    color: \"primary.main\",\n                                    mb: 2\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"h1\",\n                                gutterBottom: true,\n                                children: \"Asset Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                variant: \"body2\",\n                                color: \"text.secondary\",\n                                children: \"Sign in to your account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this),\n                    returnUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        severity: \"info\",\n                        sx: {\n                            mb: 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"body2\",\n                            children: \"You will be redirected to the asset details page after login.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 13\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        severity: \"error\",\n                        sx: {\n                            mb: 3\n                        },\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                fullWidth: true,\n                                label: \"Email\",\n                                type: \"email\",\n                                value: formData.email,\n                                onChange: handleInputChange(\"email\"),\n                                required: true,\n                                sx: {\n                                    mb: 3\n                                },\n                                InputProps: {\n                                    startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        position: \"start\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Business_Email_Lock_Visibility_VisibilityOff_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            color: \"action\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 19\n                                    }, void 0)\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                fullWidth: true,\n                                label: \"Password\",\n                                type: showPassword ? \"text\" : \"password\",\n                                value: formData.password,\n                                onChange: handleInputChange(\"password\"),\n                                required: true,\n                                sx: {\n                                    mb: 3\n                                },\n                                InputProps: {\n                                    startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        position: \"start\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Business_Email_Lock_Visibility_VisibilityOff_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            color: \"action\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        position: \"end\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            edge: \"end\",\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Business_Email_Lock_Visibility_VisibilityOff_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 39\n                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Business_Email_Lock_Visibility_VisibilityOff_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 59\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 19\n                                    }, void 0)\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    display: \"flex\",\n                                    justifyContent: \"space-between\",\n                                    alignItems: \"center\",\n                                    mb: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            checked: formData.rememberMe,\n                                            onChange: handleInputChange(\"rememberMe\"),\n                                            size: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        label: \"Remember me\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        href: \"/forgot-password\",\n                                        variant: \"body2\",\n                                        children: \"Forgot password?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Checkbox_FormControlLabel_IconButton_InputAdornment_Link_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                type: \"submit\",\n                                fullWidth: true,\n                                variant: \"contained\",\n                                size: \"large\",\n                                disabled: isLoading,\n                                sx: {\n                                    mb: 3\n                                },\n                                children: isLoading ? \"Signing in...\" : \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(blank-layout-pages)\\\\login\\\\page.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"IyLqo+FB1YTyShqTuzTgmhI5Rcs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(blank-layout-pages)/login/page.tsx\n"));

/***/ })

});