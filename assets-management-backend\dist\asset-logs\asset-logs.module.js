"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetLogsModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const asset_logs_service_1 = require("./asset-logs.service");
const asset_logs_controller_1 = require("./asset-logs.controller");
const asset_log_schema_1 = require("../schemas/asset-log.schema");
let AssetLogsModule = class AssetLogsModule {
};
exports.AssetLogsModule = AssetLogsModule;
exports.AssetLogsModule = AssetLogsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([{ name: asset_log_schema_1.AssetLog.name, schema: asset_log_schema_1.AssetLogSchema }]),
        ],
        controllers: [asset_logs_controller_1.AssetLogsController],
        providers: [asset_logs_service_1.AssetLogsService],
        exports: [asset_logs_service_1.AssetLogsService],
    })
], AssetLogsModule);
//# sourceMappingURL=asset-logs.module.js.map