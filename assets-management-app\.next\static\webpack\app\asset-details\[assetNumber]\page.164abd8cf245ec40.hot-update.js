"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/asset-details/[assetNumber]/page",{

/***/ "(app-pages-browser)/./src/app/asset-details/[assetNumber]/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/asset-details/[assetNumber]/page.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AssetDetailsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/QrCode.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Info.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Category.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CalendarToday.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Print.js\");\n/* harmony import */ var _lib_api_assetItemService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/assetItemService */ \"(app-pages-browser)/./src/lib/api/assetItemService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AssetDetailsPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const assetNumber = params.assetNumber;\n    const [assetItem, setAssetItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Authentication hooks\n    const { user, isAuthenticated, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { isAdmin } = useRole();\n    // Check authentication first\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!authLoading) {\n            if (!isAuthenticated || !isAdmin()) {\n                // Redirect to login with return URL\n                const currentUrl = \"/asset-details/\".concat(assetNumber);\n                const returnUrl = encodeURIComponent(currentUrl);\n                router.push(\"/login?returnUrl=\".concat(returnUrl));\n                return;\n            }\n        }\n    }, [\n        authLoading,\n        isAuthenticated,\n        isAdmin,\n        assetNumber,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAssetDetails = async ()=>{\n            // Don't load data if not authenticated\n            if (!isAuthenticated || !isAdmin()) {\n                return;\n            }\n            try {\n                setLoading(true);\n                setError(null);\n                console.log(\"Loading asset details for asset number:\", assetNumber);\n                // Use the new direct API endpoint\n                const assetItemData = await _lib_api_assetItemService__WEBPACK_IMPORTED_MODULE_4__.assetItemService.getAssetItemByAssetNumber(assetNumber);\n                console.log(\"Loaded asset item data:\", assetItemData);\n                setAssetItem(assetItemData);\n            } catch (err) {\n                console.error(\"Failed to load asset details:\", err);\n                setError(\"Failed to load asset details: \".concat(err instanceof Error ? err.message : \"Unknown error\"));\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (assetNumber && !authLoading && isAuthenticated && isAdmin()) {\n            loadAssetDetails();\n        }\n    }, [\n        assetNumber,\n        authLoading,\n        isAuthenticated,\n        isAdmin\n    ]);\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case \"in_stock\":\n                return \"success\";\n            case \"in_use\":\n                return \"primary\";\n            case \"maintenance\":\n                return \"warning\";\n            case \"retired\":\n                return \"error\";\n            default:\n                return \"default\";\n        }\n    };\n    const getConditionColor = (condition)=>{\n        switch(condition.toLowerCase()){\n            case \"excellent\":\n                return \"success\";\n            case \"good\":\n                return \"info\";\n            case \"fair\":\n                return \"warning\";\n            case \"poor\":\n                return \"error\";\n            default:\n                return \"default\";\n        }\n    };\n    const formatStatus = (status)=>{\n        return status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase());\n    };\n    const formatCondition = (condition)=>{\n        return condition.charAt(0).toUpperCase() + condition.slice(1).toLowerCase();\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        }).format(amount);\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"Not set\";\n        return new Date(dateString).toLocaleDateString();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            maxWidth: \"md\",\n            sx: {\n                py: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"50vh\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n            lineNumber: 174,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !assetItem) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            maxWidth: \"md\",\n            sx: {\n                py: 4\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    severity: \"error\",\n                    sx: {\n                        mb: 2\n                    },\n                    children: error || \"Asset not found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    variant: \"contained\",\n                    href: \"/assets\",\n                    children: \"Back to Assets\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        maxWidth: \"md\",\n        sx: {\n            py: 4\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    mb: 4,\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        sx: {\n                            mx: \"auto\",\n                            mb: 2,\n                            bgcolor: \"primary.main\",\n                            width: 64,\n                            height: 64\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            sx: {\n                                fontSize: 32\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        variant: \"h4\",\n                        component: \"h1\",\n                        gutterBottom: true,\n                        children: assetItem.asset.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        variant: \"h6\",\n                        color: \"text.secondary\",\n                        gutterBottom: true,\n                        children: [\n                            \"Asset Number: \",\n                            assetItem.assetNumber\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            gap: 1,\n                            mb: 2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                label: formatStatus(assetItem.status),\n                                color: getStatusColor(assetItem.status),\n                                variant: \"filled\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                label: formatCondition(assetItem.condition),\n                                color: getConditionColor(assetItem.condition),\n                                variant: \"outlined\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    mb: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"h6\",\n                            gutterBottom: true,\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    color: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                \"Asset Information\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.asset.description || \"No description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Unit Price\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: formatCurrency(assetItem.asset.unitPrice)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Manufacturer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.asset.manufacturer || \"Not specified\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Model\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.asset.model || \"Not specified\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                container: true,\n                spacing: 2,\n                sx: {\n                    mb: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                color: \"primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Category\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"body1\",\n                                        children: assetItem.asset.category.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                color: \"primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Location\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"body1\",\n                                        children: assetItem.asset.location.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"body2\",\n                                        color: \"text.secondary\",\n                                        children: [\n                                            \"Type: \",\n                                            assetItem.asset.location.type\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    mb: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"h6\",\n                            gutterBottom: true,\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    color: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                \"Item Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Serial Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.serialNumber || \"Not set\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Purchase Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: formatDate(assetItem.purchaseDate)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Warranty Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: formatDate(assetItem.warrantyDate)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                assetItem.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Notes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.notes\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this),\n            assetItem.assignedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    mb: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"h6\",\n                            gutterBottom: true,\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    color: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this),\n                                \"Assigned To\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    sx: {\n                                        bgcolor: \"primary.main\"\n                                    },\n                                    children: assetItem.assignedUser.name.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.assignedUser.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: assetItem.assignedUser.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 327,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    textAlign: \"center\",\n                    mt: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        variant: \"contained\",\n                        href: \"/assets\",\n                        sx: {\n                            mr: 2\n                        },\n                        children: \"Back to Assets\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        variant: \"outlined\",\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 47\n                        }, void 0),\n                        onClick: ()=>window.print(),\n                        children: \"Print Details\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n_s(AssetDetailsPage, \"wwzQ/WMMXfJsg6u3CGcydD7Og74=\", true, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = AssetDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"AssetDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/asset-details/[assetNumber]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api/index.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAuthenticated = !!user && _lib_api__WEBPACK_IMPORTED_MODULE_3__.authService.isAuthenticated();\n    const clearError = ()=>setError(null);\n    const refreshUser = async ()=>{\n        if (!_lib_api__WEBPACK_IMPORTED_MODULE_3__.authService.isAuthenticated()) {\n            setUser(null);\n            setIsLoading(false);\n            return;\n        }\n        try {\n            setIsLoading(true);\n            const userData = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authService.getProfile();\n            setUser(userData);\n            setError(null);\n        } catch (error) {\n            console.error(\"Failed to refresh user:\", error);\n            setUser(null);\n            setError(error instanceof Error ? error.message : \"Failed to refresh user\");\n            _lib_api__WEBPACK_IMPORTED_MODULE_3__.authService.logout();\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const login = async (credentials)=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authService.login(credentials);\n            setUser(response.user);\n            // Redirect to dashboard after successful login\n            router.replace(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            setError(error instanceof Error ? error.message : \"Login failed\");\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authService.register(userData);\n            setUser(response.user);\n            // Redirect to dashboard after successful registration\n            router.replace(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Registration failed:\", error);\n            setError(error instanceof Error ? error.message : \"Registration failed\");\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = ()=>{\n        _lib_api__WEBPACK_IMPORTED_MODULE_3__.authService.logout();\n        setUser(null);\n        setError(null);\n        router.replace(\"/login\");\n    };\n    // Initialize auth state on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        refreshUser();\n    }, []);\n    // Listen for storage changes (logout from another tab)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleStorageChange = (e)=>{\n            if (e.key === \"auth_token\" && !e.newValue) {\n                // Token was removed, user logged out\n                setUser(null);\n                setError(null);\n                router.replace(\"/login\");\n            }\n        };\n        window.addEventListener(\"storage\", handleStorageChange);\n        return ()=>window.removeEventListener(\"storage\", handleStorageChange);\n    }, [\n        router\n    ]);\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        error,\n        login,\n        register,\n        logout,\n        refreshUser,\n        clearError\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 135,\n        columnNumber: 10\n    }, this);\n}\n_s(AuthProvider, \"EAPRY10XFg762l0zG7+zEIjP2Fw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

});