import { apiClient } from './client';
import { API_CONFIG } from './config';
import { AssetLog, LogAction } from '@/types/api';

export interface LogsParams {
  limit?: number;
  skip?: number;
}

export interface DateRangeParams {
  startDate: string;
  endDate: string;
  limit?: number;
}

export class AssetLogsService {
  async getRecentLogs(limit?: number): Promise<AssetLog[]> {
    return apiClient.get<AssetLog[]>(
      API_CONFIG.ENDPOINTS.ASSET_LOGS.RECENT,
      { limit }
    );
  }

  async getAssetLogs(assetId: string, params?: LogsParams): Promise<AssetLog[]> {
    return apiClient.get<AssetLog[]>(
      API_CONFIG.ENDPOINTS.ASSET_LOGS.BY_ASSET(assetId),
      params
    );
  }

  async getUserActivityLogs(userId: string, params?: LogsParams): Promise<AssetLog[]> {
    return apiClient.get<AssetLog[]>(
      API_CONFIG.ENDPOINTS.ASSET_LOGS.BY_USER(userId),
      params
    );
  }

  async getLogsByAction(action: LogAction, limit?: number): Promise<AssetLog[]> {
    return apiClient.get<AssetLog[]>(
      API_CONFIG.ENDPOINTS.ASSET_LOGS.BY_ACTION(action),
      { limit }
    );
  }

  async getLogsByDateRange(params: DateRangeParams): Promise<AssetLog[]> {
    return apiClient.get<AssetLog[]>(
      API_CONFIG.ENDPOINTS.ASSET_LOGS.DATE_RANGE,
      params
    );
  }

  // Helper methods for common log queries
  async getAssetCreationLogs(limit?: number): Promise<AssetLog[]> {
    return this.getLogsByAction(LogAction.CREATED, limit);
  }

  async getAssetAssignmentLogs(limit?: number): Promise<AssetLog[]> {
    return this.getLogsByAction(LogAction.ASSIGNED, limit);
  }

  async getAssetStatusChangeLogs(limit?: number): Promise<AssetLog[]> {
    return this.getLogsByAction(LogAction.STATUS_CHANGED, limit);
  }

  async getTodaysLogs(): Promise<AssetLog[]> {
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));

    return this.getLogsByDateRange({
      startDate: startOfDay.toISOString(),
      endDate: endOfDay.toISOString(),
      limit: 100,
    });
  }

  async getWeeklyLogs(): Promise<AssetLog[]> {
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    return this.getLogsByDateRange({
      startDate: weekAgo.toISOString(),
      endDate: today.toISOString(),
      limit: 500,
    });
  }

  async getMonthlyLogs(): Promise<AssetLog[]> {
    const today = new Date();
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    return this.getLogsByDateRange({
      startDate: monthAgo.toISOString(),
      endDate: today.toISOString(),
      limit: 1000,
    });
  }

  // Format log action for display
  formatLogAction(action: LogAction): string {
    const actionMap: Record<LogAction, string> = {
      [LogAction.CREATED]: 'Created',
      [LogAction.UPDATED]: 'Updated',
      [LogAction.DELETED]: 'Deleted',
      [LogAction.ASSIGNED]: 'Assigned',
      [LogAction.UNASSIGNED]: 'Unassigned',
      [LogAction.STATUS_CHANGED]: 'Status Changed',
      [LogAction.LOCATION_CHANGED]: 'Location Changed',
      [LogAction.MAINTENANCE]: 'Maintenance',
      [LogAction.RETIRED]: 'Retired',
    };

    return actionMap[action] || action;
  }

  // Get log action color for UI
  getLogActionColor(action: LogAction): string {
    const colorMap: Record<LogAction, string> = {
      [LogAction.CREATED]: 'success',
      [LogAction.UPDATED]: 'info',
      [LogAction.DELETED]: 'error',
      [LogAction.ASSIGNED]: 'primary',
      [LogAction.UNASSIGNED]: 'warning',
      [LogAction.STATUS_CHANGED]: 'info',
      [LogAction.LOCATION_CHANGED]: 'info',
      [LogAction.MAINTENANCE]: 'warning',
      [LogAction.RETIRED]: 'secondary',
    };

    return colorMap[action] || 'default';
  }
}

export const assetLogsService = new AssetLogsService();
