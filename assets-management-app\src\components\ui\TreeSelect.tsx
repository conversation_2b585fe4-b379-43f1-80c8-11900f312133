'use client'

import { useState, useRef } from 'react'
import {
  FormControl,
  InputLabel,
  OutlinedInput,
  Box,
  Popper,
  Paper,
  ClickAwayListener,
  Typography,
  Chip,
  IconButton,
  Fade,
  FormHelperText
} from '@mui/material'
import {
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  Clear as ClearIcon,
  KeyboardArrowDown as ArrowDownIcon
} from '@mui/icons-material'
import { SimpleTreeView } from '@mui/x-tree-view/SimpleTreeView'
import { TreeItem } from '@mui/x-tree-view/TreeItem'

export interface TreeNode {
  id: string
  name: string
  children?: TreeNode[]
  [key: string]: any
}

interface TreeSelectProps {
  label: string
  value: string
  onChange: (value: string) => void
  options: TreeNode[]
  placeholder?: string
  error?: boolean
  helperText?: string
  required?: boolean
  disabled?: boolean
  fullWidth?: boolean
  getDisplayPath?: (nodeId: string, options: TreeNode[]) => string
}

export default function TreeSelect({
  label,
  value,
  onChange,
  options,
  placeholder = 'Select an option',
  error = false,
  helperText,
  required = false,
  disabled = false,
  fullWidth = true,
  getDisplayPath
}: TreeSelectProps) {
  const [open, setOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const anchorRef = useRef<HTMLDivElement>(null)

  // Get all node IDs for default expansion
  const getAllNodeIds = (nodes: TreeNode[]): string[] => {
    const ids: string[] = []
    const traverse = (nodeList: TreeNode[]) => {
      nodeList.forEach(node => {
        ids.push(node.id)
        if (node.children && node.children.length > 0) {
          traverse(node.children)
        }
      })
    }
    traverse(nodes)
    return ids
  }

  // Find node by ID
  const findNodeById = (nodeId: string, nodes: TreeNode[]): TreeNode | null => {
    for (const node of nodes) {
      if (node.id === nodeId) {
        return node
      }
      if (node.children && node.children.length > 0) {
        const found = findNodeById(nodeId, node.children)
        if (found) return found
      }
    }
    return null
  }

  // Get display text for selected value
  const getDisplayText = (): string => {
    if (!value) return ''
    
    if (getDisplayPath) {
      return getDisplayPath(value, options)
    }
    
    const node = findNodeById(value, options)
    return node ? node.name : ''
  }

  // Build path for a node
  const buildPath = (nodeId: string, nodes: TreeNode[], currentPath: string[] = []): string[] => {
    for (const node of nodes) {
      const newPath = [...currentPath, node.name]
      if (node.id === nodeId) {
        return newPath
      }
      if (node.children && node.children.length > 0) {
        const found = buildPath(nodeId, node.children, newPath)
        if (found.length > 0) return found
      }
    }
    return []
  }

  const handleToggle = () => {
    if (!disabled) {
      setOpen(!open)
      if (!open) {
        // Expand all items when opening
        setExpandedItems(getAllNodeIds(options))
      }
    }
  }

  const handleClose = () => {
    setOpen(false)
  }

  const handleItemClick = (nodeId: string) => {
    onChange(nodeId)
    setOpen(false)
  }

  const handleClear = (event: React.MouseEvent) => {
    event.stopPropagation()
    onChange('')
  }

  // Render tree items
  const renderTreeItems = (nodes: TreeNode[]): React.ReactNode => {
    return nodes.map((node) => (
      <TreeItem
        key={node.id}
        itemId={node.id}
        label={
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              py: 0.5,
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: 'action.hover',
                borderRadius: 1
              },
              backgroundColor: value === node.id ? 'primary.light' : 'transparent',
              borderRadius: value === node.id ? 1 : 0,
              color: value === node.id ? 'primary.contrastText' : 'inherit'
            }}
            onClick={() => handleItemClick(node.id)}
          >
            <Typography variant="body2" sx={{ flexGrow: 1 }}>
              {node.name}
            </Typography>
            {value === node.id && (
              <Chip
                size="small"
                label="Selected"
                color="primary"
                variant="outlined"
                sx={{ ml: 1, height: 20 }}
              />
            )}
          </Box>
        }
      >
        {node.children && node.children.length > 0 && renderTreeItems(node.children)}
      </TreeItem>
    ))
  }

  const displayText = getDisplayText()
  const pathArray = value ? buildPath(value, options) : []

  return (
    <FormControl fullWidth={fullWidth} error={error} disabled={disabled}>
      <InputLabel required={required}>{label}</InputLabel>
      <OutlinedInput
        ref={anchorRef}
        value={displayText}
        label={label}
        placeholder={placeholder}
        readOnly
        onClick={handleToggle}
        endAdornment={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {value && !disabled && (
              <IconButton
                size="small"
                onClick={handleClear}
                sx={{ mr: 0.5 }}
              >
                <ClearIcon fontSize="small" />
              </IconButton>
            )}
            <IconButton
              size="small"
              onClick={handleToggle}
              disabled={disabled}
            >
              <ArrowDownIcon fontSize="small" />
            </IconButton>
          </Box>
        }
        sx={{
          cursor: disabled ? 'default' : 'pointer',
          '& input': {
            cursor: disabled ? 'default' : 'pointer'
          }
        }}
      />
      
      {/* Display breadcrumb path for selected item */}
      {value && pathArray.length > 0 && (
        <Box sx={{ mt: 0.5, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {pathArray.map((pathItem, index) => (
            <Typography
              key={index}
              variant="caption"
              sx={{
                color: 'text.secondary',
                '&:not(:last-child)::after': {
                  content: '" > "',
                  ml: 0.5
                }
              }}
            >
              {pathItem}
            </Typography>
          ))}
        </Box>
      )}

      <Popper
        open={open}
        anchorEl={anchorRef.current}
        placement="bottom-start"
        transition
        disablePortal
        sx={{ zIndex: 1300, width: anchorRef.current?.offsetWidth }}
      >
        {({ TransitionProps }) => (
          <Fade {...TransitionProps}>
            <Paper
              sx={{
                maxHeight: 400,
                overflow: 'auto',
                mt: 1,
                border: 1,
                borderColor: 'divider',
                boxShadow: 3
              }}
            >
              <ClickAwayListener onClickAway={handleClose}>
                <Box sx={{ p: 1 }}>
                  {options.length > 0 ? (
                    <SimpleTreeView
                      defaultCollapseIcon={<ExpandMoreIcon />}
                      defaultExpandIcon={<ChevronRightIcon />}
                      expandedItems={expandedItems}
                      onExpandedItemsChange={(event, itemIds) => setExpandedItems(itemIds)}
                    >
                      {renderTreeItems(options)}
                    </SimpleTreeView>
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
                      No options available
                    </Typography>
                  )}
                </Box>
              </ClickAwayListener>
            </Paper>
          </Fade>
        )}
      </Popper>
      
      {helperText && (
        <FormHelperText>{helperText}</FormHelperText>
      )}
    </FormControl>
  )
}
