{"c": ["app/(dashboard)/page", "app/layout", "webpack"], "r": ["_app-pages-browser_src_libs_styles_AppReactApexCharts_tsx", "_app-pages-browser_node_modules_react-apexcharts_dist_react-apexcharts_min_js"], "m": ["(app-pages-browser)/./node_modules/@mui/material/Avatar/index.js", "(app-pages-browser)/./node_modules/@mui/material/Button/index.js", "(app-pages-browser)/./node_modules/@mui/material/Card/index.js", "(app-pages-browser)/./node_modules/@mui/material/CardContent/index.js", "(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js", "(app-pages-browser)/./node_modules/@mui/material/CardHeader/cardHeaderClasses.js", "(app-pages-browser)/./node_modules/@mui/material/CardHeader/index.js", "(app-pages-browser)/./node_modules/@mui/material/Chip/index.js", "(app-pages-browser)/./node_modules/@mui/material/Grid/index.js", "(app-pages-browser)/./node_modules/@mui/material/LinearProgress/index.js", "(app-pages-browser)/./node_modules/@mui/material/Typography/index.js", "(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial%5C%5CAvatar%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial%5C%5CButton%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial%5C%5CCard%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial%5C%5CCardContent%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial%5C%5CCardHeader%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial%5C%5CChip%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial%5C%5CGrid%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial%5C%5CLinearProgress%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial%5C%5CTypography%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5C%40core%5C%5Ccomponents%5C%5Cmui%5C%5CAvatar.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5C%40core%5C%5Ccomponents%5C%5Coption-menu%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5C%40core%5C%5Cstyles%5C%5Ctable.module.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Ccomponents%5C%5CLink.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Cviews%5C%5Cdashboard%5C%5CDistributedColumnChart.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Cviews%5C%5Cdashboard%5C%5CLineChart.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Cviews%5C%5Cdashboard%5C%5CWeeklyOverview.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js", "(app-pages-browser)/./src/@core/components/mui/Avatar.tsx", "(app-pages-browser)/./src/@core/components/option-menu/index.tsx", "(app-pages-browser)/./src/@core/styles/table.module.css", "(app-pages-browser)/./src/components/Link.tsx", "(app-pages-browser)/./src/views/dashboard/DistributedColumnChart.tsx", "(app-pages-browser)/./src/views/dashboard/LineChart.tsx", "(app-pages-browser)/./src/views/dashboard/WeeklyOverview.tsx", "(app-pages-browser)/./src/libs/ApexCharts.tsx", "(app-pages-browser)/./src/libs/styles/AppReactApexCharts.tsx", "(app-pages-browser)/./node_modules/apexcharts/dist/apexcharts.common.js", "(app-pages-browser)/./node_modules/react-apexcharts/dist/react-apexcharts.min.js"]}