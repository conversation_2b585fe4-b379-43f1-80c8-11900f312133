import { Repository } from 'typeorm';
import { Asset, AssetStatus, AssetCondition } from '../entities/asset.entity';
import { AssetItem } from '../entities/asset-item.entity';
import { Category } from '../entities/category.entity';
import { Location } from '../entities/location.entity';
import { User } from '../entities/user.entity';
import { CreateAssetDto } from './dto/create-asset.dto';
import { UpdateAssetDto } from './dto/update-asset.dto';
import { AssignAssetDto } from './dto/assign-asset.dto';
import { AssetNumberService } from './asset-number.service';
export interface AssetSearchOptions {
    search?: string;
    categoryId?: string;
    locationId?: string;
    status?: AssetStatus;
    condition?: AssetCondition;
    assignedToId?: string;
    manufacturer?: string;
    model?: string;
    minValue?: number;
    maxValue?: number;
    purchaseDateFrom?: string;
    purchaseDateTo?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
}
export declare class AssetsService {
    private assetRepository;
    private assetItemRepository;
    private categoryRepository;
    private locationRepository;
    private userRepository;
    private assetNumberService;
    constructor(assetRepository: Repository<Asset>, assetItemRepository: Repository<AssetItem>, categoryRepository: Repository<Category>, locationRepository: Repository<Location>, userRepository: Repository<User>, assetNumberService: AssetNumberService);
    create(createAssetDto: CreateAssetDto): Promise<Asset>;
    findAll(options?: AssetSearchOptions): Promise<{
        assets: Asset[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findOne(id: string): Promise<Asset>;
    findByAssetNumber(assetNumber: string): Promise<Asset>;
    update(id: string, updateAssetDto: UpdateAssetDto): Promise<Asset>;
    remove(id: string): Promise<void>;
    assignAsset(id: string, assignAssetDto: AssignAssetDto): Promise<Asset>;
    unassignAsset(id: string): Promise<Asset>;
    getAssetStats(): Promise<{
        totalAssets: number;
        totalValue: number;
        statusBreakdown: Record<AssetStatus, number>;
        categoryBreakdown: Array<{
            categoryName: string;
            count: number;
            value: number;
        }>;
    }>;
}
