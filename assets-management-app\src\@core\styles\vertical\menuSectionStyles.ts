// MUI Imports
import type { Theme } from '@mui/material/styles'

// Type Imports
import type { MenuProps } from '@menu/vertical-menu'

// Util Imports
import { menuClasses } from '@menu/utils/menuClasses'

const menuSectionStyles = (theme: Theme): MenuProps['menuSectionStyles'] => {
  return {
    root: {
      marginBlockStart: theme.spacing(6),
      marginInline: theme.spacing(2),
      [`& .${menuClasses.menuSectionContent}`]: {
        color: 'var(--mui-palette-text-secondary)',
        paddingInline: `${theme.spacing(2)} !important`,
        paddingBlock: `${theme.spacing(2)} !important`,
        gap: theme.spacing(2),
        fontWeight: 600,
        fontSize: '0.75rem',
        textTransform: 'uppercase',
        letterSpacing: '0.5px',

        '&:before': {
          content: '""',
          blockSize: 1,
          inlineSize: '1rem',
          backgroundColor: 'var(--mui-palette-primary-main)',
          opacity: 0.3
        },
        '&:after': {
          content: '""',
          blockSize: 1,
          flexGrow: 1,
          backgroundColor: 'var(--mui-palette-divider)',
          opacity: 0.5
        }
      },
      [`& .${menuClasses.menuSectionLabel}`]: {
        flexGrow: 0,
        fontSize: '0.75rem',
        lineHeight: 1.5,
        fontWeight: 600,
        color: 'var(--mui-palette-text-secondary)'
      }
    }
  }
}

export default menuSectionStyles
