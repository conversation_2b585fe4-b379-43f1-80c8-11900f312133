/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./src/@core/styles/table.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
.table_table__cB3AL {
  inline-size: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
  white-space: nowrap;
}

  .table_table__cB3AL [align='right'] > * {
    text-align: end;
  }

  .table_table__cB3AL [align='center'] > * {
    text-align: center;
  }

  .table_table__cB3AL thead {
    text-transform: uppercase;
    color: var(--mui-palette-text-primary);
  }

  .table_table__cB3AL thead th {
      font-weight: 500;
      font-size: 0.8125rem;
      letter-spacing: 0.2px;
      line-height: 1.8462;
      text-align: start;
      block-size: 56px;
      background-color: var(--mui-palette-customColors-tableHeaderBg);
    }

  .table_table__cB3AL thead th:not(:first-of-type):not(:last-of-type) {
        padding-block: 0.5rem;
        padding-inline: 1rem;
      }

  .table_table__cB3AL thead th:first-of-type:not(:has(input[type='checkbox'])) {
          padding-block: 0.5rem;
          padding-inline: 1.25rem 1rem;
        }

  .table_table__cB3AL thead th:first-of-type:has(input[type='checkbox']) {
          padding-inline-start: 0.6875rem;
        }

  .table_table__cB3AL thead th:last-of-type {
        padding-block: 0.5rem;
        padding-inline: 1rem 1.25rem;
      }

  .table_table__cB3AL tbody {
    color: var(--mui-palette-text-secondary);
  }

  .table_table__cB3AL tbody th,
    .table_table__cB3AL tbody td {
      font-size: 0.9375rem;
      line-height: 1.4667;
      block-size: 50px;
    }

  .table_table__cB3AL tbody th:not(:first-of-type):not(:last-of-type), .table_table__cB3AL tbody td:not(:first-of-type):not(:last-of-type) {
        padding-block: 0.5rem;
        padding-inline: 1rem;
      }

  .table_table__cB3AL tbody th:first-of-type:not(:has(input[type='checkbox'])), .table_table__cB3AL tbody td:first-of-type:not(:has(input[type='checkbox'])) {
          padding-block: 0.5rem;
          padding-inline: 1.25rem 1rem;
        }

  .table_table__cB3AL tbody th:first-of-type:has(input[type='checkbox']), .table_table__cB3AL tbody td:first-of-type:has(input[type='checkbox']) {
          padding-inline-start: 0.6875rem;
        }

  .table_table__cB3AL tbody th:last-of-type, .table_table__cB3AL tbody td:last-of-type {
        padding-block: 0.5rem;
        padding-inline: 1rem 1.25rem;
      }

  .table_table__cB3AL tbody tr:not(:last-child) {
      border-block-end: 1px solid var(--border-color);
    }

.table_cellWithInput__N6u24 input {
  inline-size: 100%;
  background-color: transparent;
  font-size: inherit;
  color: inherit;
  border-radius: var(--mui-shape-customBorderRadius-sm);
  padding-block: 6px;
  padding-inline: 10px;
  margin-inline: -10px;
}

.table_cellWithInput__N6u24 input:focus-visible {
    outline: 1px solid var(--mui-palette-primary-main);
  }

