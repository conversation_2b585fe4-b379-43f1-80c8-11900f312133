import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Asset } from './asset.entity';

export enum LocationType {
  REGION = 'region',
  BUILDING = 'building',
  FLOOR = 'floor',
  ROOM = 'room',
  AREA = 'area',
}

@Entity('locations')
export class Location {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  code: string; // Optional location code (e.g., "INV", "WH", "BR")

  @Column({
    type: 'enum',
    enum: LocationType,
    default: LocationType.ROOM,
  })
  type: LocationType;

  @Column({ nullable: true })
  address: string;

  @Column({ nullable: true })
  coordinates: string; // JSON string for lat/lng

  @Column({ default: true })
  isActive: boolean;

  // Self-referencing for nested locations
  @ManyToOne(() => Location, (location) => location.children, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'parentId' })
  parent: Location;

  @Column({ nullable: true })
  parentId: string;

  @OneToMany(() => Location, (location) => location.parent)
  children: Location[];

  @OneToMany(() => Asset, (asset) => asset.location)
  assets: Asset[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual property for full path
  get fullPath(): string {
    if (this.parent) {
      return `${this.parent.fullPath} > ${this.name}`;
    }
    return this.name;
  }
}
