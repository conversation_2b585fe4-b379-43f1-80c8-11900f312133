"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx":
/*!************************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AssetDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Category,Inventory,LocationOn,Person,Refresh!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Category,Inventory,LocationOn,Person,Refresh!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Inventory.js\");\n/* harmony import */ var _barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Category,Inventory,LocationOn,Person,Refresh!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Category.js\");\n/* harmony import */ var _barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Category,Inventory,LocationOn,Person,Refresh!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Category,Inventory,LocationOn,Person,Refresh!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst statusColors = {\n    in_use: \"primary\",\n    in_stock: \"success\",\n    maintenance: \"warning\",\n    retired: \"secondary\",\n    lost: \"error\",\n    damaged: \"error\",\n    unknown: \"default\"\n};\nconst conditionColors = {\n    excellent: \"success\",\n    good: \"info\",\n    fair: \"warning\",\n    poor: \"error\",\n    unknown: \"default\"\n};\nfunction AssetDashboard() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assets, setAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load dashboard data\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Load all data in parallel\n            const [assetsResponse, categoriesData, locationsData, usersData] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.assetsService.getAssets({\n                    limit: 1000\n                }),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.getCategories(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.locationsService.getLocations(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.usersService.getUsers()\n            ]);\n            const allAssets = assetsResponse.assets;\n            setAssets(allAssets);\n            setCategories(categoriesData);\n            setLocations(locationsData);\n            setUsers(usersData);\n            // Calculate statistics\n            const totalAssets = allAssets.length;\n            const statusCounts = allAssets.reduce((acc, asset)=>{\n                const status = asset.status || \"unknown\";\n                acc[status] = (acc[status] || 0) + 1;\n                return acc;\n            }, {});\n            const conditionCounts = allAssets.reduce((acc, asset)=>{\n                const condition = asset.condition || \"unknown\";\n                acc[condition] = (acc[condition] || 0) + 1;\n                return acc;\n            }, {});\n            const categoryCounts = allAssets.reduce((acc, asset)=>{\n                var _asset_category;\n                const categoryName = ((_asset_category = asset.category) === null || _asset_category === void 0 ? void 0 : _asset_category.name) || \"Unknown\";\n                acc[categoryName] = (acc[categoryName] || 0) + 1;\n                return acc;\n            }, {});\n            const locationCounts = allAssets.reduce((acc, asset)=>{\n                var _asset_location;\n                const locationName = ((_asset_location = asset.location) === null || _asset_location === void 0 ? void 0 : _asset_location.name) || \"Unknown\";\n                acc[locationName] = (acc[locationName] || 0) + 1;\n                return acc;\n            }, {});\n            // Create breakdown arrays\n            const categoryBreakdown = Object.entries(categoryCounts).map((param)=>{\n                let [name, count] = param;\n                return {\n                    name,\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const locationBreakdown = Object.entries(locationCounts).map((param)=>{\n                let [name, count] = param;\n                return {\n                    name,\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const statusBreakdown = Object.entries(statusCounts).map((param)=>{\n                let [status, count] = param;\n                return {\n                    status: status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const conditionBreakdown = Object.entries(conditionCounts).map((param)=>{\n                let [condition, count] = param;\n                return {\n                    condition: condition.charAt(0).toUpperCase() + condition.slice(1),\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            // Get recent assets (last 5)\n            const recentAssets = allAssets.sort((a, b)=>new Date(b.createdAt || \"\").getTime() - new Date(a.createdAt || \"\").getTime()).slice(0, 5);\n            const dashboardStats = {\n                totalAssets,\n                inUse: statusCounts.in_use || 0,\n                inStock: statusCounts.in_stock || 0,\n                maintenance: statusCounts.maintenance || 0,\n                retired: statusCounts.retired || 0,\n                lost: statusCounts.lost || 0,\n                damaged: statusCounts.damaged || 0,\n                totalCategories: categoriesData.length,\n                totalLocations: locationsData.length,\n                totalUsers: usersData.length,\n                recentAssets,\n                categoryBreakdown,\n                locationBreakdown,\n                statusBreakdown,\n                conditionBreakdown\n            };\n            setStats(dashboardStats);\n        } catch (err) {\n            console.error(\"Failed to load dashboard data:\", err);\n            setError(\"Failed to load dashboard data. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const StatCard = (param)=>{\n        let { title, value, icon, color, subtitle } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            sx: {\n                background: \"linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)\",\n                backdropFilter: \"blur(10px)\",\n                border: \"1px solid rgba(255,255,255,0.2)\",\n                boxShadow: \"none\",\n                borderColor: \"divider\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    p: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    color: \"text.secondary\",\n                                    gutterBottom: true,\n                                    variant: \"body2\",\n                                    sx: {\n                                        fontWeight: 500\n                                    },\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"h3\",\n                                    component: \"div\",\n                                    color: color,\n                                    sx: {\n                                        fontWeight: 700,\n                                        mb: 0.5\n                                    },\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        fontSize: \"0.875rem\"\n                                    },\n                                    children: subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this),\n                        icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                bgcolor: \"\".concat(color, \".main\"),\n                                width: 48,\n                                height: 48\n                            },\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 216,\n            columnNumber: 5\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3,\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"60vh\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                size: 60\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    severity: \"error\",\n                    sx: {\n                        mb: 3\n                    },\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    variant: \"contained\",\n                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 48\n                    }, void 0),\n                    onClick: loadDashboardData,\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, this);\n    }\n    if (!stats) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                severity: \"info\",\n                children: \"No data available\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 280,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        sx: {\n            p: 4,\n            backgroundColor: \"grey.50\",\n            minHeight: \"100vh\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"h1\",\n                                gutterBottom: true,\n                                sx: {\n                                    fontWeight: 700,\n                                    color: \"text.primary\"\n                                },\n                                children: \"Asset Management Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"body1\",\n                                color: \"text.secondary\",\n                                children: \"Comprehensive overview of your organization's assets\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        variant: \"outlined\",\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 22\n                        }, void 0),\n                        onClick: loadDashboardData,\n                        sx: {\n                            borderRadius: 2,\n                            textTransform: \"none\",\n                            fontWeight: 600\n                        },\n                        children: \"Refresh Data\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Total Assets\",\n                            value: stats.totalAssets.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"primary\",\n                            subtitle: \"All registered assets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Categories\",\n                            value: stats.totalCategories.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"info\",\n                            subtitle: \"Asset categories\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Locations\",\n                            value: stats.totalLocations.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"warning\",\n                            subtitle: \"Storage locations\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Users\",\n                            value: stats.totalUsers.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"success\",\n                            subtitle: \"System users\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"In Use\",\n                            value: stats.inUse.toLocaleString(),\n                            color: \"primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Available\",\n                            value: stats.inStock.toLocaleString(),\n                            color: \"success\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Maintenance\",\n                            value: stats.maintenance.toLocaleString(),\n                            color: \"warning\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Retired\",\n                            value: stats.retired.toLocaleString(),\n                            color: \"secondary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Lost\",\n                            value: stats.lost.toLocaleString(),\n                            color: \"error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Damaged\",\n                            value: stats.damaged.toLocaleString(),\n                            color: \"error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 353,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\",\n                                boxShadow: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Asset Status Distribution\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            mt: 2\n                                        },\n                                        children: stats.statusBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    mb: index === stats.statusBreakdown.length - 1 ? 0 : 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"space-between\",\n                                                            mb: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 500\n                                                                },\n                                                                children: item.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: [\n                                                                    item.count,\n                                                                    \" (\",\n                                                                    item.percentage,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        variant: \"determinate\",\n                                                        value: item.percentage,\n                                                        sx: {\n                                                            height: 8,\n                                                            borderRadius: 4,\n                                                            backgroundColor: \"grey.200\",\n                                                            \"& .MuiLinearProgress-bar\": {\n                                                                borderRadius: 4\n                                                            }\n                                                        },\n                                                        color: item.status.toLowerCase().includes(\"use\") ? \"primary\" : item.status.toLowerCase().includes(\"stock\") ? \"success\" : item.status.toLowerCase().includes(\"maintenance\") ? \"warning\" : \"secondary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.status, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\",\n                                boxShadow: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Assets by Category\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            size: \"small\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Count\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Percentage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    children: stats.categoryBreakdown.slice(0, 8).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 460,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            category.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    sx: {\n                                                                        fontWeight: 500\n                                                                    },\n                                                                    children: category.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        label: \"\".concat(category.percentage, \"%\"),\n                                                                        size: \"small\",\n                                                                        color: category.percentage > 20 ? \"primary\" : \"default\",\n                                                                        variant: category.percentage > 20 ? \"filled\" : \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, category.name, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\",\n                                boxShadow: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Assets by Location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            size: \"small\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Count\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Percentage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    children: stats.locationBreakdown.slice(0, 8).map((location)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"warning\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 516,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            location.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 515,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    sx: {\n                                                                        fontWeight: 500\n                                                                    },\n                                                                    children: location.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        label: \"\".concat(location.percentage, \"%\"),\n                                                                        size: \"small\",\n                                                                        color: location.percentage > 15 ? \"warning\" : \"default\",\n                                                                        variant: location.percentage > 15 ? \"filled\" : \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 524,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, location.name, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 486,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\",\n                                boxShadow: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Asset Condition Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            mt: 2\n                                        },\n                                        children: stats.conditionBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    mb: index === stats.conditionBreakdown.length - 1 ? 0 : 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"space-between\",\n                                                            mb: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 500\n                                                                },\n                                                                children: item.condition\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: [\n                                                                    item.count,\n                                                                    \" (\",\n                                                                    item.percentage,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        variant: \"determinate\",\n                                                        value: item.percentage,\n                                                        sx: {\n                                                            height: 8,\n                                                            borderRadius: 4,\n                                                            backgroundColor: \"grey.200\",\n                                                            \"& .MuiLinearProgress-bar\": {\n                                                                borderRadius: 4\n                                                            }\n                                                        },\n                                                        color: item.condition.toLowerCase() === \"excellent\" ? \"success\" : item.condition.toLowerCase() === \"good\" ? \"info\" : item.condition.toLowerCase() === \"fair\" ? \"warning\" : \"error\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.condition, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\",\n                                boxShadow: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Recently Added Assets\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Asset Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Condition\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Assigned To\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Date Added\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    children: stats.recentAssets.map((asset)=>{\n                                                        var _asset_category, _asset_location, _asset_assignedTo;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        fontWeight: \"medium\",\n                                                                        color: \"primary.main\",\n                                                                        children: asset.assetNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 624,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        fontWeight: \"medium\",\n                                                                        children: asset.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 630,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 629,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 636,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_category = asset.category) === null || _asset_category === void 0 ? void 0 : _asset_category.name) || \"Unknown\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 637,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"warning\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 642,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_location = asset.location) === null || _asset_location === void 0 ? void 0 : _asset_location.name) || \"Unknown\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 643,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 641,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 640,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        label: asset.status ? asset.status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase()) : \"Unknown\",\n                                                                        color: asset.status ? statusColors[asset.status] : \"default\",\n                                                                        size: \"small\",\n                                                                        variant: \"filled\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 647,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        label: asset.condition ? asset.condition.charAt(0).toUpperCase() + asset.condition.slice(1) : \"Unknown\",\n                                                                        color: asset.condition ? conditionColors[asset.condition] : \"default\",\n                                                                        size: \"small\",\n                                                                        variant: \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 659,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_Inventory_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"action\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 676,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_assignedTo = asset.assignedTo) === null || _asset_assignedTo === void 0 ? void 0 : _asset_assignedTo.fullName) || \"Unassigned\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 677,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        color: \"text.secondary\",\n                                                                        children: asset.createdAt ? new Date(asset.createdAt).toLocaleDateString() : \"Unknown\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 681,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, asset.id, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 15\n                                    }, this),\n                                    stats.recentAssets.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            textAlign: \"center\",\n                                            py: 4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"No recent assets found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 595,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 594,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 374,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, this);\n}\n_s(AssetDashboard, \"XHXyvutMjVwFwI//GM0yYOuxtd0=\");\n_c = AssetDashboard;\nvar _c;\n$RefreshReg$(_c, \"AssetDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZCkvZGFzaGJvYXJkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBd0JyQjtBQVdNO0FBQ2dFO0FBc0I1RixNQUFNaUMsZUFBZTtJQUNuQkMsUUFBUTtJQUNSQyxVQUFVO0lBQ1ZDLGFBQWE7SUFDYkMsU0FBUztJQUNUQyxNQUFNO0lBQ05DLFNBQVM7SUFDVEMsU0FBUztBQUNYO0FBRUEsTUFBTUMsa0JBQWtCO0lBQ3RCQyxXQUFXO0lBQ1hDLE1BQU07SUFDTkMsTUFBTTtJQUNOQyxNQUFNO0lBQ05MLFNBQVM7QUFDWDtBQUVlLFNBQVNNOztJQUN0QixNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR2hELCtDQUFRQSxDQUF3QjtJQUMxRCxNQUFNLENBQUNpRCxTQUFTQyxXQUFXLEdBQUdsRCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNtRCxPQUFPQyxTQUFTLEdBQUdwRCwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDcUQsUUFBUUMsVUFBVSxHQUFHdEQsK0NBQVFBLENBQVUsRUFBRTtJQUNoRCxNQUFNLENBQUN1RCxZQUFZQyxjQUFjLEdBQUd4RCwrQ0FBUUEsQ0FBYSxFQUFFO0lBQzNELE1BQU0sQ0FBQ3lELFdBQVdDLGFBQWEsR0FBRzFELCtDQUFRQSxDQUFhLEVBQUU7SUFDekQsTUFBTSxDQUFDMkQsT0FBT0MsU0FBUyxHQUFHNUQsK0NBQVFBLENBQVMsRUFBRTtJQUU3QyxzQkFBc0I7SUFDdEIsTUFBTTZELG9CQUFvQjtRQUN4QixJQUFJO1lBQ0ZYLFdBQVc7WUFDWEUsU0FBUztZQUVULDRCQUE0QjtZQUM1QixNQUFNLENBQUNVLGdCQUFnQkMsZ0JBQWdCQyxlQUFlQyxVQUFVLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO2dCQUNuRnRDLG1EQUFhQSxDQUFDdUMsU0FBUyxDQUFDO29CQUFFQyxPQUFPO2dCQUFLO2dCQUN0Q3ZDLHVEQUFpQkEsQ0FBQ3dDLGFBQWE7Z0JBQy9CdkMsc0RBQWdCQSxDQUFDd0MsWUFBWTtnQkFDN0J2QyxrREFBWUEsQ0FBQ3dDLFFBQVE7YUFDdEI7WUFFRCxNQUFNQyxZQUFZWCxlQUFlVCxNQUFNO1lBQ3ZDQyxVQUFVbUI7WUFDVmpCLGNBQWNPO1lBQ2RMLGFBQWFNO1lBQ2JKLFNBQVNLO1lBRVQsdUJBQXVCO1lBQ3ZCLE1BQU1TLGNBQWNELFVBQVVFLE1BQU07WUFDcEMsTUFBTUMsZUFBZUgsVUFBVUksTUFBTSxDQUNuQyxDQUFDQyxLQUFLQztnQkFDSixNQUFNQyxTQUFTRCxNQUFNQyxNQUFNLElBQUk7Z0JBQy9CRixHQUFHLENBQUNFLE9BQU8sR0FBRyxDQUFDRixHQUFHLENBQUNFLE9BQU8sSUFBSSxLQUFLO2dCQUNuQyxPQUFPRjtZQUNULEdBQ0EsQ0FBQztZQUdILE1BQU1HLGtCQUFrQlIsVUFBVUksTUFBTSxDQUN0QyxDQUFDQyxLQUFLQztnQkFDSixNQUFNRyxZQUFZSCxNQUFNRyxTQUFTLElBQUk7Z0JBQ3JDSixHQUFHLENBQUNJLFVBQVUsR0FBRyxDQUFDSixHQUFHLENBQUNJLFVBQVUsSUFBSSxLQUFLO2dCQUN6QyxPQUFPSjtZQUNULEdBQ0EsQ0FBQztZQUdILE1BQU1LLGlCQUFpQlYsVUFBVUksTUFBTSxDQUNyQyxDQUFDQyxLQUFLQztvQkFDaUJBO2dCQUFyQixNQUFNSyxlQUFlTCxFQUFBQSxrQkFBQUEsTUFBTU0sUUFBUSxjQUFkTixzQ0FBQUEsZ0JBQWdCTyxJQUFJLEtBQUk7Z0JBQzdDUixHQUFHLENBQUNNLGFBQWEsR0FBRyxDQUFDTixHQUFHLENBQUNNLGFBQWEsSUFBSSxLQUFLO2dCQUMvQyxPQUFPTjtZQUNULEdBQ0EsQ0FBQztZQUdILE1BQU1TLGlCQUFpQmQsVUFBVUksTUFBTSxDQUNyQyxDQUFDQyxLQUFLQztvQkFDaUJBO2dCQUFyQixNQUFNUyxlQUFlVCxFQUFBQSxrQkFBQUEsTUFBTVUsUUFBUSxjQUFkVixzQ0FBQUEsZ0JBQWdCTyxJQUFJLEtBQUk7Z0JBQzdDUixHQUFHLENBQUNVLGFBQWEsR0FBRyxDQUFDVixHQUFHLENBQUNVLGFBQWEsSUFBSSxLQUFLO2dCQUMvQyxPQUFPVjtZQUNULEdBQ0EsQ0FBQztZQUdILDBCQUEwQjtZQUMxQixNQUFNWSxvQkFBb0JDLE9BQU9DLE9BQU8sQ0FBQ1QsZ0JBQ3RDVSxHQUFHLENBQUM7b0JBQUMsQ0FBQ1AsTUFBTVEsTUFBTTt1QkFBTTtvQkFDdkJSO29CQUNBUTtvQkFDQUMsWUFBWUMsS0FBS0MsS0FBSyxDQUFDLFFBQVN2QixjQUFlO2dCQUNqRDtlQUNDd0IsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1BLEVBQUVOLEtBQUssR0FBR0ssRUFBRUwsS0FBSztZQUVuQyxNQUFNTyxvQkFBb0JWLE9BQU9DLE9BQU8sQ0FBQ0wsZ0JBQ3RDTSxHQUFHLENBQUM7b0JBQUMsQ0FBQ1AsTUFBTVEsTUFBTTt1QkFBTTtvQkFDdkJSO29CQUNBUTtvQkFDQUMsWUFBWUMsS0FBS0MsS0FBSyxDQUFDLFFBQVN2QixjQUFlO2dCQUNqRDtlQUNDd0IsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1BLEVBQUVOLEtBQUssR0FBR0ssRUFBRUwsS0FBSztZQUVuQyxNQUFNUSxrQkFBa0JYLE9BQU9DLE9BQU8sQ0FBQ2hCLGNBQ3BDaUIsR0FBRyxDQUFDO29CQUFDLENBQUNiLFFBQVFjLE1BQU07dUJBQU07b0JBQ3pCZCxRQUFRQSxPQUFPdUIsT0FBTyxDQUFDLEtBQUssS0FBS0EsT0FBTyxDQUFDLFNBQVNDLENBQUFBLElBQUtBLEVBQUVDLFdBQVc7b0JBQ3BFWDtvQkFDQUMsWUFBWUMsS0FBS0MsS0FBSyxDQUFDLFFBQVN2QixjQUFlO2dCQUNqRDtlQUNDd0IsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1BLEVBQUVOLEtBQUssR0FBR0ssRUFBRUwsS0FBSztZQUVuQyxNQUFNWSxxQkFBcUJmLE9BQU9DLE9BQU8sQ0FBQ1gsaUJBQ3ZDWSxHQUFHLENBQUM7b0JBQUMsQ0FBQ1gsV0FBV1ksTUFBTTt1QkFBTTtvQkFDNUJaLFdBQVdBLFVBQVV5QixNQUFNLENBQUMsR0FBR0YsV0FBVyxLQUFLdkIsVUFBVTBCLEtBQUssQ0FBQztvQkFDL0RkO29CQUNBQyxZQUFZQyxLQUFLQyxLQUFLLENBQUMsUUFBU3ZCLGNBQWU7Z0JBQ2pEO2VBQ0N3QixJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUEsRUFBRU4sS0FBSyxHQUFHSyxFQUFFTCxLQUFLO1lBRW5DLDZCQUE2QjtZQUM3QixNQUFNZSxlQUFlcEMsVUFDbEJ5QixJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTSxJQUFJVSxLQUFLVixFQUFFVyxTQUFTLElBQUksSUFBSUMsT0FBTyxLQUFLLElBQUlGLEtBQUtYLEVBQUVZLFNBQVMsSUFBSSxJQUFJQyxPQUFPLElBQzFGSixLQUFLLENBQUMsR0FBRztZQUVaLE1BQU1LLGlCQUFpQztnQkFDckN2QztnQkFDQXdDLE9BQU90QyxhQUFhMUMsTUFBTSxJQUFJO2dCQUM5QmlGLFNBQVN2QyxhQUFhekMsUUFBUSxJQUFJO2dCQUNsQ0MsYUFBYXdDLGFBQWF4QyxXQUFXLElBQUk7Z0JBQ3pDQyxTQUFTdUMsYUFBYXZDLE9BQU8sSUFBSTtnQkFDakNDLE1BQU1zQyxhQUFhdEMsSUFBSSxJQUFJO2dCQUMzQkMsU0FBU3FDLGFBQWFyQyxPQUFPLElBQUk7Z0JBQ2pDNkUsaUJBQWlCckQsZUFBZVksTUFBTTtnQkFDdEMwQyxnQkFBZ0JyRCxjQUFjVyxNQUFNO2dCQUNwQzJDLFlBQVlyRCxVQUFVVSxNQUFNO2dCQUM1QmtDO2dCQUNBbkI7Z0JBQ0FXO2dCQUNBQztnQkFDQUk7WUFDRjtZQUVBMUQsU0FBU2lFO1FBQ1gsRUFBRSxPQUFPTSxLQUFVO1lBQ2pCQyxRQUFRckUsS0FBSyxDQUFDLGtDQUFrQ29FO1lBQ2hEbkUsU0FBUztRQUNYLFNBQVU7WUFDUkYsV0FBVztRQUNiO0lBQ0Y7SUFFQWpELGdEQUFTQSxDQUFDO1FBQ1I0RDtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU00RCxXQUFXO1lBQUMsRUFBRUMsS0FBSyxFQUFFQyxLQUFLLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFQyxRQUFRLEVBQU87NkJBQzVELDhEQUFDNUgscU9BQUlBO1lBQ0g2SCxJQUFJO2dCQUNGQyxZQUFZO2dCQUNaQyxnQkFBZ0I7Z0JBQ2hCQyxRQUFRO2dCQUNSQyxXQUFXO2dCQUNYQyxhQUFhO1lBQ2Y7c0JBRUEsNEVBQUNqSSxxT0FBV0E7Z0JBQUM0SCxJQUFJO29CQUFFTSxHQUFHO2dCQUFFOzBCQUN0Qiw0RUFBQy9ILHFPQUFHQTtvQkFBQ3lILElBQUk7d0JBQUVPLFNBQVM7d0JBQVFDLFlBQVk7d0JBQVVDLGdCQUFnQjtvQkFBZ0I7O3NDQUNoRiw4REFBQ2xJLHFPQUFHQTs7OENBQ0YsOERBQUNGLHFPQUFVQTtvQ0FBQ3lILE9BQU07b0NBQWlCWSxZQUFZO29DQUFDQyxTQUFRO29DQUFRWCxJQUFJO3dDQUFFWSxZQUFZO29DQUFJOzhDQUNuRmpCOzs7Ozs7OENBRUgsOERBQUN0SCxxT0FBVUE7b0NBQUNzSSxTQUFRO29DQUFLRSxXQUFVO29DQUFNZixPQUFPQTtvQ0FBT0UsSUFBSTt3Q0FBRVksWUFBWTt3Q0FBS0UsSUFBSTtvQ0FBSTs4Q0FDbkZsQjs7Ozs7O2dDQUVGRywwQkFDQyw4REFBQzFILHFPQUFVQTtvQ0FBQ3NJLFNBQVE7b0NBQVFiLE9BQU07b0NBQWlCRSxJQUFJO3dDQUFFZSxVQUFVO29DQUFXOzhDQUMzRWhCOzs7Ozs7Ozs7Ozs7d0JBSU5GLHNCQUNDLDhEQUFDN0cscU9BQU1BOzRCQUNMZ0gsSUFBSTtnQ0FDRmdCLFNBQVMsR0FBUyxPQUFObEIsT0FBTTtnQ0FDbEJtQixPQUFPO2dDQUNQQyxRQUFROzRCQUVWO3NDQUVDckI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBUWIsSUFBSTNFLFNBQVM7UUFDWCxxQkFDRSw4REFBQzNDLHFPQUFHQTtZQUFDeUgsSUFBSTtnQkFBRU0sR0FBRztnQkFBR0MsU0FBUztnQkFBUUUsZ0JBQWdCO2dCQUFVRCxZQUFZO2dCQUFVVyxXQUFXO1lBQU87c0JBQ2xHLDRFQUFDakkscU9BQWdCQTtnQkFBQ2tJLE1BQU07Ozs7Ozs7Ozs7O0lBRzlCO0lBRUEsSUFBSWhHLE9BQU87UUFDVCxxQkFDRSw4REFBQzdDLHFPQUFHQTtZQUFDeUgsSUFBSTtnQkFBRU0sR0FBRztZQUFFOzs4QkFDZCw4REFBQ25ILHFPQUFLQTtvQkFBQ2tJLFVBQVM7b0JBQVFyQixJQUFJO3dCQUFFYyxJQUFJO29CQUFFOzhCQUNqQzFGOzs7Ozs7OEJBRUgsOERBQUNuQyxzT0FBTUE7b0JBQUMwSCxTQUFRO29CQUFZVyx5QkFBVyw4REFBQ3pILCtIQUFXQTs7Ozs7b0JBQUswSCxTQUFTekY7OEJBQW1COzs7Ozs7Ozs7Ozs7SUFLMUY7SUFFQSxJQUFJLENBQUNkLE9BQU87UUFDVixxQkFDRSw4REFBQ3pDLHFPQUFHQTtZQUFDeUgsSUFBSTtnQkFBRU0sR0FBRztZQUFFO3NCQUNkLDRFQUFDbkgscU9BQUtBO2dCQUFDa0ksVUFBUzswQkFBTzs7Ozs7Ozs7Ozs7SUFHN0I7SUFFQSxxQkFDRSw4REFBQzlJLHFPQUFHQTtRQUFDeUgsSUFBSTtZQUFFTSxHQUFHO1lBQUdrQixpQkFBaUI7WUFBV0wsV0FBVztRQUFROzswQkFFOUQsOERBQUM1SSxxT0FBR0E7Z0JBQUN5SCxJQUFJO29CQUFFTyxTQUFTO29CQUFRRSxnQkFBZ0I7b0JBQWlCRCxZQUFZO29CQUFVTSxJQUFJO2dCQUFFOztrQ0FDdkYsOERBQUN2SSxxT0FBR0E7OzBDQUNGLDhEQUFDRixxT0FBVUE7Z0NBQUNzSSxTQUFRO2dDQUFLRSxXQUFVO2dDQUFLSCxZQUFZO2dDQUFDVixJQUFJO29DQUFFWSxZQUFZO29DQUFLZCxPQUFPO2dDQUFlOzBDQUFHOzs7Ozs7MENBR3JHLDhEQUFDekgscU9BQVVBO2dDQUFDc0ksU0FBUTtnQ0FBUWIsT0FBTTswQ0FBaUI7Ozs7Ozs7Ozs7OztrQ0FJckQsOERBQUM3RyxzT0FBTUE7d0JBQ0wwSCxTQUFRO3dCQUNSVyx5QkFBVyw4REFBQ3pILCtIQUFXQTs7Ozs7d0JBQ3ZCMEgsU0FBU3pGO3dCQUNUa0UsSUFBSTs0QkFDRnlCLGNBQWM7NEJBQ2RDLGVBQWU7NEJBQ2ZkLFlBQVk7d0JBQ2Q7a0NBQ0Q7Ozs7Ozs7Ozs7OzswQkFNSCw4REFBQ3RJLHNPQUFJQTtnQkFBQ3FKLFNBQVM7Z0JBQUNDLFNBQVM7Z0JBQUc1QixJQUFJO29CQUFFYyxJQUFJO2dCQUFFOztrQ0FDdEMsOERBQUN4SSxzT0FBSUE7d0JBQUN1SixJQUFJO3dCQUFDQyxJQUFJO3dCQUFJQyxJQUFJO3dCQUFHQyxJQUFJO2tDQUM1Qiw0RUFBQ3RDOzRCQUNDQyxPQUFNOzRCQUNOQyxPQUFPNUUsTUFBTTJCLFdBQVcsQ0FBQ3NGLGNBQWM7NEJBQ3ZDcEMsb0JBQU0sOERBQUN4RywrSEFBYUE7Ozs7OzRCQUNwQnlHLE9BQU07NEJBQ05DLFVBQVM7Ozs7Ozs7Ozs7O2tDQUdiLDhEQUFDekgsc09BQUlBO3dCQUFDdUosSUFBSTt3QkFBQ0MsSUFBSTt3QkFBSUMsSUFBSTt3QkFBR0MsSUFBSTtrQ0FDNUIsNEVBQUN0Qzs0QkFDQ0MsT0FBTTs0QkFDTkMsT0FBTzVFLE1BQU1xRSxlQUFlLENBQUM0QyxjQUFjOzRCQUMzQ3BDLG9CQUFNLDhEQUFDcEcsK0hBQVlBOzs7Ozs0QkFDbkJxRyxPQUFNOzRCQUNOQyxVQUFTOzs7Ozs7Ozs7OztrQ0FHYiw4REFBQ3pILHNPQUFJQTt3QkFBQ3VKLElBQUk7d0JBQUNDLElBQUk7d0JBQUlDLElBQUk7d0JBQUdDLElBQUk7a0NBQzVCLDRFQUFDdEM7NEJBQ0NDLE9BQU07NEJBQ05DLE9BQU81RSxNQUFNc0UsY0FBYyxDQUFDMkMsY0FBYzs0QkFDMUNwQyxvQkFBTSw4REFBQ2xHLCtIQUFZQTs7Ozs7NEJBQ25CbUcsT0FBTTs0QkFDTkMsVUFBUzs7Ozs7Ozs7Ozs7a0NBR2IsOERBQUN6SCxzT0FBSUE7d0JBQUN1SixJQUFJO3dCQUFDQyxJQUFJO3dCQUFJQyxJQUFJO3dCQUFHQyxJQUFJO2tDQUM1Qiw0RUFBQ3RDOzRCQUNDQyxPQUFNOzRCQUNOQyxPQUFPNUUsTUFBTXVFLFVBQVUsQ0FBQzBDLGNBQWM7NEJBQ3RDcEMsb0JBQU0sOERBQUN0RywrSEFBVUE7Ozs7OzRCQUNqQnVHLE9BQU07NEJBQ05DLFVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1mLDhEQUFDekgsc09BQUlBO2dCQUFDcUosU0FBUztnQkFBQ0MsU0FBUztnQkFBRzVCLElBQUk7b0JBQUVjLElBQUk7Z0JBQUU7O2tDQUN0Qyw4REFBQ3hJLHNPQUFJQTt3QkFBQ3VKLElBQUk7d0JBQUNDLElBQUk7d0JBQUlDLElBQUk7d0JBQUdDLElBQUk7a0NBQzVCLDRFQUFDdEM7NEJBQVNDLE9BQU07NEJBQVNDLE9BQU81RSxNQUFNbUUsS0FBSyxDQUFDOEMsY0FBYzs0QkFBSW5DLE9BQU07Ozs7Ozs7Ozs7O2tDQUV0RSw4REFBQ3hILHNPQUFJQTt3QkFBQ3VKLElBQUk7d0JBQUNDLElBQUk7d0JBQUlDLElBQUk7d0JBQUdDLElBQUk7a0NBQzVCLDRFQUFDdEM7NEJBQVNDLE9BQU07NEJBQVlDLE9BQU81RSxNQUFNb0UsT0FBTyxDQUFDNkMsY0FBYzs0QkFBSW5DLE9BQU07Ozs7Ozs7Ozs7O2tDQUUzRSw4REFBQ3hILHNPQUFJQTt3QkFBQ3VKLElBQUk7d0JBQUNDLElBQUk7d0JBQUlDLElBQUk7d0JBQUdDLElBQUk7a0NBQzVCLDRFQUFDdEM7NEJBQVNDLE9BQU07NEJBQWNDLE9BQU81RSxNQUFNWCxXQUFXLENBQUM0SCxjQUFjOzRCQUFJbkMsT0FBTTs7Ozs7Ozs7Ozs7a0NBRWpGLDhEQUFDeEgsc09BQUlBO3dCQUFDdUosSUFBSTt3QkFBQ0MsSUFBSTt3QkFBSUMsSUFBSTt3QkFBR0MsSUFBSTtrQ0FDNUIsNEVBQUN0Qzs0QkFBU0MsT0FBTTs0QkFBVUMsT0FBTzVFLE1BQU1WLE9BQU8sQ0FBQzJILGNBQWM7NEJBQUluQyxPQUFNOzs7Ozs7Ozs7OztrQ0FFekUsOERBQUN4SCxzT0FBSUE7d0JBQUN1SixJQUFJO3dCQUFDQyxJQUFJO3dCQUFJQyxJQUFJO3dCQUFHQyxJQUFJO2tDQUM1Qiw0RUFBQ3RDOzRCQUFTQyxPQUFNOzRCQUFPQyxPQUFPNUUsTUFBTVQsSUFBSSxDQUFDMEgsY0FBYzs0QkFBSW5DLE9BQU07Ozs7Ozs7Ozs7O2tDQUVuRSw4REFBQ3hILHNPQUFJQTt3QkFBQ3VKLElBQUk7d0JBQUNDLElBQUk7d0JBQUlDLElBQUk7d0JBQUdDLElBQUk7a0NBQzVCLDRFQUFDdEM7NEJBQVNDLE9BQU07NEJBQVVDLE9BQU81RSxNQUFNUixPQUFPLENBQUN5SCxjQUFjOzRCQUFJbkMsT0FBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBSTNFLDhEQUFDeEgsc09BQUlBO2dCQUFDcUosU0FBUztnQkFBQ0MsU0FBUzs7a0NBRXZCLDhEQUFDdEosc09BQUlBO3dCQUFDdUosSUFBSTt3QkFBQ0MsSUFBSTt3QkFBSUUsSUFBSTtrQ0FDckIsNEVBQUM3SixxT0FBSUE7NEJBQ0g2SCxJQUFJO2dDQUNGLG1CQUFtQjtnQ0FDbkJHLFFBQVE7Z0NBQ1JFLGFBQWE7Z0NBQ2JELFdBQVc7NEJBQ2I7c0NBRUEsNEVBQUNoSSxxT0FBV0E7Z0NBQUM0SCxJQUFJO29DQUFFTSxHQUFHO2dDQUFFOztrREFDdEIsOERBQUNqSSxxT0FBVUE7d0NBQUNzSSxTQUFRO3dDQUFLRCxZQUFZO3dDQUFDVixJQUFJOzRDQUFFWSxZQUFZOzRDQUFLRSxJQUFJO3dDQUFFO2tEQUFHOzs7Ozs7a0RBR3RFLDhEQUFDdkkscU9BQUdBO3dDQUFDeUgsSUFBSTs0Q0FBRWtDLElBQUk7d0NBQUU7a0RBQ2RsSCxNQUFNdUQsZUFBZSxDQUFDVCxHQUFHLENBQUMsQ0FBQytELE1BQU1NLHNCQUNoQyw4REFBQzVKLHFPQUFHQTtnREFBbUJ5SCxJQUFJO29EQUFFYyxJQUFJcUIsVUFBVW5ILE1BQU11RCxlQUFlLENBQUMzQixNQUFNLEdBQUcsSUFBSSxJQUFJO2dEQUFFOztrRUFDbEYsOERBQUNyRSxxT0FBR0E7d0RBQUN5SCxJQUFJOzREQUFFTyxTQUFTOzREQUFRRSxnQkFBZ0I7NERBQWlCSyxJQUFJO3dEQUFFOzswRUFDakUsOERBQUN6SSxxT0FBVUE7Z0VBQUNzSSxTQUFRO2dFQUFRWCxJQUFJO29FQUFFWSxZQUFZO2dFQUFJOzBFQUMvQ2lCLEtBQUs1RSxNQUFNOzs7Ozs7MEVBRWQsOERBQUM1RSxxT0FBVUE7Z0VBQUNzSSxTQUFRO2dFQUFRWCxJQUFJO29FQUFFWSxZQUFZO2dFQUFJOztvRUFDL0NpQixLQUFLOUQsS0FBSztvRUFBQztvRUFBRzhELEtBQUs3RCxVQUFVO29FQUFDOzs7Ozs7Ozs7Ozs7O2tFQUduQyw4REFBQ2pGLHNPQUFjQTt3REFDYjRILFNBQVE7d0RBQ1JmLE9BQU9pQyxLQUFLN0QsVUFBVTt3REFDdEJnQyxJQUFJOzREQUNGa0IsUUFBUTs0REFDUk8sY0FBYzs0REFDZEQsaUJBQWlCOzREQUNqQiw0QkFBNEI7Z0VBQzFCQyxjQUFjOzREQUNoQjt3REFDRjt3REFDQTNCLE9BQ0UrQixLQUFLNUUsTUFBTSxDQUFDbUYsV0FBVyxHQUFHQyxRQUFRLENBQUMsU0FDL0IsWUFDQVIsS0FBSzVFLE1BQU0sQ0FBQ21GLFdBQVcsR0FBR0MsUUFBUSxDQUFDLFdBQ2pDLFlBQ0FSLEtBQUs1RSxNQUFNLENBQUNtRixXQUFXLEdBQUdDLFFBQVEsQ0FBQyxpQkFDakMsWUFDQTs7Ozs7OzsrQ0EzQkpSLEtBQUs1RSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FzQy9CLDhEQUFDM0Usc09BQUlBO3dCQUFDdUosSUFBSTt3QkFBQ0MsSUFBSTt3QkFBSUUsSUFBSTtrQ0FDckIsNEVBQUM3SixxT0FBSUE7NEJBQ0g2SCxJQUFJO2dDQUNGLG1CQUFtQjtnQ0FDbkJHLFFBQVE7Z0NBQ1JFLGFBQWE7Z0NBQ2JELFdBQVc7NEJBQ2I7c0NBRUEsNEVBQUNoSSxxT0FBV0E7Z0NBQUM0SCxJQUFJO29DQUFFTSxHQUFHO2dDQUFFOztrREFDdEIsOERBQUNqSSxxT0FBVUE7d0NBQUNzSSxTQUFRO3dDQUFLRCxZQUFZO3dDQUFDVixJQUFJOzRDQUFFWSxZQUFZOzRDQUFLRSxJQUFJO3dDQUFFO2tEQUFHOzs7Ozs7a0RBR3RFLDhEQUFDbEksc09BQWNBO2tEQUNiLDRFQUFDSCxzT0FBS0E7NENBQUMySSxNQUFLOzs4REFDViw4REFBQ3ZJLHNPQUFTQTs4REFDUiw0RUFBQ0Msc09BQVFBOzswRUFDUCw4REFBQ0gsc09BQVNBO2dFQUFDcUgsSUFBSTtvRUFBRVksWUFBWTtnRUFBSTswRUFBRzs7Ozs7OzBFQUNwQyw4REFBQ2pJLHNPQUFTQTtnRUFBQzJKLE9BQU07Z0VBQVF0QyxJQUFJO29FQUFFWSxZQUFZO2dFQUFJOzBFQUFHOzs7Ozs7MEVBR2xELDhEQUFDakksc09BQVNBO2dFQUFDMkosT0FBTTtnRUFBUXRDLElBQUk7b0VBQUVZLFlBQVk7Z0VBQUk7MEVBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUt0RCw4REFBQ2xJLHNPQUFTQTs4REFDUHNDLE1BQU0yQyxpQkFBaUIsQ0FBQ2tCLEtBQUssQ0FBQyxHQUFHLEdBQUdmLEdBQUcsQ0FBQ1IsQ0FBQUEseUJBQ3ZDLDhEQUFDeEUsc09BQVFBOzREQUFxQnlKLEtBQUs7OzhFQUNqQyw4REFBQzVKLHNPQUFTQTs4RUFDUiw0RUFBQ0oscU9BQUdBO3dFQUFDeUgsSUFBSTs0RUFBRU8sU0FBUzs0RUFBUUMsWUFBWTs0RUFBVWdDLEtBQUs7d0VBQUU7OzBGQUN2RCw4REFBQy9JLCtIQUFZQTtnRkFBQ3NILFVBQVM7Z0ZBQVFqQixPQUFNOzs7Ozs7NEVBQ3BDeEMsU0FBU0MsSUFBSTs7Ozs7Ozs7Ozs7OzhFQUdsQiw4REFBQzVFLHNPQUFTQTtvRUFBQzJKLE9BQU07b0VBQVF0QyxJQUFJO3dFQUFFWSxZQUFZO29FQUFJOzhFQUM1Q3RELFNBQVNTLEtBQUs7Ozs7Ozs4RUFFakIsOERBQUNwRixzT0FBU0E7b0VBQUMySixPQUFNOzhFQUNmLDRFQUFDOUosc09BQUlBO3dFQUNIaUssT0FBTyxHQUF1QixPQUFwQm5GLFNBQVNVLFVBQVUsRUFBQzt3RUFDOUJvRCxNQUFLO3dFQUNMdEIsT0FBT3hDLFNBQVNVLFVBQVUsR0FBRyxLQUFLLFlBQVk7d0VBQzlDMkMsU0FBU3JELFNBQVNVLFVBQVUsR0FBRyxLQUFLLFdBQVc7Ozs7Ozs7Ozs7OzsyREFmdENWLFNBQVNDLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBNEIxQyw4REFBQ2pGLHNPQUFJQTt3QkFBQ3VKLElBQUk7d0JBQUNDLElBQUk7d0JBQUlFLElBQUk7a0NBQ3JCLDRFQUFDN0oscU9BQUlBOzRCQUNINkgsSUFBSTtnQ0FDRixtQkFBbUI7Z0NBQ25CRyxRQUFRO2dDQUNSRSxhQUFhO2dDQUNiRCxXQUFXOzRCQUNiO3NDQUVBLDRFQUFDaEkscU9BQVdBO2dDQUFDNEgsSUFBSTtvQ0FBRU0sR0FBRztnQ0FBRTs7a0RBQ3RCLDhEQUFDakkscU9BQVVBO3dDQUFDc0ksU0FBUTt3Q0FBS0QsWUFBWTt3Q0FBQ1YsSUFBSTs0Q0FBRVksWUFBWTs0Q0FBS0UsSUFBSTt3Q0FBRTtrREFBRzs7Ozs7O2tEQUd0RSw4REFBQ2xJLHNPQUFjQTtrREFDYiw0RUFBQ0gsc09BQUtBOzRDQUFDMkksTUFBSzs7OERBQ1YsOERBQUN2SSxzT0FBU0E7OERBQ1IsNEVBQUNDLHNPQUFRQTs7MEVBQ1AsOERBQUNILHNPQUFTQTtnRUFBQ3FILElBQUk7b0VBQUVZLFlBQVk7Z0VBQUk7MEVBQUc7Ozs7OzswRUFDcEMsOERBQUNqSSxzT0FBU0E7Z0VBQUMySixPQUFNO2dFQUFRdEMsSUFBSTtvRUFBRVksWUFBWTtnRUFBSTswRUFBRzs7Ozs7OzBFQUdsRCw4REFBQ2pJLHNPQUFTQTtnRUFBQzJKLE9BQU07Z0VBQVF0QyxJQUFJO29FQUFFWSxZQUFZO2dFQUFJOzBFQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs4REFLdEQsOERBQUNsSSxzT0FBU0E7OERBQ1BzQyxNQUFNc0QsaUJBQWlCLENBQUNPLEtBQUssQ0FBQyxHQUFHLEdBQUdmLEdBQUcsQ0FBQ0osQ0FBQUEseUJBQ3ZDLDhEQUFDNUUsc09BQVFBOzREQUFxQnlKLEtBQUs7OzhFQUNqQyw4REFBQzVKLHNPQUFTQTs4RUFDUiw0RUFBQ0oscU9BQUdBO3dFQUFDeUgsSUFBSTs0RUFBRU8sU0FBUzs0RUFBUUMsWUFBWTs0RUFBVWdDLEtBQUs7d0VBQUU7OzBGQUN2RCw4REFBQzdJLCtIQUFZQTtnRkFBQ29ILFVBQVM7Z0ZBQVFqQixPQUFNOzs7Ozs7NEVBQ3BDcEMsU0FBU0gsSUFBSTs7Ozs7Ozs7Ozs7OzhFQUdsQiw4REFBQzVFLHNPQUFTQTtvRUFBQzJKLE9BQU07b0VBQVF0QyxJQUFJO3dFQUFFWSxZQUFZO29FQUFJOzhFQUM1Q2xELFNBQVNLLEtBQUs7Ozs7Ozs4RUFFakIsOERBQUNwRixzT0FBU0E7b0VBQUMySixPQUFNOzhFQUNmLDRFQUFDOUosc09BQUlBO3dFQUNIaUssT0FBTyxHQUF1QixPQUFwQi9FLFNBQVNNLFVBQVUsRUFBQzt3RUFDOUJvRCxNQUFLO3dFQUNMdEIsT0FBT3BDLFNBQVNNLFVBQVUsR0FBRyxLQUFLLFlBQVk7d0VBQzlDMkMsU0FBU2pELFNBQVNNLFVBQVUsR0FBRyxLQUFLLFdBQVc7Ozs7Ozs7Ozs7OzsyREFmdENOLFNBQVNILElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBNEIxQyw4REFBQ2pGLHNPQUFJQTt3QkFBQ3VKLElBQUk7d0JBQUNDLElBQUk7d0JBQUlFLElBQUk7a0NBQ3JCLDRFQUFDN0oscU9BQUlBOzRCQUNINkgsSUFBSTtnQ0FDRixtQkFBbUI7Z0NBQ25CRyxRQUFRO2dDQUNSRSxhQUFhO2dDQUNiRCxXQUFXOzRCQUNiO3NDQUVBLDRFQUFDaEkscU9BQVdBO2dDQUFDNEgsSUFBSTtvQ0FBRU0sR0FBRztnQ0FBRTs7a0RBQ3RCLDhEQUFDakkscU9BQVVBO3dDQUFDc0ksU0FBUTt3Q0FBS0QsWUFBWTt3Q0FBQ1YsSUFBSTs0Q0FBRVksWUFBWTs0Q0FBS0UsSUFBSTt3Q0FBRTtrREFBRzs7Ozs7O2tEQUd0RSw4REFBQ3ZJLHFPQUFHQTt3Q0FBQ3lILElBQUk7NENBQUVrQyxJQUFJO3dDQUFFO2tEQUNkbEgsTUFBTTJELGtCQUFrQixDQUFDYixHQUFHLENBQUMsQ0FBQytELE1BQU1NLHNCQUNuQyw4REFBQzVKLHFPQUFHQTtnREFBc0J5SCxJQUFJO29EQUFFYyxJQUFJcUIsVUFBVW5ILE1BQU0yRCxrQkFBa0IsQ0FBQy9CLE1BQU0sR0FBRyxJQUFJLElBQUk7Z0RBQUU7O2tFQUN4Riw4REFBQ3JFLHFPQUFHQTt3REFBQ3lILElBQUk7NERBQUVPLFNBQVM7NERBQVFFLGdCQUFnQjs0REFBaUJLLElBQUk7d0RBQUU7OzBFQUNqRSw4REFBQ3pJLHFPQUFVQTtnRUFBQ3NJLFNBQVE7Z0VBQVFYLElBQUk7b0VBQUVZLFlBQVk7Z0VBQUk7MEVBQy9DaUIsS0FBSzFFLFNBQVM7Ozs7OzswRUFFakIsOERBQUM5RSxxT0FBVUE7Z0VBQUNzSSxTQUFRO2dFQUFRWCxJQUFJO29FQUFFWSxZQUFZO2dFQUFJOztvRUFDL0NpQixLQUFLOUQsS0FBSztvRUFBQztvRUFBRzhELEtBQUs3RCxVQUFVO29FQUFDOzs7Ozs7Ozs7Ozs7O2tFQUduQyw4REFBQ2pGLHNPQUFjQTt3REFDYjRILFNBQVE7d0RBQ1JmLE9BQU9pQyxLQUFLN0QsVUFBVTt3REFDdEJnQyxJQUFJOzREQUNGa0IsUUFBUTs0REFDUk8sY0FBYzs0REFDZEQsaUJBQWlCOzREQUNqQiw0QkFBNEI7Z0VBQzFCQyxjQUFjOzREQUNoQjt3REFDRjt3REFDQTNCLE9BQ0UrQixLQUFLMUUsU0FBUyxDQUFDaUYsV0FBVyxPQUFPLGNBQzdCLFlBQ0FQLEtBQUsxRSxTQUFTLENBQUNpRixXQUFXLE9BQU8sU0FDL0IsU0FDQVAsS0FBSzFFLFNBQVMsQ0FBQ2lGLFdBQVcsT0FBTyxTQUMvQixZQUNBOzs7Ozs7OytDQTNCSlAsS0FBSzFFLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQXNDbEMsOERBQUM3RSxzT0FBSUE7d0JBQUN1SixJQUFJO3dCQUFDQyxJQUFJO2tDQUNiLDRFQUFDM0oscU9BQUlBOzRCQUNINkgsSUFBSTtnQ0FDRixtQkFBbUI7Z0NBQ25CRyxRQUFRO2dDQUNSRSxhQUFhO2dDQUNiRCxXQUFXOzRCQUNiO3NDQUVBLDRFQUFDaEkscU9BQVdBO2dDQUFDNEgsSUFBSTtvQ0FBRU0sR0FBRztnQ0FBRTs7a0RBQ3RCLDhEQUFDakkscU9BQVVBO3dDQUFDc0ksU0FBUTt3Q0FBS0QsWUFBWTt3Q0FBQ1YsSUFBSTs0Q0FBRVksWUFBWTs0Q0FBS0UsSUFBSTt3Q0FBRTtrREFBRzs7Ozs7O2tEQUd0RSw4REFBQ2xJLHNPQUFjQTtrREFDYiw0RUFBQ0gsc09BQUtBOzs4REFDSiw4REFBQ0ksc09BQVNBOzhEQUNSLDRFQUFDQyxzT0FBUUE7OzBFQUNQLDhEQUFDSCxzT0FBU0E7Z0VBQUNxSCxJQUFJO29FQUFFWSxZQUFZO2dFQUFJOzBFQUFHOzs7Ozs7MEVBQ3BDLDhEQUFDakksc09BQVNBO2dFQUFDcUgsSUFBSTtvRUFBRVksWUFBWTtnRUFBSTswRUFBRzs7Ozs7OzBFQUNwQyw4REFBQ2pJLHNPQUFTQTtnRUFBQ3FILElBQUk7b0VBQUVZLFlBQVk7Z0VBQUk7MEVBQUc7Ozs7OzswRUFDcEMsOERBQUNqSSxzT0FBU0E7Z0VBQUNxSCxJQUFJO29FQUFFWSxZQUFZO2dFQUFJOzBFQUFHOzs7Ozs7MEVBQ3BDLDhEQUFDakksc09BQVNBO2dFQUFDcUgsSUFBSTtvRUFBRVksWUFBWTtnRUFBSTswRUFBRzs7Ozs7OzBFQUNwQyw4REFBQ2pJLHNPQUFTQTtnRUFBQ3FILElBQUk7b0VBQUVZLFlBQVk7Z0VBQUk7MEVBQUc7Ozs7OzswRUFDcEMsOERBQUNqSSxzT0FBU0E7Z0VBQUNxSCxJQUFJO29FQUFFWSxZQUFZO2dFQUFJOzBFQUFHOzs7Ozs7MEVBQ3BDLDhEQUFDakksc09BQVNBO2dFQUFDcUgsSUFBSTtvRUFBRVksWUFBWTtnRUFBSTswRUFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBR3hDLDhEQUFDbEksc09BQVNBOzhEQUNQc0MsTUFBTThELFlBQVksQ0FBQ2hCLEdBQUcsQ0FBQ2QsQ0FBQUE7NERBZWFBLGlCQU1BQSxpQkFrQ0FBOzZFQXREbkMsOERBQUNsRSxzT0FBUUE7NERBQWdCeUosS0FBSzs7OEVBQzVCLDhEQUFDNUosc09BQVNBOzhFQUNSLDRFQUFDTixxT0FBVUE7d0VBQUNzSSxTQUFRO3dFQUFRQyxZQUFXO3dFQUFTZCxPQUFNO2tGQUNuRDlDLE1BQU0wRixXQUFXOzs7Ozs7Ozs7Ozs4RUFHdEIsOERBQUMvSixzT0FBU0E7OEVBQ1IsNEVBQUNOLHFPQUFVQTt3RUFBQ3NJLFNBQVE7d0VBQVFDLFlBQVc7a0ZBQ3BDNUQsTUFBTU8sSUFBSTs7Ozs7Ozs7Ozs7OEVBR2YsOERBQUM1RSxzT0FBU0E7OEVBQ1IsNEVBQUNKLHFPQUFHQTt3RUFBQ3lILElBQUk7NEVBQUVPLFNBQVM7NEVBQVFDLFlBQVk7NEVBQVVnQyxLQUFLO3dFQUFFOzswRkFDdkQsOERBQUMvSSwrSEFBWUE7Z0ZBQUNzSCxVQUFTO2dGQUFRakIsT0FBTTs7Ozs7OzBGQUNyQyw4REFBQ3pILHFPQUFVQTtnRkFBQ3NJLFNBQVE7MEZBQVMzRCxFQUFBQSxrQkFBQUEsTUFBTU0sUUFBUSxjQUFkTixzQ0FBQUEsZ0JBQWdCTyxJQUFJLEtBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OzhFQUd6RCw4REFBQzVFLHNPQUFTQTs4RUFDUiw0RUFBQ0oscU9BQUdBO3dFQUFDeUgsSUFBSTs0RUFBRU8sU0FBUzs0RUFBUUMsWUFBWTs0RUFBVWdDLEtBQUs7d0VBQUU7OzBGQUN2RCw4REFBQzdJLCtIQUFZQTtnRkFBQ29ILFVBQVM7Z0ZBQVFqQixPQUFNOzs7Ozs7MEZBQ3JDLDhEQUFDekgscU9BQVVBO2dGQUFDc0ksU0FBUTswRkFBUzNELEVBQUFBLGtCQUFBQSxNQUFNVSxRQUFRLGNBQWRWLHNDQUFBQSxnQkFBZ0JPLElBQUksS0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBR3pELDhEQUFDNUUsc09BQVNBOzhFQUNSLDRFQUFDSCxzT0FBSUE7d0VBQ0hpSyxPQUNFekYsTUFBTUMsTUFBTSxHQUNSRCxNQUFNQyxNQUFNLENBQUN1QixPQUFPLENBQUMsS0FBSyxLQUFLQSxPQUFPLENBQUMsU0FBU0MsQ0FBQUEsSUFBS0EsRUFBRUMsV0FBVyxNQUNsRTt3RUFFTm9CLE9BQU85QyxNQUFNQyxNQUFNLEdBQUcvQyxZQUFZLENBQUM4QyxNQUFNQyxNQUFNLENBQThCLEdBQUc7d0VBQ2hGbUUsTUFBSzt3RUFDTFQsU0FBUTs7Ozs7Ozs7Ozs7OEVBR1osOERBQUNoSSxzT0FBU0E7OEVBQ1IsNEVBQUNILHNPQUFJQTt3RUFDSGlLLE9BQ0V6RixNQUFNRyxTQUFTLEdBQ1hILE1BQU1HLFNBQVMsQ0FBQ3lCLE1BQU0sQ0FBQyxHQUFHRixXQUFXLEtBQUsxQixNQUFNRyxTQUFTLENBQUMwQixLQUFLLENBQUMsS0FDaEU7d0VBRU5pQixPQUNFOUMsTUFBTUcsU0FBUyxHQUNYekMsZUFBZSxDQUFDc0MsTUFBTUcsU0FBUyxDQUFpQyxHQUNoRTt3RUFFTmlFLE1BQUs7d0VBQ0xULFNBQVE7Ozs7Ozs7Ozs7OzhFQUdaLDhEQUFDaEksc09BQVNBOzhFQUNSLDRFQUFDSixxT0FBR0E7d0VBQUN5SCxJQUFJOzRFQUFFTyxTQUFTOzRFQUFRQyxZQUFZOzRFQUFVZ0MsS0FBSzt3RUFBRTs7MEZBQ3ZELDhEQUFDakosK0hBQVVBO2dGQUFDd0gsVUFBUztnRkFBUWpCLE9BQU07Ozs7OzswRkFDbkMsOERBQUN6SCxxT0FBVUE7Z0ZBQUNzSSxTQUFROzBGQUFTM0QsRUFBQUEsb0JBQUFBLE1BQU0yRixVQUFVLGNBQWhCM0Ysd0NBQUFBLGtCQUFrQjRGLFFBQVEsS0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBRy9ELDhEQUFDakssc09BQVNBOzhFQUNSLDRFQUFDTixxT0FBVUE7d0VBQUNzSSxTQUFRO3dFQUFRYixPQUFNO2tGQUMvQjlDLE1BQU1nQyxTQUFTLEdBQUcsSUFBSUQsS0FBSy9CLE1BQU1nQyxTQUFTLEVBQUU2RCxrQkFBa0IsS0FBSzs7Ozs7Ozs7Ozs7OzJEQTNEM0Q3RixNQUFNOEYsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQ0FtRTlCOUgsTUFBTThELFlBQVksQ0FBQ2xDLE1BQU0sS0FBSyxtQkFDN0IsOERBQUNyRSxxT0FBR0E7d0NBQUN5SCxJQUFJOzRDQUFFK0MsV0FBVzs0Q0FBVUMsSUFBSTt3Q0FBRTtrREFDcEMsNEVBQUMzSyxxT0FBVUE7NENBQUNzSSxTQUFROzRDQUFRYixPQUFNO3NEQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBV3JFO0dBaG5Cd0IvRTtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwLyhkYXNoYm9hcmQpL2Rhc2hib2FyZC9wYWdlLnRzeD9hZTg0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQge1xuICBDYXJkLFxuICBDYXJkQ29udGVudCxcbiAgVHlwb2dyYXBoeSxcbiAgR3JpZCxcbiAgQm94LFxuICBDaGlwLFxuICBUYWJsZSxcbiAgVGFibGVCb2R5LFxuICBUYWJsZUNlbGwsXG4gIFRhYmxlQ29udGFpbmVyLFxuICBUYWJsZUhlYWQsXG4gIFRhYmxlUm93LFxuICBMaW5lYXJQcm9ncmVzcyxcbiAgQXZhdGFyLFxuICBMaXN0LFxuICBMaXN0SXRlbSxcbiAgTGlzdEl0ZW1UZXh0LFxuICBMaXN0SXRlbUF2YXRhcixcbiAgRGl2aWRlcixcbiAgQnV0dG9uLFxuICBDaXJjdWxhclByb2dyZXNzLFxuICBBbGVydFxufSBmcm9tICdAbXVpL21hdGVyaWFsJ1xuaW1wb3J0IHtcbiAgSW52ZW50b3J5IGFzIEludmVudG9yeUljb24sXG4gIEFzc2lnbm1lbnQgYXMgQXNzaWdubWVudEljb24sXG4gIENoZWNrQ2lyY2xlIGFzIENoZWNrQ2lyY2xlSWNvbixcbiAgUGVyc29uIGFzIFBlcnNvbkljb24sXG4gIENhdGVnb3J5IGFzIENhdGVnb3J5SWNvbixcbiAgTG9jYXRpb25PbiBhcyBMb2NhdGlvbkljb24sXG4gIEJ1aWxkIGFzIE1haW50ZW5hbmNlSWNvbixcbiAgV2FybmluZyBhcyBXYXJuaW5nSWNvbixcbiAgUmVmcmVzaCBhcyBSZWZyZXNoSWNvblxufSBmcm9tICdAbXVpL2ljb25zLW1hdGVyaWFsJ1xuaW1wb3J0IHsgYXNzZXRzU2VydmljZSwgY2F0ZWdvcmllc1NlcnZpY2UsIGxvY2F0aW9uc1NlcnZpY2UsIHVzZXJzU2VydmljZSB9IGZyb20gJ0AvbGliL2FwaSdcbmltcG9ydCB7IEFzc2V0LCBDYXRlZ29yeSwgTG9jYXRpb24sIFVzZXIgfSBmcm9tICdAL3R5cGVzL2FwaSdcblxuLy8gVHlwZXMgZm9yIGRhc2hib2FyZCBkYXRhXG5pbnRlcmZhY2UgRGFzaGJvYXJkU3RhdHMge1xuICB0b3RhbEFzc2V0czogbnVtYmVyXG4gIGluVXNlOiBudW1iZXJcbiAgaW5TdG9jazogbnVtYmVyXG4gIG1haW50ZW5hbmNlOiBudW1iZXJcbiAgcmV0aXJlZDogbnVtYmVyXG4gIGxvc3Q6IG51bWJlclxuICBkYW1hZ2VkOiBudW1iZXJcbiAgdG90YWxDYXRlZ29yaWVzOiBudW1iZXJcbiAgdG90YWxMb2NhdGlvbnM6IG51bWJlclxuICB0b3RhbFVzZXJzOiBudW1iZXJcbiAgcmVjZW50QXNzZXRzOiBBc3NldFtdXG4gIGNhdGVnb3J5QnJlYWtkb3duOiB7IG5hbWU6IHN0cmluZzsgY291bnQ6IG51bWJlcjsgcGVyY2VudGFnZTogbnVtYmVyIH1bXVxuICBsb2NhdGlvbkJyZWFrZG93bjogeyBuYW1lOiBzdHJpbmc7IGNvdW50OiBudW1iZXI7IHBlcmNlbnRhZ2U6IG51bWJlciB9W11cbiAgc3RhdHVzQnJlYWtkb3duOiB7IHN0YXR1czogc3RyaW5nOyBjb3VudDogbnVtYmVyOyBwZXJjZW50YWdlOiBudW1iZXIgfVtdXG4gIGNvbmRpdGlvbkJyZWFrZG93bjogeyBjb25kaXRpb246IHN0cmluZzsgY291bnQ6IG51bWJlcjsgcGVyY2VudGFnZTogbnVtYmVyIH1bXVxufVxuXG5jb25zdCBzdGF0dXNDb2xvcnMgPSB7XG4gIGluX3VzZTogJ3ByaW1hcnknLFxuICBpbl9zdG9jazogJ3N1Y2Nlc3MnLFxuICBtYWludGVuYW5jZTogJ3dhcm5pbmcnLFxuICByZXRpcmVkOiAnc2Vjb25kYXJ5JyxcbiAgbG9zdDogJ2Vycm9yJyxcbiAgZGFtYWdlZDogJ2Vycm9yJyxcbiAgdW5rbm93bjogJ2RlZmF1bHQnXG59IGFzIGNvbnN0XG5cbmNvbnN0IGNvbmRpdGlvbkNvbG9ycyA9IHtcbiAgZXhjZWxsZW50OiAnc3VjY2VzcycsXG4gIGdvb2Q6ICdpbmZvJyxcbiAgZmFpcjogJ3dhcm5pbmcnLFxuICBwb29yOiAnZXJyb3InLFxuICB1bmtub3duOiAnZGVmYXVsdCdcbn0gYXMgY29uc3RcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXNzZXREYXNoYm9hcmQoKSB7XG4gIGNvbnN0IFtzdGF0cywgc2V0U3RhdHNdID0gdXNlU3RhdGU8RGFzaGJvYXJkU3RhdHMgfCBudWxsPihudWxsKVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFthc3NldHMsIHNldEFzc2V0c10gPSB1c2VTdGF0ZTxBc3NldFtdPihbXSlcbiAgY29uc3QgW2NhdGVnb3JpZXMsIHNldENhdGVnb3JpZXNdID0gdXNlU3RhdGU8Q2F0ZWdvcnlbXT4oW10pXG4gIGNvbnN0IFtsb2NhdGlvbnMsIHNldExvY2F0aW9uc10gPSB1c2VTdGF0ZTxMb2NhdGlvbltdPihbXSlcbiAgY29uc3QgW3VzZXJzLCBzZXRVc2Vyc10gPSB1c2VTdGF0ZTxVc2VyW10+KFtdKVxuXG4gIC8vIExvYWQgZGFzaGJvYXJkIGRhdGFcbiAgY29uc3QgbG9hZERhc2hib2FyZERhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICAgIHNldEVycm9yKG51bGwpXG5cbiAgICAgIC8vIExvYWQgYWxsIGRhdGEgaW4gcGFyYWxsZWxcbiAgICAgIGNvbnN0IFthc3NldHNSZXNwb25zZSwgY2F0ZWdvcmllc0RhdGEsIGxvY2F0aW9uc0RhdGEsIHVzZXJzRGF0YV0gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICAgIGFzc2V0c1NlcnZpY2UuZ2V0QXNzZXRzKHsgbGltaXQ6IDEwMDAgfSksIC8vIEdldCBhbGwgYXNzZXRzIGZvciBzdGF0aXN0aWNzXG4gICAgICAgIGNhdGVnb3JpZXNTZXJ2aWNlLmdldENhdGVnb3JpZXMoKSxcbiAgICAgICAgbG9jYXRpb25zU2VydmljZS5nZXRMb2NhdGlvbnMoKSxcbiAgICAgICAgdXNlcnNTZXJ2aWNlLmdldFVzZXJzKClcbiAgICAgIF0pXG5cbiAgICAgIGNvbnN0IGFsbEFzc2V0cyA9IGFzc2V0c1Jlc3BvbnNlLmFzc2V0c1xuICAgICAgc2V0QXNzZXRzKGFsbEFzc2V0cylcbiAgICAgIHNldENhdGVnb3JpZXMoY2F0ZWdvcmllc0RhdGEpXG4gICAgICBzZXRMb2NhdGlvbnMobG9jYXRpb25zRGF0YSlcbiAgICAgIHNldFVzZXJzKHVzZXJzRGF0YSlcblxuICAgICAgLy8gQ2FsY3VsYXRlIHN0YXRpc3RpY3NcbiAgICAgIGNvbnN0IHRvdGFsQXNzZXRzID0gYWxsQXNzZXRzLmxlbmd0aFxuICAgICAgY29uc3Qgc3RhdHVzQ291bnRzID0gYWxsQXNzZXRzLnJlZHVjZShcbiAgICAgICAgKGFjYywgYXNzZXQpID0+IHtcbiAgICAgICAgICBjb25zdCBzdGF0dXMgPSBhc3NldC5zdGF0dXMgfHwgJ3Vua25vd24nXG4gICAgICAgICAgYWNjW3N0YXR1c10gPSAoYWNjW3N0YXR1c10gfHwgMCkgKyAxXG4gICAgICAgICAgcmV0dXJuIGFjY1xuICAgICAgICB9LFxuICAgICAgICB7fSBhcyBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+XG4gICAgICApXG5cbiAgICAgIGNvbnN0IGNvbmRpdGlvbkNvdW50cyA9IGFsbEFzc2V0cy5yZWR1Y2UoXG4gICAgICAgIChhY2MsIGFzc2V0KSA9PiB7XG4gICAgICAgICAgY29uc3QgY29uZGl0aW9uID0gYXNzZXQuY29uZGl0aW9uIHx8ICd1bmtub3duJ1xuICAgICAgICAgIGFjY1tjb25kaXRpb25dID0gKGFjY1tjb25kaXRpb25dIHx8IDApICsgMVxuICAgICAgICAgIHJldHVybiBhY2NcbiAgICAgICAgfSxcbiAgICAgICAge30gYXMgUmVjb3JkPHN0cmluZywgbnVtYmVyPlxuICAgICAgKVxuXG4gICAgICBjb25zdCBjYXRlZ29yeUNvdW50cyA9IGFsbEFzc2V0cy5yZWR1Y2UoXG4gICAgICAgIChhY2MsIGFzc2V0KSA9PiB7XG4gICAgICAgICAgY29uc3QgY2F0ZWdvcnlOYW1lID0gYXNzZXQuY2F0ZWdvcnk/Lm5hbWUgfHwgJ1Vua25vd24nXG4gICAgICAgICAgYWNjW2NhdGVnb3J5TmFtZV0gPSAoYWNjW2NhdGVnb3J5TmFtZV0gfHwgMCkgKyAxXG4gICAgICAgICAgcmV0dXJuIGFjY1xuICAgICAgICB9LFxuICAgICAgICB7fSBhcyBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+XG4gICAgICApXG5cbiAgICAgIGNvbnN0IGxvY2F0aW9uQ291bnRzID0gYWxsQXNzZXRzLnJlZHVjZShcbiAgICAgICAgKGFjYywgYXNzZXQpID0+IHtcbiAgICAgICAgICBjb25zdCBsb2NhdGlvbk5hbWUgPSBhc3NldC5sb2NhdGlvbj8ubmFtZSB8fCAnVW5rbm93bidcbiAgICAgICAgICBhY2NbbG9jYXRpb25OYW1lXSA9IChhY2NbbG9jYXRpb25OYW1lXSB8fCAwKSArIDFcbiAgICAgICAgICByZXR1cm4gYWNjXG4gICAgICAgIH0sXG4gICAgICAgIHt9IGFzIFJlY29yZDxzdHJpbmcsIG51bWJlcj5cbiAgICAgIClcblxuICAgICAgLy8gQ3JlYXRlIGJyZWFrZG93biBhcnJheXNcbiAgICAgIGNvbnN0IGNhdGVnb3J5QnJlYWtkb3duID0gT2JqZWN0LmVudHJpZXMoY2F0ZWdvcnlDb3VudHMpXG4gICAgICAgIC5tYXAoKFtuYW1lLCBjb3VudF0pID0+ICh7XG4gICAgICAgICAgbmFtZSxcbiAgICAgICAgICBjb3VudCxcbiAgICAgICAgICBwZXJjZW50YWdlOiBNYXRoLnJvdW5kKChjb3VudCAvIHRvdGFsQXNzZXRzKSAqIDEwMClcbiAgICAgICAgfSkpXG4gICAgICAgIC5zb3J0KChhLCBiKSA9PiBiLmNvdW50IC0gYS5jb3VudClcblxuICAgICAgY29uc3QgbG9jYXRpb25CcmVha2Rvd24gPSBPYmplY3QuZW50cmllcyhsb2NhdGlvbkNvdW50cylcbiAgICAgICAgLm1hcCgoW25hbWUsIGNvdW50XSkgPT4gKHtcbiAgICAgICAgICBuYW1lLFxuICAgICAgICAgIGNvdW50LFxuICAgICAgICAgIHBlcmNlbnRhZ2U6IE1hdGgucm91bmQoKGNvdW50IC8gdG90YWxBc3NldHMpICogMTAwKVxuICAgICAgICB9KSlcbiAgICAgICAgLnNvcnQoKGEsIGIpID0+IGIuY291bnQgLSBhLmNvdW50KVxuXG4gICAgICBjb25zdCBzdGF0dXNCcmVha2Rvd24gPSBPYmplY3QuZW50cmllcyhzdGF0dXNDb3VudHMpXG4gICAgICAgIC5tYXAoKFtzdGF0dXMsIGNvdW50XSkgPT4gKHtcbiAgICAgICAgICBzdGF0dXM6IHN0YXR1cy5yZXBsYWNlKCdfJywgJyAnKS5yZXBsYWNlKC9cXGJcXHcvZywgbCA9PiBsLnRvVXBwZXJDYXNlKCkpLFxuICAgICAgICAgIGNvdW50LFxuICAgICAgICAgIHBlcmNlbnRhZ2U6IE1hdGgucm91bmQoKGNvdW50IC8gdG90YWxBc3NldHMpICogMTAwKVxuICAgICAgICB9KSlcbiAgICAgICAgLnNvcnQoKGEsIGIpID0+IGIuY291bnQgLSBhLmNvdW50KVxuXG4gICAgICBjb25zdCBjb25kaXRpb25CcmVha2Rvd24gPSBPYmplY3QuZW50cmllcyhjb25kaXRpb25Db3VudHMpXG4gICAgICAgIC5tYXAoKFtjb25kaXRpb24sIGNvdW50XSkgPT4gKHtcbiAgICAgICAgICBjb25kaXRpb246IGNvbmRpdGlvbi5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIGNvbmRpdGlvbi5zbGljZSgxKSxcbiAgICAgICAgICBjb3VudCxcbiAgICAgICAgICBwZXJjZW50YWdlOiBNYXRoLnJvdW5kKChjb3VudCAvIHRvdGFsQXNzZXRzKSAqIDEwMClcbiAgICAgICAgfSkpXG4gICAgICAgIC5zb3J0KChhLCBiKSA9PiBiLmNvdW50IC0gYS5jb3VudClcblxuICAgICAgLy8gR2V0IHJlY2VudCBhc3NldHMgKGxhc3QgNSlcbiAgICAgIGNvbnN0IHJlY2VudEFzc2V0cyA9IGFsbEFzc2V0c1xuICAgICAgICAuc29ydCgoYSwgYikgPT4gbmV3IERhdGUoYi5jcmVhdGVkQXQgfHwgJycpLmdldFRpbWUoKSAtIG5ldyBEYXRlKGEuY3JlYXRlZEF0IHx8ICcnKS5nZXRUaW1lKCkpXG4gICAgICAgIC5zbGljZSgwLCA1KVxuXG4gICAgICBjb25zdCBkYXNoYm9hcmRTdGF0czogRGFzaGJvYXJkU3RhdHMgPSB7XG4gICAgICAgIHRvdGFsQXNzZXRzLFxuICAgICAgICBpblVzZTogc3RhdHVzQ291bnRzLmluX3VzZSB8fCAwLFxuICAgICAgICBpblN0b2NrOiBzdGF0dXNDb3VudHMuaW5fc3RvY2sgfHwgMCxcbiAgICAgICAgbWFpbnRlbmFuY2U6IHN0YXR1c0NvdW50cy5tYWludGVuYW5jZSB8fCAwLFxuICAgICAgICByZXRpcmVkOiBzdGF0dXNDb3VudHMucmV0aXJlZCB8fCAwLFxuICAgICAgICBsb3N0OiBzdGF0dXNDb3VudHMubG9zdCB8fCAwLFxuICAgICAgICBkYW1hZ2VkOiBzdGF0dXNDb3VudHMuZGFtYWdlZCB8fCAwLFxuICAgICAgICB0b3RhbENhdGVnb3JpZXM6IGNhdGVnb3JpZXNEYXRhLmxlbmd0aCxcbiAgICAgICAgdG90YWxMb2NhdGlvbnM6IGxvY2F0aW9uc0RhdGEubGVuZ3RoLFxuICAgICAgICB0b3RhbFVzZXJzOiB1c2Vyc0RhdGEubGVuZ3RoLFxuICAgICAgICByZWNlbnRBc3NldHMsXG4gICAgICAgIGNhdGVnb3J5QnJlYWtkb3duLFxuICAgICAgICBsb2NhdGlvbkJyZWFrZG93bixcbiAgICAgICAgc3RhdHVzQnJlYWtkb3duLFxuICAgICAgICBjb25kaXRpb25CcmVha2Rvd25cbiAgICAgIH1cblxuICAgICAgc2V0U3RhdHMoZGFzaGJvYXJkU3RhdHMpXG4gICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGRhc2hib2FyZCBkYXRhOicsIGVycilcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gbG9hZCBkYXNoYm9hcmQgZGF0YS4gUGxlYXNlIHRyeSBhZ2Fpbi4nKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZERhc2hib2FyZERhdGEoKVxuICB9LCBbXSlcblxuICBjb25zdCBTdGF0Q2FyZCA9ICh7IHRpdGxlLCB2YWx1ZSwgaWNvbiwgY29sb3IsIHN1YnRpdGxlIH06IGFueSkgPT4gKFxuICAgIDxDYXJkXG4gICAgICBzeD17e1xuICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsMjU1LDI1NSwwLjkpIDAlLCByZ2JhKDI1NSwyNTUsMjU1LDAuNykgMTAwJSknLFxuICAgICAgICBiYWNrZHJvcEZpbHRlcjogJ2JsdXIoMTBweCknLFxuICAgICAgICBib3JkZXI6ICcxcHggc29saWQgcmdiYSgyNTUsMjU1LDI1NSwwLjIpJyxcbiAgICAgICAgYm94U2hhZG93OiAnbm9uZScsXG4gICAgICAgIGJvcmRlckNvbG9yOiAnZGl2aWRlcidcbiAgICAgIH19XG4gICAgPlxuICAgICAgPENhcmRDb250ZW50IHN4PXt7IHA6IDMgfX0+XG4gICAgICAgIDxCb3ggc3g9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJyB9fT5cbiAgICAgICAgICA8Qm94PlxuICAgICAgICAgICAgPFR5cG9ncmFwaHkgY29sb3I9J3RleHQuc2Vjb25kYXJ5JyBndXR0ZXJCb3R0b20gdmFyaWFudD0nYm9keTInIHN4PXt7IGZvbnRXZWlnaHQ6IDUwMCB9fT5cbiAgICAgICAgICAgICAge3RpdGxlfVxuICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0naDMnIGNvbXBvbmVudD0nZGl2JyBjb2xvcj17Y29sb3J9IHN4PXt7IGZvbnRXZWlnaHQ6IDcwMCwgbWI6IDAuNSB9fT5cbiAgICAgICAgICAgICAge3ZhbHVlfVxuICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAge3N1YnRpdGxlICYmIChcbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTInIGNvbG9yPSd0ZXh0LnNlY29uZGFyeScgc3g9e3sgZm9udFNpemU6ICcwLjg3NXJlbScgfX0+XG4gICAgICAgICAgICAgICAge3N1YnRpdGxlfVxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvQm94PlxuICAgICAgICAgIHtpY29uICYmIChcbiAgICAgICAgICAgIDxBdmF0YXJcbiAgICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgICBiZ2NvbG9yOiBgJHtjb2xvcn0ubWFpbmAsXG4gICAgICAgICAgICAgICAgd2lkdGg6IDQ4LFxuICAgICAgICAgICAgICAgIGhlaWdodDogNDhcbiAgICAgICAgICAgICAgICAvLyBib3hTaGFkb3c6ICcwIDRweCAxNHB4IHJnYmEoMCwwLDAsMC4xNSknXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtpY29ufVxuICAgICAgICAgICAgPC9BdmF0YXI+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9Cb3g+XG4gICAgICA8L0NhcmRDb250ZW50PlxuICAgIDwvQ2FyZD5cbiAgKVxuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxCb3ggc3g9e3sgcDogMywgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBtaW5IZWlnaHQ6ICc2MHZoJyB9fT5cbiAgICAgICAgPENpcmN1bGFyUHJvZ3Jlc3Mgc2l6ZT17NjB9IC8+XG4gICAgICA8L0JveD5cbiAgICApXG4gIH1cblxuICBpZiAoZXJyb3IpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPEJveCBzeD17eyBwOiAzIH19PlxuICAgICAgICA8QWxlcnQgc2V2ZXJpdHk9J2Vycm9yJyBzeD17eyBtYjogMyB9fT5cbiAgICAgICAgICB7ZXJyb3J9XG4gICAgICAgIDwvQWxlcnQ+XG4gICAgICAgIDxCdXR0b24gdmFyaWFudD0nY29udGFpbmVkJyBzdGFydEljb249ezxSZWZyZXNoSWNvbiAvPn0gb25DbGljaz17bG9hZERhc2hib2FyZERhdGF9PlxuICAgICAgICAgIFJldHJ5XG4gICAgICAgIDwvQnV0dG9uPlxuICAgICAgPC9Cb3g+XG4gICAgKVxuICB9XG5cbiAgaWYgKCFzdGF0cykge1xuICAgIHJldHVybiAoXG4gICAgICA8Qm94IHN4PXt7IHA6IDMgfX0+XG4gICAgICAgIDxBbGVydCBzZXZlcml0eT0naW5mbyc+Tm8gZGF0YSBhdmFpbGFibGU8L0FsZXJ0PlxuICAgICAgPC9Cb3g+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8Qm94IHN4PXt7IHA6IDQsIGJhY2tncm91bmRDb2xvcjogJ2dyZXkuNTAnLCBtaW5IZWlnaHQ6ICcxMDB2aCcgfX0+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPEJveCBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYmV0d2VlbicsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBtYjogNCB9fT5cbiAgICAgICAgPEJveD5cbiAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdoNCcgY29tcG9uZW50PSdoMScgZ3V0dGVyQm90dG9tIHN4PXt7IGZvbnRXZWlnaHQ6IDcwMCwgY29sb3I6ICd0ZXh0LnByaW1hcnknIH19PlxuICAgICAgICAgICAgQXNzZXQgTWFuYWdlbWVudCBEYXNoYm9hcmRcbiAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTEnIGNvbG9yPSd0ZXh0LnNlY29uZGFyeSc+XG4gICAgICAgICAgICBDb21wcmVoZW5zaXZlIG92ZXJ2aWV3IG9mIHlvdXIgb3JnYW5pemF0aW9uJ3MgYXNzZXRzXG4gICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICA8L0JveD5cbiAgICAgICAgPEJ1dHRvblxuICAgICAgICAgIHZhcmlhbnQ9J291dGxpbmVkJ1xuICAgICAgICAgIHN0YXJ0SWNvbj17PFJlZnJlc2hJY29uIC8+fVxuICAgICAgICAgIG9uQ2xpY2s9e2xvYWREYXNoYm9hcmREYXRhfVxuICAgICAgICAgIHN4PXt7XG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6IDIsXG4gICAgICAgICAgICB0ZXh0VHJhbnNmb3JtOiAnbm9uZScsXG4gICAgICAgICAgICBmb250V2VpZ2h0OiA2MDBcbiAgICAgICAgICB9fVxuICAgICAgICA+XG4gICAgICAgICAgUmVmcmVzaCBEYXRhXG4gICAgICAgIDwvQnV0dG9uPlxuICAgICAgPC9Cb3g+XG5cbiAgICAgIHsvKiBLZXkgU3RhdGlzdGljcyAqL31cbiAgICAgIDxHcmlkIGNvbnRhaW5lciBzcGFjaW5nPXszfSBzeD17eyBtYjogNCB9fT5cbiAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fSBtZD17M30+XG4gICAgICAgICAgPFN0YXRDYXJkXG4gICAgICAgICAgICB0aXRsZT0nVG90YWwgQXNzZXRzJ1xuICAgICAgICAgICAgdmFsdWU9e3N0YXRzLnRvdGFsQXNzZXRzLnRvTG9jYWxlU3RyaW5nKCl9XG4gICAgICAgICAgICBpY29uPXs8SW52ZW50b3J5SWNvbiAvPn1cbiAgICAgICAgICAgIGNvbG9yPSdwcmltYXJ5J1xuICAgICAgICAgICAgc3VidGl0bGU9J0FsbCByZWdpc3RlcmVkIGFzc2V0cydcbiAgICAgICAgICAvPlxuICAgICAgICA8L0dyaWQ+XG4gICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17Nn0gbWQ9ezN9PlxuICAgICAgICAgIDxTdGF0Q2FyZFxuICAgICAgICAgICAgdGl0bGU9J0NhdGVnb3JpZXMnXG4gICAgICAgICAgICB2YWx1ZT17c3RhdHMudG90YWxDYXRlZ29yaWVzLnRvTG9jYWxlU3RyaW5nKCl9XG4gICAgICAgICAgICBpY29uPXs8Q2F0ZWdvcnlJY29uIC8+fVxuICAgICAgICAgICAgY29sb3I9J2luZm8nXG4gICAgICAgICAgICBzdWJ0aXRsZT0nQXNzZXQgY2F0ZWdvcmllcydcbiAgICAgICAgICAvPlxuICAgICAgICA8L0dyaWQ+XG4gICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17Nn0gbWQ9ezN9PlxuICAgICAgICAgIDxTdGF0Q2FyZFxuICAgICAgICAgICAgdGl0bGU9J0xvY2F0aW9ucydcbiAgICAgICAgICAgIHZhbHVlPXtzdGF0cy50b3RhbExvY2F0aW9ucy50b0xvY2FsZVN0cmluZygpfVxuICAgICAgICAgICAgaWNvbj17PExvY2F0aW9uSWNvbiAvPn1cbiAgICAgICAgICAgIGNvbG9yPSd3YXJuaW5nJ1xuICAgICAgICAgICAgc3VidGl0bGU9J1N0b3JhZ2UgbG9jYXRpb25zJ1xuICAgICAgICAgIC8+XG4gICAgICAgIDwvR3JpZD5cbiAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fSBtZD17M30+XG4gICAgICAgICAgPFN0YXRDYXJkXG4gICAgICAgICAgICB0aXRsZT0nVXNlcnMnXG4gICAgICAgICAgICB2YWx1ZT17c3RhdHMudG90YWxVc2Vycy50b0xvY2FsZVN0cmluZygpfVxuICAgICAgICAgICAgaWNvbj17PFBlcnNvbkljb24gLz59XG4gICAgICAgICAgICBjb2xvcj0nc3VjY2VzcydcbiAgICAgICAgICAgIHN1YnRpdGxlPSdTeXN0ZW0gdXNlcnMnXG4gICAgICAgICAgLz5cbiAgICAgICAgPC9HcmlkPlxuICAgICAgPC9HcmlkPlxuXG4gICAgICB7LyogU3RhdHVzIE92ZXJ2aWV3ICovfVxuICAgICAgPEdyaWQgY29udGFpbmVyIHNwYWNpbmc9ezN9IHN4PXt7IG1iOiA0IH19PlxuICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9IG1kPXsyfT5cbiAgICAgICAgICA8U3RhdENhcmQgdGl0bGU9J0luIFVzZScgdmFsdWU9e3N0YXRzLmluVXNlLnRvTG9jYWxlU3RyaW5nKCl9IGNvbG9yPSdwcmltYXJ5JyAvPlxuICAgICAgICA8L0dyaWQ+XG4gICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17Nn0gbWQ9ezJ9PlxuICAgICAgICAgIDxTdGF0Q2FyZCB0aXRsZT0nQXZhaWxhYmxlJyB2YWx1ZT17c3RhdHMuaW5TdG9jay50b0xvY2FsZVN0cmluZygpfSBjb2xvcj0nc3VjY2VzcycgLz5cbiAgICAgICAgPC9HcmlkPlxuICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9IG1kPXsyfT5cbiAgICAgICAgICA8U3RhdENhcmQgdGl0bGU9J01haW50ZW5hbmNlJyB2YWx1ZT17c3RhdHMubWFpbnRlbmFuY2UudG9Mb2NhbGVTdHJpbmcoKX0gY29sb3I9J3dhcm5pbmcnIC8+XG4gICAgICAgIDwvR3JpZD5cbiAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fSBtZD17Mn0+XG4gICAgICAgICAgPFN0YXRDYXJkIHRpdGxlPSdSZXRpcmVkJyB2YWx1ZT17c3RhdHMucmV0aXJlZC50b0xvY2FsZVN0cmluZygpfSBjb2xvcj0nc2Vjb25kYXJ5JyAvPlxuICAgICAgICA8L0dyaWQ+XG4gICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17Nn0gbWQ9ezJ9PlxuICAgICAgICAgIDxTdGF0Q2FyZCB0aXRsZT0nTG9zdCcgdmFsdWU9e3N0YXRzLmxvc3QudG9Mb2NhbGVTdHJpbmcoKX0gY29sb3I9J2Vycm9yJyAvPlxuICAgICAgICA8L0dyaWQ+XG4gICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17Nn0gbWQ9ezJ9PlxuICAgICAgICAgIDxTdGF0Q2FyZCB0aXRsZT0nRGFtYWdlZCcgdmFsdWU9e3N0YXRzLmRhbWFnZWQudG9Mb2NhbGVTdHJpbmcoKX0gY29sb3I9J2Vycm9yJyAvPlxuICAgICAgICA8L0dyaWQ+XG4gICAgICA8L0dyaWQ+XG5cbiAgICAgIDxHcmlkIGNvbnRhaW5lciBzcGFjaW5nPXszfT5cbiAgICAgICAgey8qIEFzc2V0IFN0YXR1cyBCcmVha2Rvd24gKi99XG4gICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBtZD17Nn0+XG4gICAgICAgICAgPENhcmRcbiAgICAgICAgICAgIHN4PXt7XG4gICAgICAgICAgICAgIC8vIGJvcmRlclJhZGl1czogMyxcbiAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkJyxcbiAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICdkaXZpZGVyJyxcbiAgICAgICAgICAgICAgYm94U2hhZG93OiAnbm9uZSdcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPENhcmRDb250ZW50IHN4PXt7IHA6IDMgfX0+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2g2JyBndXR0ZXJCb3R0b20gc3g9e3sgZm9udFdlaWdodDogNjAwLCBtYjogMyB9fT5cbiAgICAgICAgICAgICAgICBBc3NldCBTdGF0dXMgRGlzdHJpYnV0aW9uXG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPEJveCBzeD17eyBtdDogMiB9fT5cbiAgICAgICAgICAgICAgICB7c3RhdHMuc3RhdHVzQnJlYWtkb3duLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxCb3gga2V5PXtpdGVtLnN0YXR1c30gc3g9e3sgbWI6IGluZGV4ID09PSBzdGF0cy5zdGF0dXNCcmVha2Rvd24ubGVuZ3RoIC0gMSA/IDAgOiAzIH19PlxuICAgICAgICAgICAgICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJywgbWI6IDEgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTInIHN4PXt7IGZvbnRXZWlnaHQ6IDUwMCB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLnN0YXR1c31cbiAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTInIHN4PXt7IGZvbnRXZWlnaHQ6IDYwMCB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLmNvdW50fSAoe2l0ZW0ucGVyY2VudGFnZX0lKVxuICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICAgICAgICAgIDxMaW5lYXJQcm9ncmVzc1xuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9J2RldGVybWluYXRlJ1xuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtpdGVtLnBlcmNlbnRhZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogOCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogNCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ2dyZXkuMjAwJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICcmIC5NdWlMaW5lYXJQcm9ncmVzcy1iYXInOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogNFxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgY29sb3I9e1xuICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS5zdGF0dXMudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygndXNlJylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyAncHJpbWFyeSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBpdGVtLnN0YXR1cy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCdzdG9jaycpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnc3VjY2VzcydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGl0ZW0uc3RhdHVzLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ21haW50ZW5hbmNlJylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ3dhcm5pbmcnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdzZWNvbmRhcnknXG4gICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvQm94PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvR3JpZD5cblxuICAgICAgICB7LyogQ2F0ZWdvcnkgQnJlYWtkb3duICovfVxuICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gbWQ9ezZ9PlxuICAgICAgICAgIDxDYXJkXG4gICAgICAgICAgICBzeD17e1xuICAgICAgICAgICAgICAvLyBib3JkZXJSYWRpdXM6IDMsXG4gICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCcsXG4gICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnZGl2aWRlcicsXG4gICAgICAgICAgICAgIGJveFNoYWRvdzogJ25vbmUnXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBzeD17eyBwOiAzIH19PlxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdoNicgZ3V0dGVyQm90dG9tIHN4PXt7IGZvbnRXZWlnaHQ6IDYwMCwgbWI6IDMgfX0+XG4gICAgICAgICAgICAgICAgQXNzZXRzIGJ5IENhdGVnb3J5XG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPFRhYmxlQ29udGFpbmVyPlxuICAgICAgICAgICAgICAgIDxUYWJsZSBzaXplPSdzbWFsbCc+XG4gICAgICAgICAgICAgICAgICA8VGFibGVIZWFkPlxuICAgICAgICAgICAgICAgICAgICA8VGFibGVSb3c+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBzeD17eyBmb250V2VpZ2h0OiA2MDAgfX0+Q2F0ZWdvcnk8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGFsaWduPSdyaWdodCcgc3g9e3sgZm9udFdlaWdodDogNjAwIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgQ291bnRcbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGFsaWduPSdyaWdodCcgc3g9e3sgZm9udFdlaWdodDogNjAwIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgUGVyY2VudGFnZVxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICA8VGFibGVCb2R5PlxuICAgICAgICAgICAgICAgICAgICB7c3RhdHMuY2F0ZWdvcnlCcmVha2Rvd24uc2xpY2UoMCwgOCkubWFwKGNhdGVnb3J5ID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGVSb3cga2V5PXtjYXRlZ29yeS5uYW1lfSBob3Zlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCb3ggc3g9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAxIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXRlZ29yeUljb24gZm9udFNpemU9J3NtYWxsJyBjb2xvcj0ncHJpbWFyeScgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2F0ZWdvcnkubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgYWxpZ249J3JpZ2h0JyBzeD17eyBmb250V2VpZ2h0OiA1MDAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeS5jb3VudH1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBhbGlnbj0ncmlnaHQnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hpcFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPXtgJHtjYXRlZ29yeS5wZXJjZW50YWdlfSVgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9J3NtYWxsJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yPXtjYXRlZ29yeS5wZXJjZW50YWdlID4gMjAgPyAncHJpbWFyeScgOiAnZGVmYXVsdCd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17Y2F0ZWdvcnkucGVyY2VudGFnZSA+IDIwID8gJ2ZpbGxlZCcgOiAnb3V0bGluZWQnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZVJvdz5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQm9keT5cbiAgICAgICAgICAgICAgICA8L1RhYmxlPlxuICAgICAgICAgICAgICA8L1RhYmxlQ29udGFpbmVyPlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvR3JpZD5cblxuICAgICAgICB7LyogTG9jYXRpb24gQnJlYWtkb3duICovfVxuICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gbWQ9ezZ9PlxuICAgICAgICAgIDxDYXJkXG4gICAgICAgICAgICBzeD17e1xuICAgICAgICAgICAgICAvLyBib3JkZXJSYWRpdXM6IDMsXG4gICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCcsXG4gICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnZGl2aWRlcicsXG4gICAgICAgICAgICAgIGJveFNoYWRvdzogJ25vbmUnXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBzeD17eyBwOiAzIH19PlxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdoNicgZ3V0dGVyQm90dG9tIHN4PXt7IGZvbnRXZWlnaHQ6IDYwMCwgbWI6IDMgfX0+XG4gICAgICAgICAgICAgICAgQXNzZXRzIGJ5IExvY2F0aW9uXG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPFRhYmxlQ29udGFpbmVyPlxuICAgICAgICAgICAgICAgIDxUYWJsZSBzaXplPSdzbWFsbCc+XG4gICAgICAgICAgICAgICAgICA8VGFibGVIZWFkPlxuICAgICAgICAgICAgICAgICAgICA8VGFibGVSb3c+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBzeD17eyBmb250V2VpZ2h0OiA2MDAgfX0+TG9jYXRpb248L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGFsaWduPSdyaWdodCcgc3g9e3sgZm9udFdlaWdodDogNjAwIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgQ291bnRcbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGFsaWduPSdyaWdodCcgc3g9e3sgZm9udFdlaWdodDogNjAwIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgUGVyY2VudGFnZVxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICA8VGFibGVCb2R5PlxuICAgICAgICAgICAgICAgICAgICB7c3RhdHMubG9jYXRpb25CcmVha2Rvd24uc2xpY2UoMCwgOCkubWFwKGxvY2F0aW9uID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGVSb3cga2V5PXtsb2NhdGlvbi5uYW1lfSBob3Zlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCb3ggc3g9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAxIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMb2NhdGlvbkljb24gZm9udFNpemU9J3NtYWxsJyBjb2xvcj0nd2FybmluZycgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bG9jYXRpb24ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgYWxpZ249J3JpZ2h0JyBzeD17eyBmb250V2VpZ2h0OiA1MDAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtsb2NhdGlvbi5jb3VudH1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBhbGlnbj0ncmlnaHQnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hpcFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPXtgJHtsb2NhdGlvbi5wZXJjZW50YWdlfSVgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9J3NtYWxsJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yPXtsb2NhdGlvbi5wZXJjZW50YWdlID4gMTUgPyAnd2FybmluZycgOiAnZGVmYXVsdCd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17bG9jYXRpb24ucGVyY2VudGFnZSA+IDE1ID8gJ2ZpbGxlZCcgOiAnb3V0bGluZWQnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZVJvdz5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQm9keT5cbiAgICAgICAgICAgICAgICA8L1RhYmxlPlxuICAgICAgICAgICAgICA8L1RhYmxlQ29udGFpbmVyPlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvR3JpZD5cblxuICAgICAgICB7LyogQ29uZGl0aW9uIEJyZWFrZG93biAqL31cbiAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IG1kPXs2fT5cbiAgICAgICAgICA8Q2FyZFxuICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgLy8gYm9yZGVyUmFkaXVzOiAzLFxuICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQnLFxuICAgICAgICAgICAgICBib3JkZXJDb2xvcjogJ2RpdmlkZXInLFxuICAgICAgICAgICAgICBib3hTaGFkb3c6ICdub25lJ1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgc3g9e3sgcDogMyB9fT5cbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0naDYnIGd1dHRlckJvdHRvbSBzeD17eyBmb250V2VpZ2h0OiA2MDAsIG1iOiAzIH19PlxuICAgICAgICAgICAgICAgIEFzc2V0IENvbmRpdGlvbiBPdmVydmlld1xuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgIDxCb3ggc3g9e3sgbXQ6IDIgfX0+XG4gICAgICAgICAgICAgICAge3N0YXRzLmNvbmRpdGlvbkJyZWFrZG93bi5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8Qm94IGtleT17aXRlbS5jb25kaXRpb259IHN4PXt7IG1iOiBpbmRleCA9PT0gc3RhdHMuY29uZGl0aW9uQnJlYWtkb3duLmxlbmd0aCAtIDEgPyAwIDogMyB9fT5cbiAgICAgICAgICAgICAgICAgICAgPEJveCBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYmV0d2VlbicsIG1iOiAxIH19PlxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2JvZHkyJyBzeD17eyBmb250V2VpZ2h0OiA1MDAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5jb25kaXRpb259XG4gICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2JvZHkyJyBzeD17eyBmb250V2VpZ2h0OiA2MDAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5jb3VudH0gKHtpdGVtLnBlcmNlbnRhZ2V9JSlcbiAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgICAgIDwvQm94PlxuICAgICAgICAgICAgICAgICAgICA8TGluZWFyUHJvZ3Jlc3NcbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PSdkZXRlcm1pbmF0ZSdcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aXRlbS5wZXJjZW50YWdlfVxuICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XG4gICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IDgsXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IDQsXG4gICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdncmV5LjIwMCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAnJiAuTXVpTGluZWFyUHJvZ3Jlc3MtYmFyJzoge1xuICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IDRcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yPXtcbiAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0uY29uZGl0aW9uLnRvTG93ZXJDYXNlKCkgPT09ICdleGNlbGxlbnQnXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gJ3N1Y2Nlc3MnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogaXRlbS5jb25kaXRpb24udG9Mb3dlckNhc2UoKSA9PT0gJ2dvb2QnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnaW5mbydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGl0ZW0uY29uZGl0aW9uLnRvTG93ZXJDYXNlKCkgPT09ICdmYWlyJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnd2FybmluZydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2Vycm9yJ1xuICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvQm94PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L0JveD5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L0dyaWQ+XG5cbiAgICAgICAgey8qIFJlY2VudCBBc3NldHMgKi99XG4gICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfT5cbiAgICAgICAgICA8Q2FyZFxuICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgLy8gYm9yZGVyUmFkaXVzOiAzLFxuICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQnLFxuICAgICAgICAgICAgICBib3JkZXJDb2xvcjogJ2RpdmlkZXInLFxuICAgICAgICAgICAgICBib3hTaGFkb3c6ICdub25lJ1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgc3g9e3sgcDogMyB9fT5cbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0naDYnIGd1dHRlckJvdHRvbSBzeD17eyBmb250V2VpZ2h0OiA2MDAsIG1iOiAzIH19PlxuICAgICAgICAgICAgICAgIFJlY2VudGx5IEFkZGVkIEFzc2V0c1xuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgIDxUYWJsZUNvbnRhaW5lcj5cbiAgICAgICAgICAgICAgICA8VGFibGU+XG4gICAgICAgICAgICAgICAgICA8VGFibGVIZWFkPlxuICAgICAgICAgICAgICAgICAgICA8VGFibGVSb3c+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBzeD17eyBmb250V2VpZ2h0OiA2MDAgfX0+QXNzZXQgTnVtYmVyPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBzeD17eyBmb250V2VpZ2h0OiA2MDAgfX0+TmFtZTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgc3g9e3sgZm9udFdlaWdodDogNjAwIH19PkNhdGVnb3J5PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBzeD17eyBmb250V2VpZ2h0OiA2MDAgfX0+TG9jYXRpb248L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIHN4PXt7IGZvbnRXZWlnaHQ6IDYwMCB9fT5TdGF0dXM8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIHN4PXt7IGZvbnRXZWlnaHQ6IDYwMCB9fT5Db25kaXRpb248L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIHN4PXt7IGZvbnRXZWlnaHQ6IDYwMCB9fT5Bc3NpZ25lZCBUbzwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgc3g9e3sgZm9udFdlaWdodDogNjAwIH19PkRhdGUgQWRkZWQ8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgPC9UYWJsZVJvdz5cbiAgICAgICAgICAgICAgICAgIDwvVGFibGVIZWFkPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlQm9keT5cbiAgICAgICAgICAgICAgICAgICAge3N0YXRzLnJlY2VudEFzc2V0cy5tYXAoYXNzZXQgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZVJvdyBrZXk9e2Fzc2V0LmlkfSBob3Zlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2JvZHkyJyBmb250V2VpZ2h0PSdtZWRpdW0nIGNvbG9yPSdwcmltYXJ5Lm1haW4nPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHthc3NldC5hc3NldE51bWJlcn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5MicgZm9udFdlaWdodD0nbWVkaXVtJz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXNzZXQubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogMSB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2F0ZWdvcnlJY29uIGZvbnRTaXplPSdzbWFsbCcgY29sb3I9J3ByaW1hcnknIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTInPnthc3NldC5jYXRlZ29yeT8ubmFtZSB8fCAnVW5rbm93bid9PC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JveD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJveCBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6IDEgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExvY2F0aW9uSWNvbiBmb250U2l6ZT0nc21hbGwnIGNvbG9yPSd3YXJuaW5nJyAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2JvZHkyJz57YXNzZXQubG9jYXRpb24/Lm5hbWUgfHwgJ1Vua25vd24nfTwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGlwXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXNzZXQuc3RhdHVzXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gYXNzZXQuc3RhdHVzLnJlcGxhY2UoJ18nLCAnICcpLnJlcGxhY2UoL1xcYlxcdy9nLCBsID0+IGwudG9VcHBlckNhc2UoKSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnVW5rbm93bidcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9e2Fzc2V0LnN0YXR1cyA/IHN0YXR1c0NvbG9yc1thc3NldC5zdGF0dXMgYXMga2V5b2YgdHlwZW9mIHN0YXR1c0NvbG9yc10gOiAnZGVmYXVsdCd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT0nc21hbGwnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD0nZmlsbGVkJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hpcFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFzc2V0LmNvbmRpdGlvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGFzc2V0LmNvbmRpdGlvbi5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIGFzc2V0LmNvbmRpdGlvbi5zbGljZSgxKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdVbmtub3duJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj17XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhc3NldC5jb25kaXRpb25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBjb25kaXRpb25Db2xvcnNbYXNzZXQuY29uZGl0aW9uIGFzIGtleW9mIHR5cGVvZiBjb25kaXRpb25Db2xvcnNdXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2RlZmF1bHQnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9J3NtYWxsJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9J291dGxpbmVkJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogMSB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UGVyc29uSWNvbiBmb250U2l6ZT0nc21hbGwnIGNvbG9yPSdhY3Rpb24nIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTInPnthc3NldC5hc3NpZ25lZFRvPy5mdWxsTmFtZSB8fCAnVW5hc3NpZ25lZCd9PC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JveD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTInIGNvbG9yPSd0ZXh0LnNlY29uZGFyeSc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2Fzc2V0LmNyZWF0ZWRBdCA/IG5ldyBEYXRlKGFzc2V0LmNyZWF0ZWRBdCkudG9Mb2NhbGVEYXRlU3RyaW5nKCkgOiAnVW5rbm93bid9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUJvZHk+XG4gICAgICAgICAgICAgICAgPC9UYWJsZT5cbiAgICAgICAgICAgICAgPC9UYWJsZUNvbnRhaW5lcj5cbiAgICAgICAgICAgICAge3N0YXRzLnJlY2VudEFzc2V0cy5sZW5ndGggPT09IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxCb3ggc3g9e3sgdGV4dEFsaWduOiAnY2VudGVyJywgcHk6IDQgfX0+XG4gICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5MicgY29sb3I9J3RleHQuc2Vjb25kYXJ5Jz5cbiAgICAgICAgICAgICAgICAgICAgTm8gcmVjZW50IGFzc2V0cyBmb3VuZFxuICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgIDwvQm94PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvR3JpZD5cbiAgICAgIDwvR3JpZD5cbiAgICA8L0JveD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiVHlwb2dyYXBoeSIsIkdyaWQiLCJCb3giLCJDaGlwIiwiVGFibGUiLCJUYWJsZUJvZHkiLCJUYWJsZUNlbGwiLCJUYWJsZUNvbnRhaW5lciIsIlRhYmxlSGVhZCIsIlRhYmxlUm93IiwiTGluZWFyUHJvZ3Jlc3MiLCJBdmF0YXIiLCJCdXR0b24iLCJDaXJjdWxhclByb2dyZXNzIiwiQWxlcnQiLCJJbnZlbnRvcnkiLCJJbnZlbnRvcnlJY29uIiwiUGVyc29uIiwiUGVyc29uSWNvbiIsIkNhdGVnb3J5IiwiQ2F0ZWdvcnlJY29uIiwiTG9jYXRpb25PbiIsIkxvY2F0aW9uSWNvbiIsIlJlZnJlc2giLCJSZWZyZXNoSWNvbiIsImFzc2V0c1NlcnZpY2UiLCJjYXRlZ29yaWVzU2VydmljZSIsImxvY2F0aW9uc1NlcnZpY2UiLCJ1c2Vyc1NlcnZpY2UiLCJzdGF0dXNDb2xvcnMiLCJpbl91c2UiLCJpbl9zdG9jayIsIm1haW50ZW5hbmNlIiwicmV0aXJlZCIsImxvc3QiLCJkYW1hZ2VkIiwidW5rbm93biIsImNvbmRpdGlvbkNvbG9ycyIsImV4Y2VsbGVudCIsImdvb2QiLCJmYWlyIiwicG9vciIsIkFzc2V0RGFzaGJvYXJkIiwic3RhdHMiLCJzZXRTdGF0cyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImFzc2V0cyIsInNldEFzc2V0cyIsImNhdGVnb3JpZXMiLCJzZXRDYXRlZ29yaWVzIiwibG9jYXRpb25zIiwic2V0TG9jYXRpb25zIiwidXNlcnMiLCJzZXRVc2VycyIsImxvYWREYXNoYm9hcmREYXRhIiwiYXNzZXRzUmVzcG9uc2UiLCJjYXRlZ29yaWVzRGF0YSIsImxvY2F0aW9uc0RhdGEiLCJ1c2Vyc0RhdGEiLCJQcm9taXNlIiwiYWxsIiwiZ2V0QXNzZXRzIiwibGltaXQiLCJnZXRDYXRlZ29yaWVzIiwiZ2V0TG9jYXRpb25zIiwiZ2V0VXNlcnMiLCJhbGxBc3NldHMiLCJ0b3RhbEFzc2V0cyIsImxlbmd0aCIsInN0YXR1c0NvdW50cyIsInJlZHVjZSIsImFjYyIsImFzc2V0Iiwic3RhdHVzIiwiY29uZGl0aW9uQ291bnRzIiwiY29uZGl0aW9uIiwiY2F0ZWdvcnlDb3VudHMiLCJjYXRlZ29yeU5hbWUiLCJjYXRlZ29yeSIsIm5hbWUiLCJsb2NhdGlvbkNvdW50cyIsImxvY2F0aW9uTmFtZSIsImxvY2F0aW9uIiwiY2F0ZWdvcnlCcmVha2Rvd24iLCJPYmplY3QiLCJlbnRyaWVzIiwibWFwIiwiY291bnQiLCJwZXJjZW50YWdlIiwiTWF0aCIsInJvdW5kIiwic29ydCIsImEiLCJiIiwibG9jYXRpb25CcmVha2Rvd24iLCJzdGF0dXNCcmVha2Rvd24iLCJyZXBsYWNlIiwibCIsInRvVXBwZXJDYXNlIiwiY29uZGl0aW9uQnJlYWtkb3duIiwiY2hhckF0Iiwic2xpY2UiLCJyZWNlbnRBc3NldHMiLCJEYXRlIiwiY3JlYXRlZEF0IiwiZ2V0VGltZSIsImRhc2hib2FyZFN0YXRzIiwiaW5Vc2UiLCJpblN0b2NrIiwidG90YWxDYXRlZ29yaWVzIiwidG90YWxMb2NhdGlvbnMiLCJ0b3RhbFVzZXJzIiwiZXJyIiwiY29uc29sZSIsIlN0YXRDYXJkIiwidGl0bGUiLCJ2YWx1ZSIsImljb24iLCJjb2xvciIsInN1YnRpdGxlIiwic3giLCJiYWNrZ3JvdW5kIiwiYmFja2Ryb3BGaWx0ZXIiLCJib3JkZXIiLCJib3hTaGFkb3ciLCJib3JkZXJDb2xvciIsInAiLCJkaXNwbGF5IiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwiZ3V0dGVyQm90dG9tIiwidmFyaWFudCIsImZvbnRXZWlnaHQiLCJjb21wb25lbnQiLCJtYiIsImZvbnRTaXplIiwiYmdjb2xvciIsIndpZHRoIiwiaGVpZ2h0IiwibWluSGVpZ2h0Iiwic2l6ZSIsInNldmVyaXR5Iiwic3RhcnRJY29uIiwib25DbGljayIsImJhY2tncm91bmRDb2xvciIsImJvcmRlclJhZGl1cyIsInRleHRUcmFuc2Zvcm0iLCJjb250YWluZXIiLCJzcGFjaW5nIiwiaXRlbSIsInhzIiwic20iLCJtZCIsInRvTG9jYWxlU3RyaW5nIiwibXQiLCJpbmRleCIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJhbGlnbiIsImhvdmVyIiwiZ2FwIiwibGFiZWwiLCJhc3NldE51bWJlciIsImFzc2lnbmVkVG8iLCJmdWxsTmFtZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsImlkIiwidGV4dEFsaWduIiwicHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx\n"));

/***/ })

});