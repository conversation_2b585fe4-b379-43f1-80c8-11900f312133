import { Repository, DataSource } from 'typeorm';
import { Asset, AssetStatus, AssetCondition } from '../entities/asset.entity';
import { AssetItem } from '../entities/asset-item.entity';
import { User } from '../entities/user.entity';
export interface CreateAssetItemDto {
    assetId: string;
    status?: AssetStatus;
    condition?: AssetCondition;
    serialNumber?: string;
    purchaseDate?: Date;
    warrantyExpiry?: Date;
    notes?: string;
    assignedToId?: string;
}
export interface UpdateAssetItemDto {
    status?: AssetStatus;
    condition?: AssetCondition;
    serialNumber?: string;
    purchaseDate?: Date;
    warrantyExpiry?: Date;
    notes?: string;
    assignedToId?: string;
}
export interface TransferAssetItemDto {
    assetItemId: string;
    fromStatus: AssetStatus;
    toStatus: AssetStatus;
    reason?: string;
    assignedToId?: string;
}
export declare class AssetItemService {
    private assetRepository;
    private assetItemRepository;
    private userRepository;
    private dataSource;
    constructor(assetRepository: Repository<Asset>, assetItemRepository: Repository<AssetItem>, userRepository: Repository<User>, dataSource: DataSource);
    generateUniqueAssetNumber(assetId: string): Promise<string>;
    createAssetItem(createDto: CreateAssetItemDto, userId: string): Promise<AssetItem>;
    createMultipleAssetItems(assetId: string, quantity: number, userId: string): Promise<AssetItem[]>;
    getAssetItemById(id: string): Promise<AssetItem>;
    getAssetItemsByAssetId(assetId: string): Promise<AssetItem[]>;
    findByAssetNumber(assetNumber: string): Promise<any>;
    getAssetItemsByStatus(status: AssetStatus): Promise<AssetItem[]>;
    updateAssetItem(id: string, updateDto: UpdateAssetItemDto, userId: string): Promise<AssetItem>;
    transferAssetItem(transferDto: TransferAssetItemDto, userId: string): Promise<AssetItem>;
    getAssetQuantitiesByStatus(assetId: string): Promise<Record<AssetStatus, number>>;
    getAllAssetQuantities(): Promise<{
        assetId: string;
        assetName: string;
        quantities: Record<AssetStatus, number>;
        totalItems: number;
    }[]>;
    deleteAssetItem(id: string): Promise<void>;
}
