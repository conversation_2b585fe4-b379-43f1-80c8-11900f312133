import { Asset, Category, Location, User } from '@/types/api'

interface AssetWithDetails extends Asset {
  categoryName?: string
  locationName?: string
  assignedToName?: string
}

export class ReportGenerator {
  private currentDoc: any = null

  // Generate Assets Report
  async generateAssetsReport(
    assets: AssetWithDetails[],
    categories: Category[],
    locations: Location[],
    users: User[]
  ): Promise<void> {
    try {
      console.log('Starting report generation...')

      // Dynamic import for client-side only
      const jsPDF = (await import('jspdf')).default

      const doc = new jsPDF() as any
      console.log('jsPDF initialized successfully')

      // For now, we'll use the manual table creation since autoTable is having issues
      console.log('Using manual table creation for better compatibility')

      // Header
      this.addHeader(doc, 'Assets Management Report')
      console.log('Header added')

      // Summary section
      this.addSummarySection(doc, assets, categories, locations, users)
      console.log('Summary section added')

      // Assets table
      this.addAssetsTable(doc, assets)
      console.log('Assets table added')

      // Footer
      this.addFooter(doc)
      console.log('Footer added')

      // Store doc for saving/printing
      this.currentDoc = doc

      console.log('Report generation completed')
    } catch (error) {
      console.error('Error in generateAssetsReport:', error)
      throw error
    }
  }

  // Generate Asset Label
  async generateAssetLabel(asset: AssetWithDetails): Promise<void> {
    try {
      // Dynamic import for label
      const jsPDF = (await import('jspdf')).default

      // Create a new document for label (smaller size)
      const doc = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: [100, 60] // Label size: 100mm x 60mm
      }) as any

      // Asset name
      doc.setFontSize(14)
      doc.setFont('helvetica', 'bold')
      doc.text(asset.name, 5, 10)

      // Asset number
      doc.setFontSize(12)
      doc.setFont('helvetica', 'normal')
      doc.text(`Asset #: ${asset.assetNumber}`, 5, 18)

      // Category and Location
      doc.setFontSize(10)
      doc.text(`Category: ${asset.categoryName || 'N/A'}`, 5, 26)
      doc.text(`Location: ${asset.locationName || 'N/A'}`, 5, 32)

      // QR Code placeholder (you can integrate with QR code library)
      doc.rect(70, 5, 25, 25)
      doc.setFontSize(8)
      doc.text('QR Code', 75, 20)

      // Serial number if available
      if (asset.serialNumber) {
        doc.setFontSize(9)
        doc.text(`S/N: ${asset.serialNumber}`, 5, 40)
      }

      // Purchase date
      if (asset.purchaseDate) {
        doc.setFontSize(8)
        doc.text(`Purchased: ${new Date(asset.purchaseDate).toLocaleDateString()}`, 5, 48)
      }

      // Company info
      doc.setFontSize(8)
      doc.text('Asset Management System', 5, 55)

      // Store doc for printing
      this.currentDoc = doc
    } catch (error) {
      console.error('Error in generateAssetLabel:', error)
      throw error
    }
  }

  private addHeader(doc: any, title: string): void {
    // Company logo placeholder
    doc.setFillColor(41, 128, 185)
    doc.rect(10, 10, 190, 20, 'F')

    // Title
    doc.setTextColor(255, 255, 255)
    doc.setFontSize(18)
    doc.setFont('helvetica', 'bold')
    doc.text(title, 105, 23, { align: 'center' })

    // Date
    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 170, 25)

    // Reset text color
    doc.setTextColor(0, 0, 0)
  }

  private addSummarySection(
    doc: any,
    assets: AssetWithDetails[],
    categories: Category[],
    locations: Location[],
    users: User[]
  ): void {
    try {
      let yPosition = 45

      doc.setFontSize(14)
      doc.setFont('helvetica', 'bold')
      doc.text('Summary', 10, yPosition)

      yPosition += 10

      // Statistics
      const stats = [
        ['Total Assets', assets.length.toString()],
        ['Categories', categories.length.toString()],
        ['Locations', locations.length.toString()],
        ['Users', users.length.toString()],
        ['Assets In Use', assets.filter(a => a.status === 'in_use').length.toString()],
        ['Assets In Stock', assets.filter(a => a.status === 'in_stock').length.toString()],
        ['Assets Under Maintenance', assets.filter(a => a.status === 'maintenance').length.toString()],
        ['Total Value', `$${assets.reduce((sum, asset) => sum + (asset.totalValue || 0), 0).toLocaleString()}`]
      ]

      console.log('Creating summary table with stats:', stats)

      // Use manual table creation for better compatibility
      this.createSummaryTable(doc, yPosition, stats)

      console.log('Summary table created successfully')
    } catch (error) {
      console.error('Error in addSummarySection:', error)
      throw error
    }
  }

  private addAssetsTable(doc: any, assets: AssetWithDetails[]): void {
    // Enhanced table data with comprehensive asset details
    const tableData = assets.map(asset => [
      asset.assetNumber || 'N/A',
      asset.name || 'N/A',
      asset.categoryName || 'Uncategorized',
      asset.locationName || 'Unassigned',
      asset.status || 'Unknown',
      asset.condition || 'Unknown',
      asset.manufacturer || 'N/A',
      asset.model || 'N/A',
      asset.serialNumber || 'N/A',
      asset.assignedToName || 'Unassigned',
      `$${asset.unitPrice?.toLocaleString() || '0'}`,
      asset.quantity?.toString() || '1',
      `$${asset.totalValue?.toLocaleString() || '0'}`,
      asset.purchaseDate ? new Date(asset.purchaseDate).toLocaleDateString() : 'N/A',
      asset.warrantyExpiry ? new Date(asset.warrantyExpiry).toLocaleDateString() : 'N/A'
    ])

    const headers = [
      'Asset Number',
      'Name',
      'Category',
      'Location',
      'Status',
      'Condition',
      'Manufacturer',
      'Model',
      'Serial Number',
      'Assigned To',
      'Unit Price',
      'Qty',
      'Total Value',
      'Purchase Date',
      'Warranty Expiry'
    ]

    // Get the Y position after the summary table, with fallback
    const startY = (doc.lastAutoTable?.finalY || 120) + 20

    // Add section title
    doc.setFontSize(14)
    doc.setFont('helvetica', 'bold')
    doc.text('Detailed Asset Inventory', 10, startY - 5)

    // Use enhanced table creation for comprehensive details
    this.createEnhancedAssetsTable(doc, startY + 5, headers, tableData)
  }

  private createSummaryTable(doc: any, startY: number, stats: string[][]): void {
    let yPosition = startY
    const lineHeight = 8
    const leftMargin = 10
    const colWidth = 90

    // Draw header
    doc.setFillColor(41, 128, 185)
    doc.setTextColor(255, 255, 255)
    doc.setFontSize(12)
    doc.setFont('helvetica', 'bold')

    doc.rect(leftMargin, yPosition, colWidth, lineHeight, 'F')
    doc.text('Metric', leftMargin + 2, yPosition + 6)
    doc.rect(leftMargin + colWidth, yPosition, 50, lineHeight, 'F')
    doc.text('Value', leftMargin + colWidth + 2, yPosition + 6)

    yPosition += lineHeight
    doc.setTextColor(0, 0, 0)
    doc.setFont('helvetica', 'normal')
    doc.setFontSize(10)

    // Draw data rows
    stats.forEach((row, index) => {
      const fillColor = index % 2 === 0 ? [245, 245, 245] : [255, 255, 255]
      doc.setFillColor(fillColor[0], fillColor[1], fillColor[2])

      doc.rect(leftMargin, yPosition, colWidth, lineHeight, 'F')
      doc.rect(leftMargin + colWidth, yPosition, 50, lineHeight, 'F')

      doc.setTextColor(0, 0, 0)
      doc.text(row[0], leftMargin + 2, yPosition + 6)
      doc.text(row[1], leftMargin + colWidth + 2, yPosition + 6)

      yPosition += lineHeight
    })

    // Store final Y position for next section
    doc.lastAutoTable = { finalY: yPosition }
  }

  private createEnhancedAssetsTable(doc: any, startY: number, headers: string[], data: string[][]): void {
    let yPosition = startY
    const lineHeight = 8
    const leftMargin = 5
    // Enhanced column widths for comprehensive data - optimized for landscape orientation
    const colWidths = [18, 25, 18, 18, 15, 15, 18, 15, 18, 20, 15, 8, 18, 18, 18]
    const pageWidth = 200

    // Check if we need to start on a new page
    if (yPosition > 250) {
      doc.addPage()
      yPosition = 20
    }

    // Draw headers with better styling
    doc.setFillColor(41, 128, 185)
    doc.setTextColor(255, 255, 255)
    doc.setFontSize(7)
    doc.setFont('helvetica', 'bold')

    let xPosition = leftMargin
    headers.forEach((header, index) => {
      if (index < colWidths.length) {
        doc.rect(xPosition, yPosition, colWidths[index], lineHeight, 'F')
        // Truncate header text to fit column
        const headerText = header.length > 10 ? header.substring(0, 8) + '..' : header
        doc.text(headerText, xPosition + 1, yPosition + 5)
        xPosition += colWidths[index]
      }
    })

    yPosition += lineHeight
    doc.setTextColor(0, 0, 0)
    doc.setFont('helvetica', 'normal')
    doc.setFontSize(6)

    // Draw data rows with better formatting
    data.forEach((row, rowIndex) => {
      // Check for page break
      if (yPosition > 270) {
        doc.addPage()
        yPosition = 20

        // Redraw headers on new page
        doc.setFillColor(41, 128, 185)
        doc.setTextColor(255, 255, 255)
        doc.setFontSize(7)
        doc.setFont('helvetica', 'bold')

        xPosition = leftMargin
        headers.forEach((header, index) => {
          if (index < colWidths.length) {
            doc.rect(xPosition, yPosition, colWidths[index], lineHeight, 'F')
            const headerText = header.length > 10 ? header.substring(0, 8) + '..' : header
            doc.text(headerText, xPosition + 1, yPosition + 5)
            xPosition += colWidths[index]
          }
        })

        yPosition += lineHeight
        doc.setTextColor(0, 0, 0)
        doc.setFont('helvetica', 'normal')
        doc.setFontSize(6)
      }

      // Alternating row colors for better readability
      const fillColor = rowIndex % 2 === 0 ? [248, 249, 250] : [255, 255, 255]
      doc.setFillColor(fillColor[0], fillColor[1], fillColor[2])

      xPosition = leftMargin
      row.forEach((cell, cellIndex) => {
        if (cellIndex < colWidths.length) {
          // Draw cell background
          doc.rect(xPosition, yPosition, colWidths[cellIndex], lineHeight, 'F')

          // Draw cell border
          doc.setDrawColor(200, 200, 200)
          doc.rect(xPosition, yPosition, colWidths[cellIndex], lineHeight)

          doc.setTextColor(0, 0, 0)

          // Smart text truncation based on column width
          const maxChars = Math.floor(colWidths[cellIndex] / 1.5)
          let cellText = cell.toString()
          if (cellText.length > maxChars) {
            cellText = cellText.substring(0, maxChars - 2) + '..'
          }

          doc.text(cellText, xPosition + 1, yPosition + 5)
          xPosition += colWidths[cellIndex]
        }
      })
      yPosition += lineHeight
    })

    // Store final Y position for next section
    doc.lastAutoTable = { finalY: yPosition }
  }

  private createAssetsTable(doc: any, startY: number, headers: string[], data: string[][]): void {
    let yPosition = startY
    const lineHeight = 6
    const leftMargin = 10
    const colWidths = [20, 35, 20, 25, 15, 15, 25, 20, 10, 25] // Column widths

    // Draw headers
    doc.setFillColor(41, 128, 185)
    doc.setTextColor(255, 255, 255)
    doc.setFontSize(8)
    doc.setFont('helvetica', 'bold')

    let xPosition = leftMargin
    headers.forEach((header, index) => {
      doc.rect(xPosition, yPosition, colWidths[index], lineHeight, 'F')
      doc.text(header.substring(0, 8), xPosition + 1, yPosition + 4)
      xPosition += colWidths[index]
    })

    yPosition += lineHeight
    doc.setTextColor(0, 0, 0)
    doc.setFont('helvetica', 'normal')
    doc.setFontSize(7)

    // Draw data rows
    data.forEach((row, rowIndex) => {
      if (yPosition > 270) {
        // Start new page if needed
        doc.addPage()
        yPosition = 20

        // Redraw headers on new page
        doc.setFillColor(41, 128, 185)
        doc.setTextColor(255, 255, 255)
        doc.setFontSize(8)
        doc.setFont('helvetica', 'bold')

        xPosition = leftMargin
        headers.forEach((header, index) => {
          doc.rect(xPosition, yPosition, colWidths[index], lineHeight, 'F')
          doc.text(header.substring(0, 8), xPosition + 1, yPosition + 4)
          xPosition += colWidths[index]
        })

        yPosition += lineHeight
        doc.setTextColor(0, 0, 0)
        doc.setFont('helvetica', 'normal')
        doc.setFontSize(7)
      }

      const fillColor = rowIndex % 2 === 0 ? [245, 245, 245] : [255, 255, 255]
      doc.setFillColor(fillColor[0], fillColor[1], fillColor[2])

      xPosition = leftMargin
      row.forEach((cell, cellIndex) => {
        doc.rect(xPosition, yPosition, colWidths[cellIndex], lineHeight, 'F')
        doc.setTextColor(0, 0, 0)
        const cellText = cell.toString().substring(0, Math.floor(colWidths[cellIndex] / 2))
        doc.text(cellText, xPosition + 1, yPosition + 4)
        xPosition += colWidths[cellIndex]
      })
      yPosition += lineHeight
    })

    // Store final Y position for next section
    doc.lastAutoTable = { finalY: yPosition }
  }

  private addFooter(doc: any): void {
    const pageCount = doc.getNumberOfPages()

    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i)

      // Footer line
      doc.setDrawColor(200, 200, 200)
      doc.line(10, 280, 200, 280)

      // Footer text
      doc.setFontSize(8)
      doc.setTextColor(100, 100, 100)
      doc.text('Asset Management System - Confidential', 10, 285)
      doc.text(`Page ${i} of ${pageCount}`, 200, 285, { align: 'right' })
    }
  }

  // Save the PDF
  save(filename: string): void {
    if (!this.currentDoc) throw new Error('PDF not initialized')
    this.currentDoc.save(filename)
  }

  // Get PDF as blob for printing
  getBlob(): Blob {
    if (!this.currentDoc) throw new Error('PDF not initialized')
    return this.currentDoc.output('blob')
  }

  // Open PDF in new window for printing
  openForPrint(): void {
    if (!this.currentDoc) throw new Error('PDF not initialized')
    const pdfBlob = this.getBlob()
    const pdfUrl = URL.createObjectURL(pdfBlob)
    const printWindow = window.open(pdfUrl, '_blank')

    if (printWindow) {
      printWindow.onload = () => {
        printWindow.print()
      }
    }
  }
}
