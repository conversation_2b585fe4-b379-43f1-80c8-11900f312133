import { Model } from 'mongoose';
import { AssetLog, AssetLogDocument, LogAction } from '../schemas/asset-log.schema';
import { Asset } from '../entities/asset.entity';
import { User } from '../entities/user.entity';
export interface CreateLogOptions {
    assetId: string;
    assetNumber: string;
    action: LogAction;
    performedBy: string;
    performedByName: string;
    previousData?: Record<string, any>;
    newData?: Record<string, any>;
    description?: string;
    ipAddress?: string;
    userAgent?: string;
}
export declare class AssetLogsService {
    private assetLogModel;
    constructor(assetLogModel: Model<AssetLogDocument>);
    createLog(options: CreateLogOptions): Promise<AssetLog>;
    logAssetCreated(asset: Asset, user: User, ipAddress?: string, userAgent?: string): Promise<AssetLog>;
    logAssetUpdated(asset: Asset, previousData: Partial<Asset>, user: User, ipAddress?: string, userAgent?: string): Promise<AssetLog>;
    logAssetDeleted(asset: Asset, user: User, ipAddress?: string, userAgent?: string): Promise<AssetLog>;
    logAssetAssigned(asset: Asset, assignedTo: User, performedBy: User, ipAddress?: string, userAgent?: string): Promise<AssetLog>;
    logAssetUnassigned(asset: Asset, previousAssignee: User, performedBy: User, ipAddress?: string, userAgent?: string): Promise<AssetLog>;
    logStatusChange(asset: Asset, previousStatus: string, newStatus: string, user: User, ipAddress?: string, userAgent?: string): Promise<AssetLog>;
    logLocationChange(asset: Asset, previousLocation: string, newLocation: string, user: User, ipAddress?: string, userAgent?: string): Promise<AssetLog>;
    getAssetLogs(assetId: string, limit?: number, skip?: number): Promise<AssetLog[]>;
    getUserActivityLogs(userId: string, limit?: number, skip?: number): Promise<AssetLog[]>;
    getRecentLogs(limit?: number): Promise<AssetLog[]>;
    getLogsByDateRange(startDate: Date, endDate: Date, limit?: number): Promise<AssetLog[]>;
    getLogsByAction(action: LogAction, limit?: number): Promise<AssetLog[]>;
    private sanitizeAssetData;
}
