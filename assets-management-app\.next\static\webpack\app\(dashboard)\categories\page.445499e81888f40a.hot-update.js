"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/categories/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/categories/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/(dashboard)/categories/page.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CategoriesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControlLabel/FormControlLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Switch/Switch.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Folder.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Category.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Edit.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Delete.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ChevronRight.js\");\n/* harmony import */ var _mui_x_tree_view_SimpleTreeView__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/x-tree-view/SimpleTreeView */ \"(app-pages-browser)/./node_modules/@mui/x-tree-view/SimpleTreeView/SimpleTreeView.js\");\n/* harmony import */ var _mui_x_tree_view_TreeItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/x-tree-view/TreeItem */ \"(app-pages-browser)/./node_modules/@mui/x-tree-view/TreeItem/TreeItem.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Mock data for development (fallback)\nconst mockCategories = [\n    {\n        id: \"1\",\n        name: \"IT Equipment\",\n        code: \"IT\",\n        description: \"All IT related equipment and devices\",\n        isActive: true,\n        parentId: null,\n        children: [\n            {\n                id: \"2\",\n                name: \"Computers\",\n                code: \"COMP\",\n                description: \"Desktop and laptop computers\",\n                isActive: true,\n                parentId: \"1\",\n                children: []\n            },\n            {\n                id: \"3\",\n                name: \"Peripherals\",\n                code: \"PERI\",\n                description: \"Keyboards, mice, monitors, etc.\",\n                isActive: true,\n                parentId: \"1\",\n                children: []\n            }\n        ]\n    },\n    {\n        id: \"4\",\n        name: \"Furniture\",\n        code: \"FURN\",\n        description: \"Office furniture and fixtures\",\n        isActive: true,\n        parentId: null,\n        children: [\n            {\n                id: \"5\",\n                name: \"Chairs\",\n                code: \"CHAIR\",\n                description: \"Office chairs and seating\",\n                isActive: true,\n                parentId: \"4\",\n                children: []\n            },\n            {\n                id: \"6\",\n                name: \"Desks\",\n                code: \"DESK\",\n                description: \"Office desks and tables\",\n                isActive: true,\n                parentId: \"4\",\n                children: []\n            }\n        ]\n    },\n    {\n        id: \"7\",\n        name: \"Vehicles\",\n        code: \"VEH\",\n        description: \"Company vehicles\",\n        isActive: true,\n        parentId: null,\n        children: []\n    }\n];\nfunction CategoriesPage() {\n    var _this = this;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dialogOpen, setDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"tree\");\n    // Load categories from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadCategories = async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Use hierarchy endpoint for tree view to avoid duplicate IDs\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.getCategoryHierarchy();\n                setCategories(data);\n            } catch (err) {\n                var _err_message, _err_message1;\n                console.error(\"Failed to load categories:\", err);\n                if (((_err_message = err.message) === null || _err_message === void 0 ? void 0 : _err_message.includes(\"401\")) || ((_err_message1 = err.message) === null || _err_message1 === void 0 ? void 0 : _err_message1.includes(\"Unauthorized\"))) {\n                    setError(\"Authentication failed. Please log in again.\");\n                    setTimeout(()=>{\n                        window.location.href = \"/login\";\n                    }, 2000);\n                } else {\n                    setError(\"Failed to load categories. Please check if the backend server is running and try again.\");\n                }\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadCategories();\n    }, []);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        code: \"\",\n        description: \"\",\n        parentId: \"\",\n        isActive: true\n    });\n    const handleAddCategory = ()=>{\n        setFormData({\n            name: \"\",\n            code: \"\",\n            description: \"\",\n            parentId: \"\",\n            isActive: true\n        });\n        setIsEditing(false);\n        setDialogOpen(true);\n    };\n    const handleEditCategory = (category)=>{\n        setFormData({\n            name: category.name,\n            code: category.code,\n            description: category.description,\n            parentId: category.parentId || \"\",\n            isActive: category.isActive\n        });\n        setSelectedCategory(category);\n        setIsEditing(true);\n        setDialogOpen(true);\n    };\n    const handleDeleteCategory = (category)=>{\n        setSelectedCategory(category);\n        setDeleteDialogOpen(true);\n    };\n    const refreshCategories = async ()=>{\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.getCategoryHierarchy();\n            setCategories(data);\n        } catch (err) {\n            console.error(\"Failed to refresh categories:\", err);\n            setError(\"Failed to refresh categories. Please try again.\");\n        }\n    };\n    const handleSaveCategory = async ()=>{\n        try {\n            if (isEditing && selectedCategory) {\n                // Update existing category\n                await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.updateCategory(selectedCategory.id, {\n                    name: formData.name,\n                    code: formData.code,\n                    description: formData.description,\n                    parentId: formData.parentId || null,\n                    isActive: formData.isActive\n                });\n            } else {\n                // Create new category\n                await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.createCategory({\n                    name: formData.name,\n                    code: formData.code,\n                    description: formData.description,\n                    parentId: formData.parentId || null,\n                    isActive: formData.isActive\n                });\n            }\n            // Refresh the entire hierarchy\n            await refreshCategories();\n            setDialogOpen(false);\n            setSelectedCategory(null);\n        } catch (err) {\n            console.error(\"Failed to save category:\", err);\n            setError(\"Failed to save category. Please try again.\");\n        }\n    };\n    const confirmDelete = async ()=>{\n        if (!selectedCategory) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.deleteCategory(selectedCategory.id);\n            // Refresh the entire hierarchy\n            await refreshCategories();\n            setDeleteDialogOpen(false);\n            setSelectedCategory(null);\n        } catch (err) {\n            console.error(\"Failed to delete category:\", err);\n            setError(\"Failed to delete category. Please try again.\");\n        }\n    };\n    const getAllCategories = (cats)=>{\n        let result = [];\n        cats.forEach((cat)=>{\n            result.push(cat);\n            if (cat.children && cat.children.length > 0) {\n                result = result.concat(getAllCategories(cat.children));\n            }\n        });\n        return result;\n    };\n    // Helper function to build full path for a category\n    const getCategoryPath = (category, allCats)=>{\n        if (!category.parentId) {\n            return category.name;\n        }\n        const parent = allCats.find((cat)=>cat.id === category.parentId);\n        if (parent) {\n            return \"\".concat(getCategoryPath(parent, allCats), \" > \").concat(category.name);\n        }\n        return category.name;\n    };\n    const renderTreeItem = function(category) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_tree_view_TreeItem__WEBPACK_IMPORTED_MODULE_3__.TreeItem, {\n            itemId: category.id,\n            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    py: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            flex: 1\n                        },\n                        children: [\n                            category.children && category.children.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                sx: {\n                                    mr: 1,\n                                    color: \"primary.main\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 15\n                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                sx: {\n                                    mr: 1,\n                                    color: \"text.secondary\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 15\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                variant: \"body1\",\n                                sx: {\n                                    mr: 2,\n                                    fontWeight: level === 0 ? 600 : 400\n                                },\n                                children: category.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                label: category.code,\n                                size: \"small\",\n                                variant: \"outlined\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, void 0),\n                            level > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                label: \"Level \".concat(level + 1),\n                                size: \"small\",\n                                variant: \"outlined\",\n                                color: \"info\",\n                                sx: {\n                                    mr: 1,\n                                    fontSize: \"0.7rem\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 15\n                            }, void 0),\n                            !category.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                label: \"Inactive\",\n                                size: \"small\",\n                                color: \"error\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 36\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            gap: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                title: \"Edit\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    size: \"small\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleEditCategory(category);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        fontSize: \"small\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                title: \"Delete\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    size: \"small\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleDeleteCategory(category);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        fontSize: \"small\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 17\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, void 0),\n            children: category.children && category.children.map((child)=>renderTreeItem(child, level + 1))\n        }, category.id, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n            lineNumber: 275,\n            columnNumber: 5\n        }, _this);\n    };\n    const flatCategories = getAllCategories(categories);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            sx: {\n                p: 3,\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"400px\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    sx: {\n                        ml: 2\n                    },\n                    children: \"Loading categories...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n            lineNumber: 336,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        sx: {\n            p: 4,\n            backgroundColor: \"grey.50\",\n            minHeight: \"100vh\"\n        },\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mb: 3\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 347,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    mb: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"h4\",\n                        component: \"h1\",\n                        children: \"Category Management\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            gap: 2,\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    checked: viewMode === \"tree\",\n                                    onChange: (e)=>setViewMode(e.target.checked ? \"tree\" : \"table\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, void 0),\n                                label: \"Tree View\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                variant: \"contained\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 50\n                                }, void 0),\n                                onClick: handleAddCategory,\n                                color: \"primary\",\n                                children: \"Add Category\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 353,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                sx: {\n                    border: \"1px solid\",\n                    borderColor: \"divider\",\n                    boxShadow: \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    children: viewMode === \"tree\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_tree_view_SimpleTreeView__WEBPACK_IMPORTED_MODULE_21__.SimpleTreeView, {\n                        defaultCollapseIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 36\n                        }, void 0),\n                        defaultExpandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 34\n                        }, void 0),\n                        defaultExpandedItems: getAllCategories(categories).map((cat)=>cat.id),\n                        children: categories.map((category)=>renderTreeItem(category, 0))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                children: \"Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                children: \"Parent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                align: \"center\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    children: flatCategories.map((category)=>{\n                                        var _flatCategories_find;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            hover: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\"\n                                                        },\n                                                        children: [\n                                                            category.children && category.children.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                sx: {\n                                                                    mr: 1\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                sx: {\n                                                                    mr: 1\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            category.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        label: category.code,\n                                                        size: \"small\",\n                                                        variant: \"outlined\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    children: category.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    children: category.parentId ? ((_flatCategories_find = flatCategories.find((c)=>c.id === category.parentId)) === null || _flatCategories_find === void 0 ? void 0 : _flatCategories_find.name) || \"-\" : \"Root\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        label: category.isActive ? \"Active\" : \"Inactive\",\n                                                        color: category.isActive ? \"success\" : \"error\",\n                                                        size: \"small\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    align: \"center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"center\",\n                                                            gap: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                title: \"Edit\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    size: \"small\",\n                                                                    onClick: ()=>handleEditCategory(category),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                title: \"Delete\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    size: \"small\",\n                                                                    onClick: ()=>handleDeleteCategory(category),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                        lineNumber: 436,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, category.id, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 371,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                open: dialogOpen,\n                onClose: ()=>setDialogOpen(false),\n                maxWidth: \"sm\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                        children: isEditing ? \"Edit Category\" : \"Add New Category\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                        fullWidth: true,\n                                        label: \"Category Name\",\n                                        value: formData.name,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                        fullWidth: true,\n                                        label: \"Code\",\n                                        value: formData.code,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    code: e.target.value.toUpperCase()\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                        fullWidth: true,\n                                        label: \"Description\",\n                                        value: formData.description,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                })),\n                                        multiline: true,\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                        fullWidth: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                children: \"Parent Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                value: formData.parentId,\n                                                label: \"Parent Category\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            parentId: e.target.value\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                        value: \"\",\n                                                        children: \"None (Root Category)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    flatCategories.filter((cat)=>!isEditing || cat.id !== (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id)).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                            value: category.id,\n                                                            children: [\n                                                                getCategoryPath(category, flatCategories),\n                                                                \" (\",\n                                                                category.code,\n                                                                \")\"\n                                                            ]\n                                                        }, category.id, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            checked: formData.isActive,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        isActive: e.target.checked\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        label: \"Active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                onClick: ()=>setDialogOpen(false),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                onClick: handleSaveCategory,\n                                variant: \"contained\",\n                                children: isEditing ? \"Update\" : \"Create\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 515,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 451,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                open: deleteDialogOpen,\n                onClose: ()=>setDeleteDialogOpen(false),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                        children: \"Confirm Delete\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 525,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: [\n                                'Are you sure you want to delete category \"',\n                                selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.name,\n                                '\"?',\n                                (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.children) && selectedCategory.children.length > 0 ? \" This will also delete all subcategories.\" : \"\",\n                                \"This action cannot be undone.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                onClick: ()=>setDeleteDialogOpen(false),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                onClick: confirmDelete,\n                                color: \"error\",\n                                variant: \"contained\",\n                                children: \"Delete\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 535,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 524,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n        lineNumber: 344,\n        columnNumber: 5\n    }, this);\n}\n_s(CategoriesPage, \"mNpKX9zesD67Le7y4yKTOAVbaKM=\");\n_c = CategoriesPage;\nvar _c;\n$RefreshReg$(_c, \"CategoriesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/categories/page.tsx\n"));

/***/ })

});