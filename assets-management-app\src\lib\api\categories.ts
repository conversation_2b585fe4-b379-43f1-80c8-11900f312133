import { apiClient } from './client';
import { API_CONFIG } from './config';
import {
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
} from '@/types/api';

export class CategoriesService {
  async getCategories(): Promise<Category[]> {
    return apiClient.get<Category[]>(API_CONFIG.ENDPOINTS.CATEGORIES.BASE);
  }

  async getActiveCategories(): Promise<Category[]> {
    return apiClient.get<Category[]>(API_CONFIG.ENDPOINTS.CATEGORIES.ACTIVE);
  }

  async getCategoryHierarchy(): Promise<Category[]> {
    return apiClient.get<Category[]>(API_CONFIG.ENDPOINTS.CATEGORIES.HIERARCHY);
  }

  async getCategoryById(id: string): Promise<Category> {
    return apiClient.get<Category>(API_CONFIG.ENDPOINTS.CATEGORIES.BY_ID(id));
  }

  async createCategory(data: CreateCategoryRequest): Promise<Category> {
    return apiClient.post<Category>(API_CONFIG.ENDPOINTS.CATEGORIES.BASE, data);
  }

  async updateCategory(id: string, data: UpdateCategoryRequest): Promise<Category> {
    return apiClient.patch<Category>(
      API_CONFIG.ENDPOINTS.CATEGORIES.BY_ID(id),
      data
    );
  }

  async deleteCategory(id: string): Promise<void> {
    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.CATEGORIES.BY_ID(id));
  }

  // Helper method to get categories as options for forms
  async getCategoryOptions(): Promise<Array<{ value: string; label: string }>> {
    const categories = await this.getActiveCategories();
    return categories.map(category => ({
      value: category.id,
      label: category.fullPath || category.name,
    }));
  }

  // Get root categories (no parent)
  async getRootCategories(): Promise<Category[]> {
    const categories = await this.getCategories();
    return categories.filter(category => !category.parentId);
  }

  // Get subcategories of a parent category
  async getSubcategories(parentId: string): Promise<Category[]> {
    const categories = await this.getCategories();
    return categories.filter(category => category.parentId === parentId);
  }
}

export const categoriesService = new CategoriesService();
