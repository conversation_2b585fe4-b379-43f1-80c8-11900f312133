import { <PERSON><PERSON><PERSON>, <PERSON>Int, IsOptional, IsString, IsUUID, Min, IsArray, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { AssetStatus } from '../../entities/asset.entity';
import { TransferType } from '../../entities/quantity-transfer.entity';

export class TransferQuantityDto {
  @ApiProperty({
    description: 'Asset ID to transfer quantities for',
    example: 'uuid-string',
  })
  @IsUUID()
  assetId: string;

  @ApiProperty({
    description: 'Source status to transfer from',
    enum: AssetStatus,
    example: AssetStatus.IN_STOCK,
  })
  @IsEnum(AssetStatus)
  fromStatus: AssetStatus;

  @ApiProperty({
    description: 'Destination status to transfer to',
    enum: AssetStatus,
    example: AssetStatus.IN_USE,
  })
  @IsEnum(AssetStatus)
  toStatus: AssetStatus;

  @ApiProperty({
    description: 'Quantity to transfer',
    example: 5,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  quantity: number;

  @ApiPropertyOptional({
    description: 'Reason for the transfer',
    example: 'Moving assets to production environment',
  })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiPropertyOptional({
    description: 'Additional notes',
    example: 'Transferred for Q1 deployment',
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Type of transfer',
    enum: TransferType,
    default: TransferType.MANUAL,
  })
  @IsOptional()
  @IsEnum(TransferType)
  transferType?: TransferType;
}

export class BulkTransferQuantityDto {
  @ApiProperty({
    description: 'Array of transfer operations',
    type: [TransferQuantityDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TransferQuantityDto)
  transfers: TransferQuantityDto[];

  @ApiPropertyOptional({
    description: 'Batch reason for all transfers',
    example: 'Monthly inventory redistribution',
  })
  @IsOptional()
  @IsString()
  batchReason?: string;

  @ApiPropertyOptional({
    description: 'Batch notes',
    example: 'Bulk transfer for Q1 2024',
  })
  @IsOptional()
  @IsString()
  batchNotes?: string;
}

export class SetAssetQuantityDto {
  @ApiProperty({
    description: 'Asset ID to set quantities for',
    example: 'uuid-string',
  })
  @IsUUID()
  assetId: string;

  @ApiProperty({
    description: 'Quantities by status',
    example: {
      in_stock: 10,
      in_use: 5,
      maintenance: 2,
    },
  })
  quantities: Record<AssetStatus, number>;

  @ApiPropertyOptional({
    description: 'Reason for setting quantities',
    example: 'Initial stock setup',
  })
  @IsOptional()
  @IsString()
  reason?: string;
}
