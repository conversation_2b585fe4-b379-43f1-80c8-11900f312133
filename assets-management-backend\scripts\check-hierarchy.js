const axios = require('axios');

const API_BASE = 'http://localhost:3001';

// First, login to get a token
async function login() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    return response.data.token;
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
    throw error;
  }
}

async function checkHierarchy() {
  try {
    console.log('🔐 Logging in...');
    const token = await login();
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    console.log('📋 Fetching category hierarchy...');
    
    // Get category hierarchy
    const hierarchyResponse = await axios.get(`${API_BASE}/categories/hierarchy`, { headers });
    const hierarchy = hierarchyResponse.data;
    
    console.log('Category Hierarchy:');
    
    function printCategory(category, indent = '') {
      console.log(`${indent}- ${category.name} (Code: ${category.code || 'No code'}) [ID: ${category.id}]`);
      if (category.children && category.children.length > 0) {
        category.children.forEach(child => {
          printCategory(child, indent + '  ');
        });
      }
    }
    
    hierarchy.forEach(rootCategory => {
      printCategory(rootCategory);
    });

    // Find the "Business Laptops" category and trace its parent chain
    console.log('\n🔍 Finding "Business Laptops" category...');
    
    const categoriesResponse = await axios.get(`${API_BASE}/categories`, { headers });
    const categories = categoriesResponse.data;
    
    const businessLaptops = categories.find(cat => cat.name === 'Business Laptops');
    
    if (businessLaptops) {
      console.log(`Found: ${businessLaptops.name} (Code: ${businessLaptops.code})`);
      console.log(`Parent ID: ${businessLaptops.parentId || 'None'}`);
      
      if (businessLaptops.parentId) {
        const parent = categories.find(cat => cat.id === businessLaptops.parentId);
        if (parent) {
          console.log(`Parent: ${parent.name} (Code: ${parent.code || 'No code'})`);
          
          // Check if parent has a parent
          if (parent.parentId) {
            const grandParent = categories.find(cat => cat.id === parent.parentId);
            if (grandParent) {
              console.log(`Grand Parent: ${grandParent.name} (Code: ${grandParent.code || 'No code'})`);
            }
          }
        }
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

checkHierarchy();
