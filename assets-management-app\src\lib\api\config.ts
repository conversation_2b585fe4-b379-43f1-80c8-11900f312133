export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
  TIMEOUT: 10000,
  ENDPOINTS: {
    // Auth
    AUTH: {
      LOGIN: '/auth/login',
      REGISTER: '/auth/register',
      PROFILE: '/auth/profile',
      VERIFY_TOKEN: '/auth/verify-token'
    },
    // Users
    USERS: {
      BASE: '/users',
      BY_ID: (id: string) => `/users/${id}`,
      CHANGE_PASSWORD: (id: string) => `/users/${id}/change-password`
    },
    // Assets
    ASSETS: {
      BASE: '/assets',
      BY_ID: (id: string) => `/assets/${id}`,
      BY_ASSET_NUMBER: (assetNumber: string) => `/assets/asset-number/${assetNumber}`,
      ASSIGN: (id: string) => `/assets/${id}/assign`,
      UNASSIGN: (id: string) => `/assets/${id}/unassign`,
      STATS: '/assets/stats'
    },
    // Categories
    CATEGORIES: {
      BASE: '/categories',
      BY_ID: (id: string) => `/categories/${id}`,
      ACTIVE: '/categories/active',
      HIERARCHY: '/categories/hierarchy'
    },
    // Locations
    LOCATIONS: {
      BASE: '/locations',
      BY_ID: (id: string) => `/locations/${id}`,
      ACTIVE: '/locations/active',
      HIERARCHY: '/locations/hierarchy'
    },
    // Asset Logs
    ASSET_LOGS: {
      RECENT: '/asset-logs/recent',
      BY_ASSET: (assetId: string) => `/asset-logs/asset/${assetId}`,
      BY_USER: (userId: string) => `/asset-logs/user/${userId}`,
      BY_ACTION: (action: string) => `/asset-logs/action/${action}`,
      DATE_RANGE: '/asset-logs/date-range'
    },
    // Audit Logs
    AUDIT_LOGS: {
      BASE: '/audit-logs',
      BY_ID: (id: string) => `/audit-logs/${id}`,
      STATS: '/audit-logs/stats',
      ENTITY: (entityType: string, entityId: string) => `/audit-logs/entity/${entityType}/${entityId}`,
      USER: (userId: string) => `/audit-logs/user/${userId}`,
      MY_ACTIVITY: '/audit-logs/my-activity'
    },
    // Reports
    REPORTS: {
      ASSETS: '/reports/assets',
      UTILIZATION: '/reports/utilization',
      MAINTENANCE: '/reports/maintenance',
      ACTIVITY: '/reports/activity',
      SUMMARY: '/reports/summary'
    }
  }
} as const

export const getAuthToken = (): string | null => {
  if (typeof window === 'undefined') return null
  return localStorage.getItem('auth_token')
}

export const setAuthToken = (token: string): void => {
  if (typeof window === 'undefined') return
  localStorage.setItem('auth_token', token)
}

export const removeAuthToken = (): void => {
  if (typeof window === 'undefined') return
  localStorage.removeItem('auth_token')
}

export const getAuthHeaders = (): Record<string, string> => {
  const token = getAuthToken()
  console.log('getAuthHeaders: Token retrieved:', !!token)
  console.log('getAuthHeaders: Token value:', token ? `${token.substring(0, 20)}...` : 'null')
  const headers = {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` })
  }
  console.log('getAuthHeaders: Headers created:', Object.keys(headers))
  return headers
}
