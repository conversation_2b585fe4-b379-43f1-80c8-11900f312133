const axios = require('axios');

const API_BASE = 'http://localhost:3001';

// Create admin user
async function createAdmin() {
  try {
    console.log('👤 Creating admin user...');
    
    const response = await axios.post(`${API_BASE}/auth/register`, {
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'admin'
    });
    
    console.log('✅ Admin user created successfully!');
    console.log('🔐 Login credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');
    
  } catch (error) {
    if (error.response?.status === 409) {
      console.log('ℹ️  Admin user already exists');
    } else {
      console.error('❌ Error creating admin user:', error.response?.data || error.message);
    }
  }
}

createAdmin();
