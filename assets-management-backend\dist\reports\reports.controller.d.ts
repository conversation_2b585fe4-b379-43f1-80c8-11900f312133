import { ReportsService } from './reports.service';
import { AuditLogsService } from '../audit-logs/audit-logs.service';
export declare class ReportsController {
    private readonly reportsService;
    private readonly auditLogsService;
    constructor(reportsService: ReportsService, auditLogsService: AuditLogsService);
    generateAssetReport(query: any, req: any): Promise<import("./reports.service").AssetReport>;
    generateUtilizationReport(req: any): Promise<import("./reports.service").UtilizationReport>;
    generateMaintenanceReport(req: any): Promise<import("./reports.service").MaintenanceReport>;
    generateActivityReport(query: any, req: any): Promise<import("./reports.service").ActivityReport>;
    generateSummaryReport(req: any): Promise<{
        assets: import("./reports.service").AssetReport;
        utilization: import("./reports.service").UtilizationReport;
        maintenance: import("./reports.service").MaintenanceReport;
        activity: import("./reports.service").ActivityReport;
        generatedAt: Date;
    }>;
}
