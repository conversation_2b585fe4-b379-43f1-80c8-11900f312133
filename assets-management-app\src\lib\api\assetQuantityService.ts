import { apiClient } from './client'
import { AssetStatus } from '@/types/api'

export interface TransferQuantityRequest {
  assetId: string
  fromStatus: AssetStatus
  toStatus: AssetStatus
  quantity: number
  reason?: string
  notes?: string
}

export interface BulkTransferQuantityRequest {
  transfers: TransferQuantityRequest[]
  batchReason?: string
  batchNotes?: string
}

export interface SetAssetQuantityRequest {
  assetId: string
  quantities: Record<AssetStatus, number>
  reason?: string
}

export interface AssetQuantityResponse {
  [key: string]: number
}

export interface TransferResponse {
  success: boolean
  transfer: {
    id: string
    assetId: string
    fromStatus: AssetStatus | null
    toStatus: AssetStatus
    quantity: number
    reason?: string
    notes?: string
    transferredAt: string
    transferredBy: {
      id: string
      fullName: string
    }
  }
  quantities: AssetQuantityResponse
}

export interface BulkTransferResponse {
  success: boolean
  transfers: TransferResponse['transfer'][]
  batchId: string
}

export interface TransferHistoryItem {
  id: string
  assetId: string
  fromStatus: AssetStatus | null
  toStatus: AssetStatus
  quantity: number
  transferType: string
  reason?: string
  notes?: string
  transferredAt: string
  transferredBy: {
    id: string
    fullName: string
  }
  batchId?: string
}

export interface AssetQuantityOverview {
  assetId: string
  assetName: string
  quantities: AssetQuantityResponse
}

export const assetQuantityService = {
  // Get asset quantities by status
  async getAssetQuantities(assetId: string): Promise<AssetQuantityResponse> {
    const response = await apiClient.get(`/assets/${assetId}/quantities`)
    return response.data
  },

  // Transfer quantities between statuses
  async transferQuantity(data: TransferQuantityRequest): Promise<TransferResponse> {
    const response = await apiClient.post('/assets/quantities/transfer', data)
    return response.data
  },

  // Bulk transfer quantities
  async bulkTransferQuantity(data: BulkTransferQuantityRequest): Promise<BulkTransferResponse> {
    const response = await apiClient.post('/assets/quantities/bulk-transfer', data)
    return response.data
  },

  // Set asset quantities by status
  async setAssetQuantities(data: SetAssetQuantityRequest): Promise<{ success: boolean; quantities: AssetQuantityResponse }> {
    const response = await apiClient.post('/assets/quantities/set', data)
    return response.data
  },

  // Get transfer history for an asset
  async getTransferHistory(assetId: string): Promise<TransferHistoryItem[]> {
    const response = await apiClient.get(`/assets/${assetId}/transfer-history`)
    return response.data
  },

  // Get all asset quantities overview
  async getAllAssetQuantities(): Promise<AssetQuantityOverview[]> {
    const response = await apiClient.get('/assets/quantities/all')
    return response.data
  }
}
