import { AssetLogsService } from './asset-logs.service';
import { LogAction } from '../schemas/asset-log.schema';
export declare class AssetLogsController {
    private readonly assetLogsService;
    constructor(assetLogsService: AssetLogsService);
    getRecentLogs(limit?: number): Promise<import("../schemas/asset-log.schema").AssetLog[]>;
    getAssetLogs(assetId: string, limit?: number, skip?: number): Promise<import("../schemas/asset-log.schema").AssetLog[]>;
    getUserActivityLogs(userId: string, limit?: number, skip?: number): Promise<import("../schemas/asset-log.schema").AssetLog[]>;
    getLogsByAction(action: LogAction, limit?: number): Promise<import("../schemas/asset-log.schema").AssetLog[]>;
    getLogsByDateRange(startDate: string, endDate: string, limit?: number): Promise<import("../schemas/asset-log.schema").AssetLog[]>;
}
