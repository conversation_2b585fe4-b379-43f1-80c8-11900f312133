const axios = require('axios');

const API_BASE = 'http://localhost:3001';

// First, login to get a token
async function login() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123',
    });
    return response.data.token;
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
    throw error;
  }
}

async function checkExistingAssets() {
  try {
    console.log('🔐 Logging in...');
    const token = await login();

    const headers = {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    };

    console.log('📋 Fetching existing assets...');

    const assetsResponse = await axios.get(`${API_BASE}/assets`, { headers });
    const assets =
      assetsResponse.data.assets ||
      assetsResponse.data.data ||
      assetsResponse.data;

    console.log(`Found ${assets?.length || 0} existing assets:`);

    if (!assets || !Array.isArray(assets)) {
      console.log('No assets found or invalid response format');
      console.log('Response:', assetsResponse.data);
      return;
    }

    assets.forEach((asset, index) => {
      console.log(`${index + 1}. ${asset.name}`);
      console.log(`   Asset Number: ${asset.assetNumber}`);
      console.log(`   Category: ${asset.category?.name || 'Unknown'}`);
      console.log(`   Location: ${asset.location?.name || 'Unknown'}`);
      console.log(`   Status: ${asset.status}`);
      console.log('');
    });

    // Check if any assets have the old format vs new format
    const oldFormatAssets = assets.filter(
      (asset) => !asset.assetNumber.includes('-'),
    );
    const newFormatAssets = assets.filter(
      (asset) =>
        asset.assetNumber.includes('-') &&
        asset.assetNumber.split('-').length === 3,
    );

    console.log(`📊 Asset Number Format Analysis:`);
    console.log(`   Old format (no dashes): ${oldFormatAssets.length}`);
    console.log(`   New format (LOC-CAT-XXXX): ${newFormatAssets.length}`);
    console.log(
      `   Other formats: ${assets.length - oldFormatAssets.length - newFormatAssets.length}`,
    );

    if (newFormatAssets.length > 0) {
      console.log('\n✅ New format examples:');
      newFormatAssets.slice(0, 5).forEach((asset) => {
        console.log(`   ${asset.assetNumber} - ${asset.name}`);
      });
    }
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

checkExistingAssets();
