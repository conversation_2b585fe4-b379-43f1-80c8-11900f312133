import { Asset } from './asset.entity';
export declare enum LocationType {
    REGION = "region",
    BUILDING = "building",
    FLOOR = "floor",
    ROOM = "room",
    AREA = "area"
}
export declare class Location {
    id: string;
    name: string;
    description: string;
    type: LocationType;
    address: string;
    coordinates: string;
    isActive: boolean;
    parent: Location;
    parentId: string;
    children: Location[];
    assets: Asset[];
    createdAt: Date;
    updatedAt: Date;
    get fullPath(): string;
}
