"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetLogsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const asset_logs_service_1 = require("./asset-logs.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const user_entity_1 = require("../entities/user.entity");
const asset_log_schema_1 = require("../schemas/asset-log.schema");
let AssetLogsController = class AssetLogsController {
    assetLogsService;
    constructor(assetLogsService) {
        this.assetLogsService = assetLogsService;
    }
    getRecentLogs(limit) {
        return this.assetLogsService.getRecentLogs(limit);
    }
    getAssetLogs(assetId, limit, skip) {
        return this.assetLogsService.getAssetLogs(assetId, limit, skip);
    }
    getUserActivityLogs(userId, limit, skip) {
        return this.assetLogsService.getUserActivityLogs(userId, limit, skip);
    }
    getLogsByAction(action, limit) {
        return this.assetLogsService.getLogsByAction(action, limit);
    }
    getLogsByDateRange(startDate, endDate, limit) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        return this.assetLogsService.getLogsByDateRange(start, end, limit);
    }
};
exports.AssetLogsController = AssetLogsController;
__decorate([
    (0, common_1.Get)('recent'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({ summary: 'Get recent asset logs (Admin/Manager only)' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Number of logs to retrieve (default: 100)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Recent logs retrieved successfully',
    }),
    __param(0, (0, common_1.Query)('limit', new common_1.ParseIntPipe({ optional: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], AssetLogsController.prototype, "getRecentLogs", null);
__decorate([
    (0, common_1.Get)('asset/:assetId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get logs for a specific asset' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Number of logs to retrieve (default: 50)' }),
    (0, swagger_1.ApiQuery)({ name: 'skip', required: false, type: Number, description: 'Number of logs to skip (default: 0)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset logs retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('assetId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('limit', new common_1.ParseIntPipe({ optional: true }))),
    __param(2, (0, common_1.Query)('skip', new common_1.ParseIntPipe({ optional: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", void 0)
], AssetLogsController.prototype, "getAssetLogs", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({ summary: 'Get activity logs for a specific user (Admin/Manager only)' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Number of logs to retrieve (default: 50)' }),
    (0, swagger_1.ApiQuery)({ name: 'skip', required: false, type: Number, description: 'Number of logs to skip (default: 0)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'User activity logs retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('limit', new common_1.ParseIntPipe({ optional: true }))),
    __param(2, (0, common_1.Query)('skip', new common_1.ParseIntPipe({ optional: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", void 0)
], AssetLogsController.prototype, "getUserActivityLogs", null);
__decorate([
    (0, common_1.Get)('action/:action'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({ summary: 'Get logs by action type (Admin/Manager only)' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Number of logs to retrieve (default: 100)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Logs by action retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('action')),
    __param(1, (0, common_1.Query)('limit', new common_1.ParseIntPipe({ optional: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", void 0)
], AssetLogsController.prototype, "getLogsByAction", null);
__decorate([
    (0, common_1.Get)('date-range'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.MANAGER),
    (0, swagger_1.ApiOperation)({ summary: 'Get logs by date range (Admin/Manager only)' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: true, description: 'Start date (ISO string)' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: true, description: 'End date (ISO string)' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Number of logs to retrieve (default: 100)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Logs by date range retrieved successfully',
    }),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __param(2, (0, common_1.Query)('limit', new common_1.ParseIntPipe({ optional: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number]),
    __metadata("design:returntype", void 0)
], AssetLogsController.prototype, "getLogsByDateRange", null);
exports.AssetLogsController = AssetLogsController = __decorate([
    (0, swagger_1.ApiTags)('Asset Logs'),
    (0, common_1.Controller)('asset-logs'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [asset_logs_service_1.AssetLogsService])
], AssetLogsController);
//# sourceMappingURL=asset-logs.controller.js.map