"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/categories/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/categories/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/(dashboard)/categories/page.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CategoriesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControlLabel/FormControlLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Switch/Switch.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,FormControlLabel,Grid,IconButton,InputLabel,MenuItem,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Folder.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Category.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Edit.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Delete.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Category,ChevronRight,Delete,Edit,ExpandMore,Folder!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ChevronRight.js\");\n/* harmony import */ var _mui_x_tree_view_SimpleTreeView__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/x-tree-view/SimpleTreeView */ \"(app-pages-browser)/./node_modules/@mui/x-tree-view/SimpleTreeView/SimpleTreeView.js\");\n/* harmony import */ var _mui_x_tree_view_TreeItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/x-tree-view/TreeItem */ \"(app-pages-browser)/./node_modules/@mui/x-tree-view/TreeItem/TreeItem.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Mock data for development (fallback)\nconst mockCategories = [\n    {\n        id: \"1\",\n        name: \"IT Equipment\",\n        code: \"IT\",\n        description: \"All IT related equipment and devices\",\n        isActive: true,\n        parentId: null,\n        children: [\n            {\n                id: \"2\",\n                name: \"Computers\",\n                code: \"COMP\",\n                description: \"Desktop and laptop computers\",\n                isActive: true,\n                parentId: \"1\",\n                children: []\n            },\n            {\n                id: \"3\",\n                name: \"Peripherals\",\n                code: \"PERI\",\n                description: \"Keyboards, mice, monitors, etc.\",\n                isActive: true,\n                parentId: \"1\",\n                children: []\n            }\n        ]\n    },\n    {\n        id: \"4\",\n        name: \"Furniture\",\n        code: \"FURN\",\n        description: \"Office furniture and fixtures\",\n        isActive: true,\n        parentId: null,\n        children: [\n            {\n                id: \"5\",\n                name: \"Chairs\",\n                code: \"CHAIR\",\n                description: \"Office chairs and seating\",\n                isActive: true,\n                parentId: \"4\",\n                children: []\n            },\n            {\n                id: \"6\",\n                name: \"Desks\",\n                code: \"DESK\",\n                description: \"Office desks and tables\",\n                isActive: true,\n                parentId: \"4\",\n                children: []\n            }\n        ]\n    },\n    {\n        id: \"7\",\n        name: \"Vehicles\",\n        code: \"VEH\",\n        description: \"Company vehicles\",\n        isActive: true,\n        parentId: null,\n        children: []\n    }\n];\nfunction CategoriesPage() {\n    var _this = this;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dialogOpen, setDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"tree\");\n    // Load categories from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadCategories = async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Use hierarchy endpoint for tree view to avoid duplicate IDs\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.getCategoryHierarchy();\n                console.log(\"Categories hierarchy loaded:\", JSON.stringify(data, null, 2));\n                setCategories(data);\n            } catch (err) {\n                var _err_message, _err_message1;\n                console.error(\"Failed to load categories:\", err);\n                if (((_err_message = err.message) === null || _err_message === void 0 ? void 0 : _err_message.includes(\"401\")) || ((_err_message1 = err.message) === null || _err_message1 === void 0 ? void 0 : _err_message1.includes(\"Unauthorized\"))) {\n                    setError(\"Authentication failed. Please log in again.\");\n                    setTimeout(()=>{\n                        window.location.href = \"/login\";\n                    }, 2000);\n                } else {\n                    setError(\"Failed to load categories. Please check if the backend server is running and try again.\");\n                }\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadCategories();\n    }, []);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        code: \"\",\n        description: \"\",\n        parentId: \"\",\n        isActive: true\n    });\n    const handleAddCategory = ()=>{\n        setFormData({\n            name: \"\",\n            code: \"\",\n            description: \"\",\n            parentId: \"\",\n            isActive: true\n        });\n        setIsEditing(false);\n        setDialogOpen(true);\n    };\n    const handleEditCategory = (category)=>{\n        setFormData({\n            name: category.name,\n            code: category.code,\n            description: category.description,\n            parentId: category.parentId || \"\",\n            isActive: category.isActive\n        });\n        setSelectedCategory(category);\n        setIsEditing(true);\n        setDialogOpen(true);\n    };\n    const handleDeleteCategory = (category)=>{\n        setSelectedCategory(category);\n        setDeleteDialogOpen(true);\n    };\n    const refreshCategories = async ()=>{\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.getCategoryHierarchy();\n            setCategories(data);\n        } catch (err) {\n            console.error(\"Failed to refresh categories:\", err);\n            setError(\"Failed to refresh categories. Please try again.\");\n        }\n    };\n    const handleSaveCategory = async ()=>{\n        try {\n            if (isEditing && selectedCategory) {\n                // Update existing category\n                await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.updateCategory(selectedCategory.id, {\n                    name: formData.name,\n                    code: formData.code,\n                    description: formData.description,\n                    parentId: formData.parentId || null,\n                    isActive: formData.isActive\n                });\n            } else {\n                // Create new category\n                await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.createCategory({\n                    name: formData.name,\n                    code: formData.code,\n                    description: formData.description,\n                    parentId: formData.parentId || null,\n                    isActive: formData.isActive\n                });\n            }\n            // Refresh the entire hierarchy\n            await refreshCategories();\n            setDialogOpen(false);\n            setSelectedCategory(null);\n        } catch (err) {\n            console.error(\"Failed to save category:\", err);\n            setError(\"Failed to save category. Please try again.\");\n        }\n    };\n    const confirmDelete = async ()=>{\n        if (!selectedCategory) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.deleteCategory(selectedCategory.id);\n            // Refresh the entire hierarchy\n            await refreshCategories();\n            setDeleteDialogOpen(false);\n            setSelectedCategory(null);\n        } catch (err) {\n            console.error(\"Failed to delete category:\", err);\n            setError(\"Failed to delete category. Please try again.\");\n        }\n    };\n    const getAllCategories = (cats)=>{\n        let result = [];\n        cats.forEach((cat)=>{\n            result.push(cat);\n            if (cat.children && cat.children.length > 0) {\n                result = result.concat(getAllCategories(cat.children));\n            }\n        });\n        return result;\n    };\n    // Helper function to build full path for a category\n    const getCategoryPath = (category, allCats)=>{\n        if (!category.parentId) {\n            return category.name;\n        }\n        const parent = allCats.find((cat)=>cat.id === category.parentId);\n        if (parent) {\n            return \"\".concat(getCategoryPath(parent, allCats), \" > \").concat(category.name);\n        }\n        return category.name;\n    };\n    const renderTreeItem = function(category) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_tree_view_TreeItem__WEBPACK_IMPORTED_MODULE_3__.TreeItem, {\n            itemId: category.id,\n            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    py: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            flex: 1\n                        },\n                        children: [\n                            category.children && category.children.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                sx: {\n                                    mr: 1,\n                                    color: \"primary.main\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 15\n                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                sx: {\n                                    mr: 1,\n                                    color: \"text.secondary\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                variant: \"body1\",\n                                sx: {\n                                    mr: 2,\n                                    fontWeight: level === 0 ? 600 : 400\n                                },\n                                children: category.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                label: category.code,\n                                size: \"small\",\n                                variant: \"outlined\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, void 0),\n                            level > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                label: \"Level \".concat(level + 1),\n                                size: \"small\",\n                                variant: \"outlined\",\n                                color: \"info\",\n                                sx: {\n                                    mr: 1,\n                                    fontSize: \"0.7rem\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, void 0),\n                            !category.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                label: \"Inactive\",\n                                size: \"small\",\n                                color: \"error\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 36\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            gap: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                title: \"Edit\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    size: \"small\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleEditCategory(category);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        fontSize: \"small\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                title: \"Delete\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    size: \"small\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleDeleteCategory(category);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        fontSize: \"small\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, void 0),\n            children: category.children && category.children.map((child)=>renderTreeItem(child, level + 1))\n        }, category.id, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n            lineNumber: 276,\n            columnNumber: 5\n        }, _this);\n    };\n    const flatCategories = getAllCategories(categories);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            sx: {\n                p: 3,\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"400px\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    sx: {\n                        ml: 2\n                    },\n                    children: \"Loading categories...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n            lineNumber: 337,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        sx: {\n            p: 4,\n            backgroundColor: \"grey.50\",\n            minHeight: \"100vh\"\n        },\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mb: 3\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 348,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    mb: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"h4\",\n                        component: \"h1\",\n                        children: \"Category Management\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            gap: 2,\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    checked: viewMode === \"tree\",\n                                    onChange: (e)=>setViewMode(e.target.checked ? \"tree\" : \"table\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, void 0),\n                                label: \"Tree View\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                variant: \"contained\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 50\n                                }, void 0),\n                                onClick: handleAddCategory,\n                                color: \"primary\",\n                                children: \"Add Category\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                sx: {\n                    border: \"1px solid\",\n                    borderColor: \"divider\",\n                    boxShadow: \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    children: viewMode === \"tree\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_tree_view_SimpleTreeView__WEBPACK_IMPORTED_MODULE_21__.SimpleTreeView, {\n                        defaultCollapseIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 36\n                        }, void 0),\n                        defaultExpandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 34\n                        }, void 0),\n                        defaultExpandedItems: getAllCategories(categories).map((cat)=>cat.id),\n                        children: categories.map((category)=>renderTreeItem(category, 0))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                children: \"Code\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                children: \"Parent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                align: \"center\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    children: flatCategories.map((category)=>{\n                                        var _flatCategories_find;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            hover: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\"\n                                                        },\n                                                        children: [\n                                                            category.children && category.children.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                sx: {\n                                                                    mr: 1\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                sx: {\n                                                                    mr: 1\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            category.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        label: category.code,\n                                                        size: \"small\",\n                                                        variant: \"outlined\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    children: category.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    children: category.parentId ? ((_flatCategories_find = flatCategories.find((c)=>c.id === category.parentId)) === null || _flatCategories_find === void 0 ? void 0 : _flatCategories_find.name) || \"-\" : \"Root\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        label: category.isActive ? \"Active\" : \"Inactive\",\n                                                        color: category.isActive ? \"success\" : \"error\",\n                                                        size: \"small\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    align: \"center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"center\",\n                                                            gap: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                title: \"Edit\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    size: \"small\",\n                                                                    onClick: ()=>handleEditCategory(category),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                        lineNumber: 432,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                title: \"Delete\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    size: \"small\",\n                                                                    onClick: ()=>handleDeleteCategory(category),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Category_ChevronRight_Delete_Edit_ExpandMore_Folder_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, category.id, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                open: dialogOpen,\n                onClose: ()=>setDialogOpen(false),\n                maxWidth: \"sm\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                        children: isEditing ? \"Edit Category\" : \"Add New Category\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                        fullWidth: true,\n                                        label: \"Category Name\",\n                                        value: formData.name,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                        fullWidth: true,\n                                        label: \"Code\",\n                                        value: formData.code,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    code: e.target.value.toUpperCase()\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                        fullWidth: true,\n                                        label: \"Description\",\n                                        value: formData.description,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                })),\n                                        multiline: true,\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                        fullWidth: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                children: \"Parent Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                value: formData.parentId,\n                                                label: \"Parent Category\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            parentId: e.target.value\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                        value: \"\",\n                                                        children: \"None (Root Category)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    flatCategories.filter((cat)=>!isEditing || cat.id !== (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id)).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                            value: category.id,\n                                                            children: [\n                                                                getCategoryPath(category, flatCategories),\n                                                                \" (\",\n                                                                category.code,\n                                                                \")\"\n                                                            ]\n                                                        }, category.id, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            checked: formData.isActive,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        isActive: e.target.checked\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        label: \"Active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 455,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                onClick: ()=>setDialogOpen(false),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                onClick: handleSaveCategory,\n                                variant: \"contained\",\n                                children: isEditing ? \"Update\" : \"Create\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 452,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                open: deleteDialogOpen,\n                onClose: ()=>setDeleteDialogOpen(false),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                        children: \"Confirm Delete\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: [\n                                'Are you sure you want to delete category \"',\n                                selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.name,\n                                '\"?',\n                                (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.children) && selectedCategory.children.length > 0 ? \" This will also delete all subcategories.\" : \"\",\n                                \"This action cannot be undone.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 527,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                onClick: ()=>setDeleteDialogOpen(false),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_FormControlLabel_Grid_IconButton_InputLabel_MenuItem_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                onClick: confirmDelete,\n                                color: \"error\",\n                                variant: \"contained\",\n                                children: \"Delete\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n                lineNumber: 525,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\categories\\\\page.tsx\",\n        lineNumber: 345,\n        columnNumber: 5\n    }, this);\n}\n_s(CategoriesPage, \"mNpKX9zesD67Le7y4yKTOAVbaKM=\");\n_c = CategoriesPage;\nvar _c;\n$RefreshReg$(_c, \"CategoriesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZCkvY2F0ZWdvcmllcy9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQThCckI7QUFVTTtBQUNvQztBQUNaO0FBQ1A7QUFHN0MsdUNBQXVDO0FBQ3ZDLE1BQU0rQyxpQkFBaUI7SUFDckI7UUFDRUMsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtZQUNSO2dCQUNFTixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxVQUFVO2dCQUNWQyxVQUFVO2dCQUNWQyxVQUFVLEVBQUU7WUFDZDtZQUNBO2dCQUNFTixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxVQUFVO2dCQUNWQyxVQUFVO2dCQUNWQyxVQUFVLEVBQUU7WUFDZDtTQUNEO0lBQ0g7SUFDQTtRQUNFTixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1lBQ1I7Z0JBQ0VOLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLFVBQVU7Z0JBQ1ZDLFVBQVU7Z0JBQ1ZDLFVBQVUsRUFBRTtZQUNkO1lBQ0E7Z0JBQ0VOLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLFVBQVU7Z0JBQ1ZDLFVBQVU7Z0JBQ1ZDLFVBQVUsRUFBRTtZQUNkO1NBQ0Q7SUFDSDtJQUNBO1FBQ0VOLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVUsRUFBRTtJQUNkO0NBQ0Q7QUFFYyxTQUFTQzs7O0lBQ3RCLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHekQsK0NBQVFBLENBQWEsRUFBRTtJQUMzRCxNQUFNLENBQUMwRCxTQUFTQyxXQUFXLEdBQUczRCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUM0RCxPQUFPQyxTQUFTLEdBQUc3RCwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDOEQsWUFBWUMsY0FBYyxHQUFHL0QsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDZ0Usa0JBQWtCQyxvQkFBb0IsR0FBR2pFLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ2tFLGtCQUFrQkMsb0JBQW9CLEdBQUduRSwrQ0FBUUEsQ0FBa0I7SUFDMUUsTUFBTSxDQUFDb0UsV0FBV0MsYUFBYSxHQUFHckUsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDc0UsVUFBVUMsWUFBWSxHQUFHdkUsK0NBQVFBLENBQW1CO0lBRTNELDJCQUEyQjtJQUMzQkMsZ0RBQVNBLENBQUM7UUFDUixNQUFNdUUsaUJBQWlCO1lBQ3JCLElBQUk7Z0JBQ0ZiLFdBQVc7Z0JBQ1hFLFNBQVM7Z0JBRVQsOERBQThEO2dCQUM5RCxNQUFNWSxPQUFPLE1BQU0zQix1REFBaUJBLENBQUM0QixvQkFBb0I7Z0JBQ3pEQyxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDQyxLQUFLQyxTQUFTLENBQUNMLE1BQU0sTUFBTTtnQkFDdkVoQixjQUFjZ0I7WUFDaEIsRUFBRSxPQUFPTSxLQUFVO29CQUViQSxjQUFnQ0E7Z0JBRHBDSixRQUFRZixLQUFLLENBQUMsOEJBQThCbUI7Z0JBQzVDLElBQUlBLEVBQUFBLGVBQUFBLElBQUlDLE9BQU8sY0FBWEQsbUNBQUFBLGFBQWFFLFFBQVEsQ0FBQyxhQUFVRixnQkFBQUEsSUFBSUMsT0FBTyxjQUFYRCxvQ0FBQUEsY0FBYUUsUUFBUSxDQUFDLGtCQUFpQjtvQkFDekVwQixTQUFTO29CQUNUcUIsV0FBVzt3QkFDVEMsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7b0JBQ3pCLEdBQUc7Z0JBQ0wsT0FBTztvQkFDTHhCLFNBQVM7Z0JBQ1g7WUFDRixTQUFVO2dCQUNSRixXQUFXO1lBQ2I7UUFDRjtRQUVBYTtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU0sQ0FBQ2MsVUFBVUMsWUFBWSxHQUFHdkYsK0NBQVFBLENBQUM7UUFDdkNpRCxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiRSxVQUFVO1FBQ1ZELFVBQVU7SUFDWjtJQUVBLE1BQU1vQyxvQkFBb0I7UUFDeEJELFlBQVk7WUFDVnRDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxhQUFhO1lBQ2JFLFVBQVU7WUFDVkQsVUFBVTtRQUNaO1FBQ0FpQixhQUFhO1FBQ2JOLGNBQWM7SUFDaEI7SUFFQSxNQUFNMEIscUJBQXFCLENBQUNDO1FBQzFCSCxZQUFZO1lBQ1Z0QyxNQUFNeUMsU0FBU3pDLElBQUk7WUFDbkJDLE1BQU13QyxTQUFTeEMsSUFBSTtZQUNuQkMsYUFBYXVDLFNBQVN2QyxXQUFXO1lBQ2pDRSxVQUFVcUMsU0FBU3JDLFFBQVEsSUFBSTtZQUMvQkQsVUFBVXNDLFNBQVN0QyxRQUFRO1FBQzdCO1FBQ0FlLG9CQUFvQnVCO1FBQ3BCckIsYUFBYTtRQUNiTixjQUFjO0lBQ2hCO0lBRUEsTUFBTTRCLHVCQUF1QixDQUFDRDtRQUM1QnZCLG9CQUFvQnVCO1FBQ3BCekIsb0JBQW9CO0lBQ3RCO0lBRUEsTUFBTTJCLG9CQUFvQjtRQUN4QixJQUFJO1lBQ0YsTUFBTW5CLE9BQU8sTUFBTTNCLHVEQUFpQkEsQ0FBQzRCLG9CQUFvQjtZQUN6RGpCLGNBQWNnQjtRQUNoQixFQUFFLE9BQU9NLEtBQUs7WUFDWkosUUFBUWYsS0FBSyxDQUFDLGlDQUFpQ21CO1lBQy9DbEIsU0FBUztRQUNYO0lBQ0Y7SUFFQSxNQUFNZ0MscUJBQXFCO1FBQ3pCLElBQUk7WUFDRixJQUFJekIsYUFBYUYsa0JBQWtCO2dCQUNqQywyQkFBMkI7Z0JBQzNCLE1BQU1wQix1REFBaUJBLENBQUNnRCxjQUFjLENBQUM1QixpQkFBaUJsQixFQUFFLEVBQUU7b0JBQzFEQyxNQUFNcUMsU0FBU3JDLElBQUk7b0JBQ25CQyxNQUFNb0MsU0FBU3BDLElBQUk7b0JBQ25CQyxhQUFhbUMsU0FBU25DLFdBQVc7b0JBQ2pDRSxVQUFVaUMsU0FBU2pDLFFBQVEsSUFBSTtvQkFDL0JELFVBQVVrQyxTQUFTbEMsUUFBUTtnQkFDN0I7WUFDRixPQUFPO2dCQUNMLHNCQUFzQjtnQkFDdEIsTUFBTU4sdURBQWlCQSxDQUFDaUQsY0FBYyxDQUFDO29CQUNyQzlDLE1BQU1xQyxTQUFTckMsSUFBSTtvQkFDbkJDLE1BQU1vQyxTQUFTcEMsSUFBSTtvQkFDbkJDLGFBQWFtQyxTQUFTbkMsV0FBVztvQkFDakNFLFVBQVVpQyxTQUFTakMsUUFBUSxJQUFJO29CQUMvQkQsVUFBVWtDLFNBQVNsQyxRQUFRO2dCQUM3QjtZQUNGO1lBRUEsK0JBQStCO1lBQy9CLE1BQU13QztZQUNON0IsY0FBYztZQUNkSSxvQkFBb0I7UUFDdEIsRUFBRSxPQUFPWSxLQUFLO1lBQ1pKLFFBQVFmLEtBQUssQ0FBQyw0QkFBNEJtQjtZQUMxQ2xCLFNBQVM7UUFDWDtJQUNGO0lBRUEsTUFBTW1DLGdCQUFnQjtRQUNwQixJQUFJLENBQUM5QixrQkFBa0I7UUFFdkIsSUFBSTtZQUNGLE1BQU1wQix1REFBaUJBLENBQUNtRCxjQUFjLENBQUMvQixpQkFBaUJsQixFQUFFO1lBQzFELCtCQUErQjtZQUMvQixNQUFNNEM7WUFDTjNCLG9CQUFvQjtZQUNwQkUsb0JBQW9CO1FBQ3RCLEVBQUUsT0FBT1ksS0FBSztZQUNaSixRQUFRZixLQUFLLENBQUMsOEJBQThCbUI7WUFDNUNsQixTQUFTO1FBQ1g7SUFDRjtJQUVBLE1BQU1xQyxtQkFBbUIsQ0FBQ0M7UUFDeEIsSUFBSUMsU0FBcUIsRUFBRTtRQUMzQkQsS0FBS0UsT0FBTyxDQUFDQyxDQUFBQTtZQUNYRixPQUFPRyxJQUFJLENBQUNEO1lBQ1osSUFBSUEsSUFBSWhELFFBQVEsSUFBSWdELElBQUloRCxRQUFRLENBQUNrRCxNQUFNLEdBQUcsR0FBRztnQkFDM0NKLFNBQVNBLE9BQU9LLE1BQU0sQ0FBQ1AsaUJBQWlCSSxJQUFJaEQsUUFBUTtZQUN0RDtRQUNGO1FBQ0EsT0FBTzhDO0lBQ1Q7SUFFQSxvREFBb0Q7SUFDcEQsTUFBTU0sa0JBQWtCLENBQUNoQixVQUFvQmlCO1FBQzNDLElBQUksQ0FBQ2pCLFNBQVNyQyxRQUFRLEVBQUU7WUFDdEIsT0FBT3FDLFNBQVN6QyxJQUFJO1FBQ3RCO1FBQ0EsTUFBTTJELFNBQVNELFFBQVFFLElBQUksQ0FBQ1AsQ0FBQUEsTUFBT0EsSUFBSXRELEVBQUUsS0FBSzBDLFNBQVNyQyxRQUFRO1FBQy9ELElBQUl1RCxRQUFRO1lBQ1YsT0FBTyxHQUF5Q2xCLE9BQXRDZ0IsZ0JBQWdCRSxRQUFRRCxVQUFTLE9BQW1CLE9BQWRqQixTQUFTekMsSUFBSTtRQUMvRDtRQUNBLE9BQU95QyxTQUFTekMsSUFBSTtJQUN0QjtJQUVBLE1BQU02RCxpQkFBaUIsU0FBQ3BCO1lBQW9CcUIseUVBQWdCOzZCQUMxRCw4REFBQ2xFLCtEQUFRQTtZQUVQbUUsUUFBUXRCLFNBQVMxQyxFQUFFO1lBQ25CaUUscUJBQ0UsOERBQUN6RywwVkFBR0E7Z0JBQUMwRyxJQUFJO29CQUFFQyxTQUFTO29CQUFRQyxZQUFZO29CQUFVQyxJQUFJO2dCQUFFOztrQ0FDdEQsOERBQUM3RywwVkFBR0E7d0JBQUMwRyxJQUFJOzRCQUFFQyxTQUFTOzRCQUFRQyxZQUFZOzRCQUFVRSxNQUFNO3dCQUFFOzs0QkFDdkQ1QixTQUFTcEMsUUFBUSxJQUFJb0MsU0FBU3BDLFFBQVEsQ0FBQ2tELE1BQU0sR0FBRyxrQkFDL0MsOERBQUM3RCx5SUFBVUE7Z0NBQUN1RSxJQUFJO29DQUFFSyxJQUFJO29DQUFHQyxPQUFPO2dDQUFlOzs7Ozt1REFFL0MsOERBQUMvRSx5SUFBWUE7Z0NBQUN5RSxJQUFJO29DQUFFSyxJQUFJO29DQUFHQyxPQUFPO2dDQUFpQjs7Ozs7OzBDQUVyRCw4REFBQ3BILDBWQUFVQTtnQ0FBQ3FILFNBQVE7Z0NBQVFQLElBQUk7b0NBQUVLLElBQUk7b0NBQUdHLFlBQVlYLFVBQVUsSUFBSSxNQUFNO2dDQUFJOzBDQUMxRXJCLFNBQVN6QyxJQUFJOzs7Ozs7MENBRWhCLDhEQUFDN0IsMFZBQUlBO2dDQUFDNkYsT0FBT3ZCLFNBQVN4QyxJQUFJO2dDQUFFeUUsTUFBSztnQ0FBUUYsU0FBUTtnQ0FBV1AsSUFBSTtvQ0FBRUssSUFBSTtnQ0FBRTs7Ozs7OzRCQUN2RVIsUUFBUSxtQkFDUCw4REFBQzNGLDBWQUFJQTtnQ0FDSDZGLE9BQU8sU0FBbUIsT0FBVkYsUUFBUTtnQ0FDeEJZLE1BQUs7Z0NBQ0xGLFNBQVE7Z0NBQ1JELE9BQU07Z0NBQ05OLElBQUk7b0NBQUVLLElBQUk7b0NBQUdLLFVBQVU7Z0NBQVM7Ozs7Ozs0QkFHbkMsQ0FBQ2xDLFNBQVN0QyxRQUFRLGtCQUFJLDhEQUFDaEMsMFZBQUlBO2dDQUFDNkYsT0FBTTtnQ0FBV1UsTUFBSztnQ0FBUUgsT0FBTTtnQ0FBUU4sSUFBSTtvQ0FBRUssSUFBSTtnQ0FBRTs7Ozs7Ozs7Ozs7O2tDQUV2Riw4REFBQy9HLDBWQUFHQTt3QkFBQzBHLElBQUk7NEJBQUVDLFNBQVM7NEJBQVFVLEtBQUs7d0JBQUU7OzBDQUNqQyw4REFBQ3BHLDBWQUFPQTtnQ0FBQ3FHLE9BQU07MENBQ2IsNEVBQUNySCwyVkFBVUE7b0NBQ1RrSCxNQUFLO29DQUNMSSxTQUFTQyxDQUFBQTt3Q0FDUEEsRUFBRUMsZUFBZTt3Q0FDakJ4QyxtQkFBbUJDO29DQUNyQjs4Q0FFQSw0RUFBQ3pELDBJQUFRQTt3Q0FBQzJGLFVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7MENBR3ZCLDhEQUFDbkcsMFZBQU9BO2dDQUFDcUcsT0FBTTswQ0FDYiw0RUFBQ3JILDJWQUFVQTtvQ0FDVGtILE1BQUs7b0NBQ0xJLFNBQVNDLENBQUFBO3dDQUNQQSxFQUFFQyxlQUFlO3dDQUNqQnRDLHFCQUFxQkQ7b0NBQ3ZCOzhDQUVBLDRFQUFDdkQsMElBQVVBO3dDQUFDeUYsVUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQkFPOUJsQyxTQUFTcEMsUUFBUSxJQUFJb0MsU0FBU3BDLFFBQVEsQ0FBQzRFLEdBQUcsQ0FBQ0MsQ0FBQUEsUUFBU3JCLGVBQWVxQixPQUFPcEIsUUFBUTtXQXBEOUVyQixTQUFTMUMsRUFBRTs7Ozs7O0lBd0RwQixNQUFNb0YsaUJBQWlCbEMsaUJBQWlCMUM7SUFFeEMsSUFBSUUsU0FBUztRQUNYLHFCQUNFLDhEQUFDbEQsMFZBQUdBO1lBQUMwRyxJQUFJO2dCQUFFbUIsR0FBRztnQkFBR2xCLFNBQVM7Z0JBQVFtQixnQkFBZ0I7Z0JBQVVsQixZQUFZO2dCQUFVbUIsV0FBVztZQUFROzs4QkFDbkcsOERBQUMxRywyVkFBZ0JBOzs7Ozs4QkFDakIsOERBQUN6QiwwVkFBVUE7b0JBQUM4RyxJQUFJO3dCQUFFc0IsSUFBSTtvQkFBRTs4QkFBRzs7Ozs7Ozs7Ozs7O0lBR2pDO0lBRUEscUJBQ0UsOERBQUNoSSwwVkFBR0E7UUFBQzBHLElBQUk7WUFBRW1CLEdBQUc7WUFBR0ksaUJBQWlCO1lBQVdGLFdBQVc7UUFBUTs7WUFFN0QzRSx1QkFDQyw4REFBQ2hDLDJWQUFLQTtnQkFBQzhHLFVBQVM7Z0JBQVF4QixJQUFJO29CQUFFeUIsSUFBSTtnQkFBRTtnQkFBR0MsU0FBUyxJQUFNL0UsU0FBUzswQkFDNUREOzs7Ozs7MEJBS0wsOERBQUNwRCwwVkFBR0E7Z0JBQUMwRyxJQUFJO29CQUFFQyxTQUFTO29CQUFRbUIsZ0JBQWdCO29CQUFpQmxCLFlBQVk7b0JBQVV1QixJQUFJO2dCQUFFOztrQ0FDdkYsOERBQUN2SSwwVkFBVUE7d0JBQUNxSCxTQUFRO3dCQUFLb0IsV0FBVTtrQ0FBSzs7Ozs7O2tDQUd4Qyw4REFBQ3JJLDBWQUFHQTt3QkFBQzBHLElBQUk7NEJBQUVDLFNBQVM7NEJBQVFVLEtBQUs7NEJBQUdULFlBQVk7d0JBQVM7OzBDQUN2RCw4REFBQ3pGLDJWQUFnQkE7Z0NBQ2ZtSCx1QkFDRSw4REFBQ3BILDJWQUFNQTtvQ0FBQ3FILFNBQVN6RSxhQUFhO29DQUFRMEUsVUFBVWhCLENBQUFBLElBQUt6RCxZQUFZeUQsRUFBRWlCLE1BQU0sQ0FBQ0YsT0FBTyxHQUFHLFNBQVM7Ozs7OztnQ0FFL0Y5QixPQUFNOzs7Ozs7MENBRVIsOERBQUM1RywyVkFBTUE7Z0NBQUNvSCxTQUFRO2dDQUFZeUIseUJBQVcsOERBQUNuSCwwSUFBT0E7Ozs7O2dDQUFLZ0csU0FBU3ZDO2dDQUFtQmdDLE9BQU07MENBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPcEcsOERBQUN0SCwyVkFBSUE7Z0JBQ0hnSCxJQUFJO29CQUNGaUMsUUFBUTtvQkFDUkMsYUFBYTtvQkFDYkMsV0FBVztnQkFDYjswQkFFQSw0RUFBQ2xKLDJWQUFXQTs4QkFDVG1FLGFBQWEsdUJBQ1osOERBQUMxQiw0RUFBY0E7d0JBQ2IwRyxtQ0FBcUIsOERBQUNqSCwwSUFBY0E7Ozs7O3dCQUNwQ2tILGlDQUFtQiw4REFBQ2hILDBJQUFnQkE7Ozs7O3dCQUNwQ2lILHNCQUFzQnRELGlCQUFpQjFDLFlBQVkwRSxHQUFHLENBQUM1QixDQUFBQSxNQUFPQSxJQUFJdEQsRUFBRTtrQ0FFbkVRLFdBQVcwRSxHQUFHLENBQUN4QyxDQUFBQSxXQUFZb0IsZUFBZXBCLFVBQVU7Ozs7OzZDQUd2RCw4REFBQ3pFLDJWQUFjQTtrQ0FDYiw0RUFBQ0gsMlZBQUtBOzs4Q0FDSiw4REFBQ0ksMlZBQVNBOzhDQUNSLDRFQUFDQywyVkFBUUE7OzBEQUNQLDhEQUFDSCwyVkFBU0E7MERBQUM7Ozs7OzswREFDWCw4REFBQ0EsMlZBQVNBOzBEQUFDOzs7Ozs7MERBQ1gsOERBQUNBLDJWQUFTQTswREFBQzs7Ozs7OzBEQUNYLDhEQUFDQSwyVkFBU0E7MERBQUM7Ozs7OzswREFDWCw4REFBQ0EsMlZBQVNBOzBEQUFDOzs7Ozs7MERBQ1gsOERBQUNBLDJWQUFTQTtnREFBQ3lJLE9BQU07MERBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUc5Qiw4REFBQzFJLDJWQUFTQTs4Q0FDUHFILGVBQWVGLEdBQUcsQ0FBQ3hDLENBQUFBOzRDQWlCTzBDOzZEQWhCekIsOERBQUNqSCwyVkFBUUE7NENBQW1CdUksS0FBSzs7OERBQy9CLDhEQUFDMUksMlZBQVNBOzhEQUNSLDRFQUFDUiwwVkFBR0E7d0RBQUMwRyxJQUFJOzREQUFFQyxTQUFTOzREQUFRQyxZQUFZO3dEQUFTOzs0REFDOUMxQixTQUFTcEMsUUFBUSxJQUFJb0MsU0FBU3BDLFFBQVEsQ0FBQ2tELE1BQU0sR0FBRyxrQkFDL0MsOERBQUM3RCx5SUFBVUE7Z0VBQUN1RSxJQUFJO29FQUFFSyxJQUFJO2dFQUFFOzs7OztxRkFFeEIsOERBQUM5RSx5SUFBWUE7Z0VBQUN5RSxJQUFJO29FQUFFSyxJQUFJO2dFQUFFOzs7Ozs7NERBRTNCN0IsU0FBU3pDLElBQUk7Ozs7Ozs7Ozs7Ozs4REFHbEIsOERBQUNqQywyVkFBU0E7OERBQ1IsNEVBQUNJLDBWQUFJQTt3REFBQzZGLE9BQU92QixTQUFTeEMsSUFBSTt3REFBRXlFLE1BQUs7d0RBQVFGLFNBQVE7Ozs7Ozs7Ozs7OzhEQUVuRCw4REFBQ3pHLDJWQUFTQTs4REFBRTBFLFNBQVN2QyxXQUFXOzs7Ozs7OERBQ2hDLDhEQUFDbkMsMlZBQVNBOzhEQUNQMEUsU0FBU3JDLFFBQVEsR0FBRytFLEVBQUFBLHVCQUFBQSxlQUFldkIsSUFBSSxDQUFDOEMsQ0FBQUEsSUFBS0EsRUFBRTNHLEVBQUUsS0FBSzBDLFNBQVNyQyxRQUFRLGVBQW5EK0UsMkNBQUFBLHFCQUFzRG5GLElBQUksS0FBSSxNQUFNOzs7Ozs7OERBRTNGLDhEQUFDakMsMlZBQVNBOzhEQUNSLDRFQUFDSSwwVkFBSUE7d0RBQ0g2RixPQUFPdkIsU0FBU3RDLFFBQVEsR0FBRyxXQUFXO3dEQUN0Q29FLE9BQU85QixTQUFTdEMsUUFBUSxHQUFHLFlBQVk7d0RBQ3ZDdUUsTUFBSzs7Ozs7Ozs7Ozs7OERBR1QsOERBQUMzRywyVkFBU0E7b0RBQUN5SSxPQUFNOzhEQUNmLDRFQUFDakosMFZBQUdBO3dEQUFDMEcsSUFBSTs0REFBRUMsU0FBUzs0REFBUW1CLGdCQUFnQjs0REFBVVQsS0FBSzt3REFBRTs7MEVBQzNELDhEQUFDcEcsMFZBQU9BO2dFQUFDcUcsT0FBTTswRUFDYiw0RUFBQ3JILDJWQUFVQTtvRUFBQ2tILE1BQUs7b0VBQVFJLFNBQVMsSUFBTXRDLG1CQUFtQkM7OEVBQ3pELDRFQUFDekQsMElBQVFBOzs7Ozs7Ozs7Ozs7Ozs7MEVBR2IsOERBQUNSLDBWQUFPQTtnRUFBQ3FHLE9BQU07MEVBQ2IsNEVBQUNySCwyVkFBVUE7b0VBQUNrSCxNQUFLO29FQUFRSSxTQUFTLElBQU1wQyxxQkFBcUJEOzhFQUMzRCw0RUFBQ3ZELDBJQUFVQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJDQWxDTnVELFNBQVMxQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFpRHhDLDhEQUFDdEMsMlZBQU1BO2dCQUFDa0osTUFBTTlGO2dCQUFZOEUsU0FBUyxJQUFNN0UsY0FBYztnQkFBUThGLFVBQVM7Z0JBQUtDLFNBQVM7O2tDQUNwRiw4REFBQ25KLDJWQUFXQTtrQ0FBRXlELFlBQVksa0JBQWtCOzs7Ozs7a0NBQzVDLDhEQUFDeEQsMlZBQWFBO2tDQUNaLDRFQUFDTCwyVkFBSUE7NEJBQUN3SixTQUFTOzRCQUFDQyxTQUFTOzRCQUFHOUMsSUFBSTtnQ0FBRStDLElBQUk7NEJBQUU7OzhDQUN0Qyw4REFBQzFKLDJWQUFJQTtvQ0FBQzJKLElBQUk7b0NBQUNDLElBQUk7b0NBQUlDLElBQUk7OENBQ3JCLDRFQUFDOUosMlZBQVNBO3dDQUNSd0osU0FBUzt3Q0FDVDdDLE9BQU07d0NBQ05vRCxPQUFPL0UsU0FBU3JDLElBQUk7d0NBQ3BCK0YsVUFBVWhCLENBQUFBLElBQUt6QyxZQUFZK0UsQ0FBQUEsT0FBUztvREFBRSxHQUFHQSxJQUFJO29EQUFFckgsTUFBTStFLEVBQUVpQixNQUFNLENBQUNvQixLQUFLO2dEQUFDO3dDQUNwRUUsUUFBUTs7Ozs7Ozs7Ozs7OENBR1osOERBQUNoSywyVkFBSUE7b0NBQUMySixJQUFJO29DQUFDQyxJQUFJO29DQUFJQyxJQUFJOzhDQUNyQiw0RUFBQzlKLDJWQUFTQTt3Q0FDUndKLFNBQVM7d0NBQ1Q3QyxPQUFNO3dDQUNOb0QsT0FBTy9FLFNBQVNwQyxJQUFJO3dDQUNwQjhGLFVBQVVoQixDQUFBQSxJQUFLekMsWUFBWStFLENBQUFBLE9BQVM7b0RBQUUsR0FBR0EsSUFBSTtvREFBRXBILE1BQU04RSxFQUFFaUIsTUFBTSxDQUFDb0IsS0FBSyxDQUFDRyxXQUFXO2dEQUFHO3dDQUNsRkQsUUFBUTs7Ozs7Ozs7Ozs7OENBR1osOERBQUNoSywyVkFBSUE7b0NBQUMySixJQUFJO29DQUFDQyxJQUFJOzhDQUNiLDRFQUFDN0osMlZBQVNBO3dDQUNSd0osU0FBUzt3Q0FDVDdDLE9BQU07d0NBQ05vRCxPQUFPL0UsU0FBU25DLFdBQVc7d0NBQzNCNkYsVUFBVWhCLENBQUFBLElBQUt6QyxZQUFZK0UsQ0FBQUEsT0FBUztvREFBRSxHQUFHQSxJQUFJO29EQUFFbkgsYUFBYTZFLEVBQUVpQixNQUFNLENBQUNvQixLQUFLO2dEQUFDO3dDQUMzRUksU0FBUzt3Q0FDVEMsTUFBTTs7Ozs7Ozs7Ozs7OENBR1YsOERBQUNuSywyVkFBSUE7b0NBQUMySixJQUFJO29DQUFDQyxJQUFJOzhDQUNiLDRFQUFDOUksMlZBQVdBO3dDQUFDeUksU0FBUzs7MERBQ3BCLDhEQUFDeEksMlZBQVVBOzBEQUFDOzs7Ozs7MERBQ1osOERBQUNDLDJWQUFNQTtnREFDTDhJLE9BQU8vRSxTQUFTakMsUUFBUTtnREFDeEI0RCxPQUFNO2dEQUNOK0IsVUFBVWhCLENBQUFBLElBQUt6QyxZQUFZK0UsQ0FBQUEsT0FBUzs0REFBRSxHQUFHQSxJQUFJOzREQUFFakgsVUFBVTJFLEVBQUVpQixNQUFNLENBQUNvQixLQUFLO3dEQUFDOztrRUFFeEUsOERBQUM3SSwyVkFBUUE7d0RBQUM2SSxPQUFNO2tFQUFHOzs7Ozs7b0RBQ2xCakMsZUFDRXVDLE1BQU0sQ0FBQ3JFLENBQUFBLE1BQU8sQ0FBQ2xDLGFBQWFrQyxJQUFJdEQsRUFBRSxNQUFLa0IsNkJBQUFBLHVDQUFBQSxpQkFBa0JsQixFQUFFLEdBQzNEa0YsR0FBRyxDQUFDeEMsQ0FBQUEseUJBQ0gsOERBQUNsRSwyVkFBUUE7NERBQW1CNkksT0FBTzNFLFNBQVMxQyxFQUFFOztnRUFDM0MwRCxnQkFBZ0JoQixVQUFVMEM7Z0VBQWdCO2dFQUFHMUMsU0FBU3hDLElBQUk7Z0VBQUM7OzJEQUQvQ3dDLFNBQVMxQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU9wQyw4REFBQ3pDLDJWQUFJQTtvQ0FBQzJKLElBQUk7b0NBQUNDLElBQUk7OENBQ2IsNEVBQUN4SSwyVkFBZ0JBO3dDQUNmbUgsdUJBQ0UsOERBQUNwSCwyVkFBTUE7NENBQ0xxSCxTQUFTekQsU0FBU2xDLFFBQVE7NENBQzFCNEYsVUFBVWhCLENBQUFBLElBQUt6QyxZQUFZK0UsQ0FBQUEsT0FBUzt3REFBRSxHQUFHQSxJQUFJO3dEQUFFbEgsVUFBVTRFLEVBQUVpQixNQUFNLENBQUNGLE9BQU87b0RBQUM7Ozs7Ozt3Q0FHOUU5QixPQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtkLDhEQUFDcEcsMlZBQWFBOzswQ0FDWiw4REFBQ1IsMlZBQU1BO2dDQUFDMEgsU0FBUyxJQUFNaEUsY0FBYzswQ0FBUTs7Ozs7OzBDQUM3Qyw4REFBQzFELDJWQUFNQTtnQ0FBQzBILFNBQVNsQztnQ0FBb0I0QixTQUFROzBDQUMxQ3JELFlBQVksV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU05Qiw4REFBQzFELDJWQUFNQTtnQkFBQ2tKLE1BQU01RjtnQkFBa0I0RSxTQUFTLElBQU0zRSxvQkFBb0I7O2tDQUNqRSw4REFBQ3RELDJWQUFXQTtrQ0FBQzs7Ozs7O2tDQUNiLDhEQUFDQywyVkFBYUE7a0NBQ1osNEVBQUNSLDBWQUFVQTs7Z0NBQUM7Z0NBQ2lDOEQsNkJBQUFBLHVDQUFBQSxpQkFBa0JqQixJQUFJO2dDQUFDO2dDQUNqRWlCLENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCWixRQUFRLEtBQUlZLGlCQUFpQlosUUFBUSxDQUFDa0QsTUFBTSxHQUFHLElBQzlELDhDQUNBO2dDQUFHOzs7Ozs7Ozs7Ozs7a0NBSVgsOERBQUMzRiwyVkFBYUE7OzBDQUNaLDhEQUFDUiwyVkFBTUE7Z0NBQUMwSCxTQUFTLElBQU05RCxvQkFBb0I7MENBQVE7Ozs7OzswQ0FDbkQsOERBQUM1RCwyVkFBTUE7Z0NBQUMwSCxTQUFTL0I7Z0NBQWV3QixPQUFNO2dDQUFRQyxTQUFROzBDQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPNUU7R0EzYXdCbEU7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC8oZGFzaGJvYXJkKS9jYXRlZ29yaWVzL3BhZ2UudHN4PzI2MmYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7XG4gIENhcmQsXG4gIENhcmRDb250ZW50LFxuICBUeXBvZ3JhcGh5LFxuICBCdXR0b24sXG4gIFRleHRGaWVsZCxcbiAgR3JpZCxcbiAgQm94LFxuICBJY29uQnV0dG9uLFxuICBEaWFsb2csXG4gIERpYWxvZ1RpdGxlLFxuICBEaWFsb2dDb250ZW50LFxuICBEaWFsb2dBY3Rpb25zLFxuICBUYWJsZSxcbiAgVGFibGVCb2R5LFxuICBUYWJsZUNlbGwsXG4gIFRhYmxlQ29udGFpbmVyLFxuICBUYWJsZUhlYWQsXG4gIFRhYmxlUm93LFxuICBDaGlwLFxuICBGb3JtQ29udHJvbCxcbiAgSW5wdXRMYWJlbCxcbiAgU2VsZWN0LFxuICBNZW51SXRlbSxcbiAgVG9vbHRpcCxcbiAgU3dpdGNoLFxuICBGb3JtQ29udHJvbExhYmVsLFxuICBBbGVydCxcbiAgQ2lyY3VsYXJQcm9ncmVzc1xufSBmcm9tICdAbXVpL21hdGVyaWFsJ1xuaW1wb3J0IHtcbiAgQWRkIGFzIEFkZEljb24sXG4gIEVkaXQgYXMgRWRpdEljb24sXG4gIERlbGV0ZSBhcyBEZWxldGVJY29uLFxuICBFeHBhbmRNb3JlIGFzIEV4cGFuZE1vcmVJY29uLFxuICBDaGV2cm9uUmlnaHQgYXMgQ2hldnJvblJpZ2h0SWNvbixcbiAgQ2F0ZWdvcnkgYXMgQ2F0ZWdvcnlJY29uLFxuICBGb2xkZXIgYXMgRm9sZGVySWNvbixcbiAgRm9sZGVyT3BlbiBhcyBGb2xkZXJPcGVuSWNvblxufSBmcm9tICdAbXVpL2ljb25zLW1hdGVyaWFsJ1xuaW1wb3J0IHsgU2ltcGxlVHJlZVZpZXcgfSBmcm9tICdAbXVpL3gtdHJlZS12aWV3L1NpbXBsZVRyZWVWaWV3J1xuaW1wb3J0IHsgVHJlZUl0ZW0gfSBmcm9tICdAbXVpL3gtdHJlZS12aWV3L1RyZWVJdGVtJ1xuaW1wb3J0IHsgY2F0ZWdvcmllc1NlcnZpY2UgfSBmcm9tICdAL2xpYi9hcGknXG5pbXBvcnQgeyBDYXRlZ29yeSB9IGZyb20gJ0AvdHlwZXMvYXBpJ1xuXG4vLyBNb2NrIGRhdGEgZm9yIGRldmVsb3BtZW50IChmYWxsYmFjaylcbmNvbnN0IG1vY2tDYXRlZ29yaWVzID0gW1xuICB7XG4gICAgaWQ6ICcxJyxcbiAgICBuYW1lOiAnSVQgRXF1aXBtZW50JyxcbiAgICBjb2RlOiAnSVQnLFxuICAgIGRlc2NyaXB0aW9uOiAnQWxsIElUIHJlbGF0ZWQgZXF1aXBtZW50IGFuZCBkZXZpY2VzJyxcbiAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICBwYXJlbnRJZDogbnVsbCxcbiAgICBjaGlsZHJlbjogW1xuICAgICAge1xuICAgICAgICBpZDogJzInLFxuICAgICAgICBuYW1lOiAnQ29tcHV0ZXJzJyxcbiAgICAgICAgY29kZTogJ0NPTVAnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0Rlc2t0b3AgYW5kIGxhcHRvcCBjb21wdXRlcnMnLFxuICAgICAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICAgICAgcGFyZW50SWQ6ICcxJyxcbiAgICAgICAgY2hpbGRyZW46IFtdXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJzMnLFxuICAgICAgICBuYW1lOiAnUGVyaXBoZXJhbHMnLFxuICAgICAgICBjb2RlOiAnUEVSSScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnS2V5Ym9hcmRzLCBtaWNlLCBtb25pdG9ycywgZXRjLicsXG4gICAgICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgICAgICBwYXJlbnRJZDogJzEnLFxuICAgICAgICBjaGlsZHJlbjogW11cbiAgICAgIH1cbiAgICBdXG4gIH0sXG4gIHtcbiAgICBpZDogJzQnLFxuICAgIG5hbWU6ICdGdXJuaXR1cmUnLFxuICAgIGNvZGU6ICdGVVJOJyxcbiAgICBkZXNjcmlwdGlvbjogJ09mZmljZSBmdXJuaXR1cmUgYW5kIGZpeHR1cmVzJyxcbiAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICBwYXJlbnRJZDogbnVsbCxcbiAgICBjaGlsZHJlbjogW1xuICAgICAge1xuICAgICAgICBpZDogJzUnLFxuICAgICAgICBuYW1lOiAnQ2hhaXJzJyxcbiAgICAgICAgY29kZTogJ0NIQUlSJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdPZmZpY2UgY2hhaXJzIGFuZCBzZWF0aW5nJyxcbiAgICAgICAgaXNBY3RpdmU6IHRydWUsXG4gICAgICAgIHBhcmVudElkOiAnNCcsXG4gICAgICAgIGNoaWxkcmVuOiBbXVxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICc2JyxcbiAgICAgICAgbmFtZTogJ0Rlc2tzJyxcbiAgICAgICAgY29kZTogJ0RFU0snLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ09mZmljZSBkZXNrcyBhbmQgdGFibGVzJyxcbiAgICAgICAgaXNBY3RpdmU6IHRydWUsXG4gICAgICAgIHBhcmVudElkOiAnNCcsXG4gICAgICAgIGNoaWxkcmVuOiBbXVxuICAgICAgfVxuICAgIF1cbiAgfSxcbiAge1xuICAgIGlkOiAnNycsXG4gICAgbmFtZTogJ1ZlaGljbGVzJyxcbiAgICBjb2RlOiAnVkVIJyxcbiAgICBkZXNjcmlwdGlvbjogJ0NvbXBhbnkgdmVoaWNsZXMnLFxuICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgIHBhcmVudElkOiBudWxsLFxuICAgIGNoaWxkcmVuOiBbXVxuICB9XG5dXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENhdGVnb3JpZXNQYWdlKCkge1xuICBjb25zdCBbY2F0ZWdvcmllcywgc2V0Q2F0ZWdvcmllc10gPSB1c2VTdGF0ZTxDYXRlZ29yeVtdPihbXSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuICBjb25zdCBbZGlhbG9nT3Blbiwgc2V0RGlhbG9nT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2RlbGV0ZURpYWxvZ09wZW4sIHNldERlbGV0ZURpYWxvZ09wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzZWxlY3RlZENhdGVnb3J5LCBzZXRTZWxlY3RlZENhdGVnb3J5XSA9IHVzZVN0YXRlPENhdGVnb3J5IHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2lzRWRpdGluZywgc2V0SXNFZGl0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbdmlld01vZGUsIHNldFZpZXdNb2RlXSA9IHVzZVN0YXRlPCd0cmVlJyB8ICd0YWJsZSc+KCd0cmVlJylcblxuICAvLyBMb2FkIGNhdGVnb3JpZXMgZnJvbSBBUElcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBsb2FkQ2F0ZWdvcmllcyA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICAgICAgc2V0RXJyb3IobnVsbClcblxuICAgICAgICAvLyBVc2UgaGllcmFyY2h5IGVuZHBvaW50IGZvciB0cmVlIHZpZXcgdG8gYXZvaWQgZHVwbGljYXRlIElEc1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgY2F0ZWdvcmllc1NlcnZpY2UuZ2V0Q2F0ZWdvcnlIaWVyYXJjaHkoKVxuICAgICAgICBjb25zb2xlLmxvZygnQ2F0ZWdvcmllcyBoaWVyYXJjaHkgbG9hZGVkOicsIEpTT04uc3RyaW5naWZ5KGRhdGEsIG51bGwsIDIpKVxuICAgICAgICBzZXRDYXRlZ29yaWVzKGRhdGEpXG4gICAgICB9IGNhdGNoIChlcnI6IGFueSkge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBjYXRlZ29yaWVzOicsIGVycilcbiAgICAgICAgaWYgKGVyci5tZXNzYWdlPy5pbmNsdWRlcygnNDAxJykgfHwgZXJyLm1lc3NhZ2U/LmluY2x1ZGVzKCdVbmF1dGhvcml6ZWQnKSkge1xuICAgICAgICAgIHNldEVycm9yKCdBdXRoZW50aWNhdGlvbiBmYWlsZWQuIFBsZWFzZSBsb2cgaW4gYWdhaW4uJylcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9sb2dpbidcbiAgICAgICAgICB9LCAyMDAwKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gbG9hZCBjYXRlZ29yaWVzLiBQbGVhc2UgY2hlY2sgaWYgdGhlIGJhY2tlbmQgc2VydmVyIGlzIHJ1bm5pbmcgYW5kIHRyeSBhZ2Fpbi4nKVxuICAgICAgICB9XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgICAgfVxuICAgIH1cblxuICAgIGxvYWRDYXRlZ29yaWVzKClcbiAgfSwgW10pXG5cbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XG4gICAgbmFtZTogJycsXG4gICAgY29kZTogJycsXG4gICAgZGVzY3JpcHRpb246ICcnLFxuICAgIHBhcmVudElkOiAnJyxcbiAgICBpc0FjdGl2ZTogdHJ1ZVxuICB9KVxuXG4gIGNvbnN0IGhhbmRsZUFkZENhdGVnb3J5ID0gKCkgPT4ge1xuICAgIHNldEZvcm1EYXRhKHtcbiAgICAgIG5hbWU6ICcnLFxuICAgICAgY29kZTogJycsXG4gICAgICBkZXNjcmlwdGlvbjogJycsXG4gICAgICBwYXJlbnRJZDogJycsXG4gICAgICBpc0FjdGl2ZTogdHJ1ZVxuICAgIH0pXG4gICAgc2V0SXNFZGl0aW5nKGZhbHNlKVxuICAgIHNldERpYWxvZ09wZW4odHJ1ZSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUVkaXRDYXRlZ29yeSA9IChjYXRlZ29yeTogQ2F0ZWdvcnkpID0+IHtcbiAgICBzZXRGb3JtRGF0YSh7XG4gICAgICBuYW1lOiBjYXRlZ29yeS5uYW1lLFxuICAgICAgY29kZTogY2F0ZWdvcnkuY29kZSxcbiAgICAgIGRlc2NyaXB0aW9uOiBjYXRlZ29yeS5kZXNjcmlwdGlvbixcbiAgICAgIHBhcmVudElkOiBjYXRlZ29yeS5wYXJlbnRJZCB8fCAnJyxcbiAgICAgIGlzQWN0aXZlOiBjYXRlZ29yeS5pc0FjdGl2ZVxuICAgIH0pXG4gICAgc2V0U2VsZWN0ZWRDYXRlZ29yeShjYXRlZ29yeSlcbiAgICBzZXRJc0VkaXRpbmcodHJ1ZSlcbiAgICBzZXREaWFsb2dPcGVuKHRydWUpXG4gIH1cblxuICBjb25zdCBoYW5kbGVEZWxldGVDYXRlZ29yeSA9IChjYXRlZ29yeTogQ2F0ZWdvcnkpID0+IHtcbiAgICBzZXRTZWxlY3RlZENhdGVnb3J5KGNhdGVnb3J5KVxuICAgIHNldERlbGV0ZURpYWxvZ09wZW4odHJ1ZSlcbiAgfVxuXG4gIGNvbnN0IHJlZnJlc2hDYXRlZ29yaWVzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgY2F0ZWdvcmllc1NlcnZpY2UuZ2V0Q2F0ZWdvcnlIaWVyYXJjaHkoKVxuICAgICAgc2V0Q2F0ZWdvcmllcyhkYXRhKVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHJlZnJlc2ggY2F0ZWdvcmllczonLCBlcnIpXG4gICAgICBzZXRFcnJvcignRmFpbGVkIHRvIHJlZnJlc2ggY2F0ZWdvcmllcy4gUGxlYXNlIHRyeSBhZ2Fpbi4nKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVNhdmVDYXRlZ29yeSA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgaWYgKGlzRWRpdGluZyAmJiBzZWxlY3RlZENhdGVnb3J5KSB7XG4gICAgICAgIC8vIFVwZGF0ZSBleGlzdGluZyBjYXRlZ29yeVxuICAgICAgICBhd2FpdCBjYXRlZ29yaWVzU2VydmljZS51cGRhdGVDYXRlZ29yeShzZWxlY3RlZENhdGVnb3J5LmlkLCB7XG4gICAgICAgICAgbmFtZTogZm9ybURhdGEubmFtZSxcbiAgICAgICAgICBjb2RlOiBmb3JtRGF0YS5jb2RlLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBmb3JtRGF0YS5kZXNjcmlwdGlvbixcbiAgICAgICAgICBwYXJlbnRJZDogZm9ybURhdGEucGFyZW50SWQgfHwgbnVsbCxcbiAgICAgICAgICBpc0FjdGl2ZTogZm9ybURhdGEuaXNBY3RpdmVcbiAgICAgICAgfSlcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIENyZWF0ZSBuZXcgY2F0ZWdvcnlcbiAgICAgICAgYXdhaXQgY2F0ZWdvcmllc1NlcnZpY2UuY3JlYXRlQ2F0ZWdvcnkoe1xuICAgICAgICAgIG5hbWU6IGZvcm1EYXRhLm5hbWUsXG4gICAgICAgICAgY29kZTogZm9ybURhdGEuY29kZSxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogZm9ybURhdGEuZGVzY3JpcHRpb24sXG4gICAgICAgICAgcGFyZW50SWQ6IGZvcm1EYXRhLnBhcmVudElkIHx8IG51bGwsXG4gICAgICAgICAgaXNBY3RpdmU6IGZvcm1EYXRhLmlzQWN0aXZlXG4gICAgICAgIH0pXG4gICAgICB9XG5cbiAgICAgIC8vIFJlZnJlc2ggdGhlIGVudGlyZSBoaWVyYXJjaHlcbiAgICAgIGF3YWl0IHJlZnJlc2hDYXRlZ29yaWVzKClcbiAgICAgIHNldERpYWxvZ09wZW4oZmFsc2UpXG4gICAgICBzZXRTZWxlY3RlZENhdGVnb3J5KG51bGwpXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2F2ZSBjYXRlZ29yeTonLCBlcnIpXG4gICAgICBzZXRFcnJvcignRmFpbGVkIHRvIHNhdmUgY2F0ZWdvcnkuIFBsZWFzZSB0cnkgYWdhaW4uJylcbiAgICB9XG4gIH1cblxuICBjb25zdCBjb25maXJtRGVsZXRlID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghc2VsZWN0ZWRDYXRlZ29yeSkgcmV0dXJuXG5cbiAgICB0cnkge1xuICAgICAgYXdhaXQgY2F0ZWdvcmllc1NlcnZpY2UuZGVsZXRlQ2F0ZWdvcnkoc2VsZWN0ZWRDYXRlZ29yeS5pZClcbiAgICAgIC8vIFJlZnJlc2ggdGhlIGVudGlyZSBoaWVyYXJjaHlcbiAgICAgIGF3YWl0IHJlZnJlc2hDYXRlZ29yaWVzKClcbiAgICAgIHNldERlbGV0ZURpYWxvZ09wZW4oZmFsc2UpXG4gICAgICBzZXRTZWxlY3RlZENhdGVnb3J5KG51bGwpXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZGVsZXRlIGNhdGVnb3J5OicsIGVycilcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gZGVsZXRlIGNhdGVnb3J5LiBQbGVhc2UgdHJ5IGFnYWluLicpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0QWxsQ2F0ZWdvcmllcyA9IChjYXRzOiBDYXRlZ29yeVtdKTogQ2F0ZWdvcnlbXSA9PiB7XG4gICAgbGV0IHJlc3VsdDogQ2F0ZWdvcnlbXSA9IFtdXG4gICAgY2F0cy5mb3JFYWNoKGNhdCA9PiB7XG4gICAgICByZXN1bHQucHVzaChjYXQpXG4gICAgICBpZiAoY2F0LmNoaWxkcmVuICYmIGNhdC5jaGlsZHJlbi5sZW5ndGggPiAwKSB7XG4gICAgICAgIHJlc3VsdCA9IHJlc3VsdC5jb25jYXQoZ2V0QWxsQ2F0ZWdvcmllcyhjYXQuY2hpbGRyZW4pKVxuICAgICAgfVxuICAgIH0pXG4gICAgcmV0dXJuIHJlc3VsdFxuICB9XG5cbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGJ1aWxkIGZ1bGwgcGF0aCBmb3IgYSBjYXRlZ29yeVxuICBjb25zdCBnZXRDYXRlZ29yeVBhdGggPSAoY2F0ZWdvcnk6IENhdGVnb3J5LCBhbGxDYXRzOiBDYXRlZ29yeVtdKTogc3RyaW5nID0+IHtcbiAgICBpZiAoIWNhdGVnb3J5LnBhcmVudElkKSB7XG4gICAgICByZXR1cm4gY2F0ZWdvcnkubmFtZVxuICAgIH1cbiAgICBjb25zdCBwYXJlbnQgPSBhbGxDYXRzLmZpbmQoY2F0ID0+IGNhdC5pZCA9PT0gY2F0ZWdvcnkucGFyZW50SWQpXG4gICAgaWYgKHBhcmVudCkge1xuICAgICAgcmV0dXJuIGAke2dldENhdGVnb3J5UGF0aChwYXJlbnQsIGFsbENhdHMpfSA+ICR7Y2F0ZWdvcnkubmFtZX1gXG4gICAgfVxuICAgIHJldHVybiBjYXRlZ29yeS5uYW1lXG4gIH1cblxuICBjb25zdCByZW5kZXJUcmVlSXRlbSA9IChjYXRlZ29yeTogQ2F0ZWdvcnksIGxldmVsOiBudW1iZXIgPSAwKSA9PiAoXG4gICAgPFRyZWVJdGVtXG4gICAgICBrZXk9e2NhdGVnb3J5LmlkfVxuICAgICAgaXRlbUlkPXtjYXRlZ29yeS5pZH1cbiAgICAgIGxhYmVsPXtcbiAgICAgICAgPEJveCBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBweTogMSB9fT5cbiAgICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGZsZXg6IDEgfX0+XG4gICAgICAgICAgICB7Y2F0ZWdvcnkuY2hpbGRyZW4gJiYgY2F0ZWdvcnkuY2hpbGRyZW4ubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgPEZvbGRlckljb24gc3g9e3sgbXI6IDEsIGNvbG9yOiAncHJpbWFyeS5tYWluJyB9fSAvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPENhdGVnb3J5SWNvbiBzeD17eyBtcjogMSwgY29sb3I6ICd0ZXh0LnNlY29uZGFyeScgfX0gLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5MScgc3g9e3sgbXI6IDIsIGZvbnRXZWlnaHQ6IGxldmVsID09PSAwID8gNjAwIDogNDAwIH19PlxuICAgICAgICAgICAgICB7Y2F0ZWdvcnkubmFtZX1cbiAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgIDxDaGlwIGxhYmVsPXtjYXRlZ29yeS5jb2RlfSBzaXplPSdzbWFsbCcgdmFyaWFudD0nb3V0bGluZWQnIHN4PXt7IG1yOiAxIH19IC8+XG4gICAgICAgICAgICB7bGV2ZWwgPiAwICYmIChcbiAgICAgICAgICAgICAgPENoaXBcbiAgICAgICAgICAgICAgICBsYWJlbD17YExldmVsICR7bGV2ZWwgKyAxfWB9XG4gICAgICAgICAgICAgICAgc2l6ZT0nc21hbGwnXG4gICAgICAgICAgICAgICAgdmFyaWFudD0nb3V0bGluZWQnXG4gICAgICAgICAgICAgICAgY29sb3I9J2luZm8nXG4gICAgICAgICAgICAgICAgc3g9e3sgbXI6IDEsIGZvbnRTaXplOiAnMC43cmVtJyB9fVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIHshY2F0ZWdvcnkuaXNBY3RpdmUgJiYgPENoaXAgbGFiZWw9J0luYWN0aXZlJyBzaXplPSdzbWFsbCcgY29sb3I9J2Vycm9yJyBzeD17eyBtcjogMSB9fSAvPn1cbiAgICAgICAgICA8L0JveD5cbiAgICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAxIH19PlxuICAgICAgICAgICAgPFRvb2x0aXAgdGl0bGU9J0VkaXQnPlxuICAgICAgICAgICAgICA8SWNvbkJ1dHRvblxuICAgICAgICAgICAgICAgIHNpemU9J3NtYWxsJ1xuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2UgPT4ge1xuICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKVxuICAgICAgICAgICAgICAgICAgaGFuZGxlRWRpdENhdGVnb3J5KGNhdGVnb3J5KVxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8RWRpdEljb24gZm9udFNpemU9J3NtYWxsJyAvPlxuICAgICAgICAgICAgICA8L0ljb25CdXR0b24+XG4gICAgICAgICAgICA8L1Rvb2x0aXA+XG4gICAgICAgICAgICA8VG9vbHRpcCB0aXRsZT0nRGVsZXRlJz5cbiAgICAgICAgICAgICAgPEljb25CdXR0b25cbiAgICAgICAgICAgICAgICBzaXplPSdzbWFsbCdcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtlID0+IHtcbiAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcbiAgICAgICAgICAgICAgICAgIGhhbmRsZURlbGV0ZUNhdGVnb3J5KGNhdGVnb3J5KVxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8RGVsZXRlSWNvbiBmb250U2l6ZT0nc21hbGwnIC8+XG4gICAgICAgICAgICAgIDwvSWNvbkJ1dHRvbj5cbiAgICAgICAgICAgIDwvVG9vbHRpcD5cbiAgICAgICAgICA8L0JveD5cbiAgICAgICAgPC9Cb3g+XG4gICAgICB9XG4gICAgPlxuICAgICAge2NhdGVnb3J5LmNoaWxkcmVuICYmIGNhdGVnb3J5LmNoaWxkcmVuLm1hcChjaGlsZCA9PiByZW5kZXJUcmVlSXRlbShjaGlsZCwgbGV2ZWwgKyAxKSl9XG4gICAgPC9UcmVlSXRlbT5cbiAgKVxuXG4gIGNvbnN0IGZsYXRDYXRlZ29yaWVzID0gZ2V0QWxsQ2F0ZWdvcmllcyhjYXRlZ29yaWVzKVxuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxCb3ggc3g9e3sgcDogMywgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBtaW5IZWlnaHQ6ICc0MDBweCcgfX0+XG4gICAgICAgIDxDaXJjdWxhclByb2dyZXNzIC8+XG4gICAgICAgIDxUeXBvZ3JhcGh5IHN4PXt7IG1sOiAyIH19PkxvYWRpbmcgY2F0ZWdvcmllcy4uLjwvVHlwb2dyYXBoeT5cbiAgICAgIDwvQm94PlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPEJveCBzeD17eyBwOiA0LCBiYWNrZ3JvdW5kQ29sb3I6ICdncmV5LjUwJywgbWluSGVpZ2h0OiAnMTAwdmgnIH19PlxuICAgICAgey8qIEVycm9yIEFsZXJ0ICovfVxuICAgICAge2Vycm9yICYmIChcbiAgICAgICAgPEFsZXJ0IHNldmVyaXR5PSdlcnJvcicgc3g9e3sgbWI6IDMgfX0gb25DbG9zZT17KCkgPT4gc2V0RXJyb3IobnVsbCl9PlxuICAgICAgICAgIHtlcnJvcn1cbiAgICAgICAgPC9BbGVydD5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJywgYWxpZ25JdGVtczogJ2NlbnRlcicsIG1iOiAzIH19PlxuICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdoNCcgY29tcG9uZW50PSdoMSc+XG4gICAgICAgICAgQ2F0ZWdvcnkgTWFuYWdlbWVudFxuICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgIDxCb3ggc3g9e3sgZGlzcGxheTogJ2ZsZXgnLCBnYXA6IDIsIGFsaWduSXRlbXM6ICdjZW50ZXInIH19PlxuICAgICAgICAgIDxGb3JtQ29udHJvbExhYmVsXG4gICAgICAgICAgICBjb250cm9sPXtcbiAgICAgICAgICAgICAgPFN3aXRjaCBjaGVja2VkPXt2aWV3TW9kZSA9PT0gJ3RyZWUnfSBvbkNoYW5nZT17ZSA9PiBzZXRWaWV3TW9kZShlLnRhcmdldC5jaGVja2VkID8gJ3RyZWUnIDogJ3RhYmxlJyl9IC8+XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBsYWJlbD0nVHJlZSBWaWV3J1xuICAgICAgICAgIC8+XG4gICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PSdjb250YWluZWQnIHN0YXJ0SWNvbj17PEFkZEljb24gLz59IG9uQ2xpY2s9e2hhbmRsZUFkZENhdGVnb3J5fSBjb2xvcj0ncHJpbWFyeSc+XG4gICAgICAgICAgICBBZGQgQ2F0ZWdvcnlcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9Cb3g+XG4gICAgICA8L0JveD5cblxuICAgICAgey8qIENhdGVnb3JpZXMgRGlzcGxheSAqL31cbiAgICAgIDxDYXJkXG4gICAgICAgIHN4PXt7XG4gICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkJyxcbiAgICAgICAgICBib3JkZXJDb2xvcjogJ2RpdmlkZXInLFxuICAgICAgICAgIGJveFNoYWRvdzogJ25vbmUnXG4gICAgICAgIH19XG4gICAgICA+XG4gICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICB7dmlld01vZGUgPT09ICd0cmVlJyA/IChcbiAgICAgICAgICAgIDxTaW1wbGVUcmVlVmlld1xuICAgICAgICAgICAgICBkZWZhdWx0Q29sbGFwc2VJY29uPXs8RXhwYW5kTW9yZUljb24gLz59XG4gICAgICAgICAgICAgIGRlZmF1bHRFeHBhbmRJY29uPXs8Q2hldnJvblJpZ2h0SWNvbiAvPn1cbiAgICAgICAgICAgICAgZGVmYXVsdEV4cGFuZGVkSXRlbXM9e2dldEFsbENhdGVnb3JpZXMoY2F0ZWdvcmllcykubWFwKGNhdCA9PiBjYXQuaWQpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7Y2F0ZWdvcmllcy5tYXAoY2F0ZWdvcnkgPT4gcmVuZGVyVHJlZUl0ZW0oY2F0ZWdvcnksIDApKX1cbiAgICAgICAgICAgIDwvU2ltcGxlVHJlZVZpZXc+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxUYWJsZUNvbnRhaW5lcj5cbiAgICAgICAgICAgICAgPFRhYmxlPlxuICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICA8VGFibGVSb3c+XG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+TmFtZTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPkNvZGU8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5EZXNjcmlwdGlvbjwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlBhcmVudDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlN0YXR1czwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGFsaWduPSdjZW50ZXInPkFjdGlvbnM8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XG4gICAgICAgICAgICAgICAgPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgPFRhYmxlQm9keT5cbiAgICAgICAgICAgICAgICAgIHtmbGF0Q2F0ZWdvcmllcy5tYXAoY2F0ZWdvcnkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8VGFibGVSb3cga2V5PXtjYXRlZ29yeS5pZH0gaG92ZXI+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCb3ggc3g9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2NhdGVnb3J5LmNoaWxkcmVuICYmIGNhdGVnb3J5LmNoaWxkcmVuLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvbGRlckljb24gc3g9e3sgbXI6IDEgfX0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2F0ZWdvcnlJY29uIHN4PXt7IG1yOiAxIH19IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGlwIGxhYmVsPXtjYXRlZ29yeS5jb2RlfSBzaXplPSdzbWFsbCcgdmFyaWFudD0nb3V0bGluZWQnIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD57Y2F0ZWdvcnkuZGVzY3JpcHRpb259PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeS5wYXJlbnRJZCA/IGZsYXRDYXRlZ29yaWVzLmZpbmQoYyA9PiBjLmlkID09PSBjYXRlZ29yeS5wYXJlbnRJZCk/Lm5hbWUgfHwgJy0nIDogJ1Jvb3QnfVxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2hpcFxuICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD17Y2F0ZWdvcnkuaXNBY3RpdmUgPyAnQWN0aXZlJyA6ICdJbmFjdGl2ZSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yPXtjYXRlZ29yeS5pc0FjdGl2ZSA/ICdzdWNjZXNzJyA6ICdlcnJvcid9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9J3NtYWxsJ1xuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGFsaWduPSdjZW50ZXInPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJveCBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJywgZ2FwOiAxIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCB0aXRsZT0nRWRpdCc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb25CdXR0b24gc2l6ZT0nc21hbGwnIG9uQ2xpY2s9eygpID0+IGhhbmRsZUVkaXRDYXRlZ29yeShjYXRlZ29yeSl9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEVkaXRJY29uIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9JY29uQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIHRpdGxlPSdEZWxldGUnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uQnV0dG9uIHNpemU9J3NtYWxsJyBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGVDYXRlZ29yeShjYXRlZ29yeSl9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPERlbGV0ZUljb24gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0ljb25CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQm94PlxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9UYWJsZUJvZHk+XG4gICAgICAgICAgICAgIDwvVGFibGU+XG4gICAgICAgICAgICA8L1RhYmxlQ29udGFpbmVyPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIHsvKiBBZGQvRWRpdCBDYXRlZ29yeSBEaWFsb2cgKi99XG4gICAgICA8RGlhbG9nIG9wZW49e2RpYWxvZ09wZW59IG9uQ2xvc2U9eygpID0+IHNldERpYWxvZ09wZW4oZmFsc2UpfSBtYXhXaWR0aD0nc20nIGZ1bGxXaWR0aD5cbiAgICAgICAgPERpYWxvZ1RpdGxlPntpc0VkaXRpbmcgPyAnRWRpdCBDYXRlZ29yeScgOiAnQWRkIE5ldyBDYXRlZ29yeSd9PC9EaWFsb2dUaXRsZT5cbiAgICAgICAgPERpYWxvZ0NvbnRlbnQ+XG4gICAgICAgICAgPEdyaWQgY29udGFpbmVyIHNwYWNpbmc9ezN9IHN4PXt7IG10OiAxIH19PlxuICAgICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IG1kPXs4fT5cbiAgICAgICAgICAgICAgPFRleHRGaWVsZFxuICAgICAgICAgICAgICAgIGZ1bGxXaWR0aFxuICAgICAgICAgICAgICAgIGxhYmVsPSdDYXRlZ29yeSBOYW1lJ1xuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5uYW1lfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtlID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgbmFtZTogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gbWQ9ezR9PlxuICAgICAgICAgICAgICA8VGV4dEZpZWxkXG4gICAgICAgICAgICAgICAgZnVsbFdpZHRoXG4gICAgICAgICAgICAgICAgbGFiZWw9J0NvZGUnXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNvZGV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9e2UgPT4gc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBjb2RlOiBlLnRhcmdldC52YWx1ZS50b1VwcGVyQ2FzZSgpIH0pKX1cbiAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9HcmlkPlxuICAgICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9PlxuICAgICAgICAgICAgICA8VGV4dEZpZWxkXG4gICAgICAgICAgICAgICAgZnVsbFdpZHRoXG4gICAgICAgICAgICAgICAgbGFiZWw9J0Rlc2NyaXB0aW9uJ1xuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17ZSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIGRlc2NyaXB0aW9uOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgbXVsdGlsaW5lXG4gICAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvR3JpZD5cbiAgICAgICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfT5cbiAgICAgICAgICAgICAgPEZvcm1Db250cm9sIGZ1bGxXaWR0aD5cbiAgICAgICAgICAgICAgICA8SW5wdXRMYWJlbD5QYXJlbnQgQ2F0ZWdvcnk8L0lucHV0TGFiZWw+XG4gICAgICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBhcmVudElkfVxuICAgICAgICAgICAgICAgICAgbGFiZWw9J1BhcmVudCBDYXRlZ29yeSdcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtlID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgcGFyZW50SWQ6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8TWVudUl0ZW0gdmFsdWU9Jyc+Tm9uZSAoUm9vdCBDYXRlZ29yeSk8L01lbnVJdGVtPlxuICAgICAgICAgICAgICAgICAge2ZsYXRDYXRlZ29yaWVzXG4gICAgICAgICAgICAgICAgICAgIC5maWx0ZXIoY2F0ID0+ICFpc0VkaXRpbmcgfHwgY2F0LmlkICE9PSBzZWxlY3RlZENhdGVnb3J5Py5pZClcbiAgICAgICAgICAgICAgICAgICAgLm1hcChjYXRlZ29yeSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtIGtleT17Y2F0ZWdvcnkuaWR9IHZhbHVlPXtjYXRlZ29yeS5pZH0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q2F0ZWdvcnlQYXRoKGNhdGVnb3J5LCBmbGF0Q2F0ZWdvcmllcyl9ICh7Y2F0ZWdvcnkuY29kZX0pXG4gICAgICAgICAgICAgICAgICAgICAgPC9NZW51SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgICAgIDwvRm9ybUNvbnRyb2w+XG4gICAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0+XG4gICAgICAgICAgICAgIDxGb3JtQ29udHJvbExhYmVsXG4gICAgICAgICAgICAgICAgY29udHJvbD17XG4gICAgICAgICAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2Zvcm1EYXRhLmlzQWN0aXZlfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17ZSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIGlzQWN0aXZlOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGxhYmVsPSdBY3RpdmUnXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgICAgPC9HcmlkPlxuICAgICAgICA8L0RpYWxvZ0NvbnRlbnQ+XG4gICAgICAgIDxEaWFsb2dBY3Rpb25zPlxuICAgICAgICAgIDxCdXR0b24gb25DbGljaz17KCkgPT4gc2V0RGlhbG9nT3BlbihmYWxzZSl9PkNhbmNlbDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b24gb25DbGljaz17aGFuZGxlU2F2ZUNhdGVnb3J5fSB2YXJpYW50PSdjb250YWluZWQnPlxuICAgICAgICAgICAge2lzRWRpdGluZyA/ICdVcGRhdGUnIDogJ0NyZWF0ZSd9XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvRGlhbG9nQWN0aW9ucz5cbiAgICAgIDwvRGlhbG9nPlxuXG4gICAgICB7LyogRGVsZXRlIENvbmZpcm1hdGlvbiBEaWFsb2cgKi99XG4gICAgICA8RGlhbG9nIG9wZW49e2RlbGV0ZURpYWxvZ09wZW59IG9uQ2xvc2U9eygpID0+IHNldERlbGV0ZURpYWxvZ09wZW4oZmFsc2UpfT5cbiAgICAgICAgPERpYWxvZ1RpdGxlPkNvbmZpcm0gRGVsZXRlPC9EaWFsb2dUaXRsZT5cbiAgICAgICAgPERpYWxvZ0NvbnRlbnQ+XG4gICAgICAgICAgPFR5cG9ncmFwaHk+XG4gICAgICAgICAgICBBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlIGNhdGVnb3J5IFwie3NlbGVjdGVkQ2F0ZWdvcnk/Lm5hbWV9XCI/XG4gICAgICAgICAgICB7c2VsZWN0ZWRDYXRlZ29yeT8uY2hpbGRyZW4gJiYgc2VsZWN0ZWRDYXRlZ29yeS5jaGlsZHJlbi5sZW5ndGggPiAwXG4gICAgICAgICAgICAgID8gJyBUaGlzIHdpbGwgYWxzbyBkZWxldGUgYWxsIHN1YmNhdGVnb3JpZXMuJ1xuICAgICAgICAgICAgICA6ICcnfVxuICAgICAgICAgICAgVGhpcyBhY3Rpb24gY2Fubm90IGJlIHVuZG9uZS5cbiAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICAgICAgPERpYWxvZ0FjdGlvbnM+XG4gICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBzZXREZWxldGVEaWFsb2dPcGVuKGZhbHNlKX0+Q2FuY2VsPC9CdXR0b24+XG4gICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtjb25maXJtRGVsZXRlfSBjb2xvcj0nZXJyb3InIHZhcmlhbnQ9J2NvbnRhaW5lZCc+XG4gICAgICAgICAgICBEZWxldGVcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9EaWFsb2dBY3Rpb25zPlxuICAgICAgPC9EaWFsb2c+XG4gICAgPC9Cb3g+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIlR5cG9ncmFwaHkiLCJCdXR0b24iLCJUZXh0RmllbGQiLCJHcmlkIiwiQm94IiwiSWNvbkJ1dHRvbiIsIkRpYWxvZyIsIkRpYWxvZ1RpdGxlIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0FjdGlvbnMiLCJUYWJsZSIsIlRhYmxlQm9keSIsIlRhYmxlQ2VsbCIsIlRhYmxlQ29udGFpbmVyIiwiVGFibGVIZWFkIiwiVGFibGVSb3ciLCJDaGlwIiwiRm9ybUNvbnRyb2wiLCJJbnB1dExhYmVsIiwiU2VsZWN0IiwiTWVudUl0ZW0iLCJUb29sdGlwIiwiU3dpdGNoIiwiRm9ybUNvbnRyb2xMYWJlbCIsIkFsZXJ0IiwiQ2lyY3VsYXJQcm9ncmVzcyIsIkFkZCIsIkFkZEljb24iLCJFZGl0IiwiRWRpdEljb24iLCJEZWxldGUiLCJEZWxldGVJY29uIiwiRXhwYW5kTW9yZSIsIkV4cGFuZE1vcmVJY29uIiwiQ2hldnJvblJpZ2h0IiwiQ2hldnJvblJpZ2h0SWNvbiIsIkNhdGVnb3J5IiwiQ2F0ZWdvcnlJY29uIiwiRm9sZGVyIiwiRm9sZGVySWNvbiIsIlNpbXBsZVRyZWVWaWV3IiwiVHJlZUl0ZW0iLCJjYXRlZ29yaWVzU2VydmljZSIsIm1vY2tDYXRlZ29yaWVzIiwiaWQiLCJuYW1lIiwiY29kZSIsImRlc2NyaXB0aW9uIiwiaXNBY3RpdmUiLCJwYXJlbnRJZCIsImNoaWxkcmVuIiwiQ2F0ZWdvcmllc1BhZ2UiLCJjYXRlZ29yaWVzIiwic2V0Q2F0ZWdvcmllcyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImRpYWxvZ09wZW4iLCJzZXREaWFsb2dPcGVuIiwiZGVsZXRlRGlhbG9nT3BlbiIsInNldERlbGV0ZURpYWxvZ09wZW4iLCJzZWxlY3RlZENhdGVnb3J5Iiwic2V0U2VsZWN0ZWRDYXRlZ29yeSIsImlzRWRpdGluZyIsInNldElzRWRpdGluZyIsInZpZXdNb2RlIiwic2V0Vmlld01vZGUiLCJsb2FkQ2F0ZWdvcmllcyIsImRhdGEiLCJnZXRDYXRlZ29yeUhpZXJhcmNoeSIsImNvbnNvbGUiLCJsb2ciLCJKU09OIiwic3RyaW5naWZ5IiwiZXJyIiwibWVzc2FnZSIsImluY2x1ZGVzIiwic2V0VGltZW91dCIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJoYW5kbGVBZGRDYXRlZ29yeSIsImhhbmRsZUVkaXRDYXRlZ29yeSIsImNhdGVnb3J5IiwiaGFuZGxlRGVsZXRlQ2F0ZWdvcnkiLCJyZWZyZXNoQ2F0ZWdvcmllcyIsImhhbmRsZVNhdmVDYXRlZ29yeSIsInVwZGF0ZUNhdGVnb3J5IiwiY3JlYXRlQ2F0ZWdvcnkiLCJjb25maXJtRGVsZXRlIiwiZGVsZXRlQ2F0ZWdvcnkiLCJnZXRBbGxDYXRlZ29yaWVzIiwiY2F0cyIsInJlc3VsdCIsImZvckVhY2giLCJjYXQiLCJwdXNoIiwibGVuZ3RoIiwiY29uY2F0IiwiZ2V0Q2F0ZWdvcnlQYXRoIiwiYWxsQ2F0cyIsInBhcmVudCIsImZpbmQiLCJyZW5kZXJUcmVlSXRlbSIsImxldmVsIiwiaXRlbUlkIiwibGFiZWwiLCJzeCIsImRpc3BsYXkiLCJhbGlnbkl0ZW1zIiwicHkiLCJmbGV4IiwibXIiLCJjb2xvciIsInZhcmlhbnQiLCJmb250V2VpZ2h0Iiwic2l6ZSIsImZvbnRTaXplIiwiZ2FwIiwidGl0bGUiLCJvbkNsaWNrIiwiZSIsInN0b3BQcm9wYWdhdGlvbiIsIm1hcCIsImNoaWxkIiwiZmxhdENhdGVnb3JpZXMiLCJwIiwianVzdGlmeUNvbnRlbnQiLCJtaW5IZWlnaHQiLCJtbCIsImJhY2tncm91bmRDb2xvciIsInNldmVyaXR5IiwibWIiLCJvbkNsb3NlIiwiY29tcG9uZW50IiwiY29udHJvbCIsImNoZWNrZWQiLCJvbkNoYW5nZSIsInRhcmdldCIsInN0YXJ0SWNvbiIsImJvcmRlciIsImJvcmRlckNvbG9yIiwiYm94U2hhZG93IiwiZGVmYXVsdENvbGxhcHNlSWNvbiIsImRlZmF1bHRFeHBhbmRJY29uIiwiZGVmYXVsdEV4cGFuZGVkSXRlbXMiLCJhbGlnbiIsImhvdmVyIiwiYyIsIm9wZW4iLCJtYXhXaWR0aCIsImZ1bGxXaWR0aCIsImNvbnRhaW5lciIsInNwYWNpbmciLCJtdCIsIml0ZW0iLCJ4cyIsIm1kIiwidmFsdWUiLCJwcmV2IiwicmVxdWlyZWQiLCJ0b1VwcGVyQ2FzZSIsIm11bHRpbGluZSIsInJvd3MiLCJmaWx0ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/categories/page.tsx\n"));

/***/ })

});