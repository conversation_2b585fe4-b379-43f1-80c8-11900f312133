"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/favicon.ico/route";
exports.ids = ["app/favicon.ico/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_metadata_route_loader_page_2Ffavicon_ico_2Froute_filePath_C_3A_5CInvicta_5CUpcoming_20Projects_5CAssets_20Management_20System_5Cassets_management_app_5Csrc_5Capp_5Cfavicon_ico_isDynamic_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-metadata-route-loader?page=%2Ffavicon.ico%2Froute&filePath=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app%5Csrc%5Capp%5Cfavicon.ico&isDynamic=0!?__next_metadata_route__ */ \"(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app%5Csrc%5Capp%5Cfavicon.ico&isDynamic=0!?__next_metadata_route__\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/favicon.ico/route\",\n        pathname: \"/favicon.ico\",\n        filename: \"favicon\",\n        bundlePath: \"app/favicon.ico/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?page=%2Ffavicon.ico%2Froute&filePath=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app%5Csrc%5Capp%5Cfavicon.ico&isDynamic=0!?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_page_2Ffavicon_ico_2Froute_filePath_C_3A_5CInvicta_5CUpcoming_20Projects_5CAssets_20Management_20System_5Cassets_management_app_5Csrc_5Capp_5Cfavicon_ico_isDynamic_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/favicon.ico/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app%5Csrc%5Capp%5Cfavicon.ico&isDynamic=0!?__next_metadata_route__":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app%5Csrc%5Capp%5Cfavicon.ico&isDynamic=0!?__next_metadata_route__ ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(app-metadata-route)/./node_modules/next/dist/api/server.js\");\n/* static asset route */\n\n\nconst contentType = \"image/x-icon\"\nconst buffer = Buffer.from(\"iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAJYUExURQAAAKFu/bKI/rOJ/pll+KV495BU+o9V+JFV/YpU7IdS5Y1T94lQ75ZY/7KH/pFV/qh6/al8/qFu/aFu/aFu/bKI/rKI/rKI/qFu/aFu/aFu/aFu/bKI/rKI/rKI/rKI/qFu/aFu/aFu/aFu/bKI/rKI/rKI/rKI/rKI/qFu/aFu/aFu/aFu/bKI/rKI/rKI/rKI/qFu/aFu/aFu/bKI/rKI/rKI/qFu/aFu/aFu/aFu/bKI/rKI/rKI/rKI/qFu/aFu/aFu/bKI/rKI/rKI/qFu/aFu/bKI/rKI/rOJ/plk+aFu/aFu/aFv/bGH/rKI/rKI/qR1+pBU+6Nx/bCF/pBU+5FV/ZFV/ZJf7qFu/bOJ/p5x8HxI1qJv/qFu/aFu/bKI/rKI/raM/3tH1oFL36Fu/aFu/aFu/bKI/rKI/oBK3odP7KFu/aFu/aNx/bKI/rKI/rKI/odP649U+aBt/aRy/a+E/rOJ/o5T+JFV/ad5/ap9/pFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/ZFV/aFu/bKI/p1q+rOJ/qyB+45W8pRg8J9s+6Jv/rOJ/6+E/J9x8ZJc8pBU+4dP64hU5JZj8qBt/KNx/bCF/rGH/aN3845c5YdP6ZFV/YZO6X9L3ItX5ppn9qh895Nj54BM24VN55BU+n1J2oNQ3H1I2Y9U+X5J231I2o5T+I1T9oxS9JFV/P///zBN0TsAAACbdFJOUwAAAAAAAAAAAAAAAAAAAAAAAAIQAwMQAkHHhB8dgcdHaMpXCgpUxv569qI0AQExnfXhdhgWdN/9v0wGBki7/fGWKyiR8NgSZ9V6aPuzQz+u+npo7u16aHrc3uDRlzKh7/GjNH2WTrj5vVJ8lmnO/tFtEXyWIIOFInyWAQF8kHb2rTIsqvX+zGELCl3Ie2TkhR4bguJyJIE9ATolzyLApgAAAAFiS0dEx40FSlsAAAGeSURBVDjLY2CgPWAUEhZhxC7FJComzsTAKCEpJc2IRQkTk4ysnDxQgcLs2YpKyhgqmFRU1dTnaEAUzNbU0tZBUcKkq6dvMGcOXMHs2YZGxgh7mJhMTM3mzEFRMNvcwtIKqoLJ2sbWbg66gtmz7R0cQYYwMTk5u8yZg0XB7NmuCm6MTGLuHnPgwJOZgcVrLkKFt4+vn38AXHre/EBWBragBQsXLYYrCQ6BSy9Zumx5KDsDR9iKlatWr4EpWLsOpnv9ho2bVoRzABVsXrFl67bt6Ap27Ny1e8VmiILNe7bs3RURiawgKnrf/t0HNsMUbD6w5WBMbFw8TEFCYlLyod2HNyMUbD68JYXTKjUtHaQgICPTmitr95HNyAo2H8nmZmAUzsnNW5dfUMjEwFN0dDOqgs3FvMC0wVhSWlbOxMfAwF9xDF1BJQcoGgSqqgVBNEfNZuwKYACLglpUBXVoCuobGlEVNDW3IClobWvvQJEHqujs6m7dvLkHqKB3c1//BA40eaAKjomTJm+eAlQwddr0GRjSYCUzZk6bhVVm0AEA5ioEEn1tQQ0AAAAASUVORK5CYII=\", 'base64'\n  )\n\nfunction GET() {\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"public, max-age=0, must-revalidate\",\n    },\n  })\n}\n\nconst dynamic = 'force-static'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app%5Csrc%5Capp%5Cfavicon.ico&isDynamic=0!?__next_metadata_route__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();