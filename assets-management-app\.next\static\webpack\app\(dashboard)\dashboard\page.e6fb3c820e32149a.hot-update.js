"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx":
/*!************************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AssetDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/ListItemAvatar/ListItemAvatar.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Inventory,Person!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Inventory.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Inventory,Person!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Inventory,Person!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Inventory,Person!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst statusColors = {\n    in_use: \"primary\",\n    in_stock: \"success\",\n    maintenance: \"warning\",\n    retired: \"secondary\",\n    lost: \"error\",\n    damaged: \"error\"\n};\nconst conditionColors = {\n    excellent: \"success\",\n    good: \"info\",\n    fair: \"warning\",\n    poor: \"error\"\n};\nfunction AssetDashboard() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assets, setAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load dashboard data\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Load all data in parallel\n            const [assetsResponse, categoriesData, locationsData, usersData] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.assetsService.getAssets({\n                    limit: 1000\n                }),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.getCategories(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.locationsService.getLocations(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.usersService.getUsers()\n            ]);\n            const allAssets = assetsResponse.assets;\n            setAssets(allAssets);\n            setCategories(categoriesData);\n            setLocations(locationsData);\n            setUsers(usersData);\n            // Calculate statistics\n            const totalAssets = allAssets.length;\n            const statusCounts = allAssets.reduce((acc, asset)=>{\n                acc[asset.status] = (acc[asset.status] || 0) + 1;\n                return acc;\n            }, {});\n            const conditionCounts = allAssets.reduce((acc, asset)=>{\n                acc[asset.condition] = (acc[asset.condition] || 0) + 1;\n                return acc;\n            }, {});\n            const categoryCounts = allAssets.reduce((acc, asset)=>{\n                var _asset_category;\n                const categoryName = ((_asset_category = asset.category) === null || _asset_category === void 0 ? void 0 : _asset_category.name) || \"Unknown\";\n                acc[categoryName] = (acc[categoryName] || 0) + 1;\n                return acc;\n            }, {});\n            const locationCounts = allAssets.reduce((acc, asset)=>{\n                var _asset_location;\n                const locationName = ((_asset_location = asset.location) === null || _asset_location === void 0 ? void 0 : _asset_location.name) || \"Unknown\";\n                acc[locationName] = (acc[locationName] || 0) + 1;\n                return acc;\n            }, {});\n            // Create breakdown arrays\n            const categoryBreakdown = Object.entries(categoryCounts).map((param)=>{\n                let [name, count] = param;\n                return {\n                    name,\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const locationBreakdown = Object.entries(locationCounts).map((param)=>{\n                let [name, count] = param;\n                return {\n                    name,\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const statusBreakdown = Object.entries(statusCounts).map((param)=>{\n                let [status, count] = param;\n                return {\n                    status: status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const conditionBreakdown = Object.entries(conditionCounts).map((param)=>{\n                let [condition, count] = param;\n                return {\n                    condition: condition.charAt(0).toUpperCase() + condition.slice(1),\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            // Get recent assets (last 5)\n            const recentAssets = allAssets.sort((a, b)=>new Date(b.createdAt || \"\").getTime() - new Date(a.createdAt || \"\").getTime()).slice(0, 5);\n            const dashboardStats = {\n                totalAssets,\n                inUse: statusCounts.in_use || 0,\n                inStock: statusCounts.in_stock || 0,\n                maintenance: statusCounts.maintenance || 0,\n                retired: statusCounts.retired || 0,\n                lost: statusCounts.lost || 0,\n                damaged: statusCounts.damaged || 0,\n                totalCategories: categoriesData.length,\n                totalLocations: locationsData.length,\n                totalUsers: usersData.length,\n                recentAssets,\n                categoryBreakdown,\n                locationBreakdown,\n                statusBreakdown,\n                conditionBreakdown\n            };\n            setStats(dashboardStats);\n        } catch (err) {\n            console.error(\"Failed to load dashboard data:\", err);\n            setError(\"Failed to load dashboard data. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const StatCard = (param)=>{\n        let { title, value, icon, color, subtitle } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    color: \"text.secondary\",\n                                    gutterBottom: true,\n                                    variant: \"body2\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"h4\",\n                                    component: \"div\",\n                                    color: color,\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                bgcolor: \"\".concat(color, \".main\"),\n                                width: 56,\n                                height: 56\n                            },\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 212,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        sx: {\n            p: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                variant: \"h4\",\n                component: \"h1\",\n                gutterBottom: true,\n                children: \"Asset Management Dashboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Total Assets\",\n                            value: stats.totalAssets.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"primary\",\n                            subtitle: \"All registered assets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Total Value\",\n                            value: \"$\".concat(stats.totalValue.toLocaleString()),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MoneyIcon, {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"success\",\n                            subtitle: \"Combined asset value\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"In Use\",\n                            value: stats.inUse.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"info\",\n                            subtitle: \"Currently assigned\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Available\",\n                            value: stats.inStock.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"success\",\n                            subtitle: \"Ready for assignment\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                container: true,\n                spacing: 3,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        children: \"Asset Status Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            mt: 2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mb: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: \"In Use\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: stats.inUse\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                variant: \"determinate\",\n                                                value: stats.inUse / stats.totalAssets * 100,\n                                                sx: {\n                                                    mb: 2,\n                                                    height: 8,\n                                                    borderRadius: 4\n                                                },\n                                                color: \"primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mb: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: \"In Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: stats.inStock\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                variant: \"determinate\",\n                                                value: stats.inStock / stats.totalAssets * 100,\n                                                sx: {\n                                                    mb: 2,\n                                                    height: 8,\n                                                    borderRadius: 4\n                                                },\n                                                color: \"success\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mb: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: \"Maintenance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: stats.maintenance\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                variant: \"determinate\",\n                                                value: stats.maintenance / stats.totalAssets * 100,\n                                                sx: {\n                                                    mb: 2,\n                                                    height: 8,\n                                                    borderRadius: 4\n                                                },\n                                                color: \"warning\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mb: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: \"Retired\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: stats.retired\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                variant: \"determinate\",\n                                                value: stats.retired / stats.totalAssets * 100,\n                                                sx: {\n                                                    height: 8,\n                                                    borderRadius: 4\n                                                },\n                                                color: \"secondary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        children: \"Assets by Category\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            size: \"small\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                align: \"right\",\n                                                                children: \"Count\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                align: \"right\",\n                                                                children: \"Value\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                align: \"right\",\n                                                                children: \"%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    children: stats.categoryBreakdown.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    children: category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: category.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        category.value.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: [\n                                                                        category.percentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, category.name, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 8,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        children: \"Recent Assets\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                children: \"Asset Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                children: \"Assigned To\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                align: \"right\",\n                                                                children: \"Value\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                children: \"Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    children: stats.recentAssets.map((asset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        fontWeight: \"medium\",\n                                                                        children: asset.assetNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    children: asset.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 397,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        label: asset.status,\n                                                                        color: statusColors[asset.status],\n                                                                        size: \"small\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    children: asset.assignedTo || \"Unassigned\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        asset.value.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    children: asset.date\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, asset.id, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        children: \"Recent Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        children: stats.recentActivity.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        alignItems: \"flex-start\",\n                                                        sx: {\n                                                            px: 0\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    sx: {\n                                                                        bgcolor: \"primary.main\",\n                                                                        width: 32,\n                                                                        height: 32\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        fontSize: \"small\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 430,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                primary: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    variant: \"body2\",\n                                                                    fontWeight: \"medium\",\n                                                                    children: activity.action\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 27\n                                                                }, void 0),\n                                                                secondary: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            color: \"text.secondary\",\n                                                                            children: activity.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 441,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                            variant: \"caption\",\n                                                                            color: \"text.secondary\",\n                                                                            children: [\n                                                                                \"by \",\n                                                                                activity.user,\n                                                                                \" • \",\n                                                                                activity.timestamp\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 444,\n                                                                            columnNumber: 29\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    index < stats.recentActivity.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, activity.id, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, this);\n}\n_s(AssetDashboard, \"XHXyvutMjVwFwI//GM0yYOuxtd0=\");\n_c = AssetDashboard;\nvar _c;\n$RefreshReg$(_c, \"AssetDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/asset-logs.ts":
/*!***********************************!*\
  !*** ./src/lib/api/asset-logs.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssetLogsService: function() { return /* binding */ AssetLogsService; },\n/* harmony export */   assetLogsService: function() { return /* binding */ assetLogsService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n/* harmony import */ var _types_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/api */ \"(app-pages-browser)/./src/types/api.ts\");\n\n\n\nclass AssetLogsService {\n    async getRecentLogs(limit) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.RECENT, {\n            limit\n        });\n    }\n    async getAssetLogs(assetId, params) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.BY_ASSET(assetId), params);\n    }\n    async getUserActivityLogs(userId, params) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.BY_USER(userId), params);\n    }\n    async getLogsByAction(action, limit) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.BY_ACTION(action), {\n            limit\n        });\n    }\n    async getLogsByDateRange(params) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.DATE_RANGE, params);\n    }\n    // Helper methods for common log queries\n    async getAssetCreationLogs(limit) {\n        return this.getLogsByAction(_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.CREATED, limit);\n    }\n    async getAssetAssignmentLogs(limit) {\n        return this.getLogsByAction(_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.ASSIGNED, limit);\n    }\n    async getAssetStatusChangeLogs(limit) {\n        return this.getLogsByAction(_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.STATUS_CHANGED, limit);\n    }\n    async getTodaysLogs() {\n        const today = new Date();\n        const startOfDay = new Date(today.setHours(0, 0, 0, 0));\n        const endOfDay = new Date(today.setHours(23, 59, 59, 999));\n        return this.getLogsByDateRange({\n            startDate: startOfDay.toISOString(),\n            endDate: endOfDay.toISOString(),\n            limit: 100\n        });\n    }\n    async getWeeklyLogs() {\n        const today = new Date();\n        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);\n        return this.getLogsByDateRange({\n            startDate: weekAgo.toISOString(),\n            endDate: today.toISOString(),\n            limit: 500\n        });\n    }\n    async getMonthlyLogs() {\n        const today = new Date();\n        const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);\n        return this.getLogsByDateRange({\n            startDate: monthAgo.toISOString(),\n            endDate: today.toISOString(),\n            limit: 1000\n        });\n    }\n    // Format log action for display\n    formatLogAction(action) {\n        const actionMap = {\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.CREATED]: \"Created\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.UPDATED]: \"Updated\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.DELETED]: \"Deleted\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.ASSIGNED]: \"Assigned\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.UNASSIGNED]: \"Unassigned\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.STATUS_CHANGED]: \"Status Changed\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.LOCATION_CHANGED]: \"Location Changed\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.MAINTENANCE]: \"Maintenance\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.RETIRED]: \"Retired\"\n        };\n        return actionMap[action] || action;\n    }\n    // Get log action color for UI\n    getLogActionColor(action) {\n        const colorMap = {\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.CREATED]: \"success\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.UPDATED]: \"info\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.DELETED]: \"error\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.ASSIGNED]: \"primary\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.UNASSIGNED]: \"warning\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.STATUS_CHANGED]: \"info\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.LOCATION_CHANGED]: \"info\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.MAINTENANCE]: \"warning\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.RETIRED]: \"secondary\"\n        };\n        return colorMap[action] || \"default\";\n    }\n}\nconst assetLogsService = new AssetLogsService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/asset-logs.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/assetItemService.ts":
/*!*****************************************!*\
  !*** ./src/lib/api/assetItemService.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assetItemService: function() { return /* binding */ assetItemService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\nconst assetItemService = {\n    // Create multiple asset items for an asset\n    async createAssetItems (assetId, data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/\".concat(assetId, \"/items\"), data);\n        return response;\n    },\n    // Get all asset items for a specific asset\n    async getAssetItems (assetId) {\n        console.log(\"AssetItemService: Getting asset items for asset ID:\", assetId);\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/\".concat(assetId, \"/items\"));\n        console.log(\"AssetItemService: Response received:\", response);\n        return response;\n    },\n    // Get asset item by ID\n    async getAssetItem (itemId) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/items/\".concat(itemId));\n        return response;\n    },\n    // Update asset item\n    async updateAssetItem (itemId, data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(\"/assets/items/\".concat(itemId), data);\n        return response;\n    },\n    // Transfer asset item status\n    async transferAssetItem (data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/items/transfer\", data);\n        return response;\n    },\n    // Get asset item quantities by status for a specific asset\n    async getAssetItemQuantities (assetId) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/\".concat(assetId, \"/items/quantities\"));\n        return response;\n    },\n    // Get all asset item quantities overview\n    async getAllAssetItemQuantities () {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/items/quantities/all\");\n        return response;\n    },\n    // Get asset item by asset number (Authenticated)\n    async getAssetItemByAssetNumber (assetNumber) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/by-asset-number/\".concat(assetNumber));\n        return response;\n    },\n    // Delete asset item\n    async deleteAssetItem (itemId) {\n        await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/assets/items/\".concat(itemId));\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/assetItemService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/assetQuantityService.ts":
/*!*********************************************!*\
  !*** ./src/lib/api/assetQuantityService.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assetQuantityService: function() { return /* binding */ assetQuantityService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\nconst assetQuantityService = {\n    // Get asset quantities by status\n    async getAssetQuantities (assetId) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/\".concat(assetId, \"/quantities\"));\n        return response.data;\n    },\n    // Transfer quantities between statuses\n    async transferQuantity (data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/quantities/transfer\", data);\n        return response.data;\n    },\n    // Bulk transfer quantities\n    async bulkTransferQuantity (data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/quantities/bulk-transfer\", data);\n        return response.data;\n    },\n    // Set asset quantities by status\n    async setAssetQuantities (data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/quantities/set\", data);\n        return response.data;\n    },\n    // Get transfer history for an asset\n    async getTransferHistory (assetId) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/\".concat(assetId, \"/transfer-history\"));\n        return response.data;\n    },\n    // Get all asset quantities overview\n    async getAllAssetQuantities () {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/quantities/all\");\n        return response.data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/assetQuantityService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/assets.ts":
/*!*******************************!*\
  !*** ./src/lib/api/assets.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssetsService: function() { return /* binding */ AssetsService; },\n/* harmony export */   assetsService: function() { return /* binding */ assetsService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n\n\nclass AssetsService {\n    async getAssets(params) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BASE, params);\n    }\n    async getAssetById(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BY_ID(id));\n    }\n    async getAssetByNumber(assetNumber) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BY_ASSET_NUMBER(assetNumber));\n    }\n    async createAsset(data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BASE, data);\n    }\n    async updateAsset(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BY_ID(id), data);\n    }\n    async deleteAsset(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BY_ID(id));\n    }\n    async assignAsset(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.ASSIGN(id), data);\n    }\n    async unassignAsset(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.UNASSIGN(id));\n    }\n    async getAssetStats() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.STATS);\n    }\n    // Search assets with debouncing support\n    async searchAssets(searchTerm, filters) {\n        return this.getAssets({\n            search: searchTerm,\n            ...filters\n        });\n    }\n    // Get assets by category\n    async getAssetsByCategory(categoryId) {\n        return this.getAssets({\n            categoryId\n        });\n    }\n    // Get assets by location\n    async getAssetsByLocation(locationId) {\n        return this.getAssets({\n            locationId\n        });\n    }\n    // Get assets assigned to a user\n    async getAssetsByUser(userId) {\n        return this.getAssets({\n            assignedToId: userId\n        });\n    }\n    // Upload asset image\n    async uploadAssetImage(assetId, file) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.uploadFile(\"/assets/\".concat(assetId, \"/upload-image\"), file);\n    }\n}\nconst assetsService = new AssetsService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/assets.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/auth.ts":
/*!*****************************!*\
  !*** ./src/lib/api/auth.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: function() { return /* binding */ AuthService; },\n/* harmony export */   authService: function() { return /* binding */ authService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n\n\nclass AuthService {\n    async login(credentials) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.LOGIN, credentials);\n        // Store the token\n        (0,_config__WEBPACK_IMPORTED_MODULE_1__.setAuthToken)(response.token);\n        return response;\n    }\n    async register(userData) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.REGISTER, userData);\n        // Store the token\n        (0,_config__WEBPACK_IMPORTED_MODULE_1__.setAuthToken)(response.token);\n        return response;\n    }\n    async getProfile() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.PROFILE);\n    }\n    async verifyToken() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.VERIFY_TOKEN);\n    }\n    logout() {\n        (0,_config__WEBPACK_IMPORTED_MODULE_1__.removeAuthToken)();\n        // Redirect to login page\n        if (true) {\n            window.location.href = \"/login\";\n        }\n    }\n    isAuthenticated() {\n        if (false) {}\n        const token = localStorage.getItem(\"auth_token\");\n        return !!token;\n    }\n}\nconst authService = new AuthService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/auth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/categories.ts":
/*!***********************************!*\
  !*** ./src/lib/api/categories.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoriesService: function() { return /* binding */ CategoriesService; },\n/* harmony export */   categoriesService: function() { return /* binding */ categoriesService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n\n\nclass CategoriesService {\n    async getCategories() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BASE);\n    }\n    async getActiveCategories() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.ACTIVE);\n    }\n    async getCategoryHierarchy() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.HIERARCHY);\n    }\n    async getCategoryById(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BY_ID(id));\n    }\n    async createCategory(data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BASE, data);\n    }\n    async updateCategory(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BY_ID(id), data);\n    }\n    async deleteCategory(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BY_ID(id));\n    }\n    // Helper method to get categories as options for forms\n    async getCategoryOptions() {\n        const categories = await this.getActiveCategories();\n        return categories.map((category)=>({\n                value: category.id,\n                label: category.fullPath || category.name\n            }));\n    }\n    // Get root categories (no parent)\n    async getRootCategories() {\n        const categories = await this.getCategories();\n        return categories.filter((category)=>!category.parentId);\n    }\n    // Get subcategories of a parent category\n    async getSubcategories(parentId) {\n        const categories = await this.getCategories();\n        return categories.filter((category)=>category.parentId === parentId);\n    }\n}\nconst categoriesService = new CategoriesService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/categories.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/client.ts":
/*!*******************************!*\
  !*** ./src/lib/api/client.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: function() { return /* binding */ ApiClient; },\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; }\n/* harmony export */ });\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n\nclass ApiClient {\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        const config = {\n            headers: (0,_config__WEBPACK_IMPORTED_MODULE_0__.getAuthHeaders)(),\n            ...options\n        };\n        console.log(\"API Client: Making request to:\", url);\n        console.log(\"API Client: Request config:\", config);\n        try {\n            const response = await fetch(url, config);\n            console.log(\"API Client: Response status:\", response.status);\n            console.log(\"API Client: Response ok:\", response.ok);\n            if (!response.ok) {\n                if (response.status === 401 || response.status === 403) {\n                    // Token expired, invalid, or insufficient permissions\n                    (0,_config__WEBPACK_IMPORTED_MODULE_0__.removeAuthToken)();\n                    // Redirect to login page\n                    if (true) {\n                        window.location.href = \"/login\";\n                    }\n                }\n                let errorData;\n                try {\n                    errorData = await response.json();\n                } catch (e) {\n                    errorData = {\n                        message: response.statusText || \"An error occurred\",\n                        statusCode: response.status\n                    };\n                }\n                const error = new Error(errorData.message || \"An error occurred\");\n                error.status = response.status;\n                error.response = {\n                    data: errorData\n                };\n                throw error;\n            }\n            // Handle empty responses\n            const contentType = response.headers.get(\"content-type\");\n            if (contentType && contentType.includes(\"application/json\")) {\n                const data = await response.json();\n                console.log(\"API Client: Response data:\", data);\n                return data;\n            } else {\n                console.log(\"API Client: Non-JSON response, returning empty object\");\n                return {};\n            }\n        } catch (error) {\n            if (error instanceof Error) {\n                // Check if this is a network error (backend not available)\n                if (error.message.includes(\"fetch\") || error.name === \"TypeError\") {\n                    throw new Error(\"Backend server not available. Please start the backend server or use demo mode.\");\n                }\n                throw error;\n            }\n            throw new Error(\"Network error occurred\");\n        }\n    }\n    async get(endpoint, params) {\n        const url = new URL(\"\".concat(this.baseURL).concat(endpoint));\n        if (params) {\n            Object.entries(params).forEach((param)=>{\n                let [key, value] = param;\n                if (value !== undefined && value !== null) {\n                    url.searchParams.append(key, String(value));\n                }\n            });\n        }\n        return this.request(url.pathname + url.search);\n    }\n    async post(endpoint, data) {\n        return this.request(endpoint, {\n            method: \"POST\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async put(endpoint, data) {\n        return this.request(endpoint, {\n            method: \"PUT\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async patch(endpoint, data) {\n        return this.request(endpoint, {\n            method: \"PATCH\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async delete(endpoint) {\n        return this.request(endpoint, {\n            method: \"DELETE\"\n        });\n    }\n    // File upload method\n    async uploadFile(endpoint, file, additionalData) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        if (additionalData) {\n            Object.entries(additionalData).forEach((param)=>{\n                let [key, value] = param;\n                formData.append(key, String(value));\n            });\n        }\n        const token = (0,_config__WEBPACK_IMPORTED_MODULE_0__.getAuthHeaders)().Authorization;\n        const headers = {};\n        if (token) {\n            headers.Authorization = token;\n        }\n        return this.request(endpoint, {\n            method: \"POST\",\n            headers,\n            body: formData\n        });\n    }\n    constructor(){\n        this.baseURL = _config__WEBPACK_IMPORTED_MODULE_0__.API_CONFIG.BASE_URL;\n    }\n}\n// Create a singleton instance\nconst apiClient = new ApiClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/client.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/config.ts":
/*!*******************************!*\
  !*** ./src/lib/api/config.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: function() { return /* binding */ API_CONFIG; },\n/* harmony export */   getAuthHeaders: function() { return /* binding */ getAuthHeaders; },\n/* harmony export */   getAuthToken: function() { return /* binding */ getAuthToken; },\n/* harmony export */   removeAuthToken: function() { return /* binding */ removeAuthToken; },\n/* harmony export */   setAuthToken: function() { return /* binding */ setAuthToken; }\n/* harmony export */ });\nconst API_CONFIG = {\n    BASE_URL: \"http://192.168.1.67:3001\" || 0,\n    TIMEOUT: 10000,\n    ENDPOINTS: {\n        // Auth\n        AUTH: {\n            LOGIN: \"/auth/login\",\n            REGISTER: \"/auth/register\",\n            PROFILE: \"/auth/profile\",\n            VERIFY_TOKEN: \"/auth/verify-token\"\n        },\n        // Users\n        USERS: {\n            BASE: \"/users\",\n            BY_ID: (id)=>\"/users/\".concat(id),\n            CHANGE_PASSWORD: (id)=>\"/users/\".concat(id, \"/change-password\")\n        },\n        // Assets\n        ASSETS: {\n            BASE: \"/assets\",\n            BY_ID: (id)=>\"/assets/\".concat(id),\n            BY_ASSET_NUMBER: (assetNumber)=>\"/assets/asset-number/\".concat(assetNumber),\n            ASSIGN: (id)=>\"/assets/\".concat(id, \"/assign\"),\n            UNASSIGN: (id)=>\"/assets/\".concat(id, \"/unassign\"),\n            STATS: \"/assets/stats\"\n        },\n        // Categories\n        CATEGORIES: {\n            BASE: \"/categories\",\n            BY_ID: (id)=>\"/categories/\".concat(id),\n            ACTIVE: \"/categories/active\",\n            HIERARCHY: \"/categories/hierarchy\"\n        },\n        // Locations\n        LOCATIONS: {\n            BASE: \"/locations\",\n            BY_ID: (id)=>\"/locations/\".concat(id),\n            ACTIVE: \"/locations/active\",\n            HIERARCHY: \"/locations/hierarchy\"\n        },\n        // Asset Logs\n        ASSET_LOGS: {\n            RECENT: \"/asset-logs/recent\",\n            BY_ASSET: (assetId)=>\"/asset-logs/asset/\".concat(assetId),\n            BY_USER: (userId)=>\"/asset-logs/user/\".concat(userId),\n            BY_ACTION: (action)=>\"/asset-logs/action/\".concat(action),\n            DATE_RANGE: \"/asset-logs/date-range\"\n        }\n    }\n};\nconst getAuthToken = ()=>{\n    if (false) {}\n    return localStorage.getItem(\"auth_token\");\n};\nconst setAuthToken = (token)=>{\n    if (false) {}\n    localStorage.setItem(\"auth_token\", token);\n};\nconst removeAuthToken = ()=>{\n    if (false) {}\n    localStorage.removeItem(\"auth_token\");\n};\nconst getAuthHeaders = ()=>{\n    const token = getAuthToken();\n    console.log(\"getAuthHeaders: Token retrieved:\", !!token);\n    console.log(\"getAuthHeaders: Token value:\", token ? \"\".concat(token.substring(0, 20), \"...\") : \"null\");\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...token && {\n            Authorization: \"Bearer \".concat(token)\n        }\n    };\n    console.log(\"getAuthHeaders: Headers created:\", Object.keys(headers));\n    return headers;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/config.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/index.ts":
/*!******************************!*\
  !*** ./src/lib/api/index.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: function() { return /* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG; },\n/* harmony export */   AssetCondition: function() { return /* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.AssetCondition; },\n/* harmony export */   AssetStatus: function() { return /* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.AssetStatus; },\n/* harmony export */   LocationType: function() { return /* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.LocationType; },\n/* harmony export */   LogAction: function() { return /* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.LogAction; },\n/* harmony export */   UserRole: function() { return /* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.UserRole; },\n/* harmony export */   UserStatus: function() { return /* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.UserStatus; },\n/* harmony export */   apiClient: function() { return /* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_8__.apiClient; },\n/* harmony export */   assetItemService: function() { return /* reexport safe */ _assetItemService__WEBPACK_IMPORTED_MODULE_3__.assetItemService; },\n/* harmony export */   assetLogsService: function() { return /* reexport safe */ _asset_logs__WEBPACK_IMPORTED_MODULE_7__.assetLogsService; },\n/* harmony export */   assetQuantityService: function() { return /* reexport safe */ _assetQuantityService__WEBPACK_IMPORTED_MODULE_2__.assetQuantityService; },\n/* harmony export */   assetsService: function() { return /* reexport safe */ _assets__WEBPACK_IMPORTED_MODULE_1__.assetsService; },\n/* harmony export */   authService: function() { return /* reexport safe */ _auth__WEBPACK_IMPORTED_MODULE_0__.authService; },\n/* harmony export */   categoriesService: function() { return /* reexport safe */ _categories__WEBPACK_IMPORTED_MODULE_4__.categoriesService; },\n/* harmony export */   getAuthToken: function() { return /* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_9__.getAuthToken; },\n/* harmony export */   locationsService: function() { return /* reexport safe */ _locations__WEBPACK_IMPORTED_MODULE_5__.locationsService; },\n/* harmony export */   removeAuthToken: function() { return /* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_9__.removeAuthToken; },\n/* harmony export */   setAuthToken: function() { return /* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_9__.setAuthToken; },\n/* harmony export */   usersService: function() { return /* reexport safe */ _users__WEBPACK_IMPORTED_MODULE_6__.usersService; }\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(app-pages-browser)/./src/lib/api/auth.ts\");\n/* harmony import */ var _assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./assets */ \"(app-pages-browser)/./src/lib/api/assets.ts\");\n/* harmony import */ var _assetQuantityService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./assetQuantityService */ \"(app-pages-browser)/./src/lib/api/assetQuantityService.ts\");\n/* harmony import */ var _assetItemService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./assetItemService */ \"(app-pages-browser)/./src/lib/api/assetItemService.ts\");\n/* harmony import */ var _categories__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./categories */ \"(app-pages-browser)/./src/lib/api/categories.ts\");\n/* harmony import */ var _locations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./locations */ \"(app-pages-browser)/./src/lib/api/locations.ts\");\n/* harmony import */ var _users__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./users */ \"(app-pages-browser)/./src/lib/api/users.ts\");\n/* harmony import */ var _asset_logs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./asset-logs */ \"(app-pages-browser)/./src/lib/api/asset-logs.ts\");\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n/* harmony import */ var _types_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/types/api */ \"(app-pages-browser)/./src/types/api.ts\");\n// Export all API services\n\n\n\n\n\n\n\n\n// Export API client and config\n\n\n// Export types\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvYXBpL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDBCQUEwQjtBQUNVO0FBQ0k7QUFDcUI7QUFDUjtBQUNMO0FBQ0Y7QUFDUjtBQUNTO0FBRS9DLCtCQUErQjtBQUNLO0FBQzhDO0FBRWxGLGVBQWU7QUFDWSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvbGliL2FwaS9pbmRleC50cz9lNjkzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydCBhbGwgQVBJIHNlcnZpY2VzXG5leHBvcnQgeyBhdXRoU2VydmljZSB9IGZyb20gJy4vYXV0aCdcbmV4cG9ydCB7IGFzc2V0c1NlcnZpY2UgfSBmcm9tICcuL2Fzc2V0cydcbmV4cG9ydCB7IGFzc2V0UXVhbnRpdHlTZXJ2aWNlIH0gZnJvbSAnLi9hc3NldFF1YW50aXR5U2VydmljZSdcbmV4cG9ydCB7IGFzc2V0SXRlbVNlcnZpY2UgfSBmcm9tICcuL2Fzc2V0SXRlbVNlcnZpY2UnXG5leHBvcnQgeyBjYXRlZ29yaWVzU2VydmljZSB9IGZyb20gJy4vY2F0ZWdvcmllcydcbmV4cG9ydCB7IGxvY2F0aW9uc1NlcnZpY2UgfSBmcm9tICcuL2xvY2F0aW9ucydcbmV4cG9ydCB7IHVzZXJzU2VydmljZSB9IGZyb20gJy4vdXNlcnMnXG5leHBvcnQgeyBhc3NldExvZ3NTZXJ2aWNlIH0gZnJvbSAnLi9hc3NldC1sb2dzJ1xuXG4vLyBFeHBvcnQgQVBJIGNsaWVudCBhbmQgY29uZmlnXG5leHBvcnQgeyBhcGlDbGllbnQgfSBmcm9tICcuL2NsaWVudCdcbmV4cG9ydCB7IEFQSV9DT05GSUcsIGdldEF1dGhUb2tlbiwgc2V0QXV0aFRva2VuLCByZW1vdmVBdXRoVG9rZW4gfSBmcm9tICcuL2NvbmZpZydcblxuLy8gRXhwb3J0IHR5cGVzXG5leHBvcnQgKiBmcm9tICdAL3R5cGVzL2FwaSdcbiJdLCJuYW1lcyI6WyJhdXRoU2VydmljZSIsImFzc2V0c1NlcnZpY2UiLCJhc3NldFF1YW50aXR5U2VydmljZSIsImFzc2V0SXRlbVNlcnZpY2UiLCJjYXRlZ29yaWVzU2VydmljZSIsImxvY2F0aW9uc1NlcnZpY2UiLCJ1c2Vyc1NlcnZpY2UiLCJhc3NldExvZ3NTZXJ2aWNlIiwiYXBpQ2xpZW50IiwiQVBJX0NPTkZJRyIsImdldEF1dGhUb2tlbiIsInNldEF1dGhUb2tlbiIsInJlbW92ZUF1dGhUb2tlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/locations.ts":
/*!**********************************!*\
  !*** ./src/lib/api/locations.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LocationsService: function() { return /* binding */ LocationsService; },\n/* harmony export */   locationsService: function() { return /* binding */ locationsService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n\n\nclass LocationsService {\n    async getLocations() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BASE);\n    }\n    async getActiveLocations() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.ACTIVE);\n    }\n    async getLocationHierarchy() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.HIERARCHY);\n    }\n    async getLocationById(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BY_ID(id));\n    }\n    async createLocation(data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BASE, data);\n    }\n    async updateLocation(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BY_ID(id), data);\n    }\n    async deleteLocation(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BY_ID(id));\n    }\n    // Helper method to get locations as options for forms\n    async getLocationOptions() {\n        const locations = await this.getActiveLocations();\n        return locations.map((location)=>({\n                value: location.id,\n                label: location.fullPath || location.name\n            }));\n    }\n    // Get root locations (no parent)\n    async getRootLocations() {\n        const locations = await this.getLocations();\n        return locations.filter((location)=>!location.parentId);\n    }\n    // Get sub-locations of a parent location\n    async getSublocations(parentId) {\n        const locations = await this.getLocations();\n        return locations.filter((location)=>location.parentId === parentId);\n    }\n    // Get locations by type\n    async getLocationsByType(type) {\n        const locations = await this.getLocations();\n        return locations.filter((location)=>location.type === type);\n    }\n    // Parse coordinates if they exist\n    parseCoordinates(coordinates) {\n        if (!coordinates) return null;\n        try {\n            const parsed = JSON.parse(coordinates);\n            if (parsed.lat && parsed.lng) {\n                return {\n                    lat: parsed.lat,\n                    lng: parsed.lng\n                };\n            }\n        } catch (error) {\n            console.error(\"Failed to parse coordinates:\", error);\n        }\n        return null;\n    }\n    // Format coordinates for storage\n    formatCoordinates(lat, lng) {\n        return JSON.stringify({\n            lat,\n            lng\n        });\n    }\n}\nconst locationsService = new LocationsService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/locations.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/users.ts":
/*!******************************!*\
  !*** ./src/lib/api/users.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UsersService: function() { return /* binding */ UsersService; },\n/* harmony export */   usersService: function() { return /* binding */ usersService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n\n\nclass UsersService {\n    async getUsers() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BASE);\n    }\n    async getUserById(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BY_ID(id));\n    }\n    async createUser(data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BASE, data);\n    }\n    async updateUser(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BY_ID(id), data);\n    }\n    async deleteUser(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BY_ID(id));\n    }\n    async changePassword(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.CHANGE_PASSWORD(id), data);\n    }\n    // Helper method to get users as options for forms\n    async getUserOptions() {\n        const users = await this.getUsers();\n        return users.map((user)=>({\n                value: user.id,\n                label: user.fullName\n            }));\n    }\n    // Get users by role\n    async getUsersByRole(role) {\n        const users = await this.getUsers();\n        return users.filter((user)=>user.role === role);\n    }\n    // Get active users only\n    async getActiveUsers() {\n        const users = await this.getUsers();\n        return users.filter((user)=>user.status === \"active\");\n    }\n    // Get users by department\n    async getUsersByDepartment(department) {\n        const users = await this.getUsers();\n        return users.filter((user)=>user.department === department);\n    }\n    // Search users by name or email\n    async searchUsers(searchTerm) {\n        const users = await this.getUsers();\n        const term = searchTerm.toLowerCase();\n        return users.filter((user)=>user.fullName.toLowerCase().includes(term) || user.email.toLowerCase().includes(term) || user.firstName.toLowerCase().includes(term) || user.lastName.toLowerCase().includes(term));\n    }\n}\nconst usersService = new UsersService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/users.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/api.ts":
/*!**************************!*\
  !*** ./src/types/api.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssetCondition: function() { return /* binding */ AssetCondition; },\n/* harmony export */   AssetStatus: function() { return /* binding */ AssetStatus; },\n/* harmony export */   LocationType: function() { return /* binding */ LocationType; },\n/* harmony export */   LogAction: function() { return /* binding */ LogAction; },\n/* harmony export */   UserRole: function() { return /* binding */ UserRole; },\n/* harmony export */   UserStatus: function() { return /* binding */ UserStatus; }\n/* harmony export */ });\n// User Types\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"ADMIN\"] = \"admin\";\n    UserRole[\"MANAGER\"] = \"manager\";\n    UserRole[\"VIEWER\"] = \"viewer\";\n})(UserRole || (UserRole = {}));\nvar UserStatus;\n(function(UserStatus) {\n    UserStatus[\"ACTIVE\"] = \"active\";\n    UserStatus[\"INACTIVE\"] = \"inactive\";\n    UserStatus[\"SUSPENDED\"] = \"suspended\";\n})(UserStatus || (UserStatus = {}));\nvar AssetStatus;\n(function(AssetStatus) {\n    AssetStatus[\"IN_STOCK\"] = \"in_stock\";\n    AssetStatus[\"IN_USE\"] = \"in_use\";\n    AssetStatus[\"MAINTENANCE\"] = \"maintenance\";\n    AssetStatus[\"RETIRED\"] = \"retired\";\n    AssetStatus[\"LOST\"] = \"lost\";\n    AssetStatus[\"DAMAGED\"] = \"damaged\";\n})(AssetStatus || (AssetStatus = {}));\nvar AssetCondition;\n(function(AssetCondition) {\n    AssetCondition[\"EXCELLENT\"] = \"excellent\";\n    AssetCondition[\"GOOD\"] = \"good\";\n    AssetCondition[\"FAIR\"] = \"fair\";\n    AssetCondition[\"POOR\"] = \"poor\";\n})(AssetCondition || (AssetCondition = {}));\nvar LocationType;\n(function(LocationType) {\n    LocationType[\"REGION\"] = \"region\";\n    LocationType[\"BUILDING\"] = \"building\";\n    LocationType[\"FLOOR\"] = \"floor\";\n    LocationType[\"ROOM\"] = \"room\";\n    LocationType[\"AREA\"] = \"area\";\n})(LocationType || (LocationType = {}));\nvar LogAction;\n(function(LogAction) {\n    LogAction[\"CREATED\"] = \"created\";\n    LogAction[\"UPDATED\"] = \"updated\";\n    LogAction[\"DELETED\"] = \"deleted\";\n    LogAction[\"ASSIGNED\"] = \"assigned\";\n    LogAction[\"UNASSIGNED\"] = \"unassigned\";\n    LogAction[\"STATUS_CHANGED\"] = \"status_changed\";\n    LogAction[\"LOCATION_CHANGED\"] = \"location_changed\";\n    LogAction[\"MAINTENANCE\"] = \"maintenance\";\n    LogAction[\"RETIRED\"] = \"retired\";\n})(LogAction || (LogAction = {}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/api.ts\n"));

/***/ })

});