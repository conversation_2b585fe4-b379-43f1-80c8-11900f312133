import { Repository } from 'typeorm';
import { Asset } from '../entities/asset.entity';
import { Category } from '../entities/category.entity';
import { Location } from '../entities/location.entity';
export declare class AssetNumberService {
    private assetRepository;
    private categoryRepository;
    private locationRepository;
    constructor(assetRepository: Repository<Asset>, categoryRepository: Repository<Category>, locationRepository: Repository<Location>);
    generateAssetNumber(categoryId: string, locationId: string): Promise<string>;
    private getRootCategoryCode;
    private getRootLocationCode;
    private generateCodeFromName;
    validateAssetNumberFormat(assetNumber: string): boolean;
    isAssetNumberUnique(assetNumber: string, excludeId?: string): Promise<boolean>;
    parseAssetNumber(assetNumber: string): {
        year: number;
        month: number;
        sequence: number;
    } | null;
    getAssetStatsByPeriod(year: number, month?: number): Promise<{
        totalAssets: number;
        assetNumbers: string[];
    }>;
}
