import { Repository } from 'typeorm';
import { Asset } from '../entities/asset.entity';
export declare class AssetNumberService {
    private assetRepository;
    constructor(assetRepository: Repository<Asset>);
    generateAssetNumber(): Promise<string>;
    validateAssetNumberFormat(assetNumber: string): boolean;
    isAssetNumberUnique(assetNumber: string, excludeId?: string): Promise<boolean>;
    parseAssetNumber(assetNumber: string): {
        year: number;
        month: number;
        sequence: number;
    } | null;
    getAssetStatsByPeriod(year: number, month?: number): Promise<{
        totalAssets: number;
        assetNumbers: string[];
    }>;
}
