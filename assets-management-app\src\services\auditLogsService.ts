import { apiClient, API_CONFIG } from '../lib/api'

export interface AuditLog {
  id: string
  action: string
  entityType: string
  entityId?: string
  entityName?: string
  description?: string
  oldValues?: any
  newValues?: any
  metadata?: any
  ipAddress?: string
  userAgent?: string
  user?: {
    id: string
    fullName: string
    email: string
  }
  userId?: string
  createdAt: string
}

export interface AuditLogFilters {
  action?: string
  entityType?: string
  entityId?: string
  userId?: string
  startDate?: string
  endDate?: string
  search?: string
  page?: number
  limit?: number
}

export interface AuditLogResponse {
  auditLogs: AuditLog[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface AuditLogStats {
  totalLogs: number
  actionStats: { action: string; count: number }[]
  entityStats: { entityType: string; count: number }[]
  recentActivity: AuditLog[]
}

class AuditLogsService {
  async getAuditLogs(filters: AuditLogFilters = {}): Promise<AuditLogResponse> {
    return apiClient.get<AuditLogResponse>(API_CONFIG.ENDPOINTS.AUDIT_LOGS.BASE, filters)
  }

  async getAuditLogById(id: string): Promise<AuditLog> {
    return apiClient.get<AuditLog>(API_CONFIG.ENDPOINTS.AUDIT_LOGS.BY_ID(id))
  }

  async getEntityAuditLogs(entityType: string, entityId: string): Promise<AuditLog[]> {
    return apiClient.get<AuditLog[]>(API_CONFIG.ENDPOINTS.AUDIT_LOGS.ENTITY(entityType, entityId))
  }

  async getUserAuditLogs(userId: string, limit?: number): Promise<AuditLog[]> {
    const params = limit ? { limit } : {}
    return apiClient.get<AuditLog[]>(API_CONFIG.ENDPOINTS.AUDIT_LOGS.USER(userId), params)
  }

  async getMyAuditLogs(limit?: number): Promise<AuditLog[]> {
    const params = limit ? { limit } : {}
    return apiClient.get<AuditLog[]>(API_CONFIG.ENDPOINTS.AUDIT_LOGS.MY_ACTIVITY, params)
  }

  async getAuditStats(): Promise<AuditLogStats> {
    return apiClient.get<AuditLogStats>(API_CONFIG.ENDPOINTS.AUDIT_LOGS.STATS)
  }

  // Helper methods for formatting
  formatAction(action: string): string {
    return action.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  formatEntityType(entityType: string): string {
    return entityType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  getActionColor(action: string): 'primary' | 'success' | 'warning' | 'error' | 'info' | 'default' {
    switch (action.toLowerCase()) {
      case 'create':
        return 'success'
      case 'update':
        return 'info'
      case 'delete':
        return 'error'
      case 'login':
        return 'primary'
      case 'logout':
        return 'default'
      case 'export':
        return 'info'
      case 'print':
        return 'info'
      case 'assign':
        return 'primary'
      case 'unassign':
        return 'warning'
      case 'status_change':
        return 'warning'
      case 'condition_change':
        return 'warning'
      default:
        return 'default'
    }
  }

  getEntityTypeColor(entityType: string): 'primary' | 'success' | 'warning' | 'error' | 'info' | 'default' {
    switch (entityType.toLowerCase()) {
      case 'asset':
        return 'primary'
      case 'asset_item':
        return 'info'
      case 'category':
        return 'success'
      case 'location':
        return 'warning'
      case 'user':
        return 'info'
      case 'system':
        return 'default'
      default:
        return 'default'
    }
  }
}

export const auditLogsService = new AuditLogsService()
