{"c": ["app/layout", "app/(dashboard)/locations/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@mui/icons-material/esm/Map.js", "(app-pages-browser)/./node_modules/@mui/icons-material/esm/Room.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Clocations%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/(dashboard)/locations/page.tsx"]}