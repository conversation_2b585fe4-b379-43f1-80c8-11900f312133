'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Grid,
  Box,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  Category as CategoryIcon,
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon
} from '@mui/icons-material'
import { SimpleTreeView } from '@mui/x-tree-view/SimpleTreeView'
import { TreeItem } from '@mui/x-tree-view/TreeItem'
import { categoriesService } from '@/lib/api'
import { Category } from '@/types/api'

// Mock data for development (fallback)
const mockCategories = [
  {
    id: '1',
    name: 'IT Equipment',
    code: 'IT',
    description: 'All IT related equipment and devices',
    isActive: true,
    parentId: null,
    children: [
      {
        id: '2',
        name: 'Computers',
        code: 'COMP',
        description: 'Desktop and laptop computers',
        isActive: true,
        parentId: '1',
        children: []
      },
      {
        id: '3',
        name: 'Peripherals',
        code: 'PERI',
        description: 'Keyboards, mice, monitors, etc.',
        isActive: true,
        parentId: '1',
        children: []
      }
    ]
  },
  {
    id: '4',
    name: 'Furniture',
    code: 'FURN',
    description: 'Office furniture and fixtures',
    isActive: true,
    parentId: null,
    children: [
      {
        id: '5',
        name: 'Chairs',
        code: 'CHAIR',
        description: 'Office chairs and seating',
        isActive: true,
        parentId: '4',
        children: []
      },
      {
        id: '6',
        name: 'Desks',
        code: 'DESK',
        description: 'Office desks and tables',
        isActive: true,
        parentId: '4',
        children: []
      }
    ]
  },
  {
    id: '7',
    name: 'Vehicles',
    code: 'VEH',
    description: 'Company vehicles',
    isActive: true,
    parentId: null,
    children: []
  }
]

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [viewMode, setViewMode] = useState<'tree' | 'table'>('tree')

  // Load categories from API
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoading(true)
        setError(null)

        const data = await categoriesService.getCategories()
        setCategories(data)
      } catch (err: any) {
        console.error('Failed to load categories:', err)
        if (err.message?.includes('401') || err.message?.includes('Unauthorized')) {
          setError('Authentication failed. Please log in again.')
          setTimeout(() => {
            window.location.href = '/login'
          }, 2000)
        } else {
          setError('Failed to load categories. Please check if the backend server is running and try again.')
        }
      } finally {
        setLoading(false)
      }
    }

    loadCategories()
  }, [])

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    parentId: '',
    isActive: true
  })

  const handleAddCategory = () => {
    setFormData({
      name: '',
      code: '',
      description: '',
      parentId: '',
      isActive: true
    })
    setIsEditing(false)
    setDialogOpen(true)
  }

  const handleEditCategory = (category: Category) => {
    setFormData({
      name: category.name,
      code: category.code,
      description: category.description,
      parentId: category.parentId || '',
      isActive: category.isActive
    })
    setSelectedCategory(category)
    setIsEditing(true)
    setDialogOpen(true)
  }

  const handleDeleteCategory = (category: Category) => {
    setSelectedCategory(category)
    setDeleteDialogOpen(true)
  }

  const handleSaveCategory = async () => {
    try {
      if (isEditing && selectedCategory) {
        // Update existing category
        const updatedCategory = await categoriesService.updateCategory(selectedCategory.id, {
          name: formData.name,
          code: formData.code,
          description: formData.description,
          parentId: formData.parentId || null,
          isActive: formData.isActive
        })

        setCategories(categories.map(cat => (cat.id === selectedCategory.id ? updatedCategory : cat)))
      } else {
        // Create new category
        const newCategory = await categoriesService.createCategory({
          name: formData.name,
          code: formData.code,
          description: formData.description,
          parentId: formData.parentId || null,
          isActive: formData.isActive
        })

        setCategories([...categories, newCategory])
      }

      setDialogOpen(false)
      setSelectedCategory(null)
    } catch (err) {
      console.error('Failed to save category:', err)
      setError('Failed to save category. Please try again.')
    }
  }

  const confirmDelete = async () => {
    if (!selectedCategory) return

    try {
      await categoriesService.deleteCategory(selectedCategory.id)
      setCategories(categories.filter(cat => cat.id !== selectedCategory.id))
      setDeleteDialogOpen(false)
      setSelectedCategory(null)
    } catch (err) {
      console.error('Failed to delete category:', err)
      setError('Failed to delete category. Please try again.')
    }
  }

  const getAllCategories = (cats: Category[]): Category[] => {
    let result: Category[] = []
    cats.forEach(cat => {
      result.push(cat)
      if (cat.children && cat.children.length > 0) {
        result = result.concat(getAllCategories(cat.children))
      }
    })
    return result
  }

  const renderTreeItem = (category: Category) => (
    <TreeItem
      key={category.id}
      itemId={category.id}
      label={
        <Box sx={{ display: 'flex', alignItems: 'center', py: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            {/* {category.children.length > 0 ? <FolderIcon sx={{ mr: 1 }} /> : <CategoryIcon sx={{ mr: 1 }} />} */}
            <Typography variant='body1' sx={{ mr: 2 }}>
              {category.name}
            </Typography>
            <Chip label={category.code} size='small' variant='outlined' sx={{ mr: 1 }} />
            {!category.isActive && <Chip label='Inactive' size='small' color='error' sx={{ mr: 1 }} />}
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title='Edit'>
              <IconButton
                size='small'
                onClick={e => {
                  e.stopPropagation()
                  handleEditCategory(category)
                }}
              >
                <EditIcon fontSize='small' />
              </IconButton>
            </Tooltip>
            <Tooltip title='Delete'>
              <IconButton
                size='small'
                onClick={e => {
                  e.stopPropagation()
                  handleDeleteCategory(category)
                }}
              >
                <DeleteIcon fontSize='small' />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      }
    >
      {category.children && category.children.map(child => renderTreeItem(child))}
    </TreeItem>
  )

  const flatCategories = getAllCategories(categories)

  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading categories...</Typography>
      </Box>
    )
  }

  return (
    <Box sx={{ p: 4, backgroundColor: 'grey.50', minHeight: '100vh' }}>
      {/* Error Alert */}
      {error && (
        <Alert severity='error' sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant='h4' component='h1'>
          Category Management
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControlLabel
            control={
              <Switch checked={viewMode === 'tree'} onChange={e => setViewMode(e.target.checked ? 'tree' : 'table')} />
            }
            label='Tree View'
          />
          <Button variant='contained' startIcon={<AddIcon />} onClick={handleAddCategory} color='primary'>
            Add Category
          </Button>
        </Box>
      </Box>

      {/* Categories Display */}
      <Card
        sx={{
          border: '1px solid',
          borderColor: 'divider',
          boxShadow: 'none'
        }}
      >
        <CardContent>
          {viewMode === 'tree' ? (
            <SimpleTreeView
              defaultCollapseIcon={<ExpandMoreIcon />}
              defaultExpandIcon={<ChevronRightIcon />}
              defaultExpandedItems={categories.map(cat => cat.id)}
            >
              {categories.map(category => renderTreeItem(category))}
            </SimpleTreeView>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Code</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Parent</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell align='center'>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {flatCategories.map(category => (
                    <TableRow key={category.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {category.children && category.children.length > 0 ? (
                            <FolderIcon sx={{ mr: 1 }} />
                          ) : (
                            <CategoryIcon sx={{ mr: 1 }} />
                          )}
                          {category.name}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip label={category.code} size='small' variant='outlined' />
                      </TableCell>
                      <TableCell>{category.description}</TableCell>
                      <TableCell>
                        {category.parentId ? flatCategories.find(c => c.id === category.parentId)?.name || '-' : 'Root'}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={category.isActive ? 'Active' : 'Inactive'}
                          color={category.isActive ? 'success' : 'error'}
                          size='small'
                        />
                      </TableCell>
                      <TableCell align='center'>
                        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                          <Tooltip title='Edit'>
                            <IconButton size='small' onClick={() => handleEditCategory(category)}>
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title='Delete'>
                            <IconButton size='small' onClick={() => handleDeleteCategory(category)}>
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Category Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth='sm' fullWidth>
        <DialogTitle>{isEditing ? 'Edit Category' : 'Add New Category'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={8}>
              <TextField
                fullWidth
                label='Category Name'
                value={formData.name}
                onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label='Code'
                value={formData.code}
                onChange={e => setFormData(prev => ({ ...prev, code: e.target.value.toUpperCase() }))}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Description'
                value={formData.description}
                onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
                multiline
                rows={3}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Parent Category</InputLabel>
                <Select
                  value={formData.parentId}
                  label='Parent Category'
                  onChange={e => setFormData(prev => ({ ...prev, parentId: e.target.value }))}
                >
                  <MenuItem value=''>None (Root Category)</MenuItem>
                  {flatCategories
                    .filter(cat => !isEditing || cat.id !== selectedCategory?.id)
                    .map(category => (
                      <MenuItem key={category.id} value={category.id}>
                        {category.name} ({category.code})
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={e => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  />
                }
                label='Active'
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveCategory} variant='contained'>
            {isEditing ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete category "{selectedCategory?.name}"?
            {selectedCategory?.children && selectedCategory.children.length > 0
              ? ' This will also delete all subcategories.'
              : ''}
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color='error' variant='contained'>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}
