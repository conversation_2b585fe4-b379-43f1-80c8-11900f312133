// Classes for vertical layout
export const verticalLayoutClasses = {
  root: 'ts-vertical-layout',
  contentWrapper: 'ts-vertical-layout-content-wrapper',
  header: 'ts-vertical-layout-header',
  headerStatic: 'ts-vertical-layout-header-static',
  headerDetached: 'ts-vertical-layout-header-detached',
  headerContentCompact: 'ts-vertical-layout-header-content-compact',
  navbar: 'ts-vertical-layout-navbar',
  navbarContent: 'ts-vertical-layout-navbar-content',
  content: 'ts-vertical-layout-content',
  contentCompact: 'ts-vertical-layout-content-compact',
  footer: 'ts-vertical-layout-footer',
  footerStatic: 'ts-vertical-layout-footer-static',
  footerDetached: 'ts-vertical-layout-footer-detached',
  footerContentWrapper: 'ts-vertical-layout-footer-content-wrapper',
  footerContent: 'ts-vertical-layout-footer-content',
  footerContentCompact: 'ts-vertical-layout-footer-content-compact'
}

// Classes for blank layout
export const blankLayoutClasses = {
  root: 'ts-blank-layout'
}
