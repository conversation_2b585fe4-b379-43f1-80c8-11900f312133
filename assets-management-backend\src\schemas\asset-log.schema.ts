import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type AssetLogDocument = AssetLog & Document;

export enum LogAction {
  CREATED = 'created',
  UPDATED = 'updated',
  DELETED = 'deleted',
  ASSIGNED = 'assigned',
  UNASSIGNED = 'unassigned',
  STATUS_CHANGED = 'status_changed',
  LOCATION_CHANGED = 'location_changed',
  MAINTENANCE = 'maintenance',
  RETIRED = 'retired',
}

@Schema({ timestamps: true })
export class AssetLog {
  @Prop({ required: true })
  assetId: string;

  @Prop({ required: true })
  assetNumber: string;

  @Prop({ required: true, enum: LogAction })
  action: LogAction;

  @Prop({ required: true })
  performedBy: string; // User ID

  @Prop({ required: true })
  performedByName: string; // User full name for easier querying

  @Prop({ type: Object })
  previousData: Record<string, any>;

  @Prop({ type: Object })
  newData: Record<string, any>;

  @Prop()
  description: string;

  @Prop()
  ipAddress: string;

  @Prop()
  userAgent: string;

  @Prop({ default: Date.now })
  timestamp: Date;
}

export const AssetLogSchema = SchemaFactory.createForClass(AssetLog);
