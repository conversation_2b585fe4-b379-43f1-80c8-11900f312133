"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuantityTransfer = exports.TransferType = void 0;
const typeorm_1 = require("typeorm");
const asset_entity_1 = require("./asset.entity");
const user_entity_1 = require("./user.entity");
var TransferType;
(function (TransferType) {
    TransferType["MANUAL"] = "manual";
    TransferType["STATUS_CHANGE"] = "status_change";
    TransferType["BULK_TRANSFER"] = "bulk_transfer";
    TransferType["INITIAL_STOCK"] = "initial_stock";
})(TransferType || (exports.TransferType = TransferType = {}));
let QuantityTransfer = class QuantityTransfer {
    id;
    asset;
    assetId;
    fromStatus;
    toStatus;
    quantity;
    transferType;
    reason;
    notes;
    transferredBy;
    transferredById;
    transferredAt;
    batchId;
    metadata;
};
exports.QuantityTransfer = QuantityTransfer;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], QuantityTransfer.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => asset_entity_1.Asset, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'assetId' }),
    __metadata("design:type", asset_entity_1.Asset)
], QuantityTransfer.prototype, "asset", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], QuantityTransfer.prototype, "assetId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: asset_entity_1.AssetStatus,
        nullable: true,
    }),
    __metadata("design:type", Object)
], QuantityTransfer.prototype, "fromStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: asset_entity_1.AssetStatus,
    }),
    __metadata("design:type", String)
], QuantityTransfer.prototype, "toStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], QuantityTransfer.prototype, "quantity", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: TransferType,
        default: TransferType.MANUAL,
    }),
    __metadata("design:type", String)
], QuantityTransfer.prototype, "transferType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], QuantityTransfer.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], QuantityTransfer.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { onDelete: 'SET NULL' }),
    (0, typeorm_1.JoinColumn)({ name: 'transferredBy' }),
    __metadata("design:type", user_entity_1.User)
], QuantityTransfer.prototype, "transferredBy", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], QuantityTransfer.prototype, "transferredById", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], QuantityTransfer.prototype, "transferredAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], QuantityTransfer.prototype, "batchId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], QuantityTransfer.prototype, "metadata", void 0);
exports.QuantityTransfer = QuantityTransfer = __decorate([
    (0, typeorm_1.Entity)('quantity_transfers')
], QuantityTransfer);
//# sourceMappingURL=quantity-transfer.entity.js.map