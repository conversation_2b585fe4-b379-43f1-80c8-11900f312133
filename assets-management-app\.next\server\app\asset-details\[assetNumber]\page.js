/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/asset-details/[assetNumber]/page";
exports.ids = ["app/asset-details/[assetNumber]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fasset-details%2F%5BassetNumber%5D%2Fpage&page=%2Fasset-details%2F%5BassetNumber%5D%2Fpage&appPaths=%2Fasset-details%2F%5BassetNumber%5D%2Fpage&pagePath=private-next-app-dir%2Fasset-details%2F%5BassetNumber%5D%2Fpage.tsx&appDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fasset-details%2F%5BassetNumber%5D%2Fpage&page=%2Fasset-details%2F%5BassetNumber%5D%2Fpage&appPaths=%2Fasset-details%2F%5BassetNumber%5D%2Fpage&pagePath=private-next-app-dir%2Fasset-details%2F%5BassetNumber%5D%2Fpage.tsx&appDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'asset-details',\n        {\n        children: [\n        '[assetNumber]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/asset-details/[assetNumber]/page.tsx */ \"(rsc)/./src/app/asset-details/[assetNumber]/page.tsx\")), \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/asset-details/[assetNumber]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/asset-details/[assetNumber]/page\",\n        pathname: \"/asset-details/[assetNumber]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fasset-details%2F%5BassetNumber%5D%2Fpage&page=%2Fasset-details%2F%5BassetNumber%5D%2Fpage&appPaths=%2Fasset-details%2F%5BassetNumber%5D%2Fpage&pagePath=private-next-app-dir%2Fasset-details%2F%5BassetNumber%5D%2Fpage.tsx&appDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNJbnZpY3RhJTVDJTVDVXBjb21pbmclMjBQcm9qZWN0cyU1QyU1Q0Fzc2V0cyUyME1hbmFnZW1lbnQlMjBTeXN0ZW0lNUMlNUNhc3NldHMtbWFuYWdlbWVudC1hcHAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhcHAtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNJbnZpY3RhJTVDJTVDVXBjb21pbmclMjBQcm9qZWN0cyU1QyU1Q0Fzc2V0cyUyME1hbmFnZW1lbnQlMjBTeXN0ZW0lNUMlNUNhc3NldHMtbWFuYWdlbWVudC1hcHAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDSW52aWN0YSU1QyU1Q1VwY29taW5nJTIwUHJvamVjdHMlNUMlNUNBc3NldHMlMjBNYW5hZ2VtZW50JTIwU3lzdGVtJTVDJTVDYXNzZXRzLW1hbmFnZW1lbnQtYXBwJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q0ludmljdGElNUMlNUNVcGNvbWluZyUyMFByb2plY3RzJTVDJTVDQXNzZXRzJTIwTWFuYWdlbWVudCUyMFN5c3RlbSU1QyU1Q2Fzc2V0cy1tYW5hZ2VtZW50LWFwcCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q0ludmljdGElNUMlNUNVcGNvbWluZyUyMFByb2plY3RzJTVDJTVDQXNzZXRzJTIwTWFuYWdlbWVudCUyMFN5c3RlbSU1QyU1Q2Fzc2V0cy1tYW5hZ2VtZW50LWFwcCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q25vdC1mb3VuZC1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDSW52aWN0YSU1QyU1Q1VwY29taW5nJTIwUHJvamVjdHMlNUMlNUNBc3NldHMlMjBNYW5hZ2VtZW50JTIwU3lzdGVtJTVDJTVDYXNzZXRzLW1hbmFnZW1lbnQtYXBwJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQWlMO0FBQ2pMO0FBQ0Esb09BQWtMO0FBQ2xMO0FBQ0EsME9BQXFMO0FBQ3JMO0FBQ0Esd09BQW9MO0FBQ3BMO0FBQ0Esa1BBQXlMO0FBQ3pMO0FBQ0Esc1FBQW1NIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAtdGVzdC8/YmI5NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXEludmljdGFcXFxcVXBjb21pbmcgUHJvamVjdHNcXFxcQXNzZXRzIE1hbmFnZW1lbnQgU3lzdGVtXFxcXGFzc2V0cy1tYW5hZ2VtZW50LWFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXEludmljdGFcXFxcVXBjb21pbmcgUHJvamVjdHNcXFxcQXNzZXRzIE1hbmFnZW1lbnQgU3lzdGVtXFxcXGFzc2V0cy1tYW5hZ2VtZW50LWFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxJbnZpY3RhXFxcXFVwY29taW5nIFByb2plY3RzXFxcXEFzc2V0cyBNYW5hZ2VtZW50IFN5c3RlbVxcXFxhc3NldHMtbWFuYWdlbWVudC1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcSW52aWN0YVxcXFxVcGNvbWluZyBQcm9qZWN0c1xcXFxBc3NldHMgTWFuYWdlbWVudCBTeXN0ZW1cXFxcYXNzZXRzLW1hbmFnZW1lbnQtYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcSW52aWN0YVxcXFxVcGNvbWluZyBQcm9qZWN0c1xcXFxBc3NldHMgTWFuYWdlbWVudCBTeXN0ZW1cXFxcYXNzZXRzLW1hbmFnZW1lbnQtYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbm90LWZvdW5kLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxJbnZpY3RhXFxcXFVwY29taW5nIFByb2plY3RzXFxcXEFzc2V0cyBNYW5hZ2VtZW50IFN5c3RlbVxcXFxhc3NldHMtbWFuYWdlbWVudC1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Creact-perfect-scrollbar%5C%5Cdist%5C%5Ccss%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Cassets%5C%5Ciconify-icons%5C%5Cgenerated-icons.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Cnode_modules%5C%5Creact-perfect-scrollbar%5C%5Cdist%5C%5Ccss%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Cassets%5C%5Ciconify-icons%5C%5Cgenerated-icons.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Capp%5C%5Casset-details%5C%5C%5BassetNumber%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Capp%5C%5Casset-details%5C%5C%5BassetNumber%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/asset-details/[assetNumber]/page.tsx */ \"(ssr)/./src/app/asset-details/[assetNumber]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNJbnZpY3RhJTVDJTVDVXBjb21pbmclMjBQcm9qZWN0cyU1QyU1Q0Fzc2V0cyUyME1hbmFnZW1lbnQlMjBTeXN0ZW0lNUMlNUNhc3NldHMtbWFuYWdlbWVudC1hcHAlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhc3NldC1kZXRhaWxzJTVDJTVDJTVCYXNzZXROdW1iZXIlNUQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd01BQXNLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAtdGVzdC8/OWVhNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXEludmljdGFcXFxcVXBjb21pbmcgUHJvamVjdHNcXFxcQXNzZXRzIE1hbmFnZW1lbnQgU3lzdGVtXFxcXGFzc2V0cy1tYW5hZ2VtZW50LWFwcFxcXFxzcmNcXFxcYXBwXFxcXGFzc2V0LWRldGFpbHNcXFxcW2Fzc2V0TnVtYmVyXVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Capp%5C%5Casset-details%5C%5C%5BassetNumber%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/asset-details/[assetNumber]/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/asset-details/[assetNumber]/page.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AssetDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/QrCode.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Info.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Category.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/CalendarToday.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Print.js\");\n/* harmony import */ var _lib_api_assetItemService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/assetItemService */ \"(ssr)/./src/lib/api/assetItemService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction AssetDetailsPage() {\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const assetNumber = params.assetNumber;\n    const [assetItem, setAssetItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Authentication hooks\n    const { user, isAuthenticated, loading: authLoading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { isAdmin } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRole)();\n    // Check authentication first\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!authLoading) {\n            if (!isAuthenticated || !isAdmin()) {\n                // Redirect to login with return URL\n                const currentUrl = `/asset-details/${assetNumber}`;\n                const returnUrl = encodeURIComponent(currentUrl);\n                router.push(`/login?returnUrl=${returnUrl}`);\n                return;\n            }\n        }\n    }, [\n        authLoading,\n        isAuthenticated,\n        isAdmin,\n        assetNumber,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAssetDetails = async ()=>{\n            // Don't load data if not authenticated\n            if (!isAuthenticated || !isAdmin()) {\n                return;\n            }\n            try {\n                setLoading(true);\n                setError(null);\n                console.log(\"Loading asset details for asset number:\", assetNumber);\n                // Use the new direct API endpoint\n                const assetItemData = await _lib_api_assetItemService__WEBPACK_IMPORTED_MODULE_4__.assetItemService.getAssetItemByAssetNumber(assetNumber);\n                console.log(\"Loaded asset item data:\", assetItemData);\n                setAssetItem(assetItemData);\n            } catch (err) {\n                console.error(\"Failed to load asset details:\", err);\n                setError(`Failed to load asset details: ${err instanceof Error ? err.message : \"Unknown error\"}`);\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (assetNumber && !authLoading && isAuthenticated && isAdmin()) {\n            loadAssetDetails();\n        }\n    }, [\n        assetNumber,\n        authLoading,\n        isAuthenticated,\n        isAdmin\n    ]);\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case \"in_stock\":\n                return \"success\";\n            case \"in_use\":\n                return \"primary\";\n            case \"maintenance\":\n                return \"warning\";\n            case \"retired\":\n                return \"error\";\n            default:\n                return \"default\";\n        }\n    };\n    const getConditionColor = (condition)=>{\n        switch(condition.toLowerCase()){\n            case \"excellent\":\n                return \"success\";\n            case \"good\":\n                return \"info\";\n            case \"fair\":\n                return \"warning\";\n            case \"poor\":\n                return \"error\";\n            default:\n                return \"default\";\n        }\n    };\n    const formatStatus = (status)=>{\n        return status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase());\n    };\n    const formatCondition = (condition)=>{\n        return condition.charAt(0).toUpperCase() + condition.slice(1).toLowerCase();\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        }).format(amount);\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"Not set\";\n        return new Date(dateString).toLocaleDateString();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            maxWidth: \"md\",\n            sx: {\n                py: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"50vh\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n            lineNumber: 174,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !assetItem) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            maxWidth: \"md\",\n            sx: {\n                py: 4\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    severity: \"error\",\n                    sx: {\n                        mb: 2\n                    },\n                    children: error || \"Asset not found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    variant: \"contained\",\n                    href: \"/assets\",\n                    children: \"Back to Assets\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        maxWidth: \"md\",\n        sx: {\n            py: 4\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    mb: 4,\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        sx: {\n                            mx: \"auto\",\n                            mb: 2,\n                            bgcolor: \"primary.main\",\n                            width: 64,\n                            height: 64\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            sx: {\n                                fontSize: 32\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        variant: \"h4\",\n                        component: \"h1\",\n                        gutterBottom: true,\n                        children: assetItem.asset.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        variant: \"h6\",\n                        color: \"text.secondary\",\n                        gutterBottom: true,\n                        children: [\n                            \"Asset Number: \",\n                            assetItem.assetNumber\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            gap: 1,\n                            mb: 2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                label: formatStatus(assetItem.status),\n                                color: getStatusColor(assetItem.status),\n                                variant: \"filled\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                label: formatCondition(assetItem.condition),\n                                color: getConditionColor(assetItem.condition),\n                                variant: \"outlined\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    mb: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"h6\",\n                            gutterBottom: true,\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    color: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                \"Asset Information\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.asset.description || \"No description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Unit Price\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: formatCurrency(assetItem.asset.unitPrice)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Manufacturer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.asset.manufacturer || \"Not specified\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Model\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.asset.model || \"Not specified\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                container: true,\n                spacing: 2,\n                sx: {\n                    mb: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                color: \"primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Category\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"body1\",\n                                        children: assetItem.asset.category.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                color: \"primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Location\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"body1\",\n                                        children: assetItem.asset.location.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"body2\",\n                                        color: \"text.secondary\",\n                                        children: [\n                                            \"Type: \",\n                                            assetItem.asset.location.type\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    mb: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"h6\",\n                            gutterBottom: true,\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    color: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                \"Item Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Serial Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.serialNumber || \"Not set\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Purchase Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: formatDate(assetItem.purchaseDate)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Warranty Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: formatDate(assetItem.warrantyDate)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                assetItem.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Notes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.notes\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this),\n            assetItem.assignedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    mb: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"h6\",\n                            gutterBottom: true,\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    color: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this),\n                                \"Assigned To\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    sx: {\n                                        bgcolor: \"primary.main\"\n                                    },\n                                    children: assetItem.assignedUser.name.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.assignedUser.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: assetItem.assignedUser.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 327,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    textAlign: \"center\",\n                    mt: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        variant: \"contained\",\n                        href: \"/assets\",\n                        sx: {\n                            mr: 2\n                        },\n                        children: \"Back to Assets\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        variant: \"outlined\",\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 47\n                        }, void 0),\n                        onClick: ()=>window.print(),\n                        children: \"Print Details\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/asset-details/[assetNumber]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useApi.ts":
/*!*****************************!*\
  !*** ./src/hooks/useApi.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useApi: () => (/* binding */ useApi),\n/* harmony export */   useAsyncApi: () => (/* binding */ useAsyncApi),\n/* harmony export */   useMutation: () => (/* binding */ useMutation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useApi(apiFunction, options = {}) {\n    const { immediate = true, onSuccess, onError } = options;\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        data: null,\n        loading: false,\n        error: null\n    });\n    const execute = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        setState((prev)=>({\n                ...prev,\n                loading: true,\n                error: null\n            }));\n        try {\n            const result = await apiFunction();\n            setState({\n                data: result,\n                loading: false,\n                error: null\n            });\n            onSuccess?.(result);\n            return result;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"An error occurred\";\n            setState((prev)=>({\n                    ...prev,\n                    loading: false,\n                    error: errorMessage\n                }));\n            onError?.(errorMessage);\n            throw error;\n        }\n    }, [\n        apiFunction,\n        onSuccess,\n        onError\n    ]);\n    const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setState({\n            data: null,\n            loading: false,\n            error: null\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (immediate) {\n            execute();\n        }\n    }, [\n        execute,\n        immediate\n    ]);\n    return {\n        ...state,\n        execute,\n        reset\n    };\n}\nfunction useAsyncApi(apiFunction, options = {}) {\n    const { onSuccess, onError } = options;\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        data: null,\n        loading: false,\n        error: null\n    });\n    const execute = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (...args)=>{\n        setState((prev)=>({\n                ...prev,\n                loading: true,\n                error: null\n            }));\n        try {\n            const result = await apiFunction(...args);\n            setState({\n                data: result,\n                loading: false,\n                error: null\n            });\n            onSuccess?.(result);\n            return result;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"An error occurred\";\n            setState((prev)=>({\n                    ...prev,\n                    loading: false,\n                    error: errorMessage\n                }));\n            onError?.(errorMessage);\n            throw error;\n        }\n    }, [\n        apiFunction,\n        onSuccess,\n        onError\n    ]);\n    const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setState({\n            data: null,\n            loading: false,\n            error: null\n        });\n    }, []);\n    return {\n        ...state,\n        execute,\n        reset\n    };\n}\n// Mutation hook for create/update/delete operations\nfunction useMutation(mutationFunction, options = {}) {\n    return useAsyncApi(mutationFunction, options);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useApi.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useRole: () => (/* binding */ useRole)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api/index.ts\");\n/* harmony import */ var _useApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useApi */ \"(ssr)/./src/hooks/useApi.ts\");\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loginMutation = (0,_useApi__WEBPACK_IMPORTED_MODULE_3__.useAsyncApi)(_lib_api__WEBPACK_IMPORTED_MODULE_2__.authService.login.bind(_lib_api__WEBPACK_IMPORTED_MODULE_2__.authService), {\n        onSuccess: (response)=>{\n            setUser(response.user);\n            setError(null);\n        },\n        onError: (error)=>{\n            setError(error);\n            setUser(null);\n        }\n    });\n    const registerMutation = (0,_useApi__WEBPACK_IMPORTED_MODULE_3__.useAsyncApi)(_lib_api__WEBPACK_IMPORTED_MODULE_2__.authService.register.bind(_lib_api__WEBPACK_IMPORTED_MODULE_2__.authService), {\n        onSuccess: (response)=>{\n            setUser(response.user);\n            setError(null);\n        },\n        onError: (error)=>{\n            setError(error);\n            setUser(null);\n        }\n    });\n    const refreshUser = async ()=>{\n        if (!_lib_api__WEBPACK_IMPORTED_MODULE_2__.authService.isAuthenticated()) {\n            setUser(null);\n            setLoading(false);\n            return;\n        }\n        try {\n            setLoading(true);\n            const userData = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authService.getProfile();\n            setUser(userData);\n            setError(null);\n        } catch (error) {\n            console.error(\"Failed to refresh user:\", error);\n            setUser(null);\n            setError(error instanceof Error ? error.message : \"Failed to refresh user\");\n            _lib_api__WEBPACK_IMPORTED_MODULE_2__.authService.logout();\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (credentials)=>{\n        await loginMutation.execute(credentials);\n    };\n    const register = async (userData)=>{\n        await registerMutation.execute(userData);\n    };\n    const logout = ()=>{\n        _lib_api__WEBPACK_IMPORTED_MODULE_2__.authService.logout();\n        setUser(null);\n        setError(null);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        refreshUser();\n    }, []);\n    const value = {\n        user,\n        loading: loading || loginMutation.loading || registerMutation.loading,\n        error: error || loginMutation.error || registerMutation.error,\n        isAuthenticated: !!user,\n        login,\n        register,\n        logout,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 97,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// Hook for checking if user has specific role\nfunction useRole() {\n    const { user } = useAuth();\n    const hasRole = (role)=>{\n        if (!user) return false;\n        if (Array.isArray(role)) {\n            return role.includes(user.role);\n        }\n        return user.role === role;\n    };\n    const isAdmin = ()=>hasRole(\"admin\");\n    const isManager = ()=>hasRole([\n            \"admin\",\n            \"manager\"\n        ]);\n    const isViewer = ()=>hasRole(\"viewer\");\n    return {\n        hasRole,\n        isAdmin,\n        isManager,\n        isViewer,\n        currentRole: user?.role\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/asset-logs.ts":
/*!***********************************!*\
  !*** ./src/lib/api/asset-logs.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssetLogsService: () => (/* binding */ AssetLogsService),\n/* harmony export */   assetLogsService: () => (/* binding */ assetLogsService)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/api/config.ts\");\n/* harmony import */ var _types_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/api */ \"(ssr)/./src/types/api.ts\");\n\n\n\nclass AssetLogsService {\n    async getRecentLogs(limit) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.RECENT, {\n            limit\n        });\n    }\n    async getAssetLogs(assetId, params) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.BY_ASSET(assetId), params);\n    }\n    async getUserActivityLogs(userId, params) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.BY_USER(userId), params);\n    }\n    async getLogsByAction(action, limit) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.BY_ACTION(action), {\n            limit\n        });\n    }\n    async getLogsByDateRange(params) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.DATE_RANGE, params);\n    }\n    // Helper methods for common log queries\n    async getAssetCreationLogs(limit) {\n        return this.getLogsByAction(_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.CREATED, limit);\n    }\n    async getAssetAssignmentLogs(limit) {\n        return this.getLogsByAction(_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.ASSIGNED, limit);\n    }\n    async getAssetStatusChangeLogs(limit) {\n        return this.getLogsByAction(_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.STATUS_CHANGED, limit);\n    }\n    async getTodaysLogs() {\n        const today = new Date();\n        const startOfDay = new Date(today.setHours(0, 0, 0, 0));\n        const endOfDay = new Date(today.setHours(23, 59, 59, 999));\n        return this.getLogsByDateRange({\n            startDate: startOfDay.toISOString(),\n            endDate: endOfDay.toISOString(),\n            limit: 100\n        });\n    }\n    async getWeeklyLogs() {\n        const today = new Date();\n        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);\n        return this.getLogsByDateRange({\n            startDate: weekAgo.toISOString(),\n            endDate: today.toISOString(),\n            limit: 500\n        });\n    }\n    async getMonthlyLogs() {\n        const today = new Date();\n        const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);\n        return this.getLogsByDateRange({\n            startDate: monthAgo.toISOString(),\n            endDate: today.toISOString(),\n            limit: 1000\n        });\n    }\n    // Format log action for display\n    formatLogAction(action) {\n        const actionMap = {\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.CREATED]: \"Created\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.UPDATED]: \"Updated\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.DELETED]: \"Deleted\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.ASSIGNED]: \"Assigned\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.UNASSIGNED]: \"Unassigned\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.STATUS_CHANGED]: \"Status Changed\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.LOCATION_CHANGED]: \"Location Changed\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.MAINTENANCE]: \"Maintenance\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.RETIRED]: \"Retired\"\n        };\n        return actionMap[action] || action;\n    }\n    // Get log action color for UI\n    getLogActionColor(action) {\n        const colorMap = {\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.CREATED]: \"success\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.UPDATED]: \"info\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.DELETED]: \"error\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.ASSIGNED]: \"primary\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.UNASSIGNED]: \"warning\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.STATUS_CHANGED]: \"info\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.LOCATION_CHANGED]: \"info\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.MAINTENANCE]: \"warning\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.RETIRED]: \"secondary\"\n        };\n        return colorMap[action] || \"default\";\n    }\n}\nconst assetLogsService = new AssetLogsService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/asset-logs.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/assetItemService.ts":
/*!*****************************************!*\
  !*** ./src/lib/api/assetItemService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assetItemService: () => (/* binding */ assetItemService)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/api/client.ts\");\n\nconst assetItemService = {\n    // Create multiple asset items for an asset\n    async createAssetItems (assetId, data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(`/assets/${assetId}/items`, data);\n        return response;\n    },\n    // Get all asset items for a specific asset\n    async getAssetItems (assetId) {\n        console.log(\"AssetItemService: Getting asset items for asset ID:\", assetId);\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(`/assets/${assetId}/items`);\n        console.log(\"AssetItemService: Response received:\", response);\n        return response;\n    },\n    // Get asset item by ID\n    async getAssetItem (itemId) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(`/assets/items/${itemId}`);\n        return response;\n    },\n    // Update asset item\n    async updateAssetItem (itemId, data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(`/assets/items/${itemId}`, data);\n        return response;\n    },\n    // Transfer asset item status\n    async transferAssetItem (data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/items/transfer\", data);\n        return response;\n    },\n    // Get asset item quantities by status for a specific asset\n    async getAssetItemQuantities (assetId) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(`/assets/${assetId}/items/quantities`);\n        return response;\n    },\n    // Get all asset item quantities overview\n    async getAllAssetItemQuantities () {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/items/quantities/all\");\n        return response;\n    },\n    // Get asset item by asset number (Authenticated)\n    async getAssetItemByAssetNumber (assetNumber) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(`/assets/by-asset-number/${assetNumber}`);\n        return response;\n    },\n    // Delete asset item\n    async deleteAssetItem (itemId) {\n        await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(`/assets/items/${itemId}`);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/assetItemService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/assetQuantityService.ts":
/*!*********************************************!*\
  !*** ./src/lib/api/assetQuantityService.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assetQuantityService: () => (/* binding */ assetQuantityService)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/api/client.ts\");\n\nconst assetQuantityService = {\n    // Get asset quantities by status\n    async getAssetQuantities (assetId) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(`/assets/${assetId}/quantities`);\n        return response.data;\n    },\n    // Transfer quantities between statuses\n    async transferQuantity (data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/quantities/transfer\", data);\n        return response.data;\n    },\n    // Bulk transfer quantities\n    async bulkTransferQuantity (data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/quantities/bulk-transfer\", data);\n        return response.data;\n    },\n    // Set asset quantities by status\n    async setAssetQuantities (data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/quantities/set\", data);\n        return response.data;\n    },\n    // Get transfer history for an asset\n    async getTransferHistory (assetId) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(`/assets/${assetId}/transfer-history`);\n        return response.data;\n    },\n    // Get all asset quantities overview\n    async getAllAssetQuantities () {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/quantities/all\");\n        return response.data;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/assetQuantityService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/assets.ts":
/*!*******************************!*\
  !*** ./src/lib/api/assets.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssetsService: () => (/* binding */ AssetsService),\n/* harmony export */   assetsService: () => (/* binding */ assetsService)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/api/config.ts\");\n\n\nclass AssetsService {\n    async getAssets(params) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BASE, params);\n    }\n    async getAssetById(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BY_ID(id));\n    }\n    async getAssetByNumber(assetNumber) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BY_ASSET_NUMBER(assetNumber));\n    }\n    async createAsset(data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BASE, data);\n    }\n    async updateAsset(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BY_ID(id), data);\n    }\n    async deleteAsset(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BY_ID(id));\n    }\n    async assignAsset(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.ASSIGN(id), data);\n    }\n    async unassignAsset(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.UNASSIGN(id));\n    }\n    async getAssetStats() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.STATS);\n    }\n    // Search assets with debouncing support\n    async searchAssets(searchTerm, filters) {\n        return this.getAssets({\n            search: searchTerm,\n            ...filters\n        });\n    }\n    // Get assets by category\n    async getAssetsByCategory(categoryId) {\n        return this.getAssets({\n            categoryId\n        });\n    }\n    // Get assets by location\n    async getAssetsByLocation(locationId) {\n        return this.getAssets({\n            locationId\n        });\n    }\n    // Get assets assigned to a user\n    async getAssetsByUser(userId) {\n        return this.getAssets({\n            assignedToId: userId\n        });\n    }\n    // Upload asset image\n    async uploadAssetImage(assetId, file) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.uploadFile(`/assets/${assetId}/upload-image`, file);\n    }\n}\nconst assetsService = new AssetsService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/assets.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/auth.ts":
/*!*****************************!*\
  !*** ./src/lib/api/auth.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService),\n/* harmony export */   authService: () => (/* binding */ authService)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/api/config.ts\");\n\n\nclass AuthService {\n    async login(credentials) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.LOGIN, credentials);\n        // Store the token\n        (0,_config__WEBPACK_IMPORTED_MODULE_1__.setAuthToken)(response.token);\n        return response;\n    }\n    async register(userData) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.REGISTER, userData);\n        // Store the token\n        (0,_config__WEBPACK_IMPORTED_MODULE_1__.setAuthToken)(response.token);\n        return response;\n    }\n    async getProfile() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.PROFILE);\n    }\n    async verifyToken() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.VERIFY_TOKEN);\n    }\n    logout() {\n        (0,_config__WEBPACK_IMPORTED_MODULE_1__.removeAuthToken)();\n        // Redirect to login page\n        if (false) {}\n    }\n    isAuthenticated() {\n        if (true) return false;\n        const token = localStorage.getItem(\"auth_token\");\n        return !!token;\n    }\n}\nconst authService = new AuthService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/categories.ts":
/*!***********************************!*\
  !*** ./src/lib/api/categories.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoriesService: () => (/* binding */ CategoriesService),\n/* harmony export */   categoriesService: () => (/* binding */ categoriesService)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/api/config.ts\");\n\n\nclass CategoriesService {\n    async getCategories() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BASE);\n    }\n    async getActiveCategories() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.ACTIVE);\n    }\n    async getCategoryHierarchy() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.HIERARCHY);\n    }\n    async getCategoryById(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BY_ID(id));\n    }\n    async createCategory(data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BASE, data);\n    }\n    async updateCategory(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BY_ID(id), data);\n    }\n    async deleteCategory(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BY_ID(id));\n    }\n    // Helper method to get categories as options for forms\n    async getCategoryOptions() {\n        const categories = await this.getActiveCategories();\n        return categories.map((category)=>({\n                value: category.id,\n                label: category.fullPath || category.name\n            }));\n    }\n    // Get root categories (no parent)\n    async getRootCategories() {\n        const categories = await this.getCategories();\n        return categories.filter((category)=>!category.parentId);\n    }\n    // Get subcategories of a parent category\n    async getSubcategories(parentId) {\n        const categories = await this.getCategories();\n        return categories.filter((category)=>category.parentId === parentId);\n    }\n}\nconst categoriesService = new CategoriesService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/categories.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/client.ts":
/*!*******************************!*\
  !*** ./src/lib/api/client.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/api/config.ts\");\n\nclass ApiClient {\n    constructor(){\n        this.baseURL = _config__WEBPACK_IMPORTED_MODULE_0__.API_CONFIG.BASE_URL;\n    }\n    async request(endpoint, options = {}) {\n        const url = `${this.baseURL}${endpoint}`;\n        const config = {\n            headers: (0,_config__WEBPACK_IMPORTED_MODULE_0__.getAuthHeaders)(),\n            ...options\n        };\n        console.log(\"API Client: Making request to:\", url);\n        console.log(\"API Client: Request config:\", config);\n        try {\n            const response = await fetch(url, config);\n            console.log(\"API Client: Response status:\", response.status);\n            console.log(\"API Client: Response ok:\", response.ok);\n            if (!response.ok) {\n                if (response.status === 401 || response.status === 403) {\n                    // Token expired, invalid, or insufficient permissions\n                    (0,_config__WEBPACK_IMPORTED_MODULE_0__.removeAuthToken)();\n                    // Redirect to login page\n                    if (false) {}\n                }\n                let errorData;\n                try {\n                    errorData = await response.json();\n                } catch  {\n                    errorData = {\n                        message: response.statusText || \"An error occurred\",\n                        statusCode: response.status\n                    };\n                }\n                const error = new Error(errorData.message || \"An error occurred\");\n                error.status = response.status;\n                error.response = {\n                    data: errorData\n                };\n                throw error;\n            }\n            // Handle empty responses\n            const contentType = response.headers.get(\"content-type\");\n            if (contentType && contentType.includes(\"application/json\")) {\n                const data = await response.json();\n                console.log(\"API Client: Response data:\", data);\n                return data;\n            } else {\n                console.log(\"API Client: Non-JSON response, returning empty object\");\n                return {};\n            }\n        } catch (error) {\n            if (error instanceof Error) {\n                // Check if this is a network error (backend not available)\n                if (error.message.includes(\"fetch\") || error.name === \"TypeError\") {\n                    throw new Error(\"Backend server not available. Please start the backend server or use demo mode.\");\n                }\n                throw error;\n            }\n            throw new Error(\"Network error occurred\");\n        }\n    }\n    async get(endpoint, params) {\n        const url = new URL(`${this.baseURL}${endpoint}`);\n        if (params) {\n            Object.entries(params).forEach(([key, value])=>{\n                if (value !== undefined && value !== null) {\n                    url.searchParams.append(key, String(value));\n                }\n            });\n        }\n        return this.request(url.pathname + url.search);\n    }\n    async post(endpoint, data) {\n        return this.request(endpoint, {\n            method: \"POST\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async put(endpoint, data) {\n        return this.request(endpoint, {\n            method: \"PUT\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async patch(endpoint, data) {\n        return this.request(endpoint, {\n            method: \"PATCH\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async delete(endpoint) {\n        return this.request(endpoint, {\n            method: \"DELETE\"\n        });\n    }\n    // File upload method\n    async uploadFile(endpoint, file, additionalData) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        if (additionalData) {\n            Object.entries(additionalData).forEach(([key, value])=>{\n                formData.append(key, String(value));\n            });\n        }\n        const token = (0,_config__WEBPACK_IMPORTED_MODULE_0__.getAuthHeaders)().Authorization;\n        const headers = {};\n        if (token) {\n            headers.Authorization = token;\n        }\n        return this.request(endpoint, {\n            method: \"POST\",\n            headers,\n            body: formData\n        });\n    }\n}\n// Create a singleton instance\nconst apiClient = new ApiClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/config.ts":
/*!*******************************!*\
  !*** ./src/lib/api/config.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   getAuthHeaders: () => (/* binding */ getAuthHeaders),\n/* harmony export */   getAuthToken: () => (/* binding */ getAuthToken),\n/* harmony export */   removeAuthToken: () => (/* binding */ removeAuthToken),\n/* harmony export */   setAuthToken: () => (/* binding */ setAuthToken)\n/* harmony export */ });\nconst API_CONFIG = {\n    BASE_URL: \"http://192.168.1.67:3001\" || 0,\n    TIMEOUT: 10000,\n    ENDPOINTS: {\n        // Auth\n        AUTH: {\n            LOGIN: \"/auth/login\",\n            REGISTER: \"/auth/register\",\n            PROFILE: \"/auth/profile\",\n            VERIFY_TOKEN: \"/auth/verify-token\"\n        },\n        // Users\n        USERS: {\n            BASE: \"/users\",\n            BY_ID: (id)=>`/users/${id}`,\n            CHANGE_PASSWORD: (id)=>`/users/${id}/change-password`\n        },\n        // Assets\n        ASSETS: {\n            BASE: \"/assets\",\n            BY_ID: (id)=>`/assets/${id}`,\n            BY_ASSET_NUMBER: (assetNumber)=>`/assets/asset-number/${assetNumber}`,\n            ASSIGN: (id)=>`/assets/${id}/assign`,\n            UNASSIGN: (id)=>`/assets/${id}/unassign`,\n            STATS: \"/assets/stats\"\n        },\n        // Categories\n        CATEGORIES: {\n            BASE: \"/categories\",\n            BY_ID: (id)=>`/categories/${id}`,\n            ACTIVE: \"/categories/active\",\n            HIERARCHY: \"/categories/hierarchy\"\n        },\n        // Locations\n        LOCATIONS: {\n            BASE: \"/locations\",\n            BY_ID: (id)=>`/locations/${id}`,\n            ACTIVE: \"/locations/active\",\n            HIERARCHY: \"/locations/hierarchy\"\n        },\n        // Asset Logs\n        ASSET_LOGS: {\n            RECENT: \"/asset-logs/recent\",\n            BY_ASSET: (assetId)=>`/asset-logs/asset/${assetId}`,\n            BY_USER: (userId)=>`/asset-logs/user/${userId}`,\n            BY_ACTION: (action)=>`/asset-logs/action/${action}`,\n            DATE_RANGE: \"/asset-logs/date-range\"\n        }\n    }\n};\nconst getAuthToken = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"auth_token\");\n};\nconst setAuthToken = (token)=>{\n    if (true) return;\n    localStorage.setItem(\"auth_token\", token);\n};\nconst removeAuthToken = ()=>{\n    if (true) return;\n    localStorage.removeItem(\"auth_token\");\n};\nconst getAuthHeaders = ()=>{\n    const token = getAuthToken();\n    console.log(\"getAuthHeaders: Token retrieved:\", !!token);\n    console.log(\"getAuthHeaders: Token value:\", token ? `${token.substring(0, 20)}...` : \"null\");\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...token && {\n            Authorization: `Bearer ${token}`\n        }\n    };\n    console.log(\"getAuthHeaders: Headers created:\", Object.keys(headers));\n    return headers;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2FwaS9jb25maWcudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBTyxNQUFNQSxhQUFhO0lBQ3hCQyxVQUFVQywwQkFBK0IsSUFBSTtJQUM3Q0csU0FBUztJQUNUQyxXQUFXO1FBQ1QsT0FBTztRQUNQQyxNQUFNO1lBQ0pDLE9BQU87WUFDUEMsVUFBVTtZQUNWQyxTQUFTO1lBQ1RDLGNBQWM7UUFDaEI7UUFDQSxRQUFRO1FBQ1JDLE9BQU87WUFDTEMsTUFBTTtZQUNOQyxPQUFPLENBQUNDLEtBQWUsQ0FBQyxPQUFPLEVBQUVBLEdBQUcsQ0FBQztZQUNyQ0MsaUJBQWlCLENBQUNELEtBQWUsQ0FBQyxPQUFPLEVBQUVBLEdBQUcsZ0JBQWdCLENBQUM7UUFDakU7UUFDQSxTQUFTO1FBQ1RFLFFBQVE7WUFDTkosTUFBTTtZQUNOQyxPQUFPLENBQUNDLEtBQWUsQ0FBQyxRQUFRLEVBQUVBLEdBQUcsQ0FBQztZQUN0Q0csaUJBQWlCLENBQUNDLGNBQXdCLENBQUMscUJBQXFCLEVBQUVBLFlBQVksQ0FBQztZQUMvRUMsUUFBUSxDQUFDTCxLQUFlLENBQUMsUUFBUSxFQUFFQSxHQUFHLE9BQU8sQ0FBQztZQUM5Q00sVUFBVSxDQUFDTixLQUFlLENBQUMsUUFBUSxFQUFFQSxHQUFHLFNBQVMsQ0FBQztZQUNsRE8sT0FBTztRQUNUO1FBQ0EsYUFBYTtRQUNiQyxZQUFZO1lBQ1ZWLE1BQU07WUFDTkMsT0FBTyxDQUFDQyxLQUFlLENBQUMsWUFBWSxFQUFFQSxHQUFHLENBQUM7WUFDMUNTLFFBQVE7WUFDUkMsV0FBVztRQUNiO1FBQ0EsWUFBWTtRQUNaQyxXQUFXO1lBQ1RiLE1BQU07WUFDTkMsT0FBTyxDQUFDQyxLQUFlLENBQUMsV0FBVyxFQUFFQSxHQUFHLENBQUM7WUFDekNTLFFBQVE7WUFDUkMsV0FBVztRQUNiO1FBQ0EsYUFBYTtRQUNiRSxZQUFZO1lBQ1ZDLFFBQVE7WUFDUkMsVUFBVSxDQUFDQyxVQUFvQixDQUFDLGtCQUFrQixFQUFFQSxRQUFRLENBQUM7WUFDN0RDLFNBQVMsQ0FBQ0MsU0FBbUIsQ0FBQyxpQkFBaUIsRUFBRUEsT0FBTyxDQUFDO1lBQ3pEQyxXQUFXLENBQUNDLFNBQW1CLENBQUMsbUJBQW1CLEVBQUVBLE9BQU8sQ0FBQztZQUM3REMsWUFBWTtRQUNkO0lBQ0Y7QUFDRixFQUFVO0FBRUgsTUFBTUMsZUFBZTtJQUMxQixJQUFJLElBQWtCLEVBQWEsT0FBTztJQUMxQyxPQUFPQyxhQUFhQyxPQUFPLENBQUM7QUFDOUIsRUFBQztBQUVNLE1BQU1DLGVBQWUsQ0FBQ0M7SUFDM0IsSUFBSSxJQUFrQixFQUFhO0lBQ25DSCxhQUFhSSxPQUFPLENBQUMsY0FBY0Q7QUFDckMsRUFBQztBQUVNLE1BQU1FLGtCQUFrQjtJQUM3QixJQUFJLElBQWtCLEVBQWE7SUFDbkNMLGFBQWFNLFVBQVUsQ0FBQztBQUMxQixFQUFDO0FBRU0sTUFBTUMsaUJBQWlCO0lBQzVCLE1BQU1KLFFBQVFKO0lBQ2RTLFFBQVFDLEdBQUcsQ0FBQyxvQ0FBb0MsQ0FBQyxDQUFDTjtJQUNsREssUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ04sUUFBUSxDQUFDLEVBQUVBLE1BQU1PLFNBQVMsQ0FBQyxHQUFHLElBQUksR0FBRyxDQUFDLEdBQUc7SUFDckYsTUFBTUMsVUFBVTtRQUNkLGdCQUFnQjtRQUNoQixHQUFJUixTQUFTO1lBQUVTLGVBQWUsQ0FBQyxPQUFPLEVBQUVULE1BQU0sQ0FBQztRQUFDLENBQUM7SUFDbkQ7SUFDQUssUUFBUUMsR0FBRyxDQUFDLG9DQUFvQ0ksT0FBT0MsSUFBSSxDQUFDSDtJQUM1RCxPQUFPQTtBQUNULEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC10ZXN0Ly4vc3JjL2xpYi9hcGkvY29uZmlnLnRzP2NjMTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IEFQSV9DT05GSUcgPSB7XG4gIEJBU0VfVVJMOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDEnLFxuICBUSU1FT1VUOiAxMDAwMCxcbiAgRU5EUE9JTlRTOiB7XG4gICAgLy8gQXV0aFxuICAgIEFVVEg6IHtcbiAgICAgIExPR0lOOiAnL2F1dGgvbG9naW4nLFxuICAgICAgUkVHSVNURVI6ICcvYXV0aC9yZWdpc3RlcicsXG4gICAgICBQUk9GSUxFOiAnL2F1dGgvcHJvZmlsZScsXG4gICAgICBWRVJJRllfVE9LRU46ICcvYXV0aC92ZXJpZnktdG9rZW4nXG4gICAgfSxcbiAgICAvLyBVc2Vyc1xuICAgIFVTRVJTOiB7XG4gICAgICBCQVNFOiAnL3VzZXJzJyxcbiAgICAgIEJZX0lEOiAoaWQ6IHN0cmluZykgPT4gYC91c2Vycy8ke2lkfWAsXG4gICAgICBDSEFOR0VfUEFTU1dPUkQ6IChpZDogc3RyaW5nKSA9PiBgL3VzZXJzLyR7aWR9L2NoYW5nZS1wYXNzd29yZGBcbiAgICB9LFxuICAgIC8vIEFzc2V0c1xuICAgIEFTU0VUUzoge1xuICAgICAgQkFTRTogJy9hc3NldHMnLFxuICAgICAgQllfSUQ6IChpZDogc3RyaW5nKSA9PiBgL2Fzc2V0cy8ke2lkfWAsXG4gICAgICBCWV9BU1NFVF9OVU1CRVI6IChhc3NldE51bWJlcjogc3RyaW5nKSA9PiBgL2Fzc2V0cy9hc3NldC1udW1iZXIvJHthc3NldE51bWJlcn1gLFxuICAgICAgQVNTSUdOOiAoaWQ6IHN0cmluZykgPT4gYC9hc3NldHMvJHtpZH0vYXNzaWduYCxcbiAgICAgIFVOQVNTSUdOOiAoaWQ6IHN0cmluZykgPT4gYC9hc3NldHMvJHtpZH0vdW5hc3NpZ25gLFxuICAgICAgU1RBVFM6ICcvYXNzZXRzL3N0YXRzJ1xuICAgIH0sXG4gICAgLy8gQ2F0ZWdvcmllc1xuICAgIENBVEVHT1JJRVM6IHtcbiAgICAgIEJBU0U6ICcvY2F0ZWdvcmllcycsXG4gICAgICBCWV9JRDogKGlkOiBzdHJpbmcpID0+IGAvY2F0ZWdvcmllcy8ke2lkfWAsXG4gICAgICBBQ1RJVkU6ICcvY2F0ZWdvcmllcy9hY3RpdmUnLFxuICAgICAgSElFUkFSQ0hZOiAnL2NhdGVnb3JpZXMvaGllcmFyY2h5J1xuICAgIH0sXG4gICAgLy8gTG9jYXRpb25zXG4gICAgTE9DQVRJT05TOiB7XG4gICAgICBCQVNFOiAnL2xvY2F0aW9ucycsXG4gICAgICBCWV9JRDogKGlkOiBzdHJpbmcpID0+IGAvbG9jYXRpb25zLyR7aWR9YCxcbiAgICAgIEFDVElWRTogJy9sb2NhdGlvbnMvYWN0aXZlJyxcbiAgICAgIEhJRVJBUkNIWTogJy9sb2NhdGlvbnMvaGllcmFyY2h5J1xuICAgIH0sXG4gICAgLy8gQXNzZXQgTG9nc1xuICAgIEFTU0VUX0xPR1M6IHtcbiAgICAgIFJFQ0VOVDogJy9hc3NldC1sb2dzL3JlY2VudCcsXG4gICAgICBCWV9BU1NFVDogKGFzc2V0SWQ6IHN0cmluZykgPT4gYC9hc3NldC1sb2dzL2Fzc2V0LyR7YXNzZXRJZH1gLFxuICAgICAgQllfVVNFUjogKHVzZXJJZDogc3RyaW5nKSA9PiBgL2Fzc2V0LWxvZ3MvdXNlci8ke3VzZXJJZH1gLFxuICAgICAgQllfQUNUSU9OOiAoYWN0aW9uOiBzdHJpbmcpID0+IGAvYXNzZXQtbG9ncy9hY3Rpb24vJHthY3Rpb259YCxcbiAgICAgIERBVEVfUkFOR0U6ICcvYXNzZXQtbG9ncy9kYXRlLXJhbmdlJ1xuICAgIH1cbiAgfVxufSBhcyBjb25zdFxuXG5leHBvcnQgY29uc3QgZ2V0QXV0aFRva2VuID0gKCk6IHN0cmluZyB8IG51bGwgPT4ge1xuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBudWxsXG4gIHJldHVybiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYXV0aF90b2tlbicpXG59XG5cbmV4cG9ydCBjb25zdCBzZXRBdXRoVG9rZW4gPSAodG9rZW46IHN0cmluZyk6IHZvaWQgPT4ge1xuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVyblxuICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnYXV0aF90b2tlbicsIHRva2VuKVxufVxuXG5leHBvcnQgY29uc3QgcmVtb3ZlQXV0aFRva2VuID0gKCk6IHZvaWQgPT4ge1xuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVyblxuICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYXV0aF90b2tlbicpXG59XG5cbmV4cG9ydCBjb25zdCBnZXRBdXRoSGVhZGVycyA9ICgpOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0+IHtcbiAgY29uc3QgdG9rZW4gPSBnZXRBdXRoVG9rZW4oKVxuICBjb25zb2xlLmxvZygnZ2V0QXV0aEhlYWRlcnM6IFRva2VuIHJldHJpZXZlZDonLCAhIXRva2VuKVxuICBjb25zb2xlLmxvZygnZ2V0QXV0aEhlYWRlcnM6IFRva2VuIHZhbHVlOicsIHRva2VuID8gYCR7dG9rZW4uc3Vic3RyaW5nKDAsIDIwKX0uLi5gIDogJ251bGwnKVxuICBjb25zdCBoZWFkZXJzID0ge1xuICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgLi4uKHRva2VuICYmIHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAgfSlcbiAgfVxuICBjb25zb2xlLmxvZygnZ2V0QXV0aEhlYWRlcnM6IEhlYWRlcnMgY3JlYXRlZDonLCBPYmplY3Qua2V5cyhoZWFkZXJzKSlcbiAgcmV0dXJuIGhlYWRlcnNcbn1cbiJdLCJuYW1lcyI6WyJBUElfQ09ORklHIiwiQkFTRV9VUkwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsIlRJTUVPVVQiLCJFTkRQT0lOVFMiLCJBVVRIIiwiTE9HSU4iLCJSRUdJU1RFUiIsIlBST0ZJTEUiLCJWRVJJRllfVE9LRU4iLCJVU0VSUyIsIkJBU0UiLCJCWV9JRCIsImlkIiwiQ0hBTkdFX1BBU1NXT1JEIiwiQVNTRVRTIiwiQllfQVNTRVRfTlVNQkVSIiwiYXNzZXROdW1iZXIiLCJBU1NJR04iLCJVTkFTU0lHTiIsIlNUQVRTIiwiQ0FURUdPUklFUyIsIkFDVElWRSIsIkhJRVJBUkNIWSIsIkxPQ0FUSU9OUyIsIkFTU0VUX0xPR1MiLCJSRUNFTlQiLCJCWV9BU1NFVCIsImFzc2V0SWQiLCJCWV9VU0VSIiwidXNlcklkIiwiQllfQUNUSU9OIiwiYWN0aW9uIiwiREFURV9SQU5HRSIsImdldEF1dGhUb2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJzZXRBdXRoVG9rZW4iLCJ0b2tlbiIsInNldEl0ZW0iLCJyZW1vdmVBdXRoVG9rZW4iLCJyZW1vdmVJdGVtIiwiZ2V0QXV0aEhlYWRlcnMiLCJjb25zb2xlIiwibG9nIiwic3Vic3RyaW5nIiwiaGVhZGVycyIsIkF1dGhvcml6YXRpb24iLCJPYmplY3QiLCJrZXlzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/config.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/index.ts":
/*!******************************!*\
  !*** ./src/lib/api/index.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG),\n/* harmony export */   AssetCondition: () => (/* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.AssetCondition),\n/* harmony export */   AssetStatus: () => (/* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.AssetStatus),\n/* harmony export */   LocationType: () => (/* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.LocationType),\n/* harmony export */   LogAction: () => (/* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.LogAction),\n/* harmony export */   UserRole: () => (/* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.UserRole),\n/* harmony export */   UserStatus: () => (/* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.UserStatus),\n/* harmony export */   apiClient: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_8__.apiClient),\n/* harmony export */   assetItemService: () => (/* reexport safe */ _assetItemService__WEBPACK_IMPORTED_MODULE_3__.assetItemService),\n/* harmony export */   assetLogsService: () => (/* reexport safe */ _asset_logs__WEBPACK_IMPORTED_MODULE_7__.assetLogsService),\n/* harmony export */   assetQuantityService: () => (/* reexport safe */ _assetQuantityService__WEBPACK_IMPORTED_MODULE_2__.assetQuantityService),\n/* harmony export */   assetsService: () => (/* reexport safe */ _assets__WEBPACK_IMPORTED_MODULE_1__.assetsService),\n/* harmony export */   authService: () => (/* reexport safe */ _auth__WEBPACK_IMPORTED_MODULE_0__.authService),\n/* harmony export */   categoriesService: () => (/* reexport safe */ _categories__WEBPACK_IMPORTED_MODULE_4__.categoriesService),\n/* harmony export */   getAuthToken: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_9__.getAuthToken),\n/* harmony export */   locationsService: () => (/* reexport safe */ _locations__WEBPACK_IMPORTED_MODULE_5__.locationsService),\n/* harmony export */   removeAuthToken: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_9__.removeAuthToken),\n/* harmony export */   setAuthToken: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_9__.setAuthToken),\n/* harmony export */   usersService: () => (/* reexport safe */ _users__WEBPACK_IMPORTED_MODULE_6__.usersService)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(ssr)/./src/lib/api/auth.ts\");\n/* harmony import */ var _assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./assets */ \"(ssr)/./src/lib/api/assets.ts\");\n/* harmony import */ var _assetQuantityService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./assetQuantityService */ \"(ssr)/./src/lib/api/assetQuantityService.ts\");\n/* harmony import */ var _assetItemService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./assetItemService */ \"(ssr)/./src/lib/api/assetItemService.ts\");\n/* harmony import */ var _categories__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./categories */ \"(ssr)/./src/lib/api/categories.ts\");\n/* harmony import */ var _locations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./locations */ \"(ssr)/./src/lib/api/locations.ts\");\n/* harmony import */ var _users__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./users */ \"(ssr)/./src/lib/api/users.ts\");\n/* harmony import */ var _asset_logs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./asset-logs */ \"(ssr)/./src/lib/api/asset-logs.ts\");\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/api/config.ts\");\n/* harmony import */ var _types_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/types/api */ \"(ssr)/./src/types/api.ts\");\n// Export all API services\n\n\n\n\n\n\n\n\n// Export API client and config\n\n\n// Export types\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2FwaS9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSwwQkFBMEI7QUFDVTtBQUNJO0FBQ3FCO0FBQ1I7QUFDTDtBQUNGO0FBQ1I7QUFDUztBQUUvQywrQkFBK0I7QUFDSztBQUM4QztBQUVsRixlQUFlO0FBQ1kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC10ZXN0Ly4vc3JjL2xpYi9hcGkvaW5kZXgudHM/ZTY5MyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnQgYWxsIEFQSSBzZXJ2aWNlc1xuZXhwb3J0IHsgYXV0aFNlcnZpY2UgfSBmcm9tICcuL2F1dGgnXG5leHBvcnQgeyBhc3NldHNTZXJ2aWNlIH0gZnJvbSAnLi9hc3NldHMnXG5leHBvcnQgeyBhc3NldFF1YW50aXR5U2VydmljZSB9IGZyb20gJy4vYXNzZXRRdWFudGl0eVNlcnZpY2UnXG5leHBvcnQgeyBhc3NldEl0ZW1TZXJ2aWNlIH0gZnJvbSAnLi9hc3NldEl0ZW1TZXJ2aWNlJ1xuZXhwb3J0IHsgY2F0ZWdvcmllc1NlcnZpY2UgfSBmcm9tICcuL2NhdGVnb3JpZXMnXG5leHBvcnQgeyBsb2NhdGlvbnNTZXJ2aWNlIH0gZnJvbSAnLi9sb2NhdGlvbnMnXG5leHBvcnQgeyB1c2Vyc1NlcnZpY2UgfSBmcm9tICcuL3VzZXJzJ1xuZXhwb3J0IHsgYXNzZXRMb2dzU2VydmljZSB9IGZyb20gJy4vYXNzZXQtbG9ncydcblxuLy8gRXhwb3J0IEFQSSBjbGllbnQgYW5kIGNvbmZpZ1xuZXhwb3J0IHsgYXBpQ2xpZW50IH0gZnJvbSAnLi9jbGllbnQnXG5leHBvcnQgeyBBUElfQ09ORklHLCBnZXRBdXRoVG9rZW4sIHNldEF1dGhUb2tlbiwgcmVtb3ZlQXV0aFRva2VuIH0gZnJvbSAnLi9jb25maWcnXG5cbi8vIEV4cG9ydCB0eXBlc1xuZXhwb3J0ICogZnJvbSAnQC90eXBlcy9hcGknXG4iXSwibmFtZXMiOlsiYXV0aFNlcnZpY2UiLCJhc3NldHNTZXJ2aWNlIiwiYXNzZXRRdWFudGl0eVNlcnZpY2UiLCJhc3NldEl0ZW1TZXJ2aWNlIiwiY2F0ZWdvcmllc1NlcnZpY2UiLCJsb2NhdGlvbnNTZXJ2aWNlIiwidXNlcnNTZXJ2aWNlIiwiYXNzZXRMb2dzU2VydmljZSIsImFwaUNsaWVudCIsIkFQSV9DT05GSUciLCJnZXRBdXRoVG9rZW4iLCJzZXRBdXRoVG9rZW4iLCJyZW1vdmVBdXRoVG9rZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/locations.ts":
/*!**********************************!*\
  !*** ./src/lib/api/locations.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LocationsService: () => (/* binding */ LocationsService),\n/* harmony export */   locationsService: () => (/* binding */ locationsService)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/api/config.ts\");\n\n\nclass LocationsService {\n    async getLocations() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BASE);\n    }\n    async getActiveLocations() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.ACTIVE);\n    }\n    async getLocationHierarchy() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.HIERARCHY);\n    }\n    async getLocationById(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BY_ID(id));\n    }\n    async createLocation(data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BASE, data);\n    }\n    async updateLocation(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BY_ID(id), data);\n    }\n    async deleteLocation(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BY_ID(id));\n    }\n    // Helper method to get locations as options for forms\n    async getLocationOptions() {\n        const locations = await this.getActiveLocations();\n        return locations.map((location)=>({\n                value: location.id,\n                label: location.fullPath || location.name\n            }));\n    }\n    // Get root locations (no parent)\n    async getRootLocations() {\n        const locations = await this.getLocations();\n        return locations.filter((location)=>!location.parentId);\n    }\n    // Get sub-locations of a parent location\n    async getSublocations(parentId) {\n        const locations = await this.getLocations();\n        return locations.filter((location)=>location.parentId === parentId);\n    }\n    // Get locations by type\n    async getLocationsByType(type) {\n        const locations = await this.getLocations();\n        return locations.filter((location)=>location.type === type);\n    }\n    // Parse coordinates if they exist\n    parseCoordinates(coordinates) {\n        if (!coordinates) return null;\n        try {\n            const parsed = JSON.parse(coordinates);\n            if (parsed.lat && parsed.lng) {\n                return {\n                    lat: parsed.lat,\n                    lng: parsed.lng\n                };\n            }\n        } catch (error) {\n            console.error(\"Failed to parse coordinates:\", error);\n        }\n        return null;\n    }\n    // Format coordinates for storage\n    formatCoordinates(lat, lng) {\n        return JSON.stringify({\n            lat,\n            lng\n        });\n    }\n}\nconst locationsService = new LocationsService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/locations.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/users.ts":
/*!******************************!*\
  !*** ./src/lib/api/users.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UsersService: () => (/* binding */ UsersService),\n/* harmony export */   usersService: () => (/* binding */ usersService)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/api/config.ts\");\n\n\nclass UsersService {\n    async getUsers() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BASE);\n    }\n    async getUserById(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BY_ID(id));\n    }\n    async createUser(data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BASE, data);\n    }\n    async updateUser(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BY_ID(id), data);\n    }\n    async deleteUser(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BY_ID(id));\n    }\n    async changePassword(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.CHANGE_PASSWORD(id), data);\n    }\n    // Helper method to get users as options for forms\n    async getUserOptions() {\n        const users = await this.getUsers();\n        return users.map((user)=>({\n                value: user.id,\n                label: user.fullName\n            }));\n    }\n    // Get users by role\n    async getUsersByRole(role) {\n        const users = await this.getUsers();\n        return users.filter((user)=>user.role === role);\n    }\n    // Get active users only\n    async getActiveUsers() {\n        const users = await this.getUsers();\n        return users.filter((user)=>user.status === \"active\");\n    }\n    // Get users by department\n    async getUsersByDepartment(department) {\n        const users = await this.getUsers();\n        return users.filter((user)=>user.department === department);\n    }\n    // Search users by name or email\n    async searchUsers(searchTerm) {\n        const users = await this.getUsers();\n        const term = searchTerm.toLowerCase();\n        return users.filter((user)=>user.fullName.toLowerCase().includes(term) || user.email.toLowerCase().includes(term) || user.firstName.toLowerCase().includes(term) || user.lastName.toLowerCase().includes(term));\n    }\n}\nconst usersService = new UsersService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/users.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/api.ts":
/*!**************************!*\
  !*** ./src/types/api.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssetCondition: () => (/* binding */ AssetCondition),\n/* harmony export */   AssetStatus: () => (/* binding */ AssetStatus),\n/* harmony export */   LocationType: () => (/* binding */ LocationType),\n/* harmony export */   LogAction: () => (/* binding */ LogAction),\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   UserStatus: () => (/* binding */ UserStatus)\n/* harmony export */ });\n// User Types\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"ADMIN\"] = \"admin\";\n    UserRole[\"MANAGER\"] = \"manager\";\n    UserRole[\"VIEWER\"] = \"viewer\";\n})(UserRole || (UserRole = {}));\nvar UserStatus;\n(function(UserStatus) {\n    UserStatus[\"ACTIVE\"] = \"active\";\n    UserStatus[\"INACTIVE\"] = \"inactive\";\n    UserStatus[\"SUSPENDED\"] = \"suspended\";\n})(UserStatus || (UserStatus = {}));\nvar AssetStatus;\n(function(AssetStatus) {\n    AssetStatus[\"IN_STOCK\"] = \"in_stock\";\n    AssetStatus[\"IN_USE\"] = \"in_use\";\n    AssetStatus[\"MAINTENANCE\"] = \"maintenance\";\n    AssetStatus[\"RETIRED\"] = \"retired\";\n    AssetStatus[\"LOST\"] = \"lost\";\n    AssetStatus[\"DAMAGED\"] = \"damaged\";\n})(AssetStatus || (AssetStatus = {}));\nvar AssetCondition;\n(function(AssetCondition) {\n    AssetCondition[\"EXCELLENT\"] = \"excellent\";\n    AssetCondition[\"GOOD\"] = \"good\";\n    AssetCondition[\"FAIR\"] = \"fair\";\n    AssetCondition[\"POOR\"] = \"poor\";\n})(AssetCondition || (AssetCondition = {}));\nvar LocationType;\n(function(LocationType) {\n    LocationType[\"REGION\"] = \"region\";\n    LocationType[\"BUILDING\"] = \"building\";\n    LocationType[\"FLOOR\"] = \"floor\";\n    LocationType[\"ROOM\"] = \"room\";\n    LocationType[\"AREA\"] = \"area\";\n})(LocationType || (LocationType = {}));\nvar LogAction;\n(function(LogAction) {\n    LogAction[\"CREATED\"] = \"created\";\n    LogAction[\"UPDATED\"] = \"updated\";\n    LogAction[\"DELETED\"] = \"deleted\";\n    LogAction[\"ASSIGNED\"] = \"assigned\";\n    LogAction[\"UNASSIGNED\"] = \"unassigned\";\n    LogAction[\"STATUS_CHANGED\"] = \"status_changed\";\n    LogAction[\"LOCATION_CHANGED\"] = \"location_changed\";\n    LogAction[\"MAINTENANCE\"] = \"maintenance\";\n    LogAction[\"RETIRED\"] = \"retired\";\n})(LogAction || (LogAction = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/api.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"82fb22e49e5c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAtdGVzdC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/Nzc4NyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjgyZmIyMmU0OWU1Y1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/assets/iconify-icons/generated-icons.css":
/*!******************************************************!*\
  !*** ./src/assets/iconify-icons/generated-icons.css ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"57a1f3591731\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXNzZXRzL2ljb25pZnktaWNvbnMvZ2VuZXJhdGVkLWljb25zLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLXRlc3QvLi9zcmMvYXNzZXRzL2ljb25pZnktaWNvbnMvZ2VuZXJhdGVkLWljb25zLmNzcz82Y2FlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTdhMWYzNTkxNzMxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/assets/iconify-icons/generated-icons.css\n");

/***/ }),

/***/ "(rsc)/./src/app/asset-details/[assetNumber]/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/asset-details/[assetNumber]/page.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Invicta\Upcoming Projects\Assets Management System\assets-management-app\src\app\asset-details\[assetNumber]\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_perfect_scrollbar_dist_css_styles_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-perfect-scrollbar/dist/css/styles.css */ \"(rsc)/./node_modules/react-perfect-scrollbar/dist/css/styles.css\");\n/* harmony import */ var _app_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _assets_iconify_icons_generated_icons_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @assets/iconify-icons/generated-icons.css */ \"(rsc)/./src/assets/iconify-icons/generated-icons.css\");\n// Third-party Imports\n\n\n// Style Imports\n\n// Generated Icon CSS Imports\n\nconst metadata = {\n    title: \"Assets App | Invicta Innovations\",\n    description: \"Assets App | Invicta Innovations\"\n};\nconst RootLayout = ({ children })=>{\n    // Vars\n    const direction = \"ltr\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        id: \"__next\",\n        dir: direction,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"flex is-full min-bs-full flex-auto flex-col\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RootLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBLHNCQUFzQjs7QUFDOEI7QUFLcEQsZ0JBQWdCO0FBQ1U7QUFFMUIsNkJBQTZCO0FBQ3FCO0FBRTNDLE1BQU1BLFdBQVc7SUFDdEJDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFRCxNQUFNQyxhQUFhLENBQUMsRUFBRUMsUUFBUSxFQUFnQjtJQUM1QyxPQUFPO0lBQ1AsTUFBTUMsWUFBWTtJQUVsQixxQkFDRSw4REFBQ0M7UUFBS0MsSUFBRztRQUFTQyxLQUFLSDtrQkFDckIsNEVBQUNJO1lBQUtDLFdBQVU7c0JBQStDTjs7Ozs7Ozs7Ozs7QUFHckU7QUFFQSxpRUFBZUQsVUFBVUEsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLXRlc3QvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUaGlyZC1wYXJ0eSBJbXBvcnRzXG5pbXBvcnQgJ3JlYWN0LXBlcmZlY3Qtc2Nyb2xsYmFyL2Rpc3QvY3NzL3N0eWxlcy5jc3MnXG5cbi8vIFR5cGUgSW1wb3J0c1xuaW1wb3J0IHR5cGUgeyBDaGlsZHJlblR5cGUgfSBmcm9tICdAY29yZS90eXBlcydcblxuLy8gU3R5bGUgSW1wb3J0c1xuaW1wb3J0ICdAL2FwcC9nbG9iYWxzLmNzcydcblxuLy8gR2VuZXJhdGVkIEljb24gQ1NTIEltcG9ydHNcbmltcG9ydCAnQGFzc2V0cy9pY29uaWZ5LWljb25zL2dlbmVyYXRlZC1pY29ucy5jc3MnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdBc3NldHMgQXBwIHwgSW52aWN0YSBJbm5vdmF0aW9ucycsXG4gIGRlc2NyaXB0aW9uOiAnQXNzZXRzIEFwcCB8IEludmljdGEgSW5ub3ZhdGlvbnMnXG59XG5cbmNvbnN0IFJvb3RMYXlvdXQgPSAoeyBjaGlsZHJlbiB9OiBDaGlsZHJlblR5cGUpID0+IHtcbiAgLy8gVmFyc1xuICBjb25zdCBkaXJlY3Rpb24gPSAnbHRyJ1xuXG4gIHJldHVybiAoXG4gICAgPGh0bWwgaWQ9J19fbmV4dCcgZGlyPXtkaXJlY3Rpb259PlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPSdmbGV4IGlzLWZ1bGwgbWluLWJzLWZ1bGwgZmxleC1hdXRvIGZsZXgtY29sJz57Y2hpbGRyZW59PC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuXG5leHBvcnQgZGVmYXVsdCBSb290TGF5b3V0XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiZGlyZWN0aW9uIiwiaHRtbCIsImlkIiwiZGlyIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"32x32\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC10ZXN0Ly4vc3JjL2FwcC9mYXZpY29uLmljbz9mOTFhIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjMyeDMyXCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@mui","vendor-chunks/@emotion","vendor-chunks/@babel","vendor-chunks/stylis","vendor-chunks/prop-types","vendor-chunks/@swc","vendor-chunks/react-transition-group","vendor-chunks/hoist-non-react-statics","vendor-chunks/clsx","vendor-chunks/react-perfect-scrollbar","vendor-chunks/react-is","vendor-chunks/object-assign"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fasset-details%2F%5BassetNumber%5D%2Fpage&page=%2Fasset-details%2F%5BassetNumber%5D%2Fpage&appPaths=%2Fasset-details%2F%5BassetNumber%5D%2Fpage&pagePath=private-next-app-dir%2Fasset-details%2F%5BassetNumber%5D%2Fpage.tsx&appDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CInvicta%5CUpcoming%20Projects%5CAssets%20Management%20System%5Cassets-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();