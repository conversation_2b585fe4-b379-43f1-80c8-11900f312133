"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetQuantityService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const asset_entity_1 = require("../entities/asset.entity");
const asset_quantity_entity_1 = require("../entities/asset-quantity.entity");
const quantity_transfer_entity_1 = require("../entities/quantity-transfer.entity");
const user_entity_1 = require("../entities/user.entity");
const uuid_1 = require("uuid");
let AssetQuantityService = class AssetQuantityService {
    assetRepository;
    assetQuantityRepository;
    quantityTransferRepository;
    userRepository;
    dataSource;
    constructor(assetRepository, assetQuantityRepository, quantityTransferRepository, userRepository, dataSource) {
        this.assetRepository = assetRepository;
        this.assetQuantityRepository = assetQuantityRepository;
        this.quantityTransferRepository = quantityTransferRepository;
        this.userRepository = userRepository;
        this.dataSource = dataSource;
    }
    async getAssetQuantities(assetId) {
        const asset = await this.assetRepository.findOne({
            where: { id: assetId },
        });
        if (!asset) {
            throw new common_1.NotFoundException('Asset not found');
        }
        return this.assetQuantityRepository.find({
            where: { assetId },
            relations: ['asset', 'lastModifiedBy'],
            order: { status: 'ASC' },
        });
    }
    async getAssetQuantitiesByStatus(assetId) {
        const quantities = await this.getAssetQuantities(assetId);
        const result = {};
        Object.values(asset_entity_1.AssetStatus).forEach((status) => {
            result[status] = 0;
        });
        quantities.forEach((q) => {
            result[q.status] = q.quantity;
        });
        return result;
    }
    async transferQuantity(transferDto, userId) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const asset = await queryRunner.manager.findOne(asset_entity_1.Asset, {
                where: { id: transferDto.assetId },
            });
            if (!asset) {
                throw new common_1.NotFoundException('Asset not found');
            }
            const user = await queryRunner.manager.findOne(user_entity_1.User, {
                where: { id: userId },
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const fromQuantity = await queryRunner.manager.findOne(asset_quantity_entity_1.AssetQuantity, {
                where: { assetId: transferDto.assetId, status: transferDto.fromStatus },
            });
            if (!fromQuantity || fromQuantity.quantity < transferDto.quantity) {
                throw new common_1.BadRequestException(`Insufficient quantity in ${transferDto.fromStatus} status. Available: ${fromQuantity?.quantity || 0}, Requested: ${transferDto.quantity}`);
            }
            await queryRunner.manager.update(asset_quantity_entity_1.AssetQuantity, { assetId: transferDto.assetId, status: transferDto.fromStatus }, {
                quantity: fromQuantity.quantity - transferDto.quantity,
                lastModifiedById: userId,
            });
            const toQuantity = await queryRunner.manager.findOne(asset_quantity_entity_1.AssetQuantity, {
                where: { assetId: transferDto.assetId, status: transferDto.toStatus },
            });
            if (toQuantity) {
                await queryRunner.manager.update(asset_quantity_entity_1.AssetQuantity, { assetId: transferDto.assetId, status: transferDto.toStatus }, {
                    quantity: toQuantity.quantity + transferDto.quantity,
                    lastModifiedById: userId,
                });
            }
            else {
                await queryRunner.manager.save(asset_quantity_entity_1.AssetQuantity, {
                    assetId: transferDto.assetId,
                    status: transferDto.toStatus,
                    quantity: transferDto.quantity,
                    lastModifiedById: userId,
                });
            }
            const transfer = await queryRunner.manager.save(quantity_transfer_entity_1.QuantityTransfer, {
                assetId: transferDto.assetId,
                fromStatus: transferDto.fromStatus,
                toStatus: transferDto.toStatus,
                quantity: transferDto.quantity,
                transferType: transferDto.transferType || quantity_transfer_entity_1.TransferType.MANUAL,
                reason: transferDto.reason,
                notes: transferDto.notes,
                transferredById: userId,
            });
            await queryRunner.commitTransaction();
            const updatedQuantities = await this.getAssetQuantitiesByStatus(transferDto.assetId);
            return {
                success: true,
                transfer,
                quantities: updatedQuantities,
            };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async bulkTransferQuantity(bulkTransferDto, userId) {
        const batchId = (0, uuid_1.v4)();
        const transfers = [];
        for (const transferDto of bulkTransferDto.transfers) {
            const result = await this.transferQuantity({
                ...transferDto,
                reason: transferDto.reason || bulkTransferDto.batchReason,
                notes: transferDto.notes || bulkTransferDto.batchNotes,
                transferType: quantity_transfer_entity_1.TransferType.BULK_TRANSFER,
            }, userId);
            await this.quantityTransferRepository.update(result.transfer.id, {
                batchId,
            });
            transfers.push(result.transfer);
        }
        return {
            success: true,
            transfers,
            batchId,
        };
    }
    async setAssetQuantities(setQuantityDto, userId) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const asset = await queryRunner.manager.findOne(asset_entity_1.Asset, {
                where: { id: setQuantityDto.assetId },
            });
            if (!asset) {
                throw new common_1.NotFoundException('Asset not found');
            }
            await queryRunner.manager.delete(asset_quantity_entity_1.AssetQuantity, {
                assetId: setQuantityDto.assetId,
            });
            for (const [status, quantity] of Object.entries(setQuantityDto.quantities)) {
                if (quantity > 0) {
                    await queryRunner.manager.save(asset_quantity_entity_1.AssetQuantity, {
                        assetId: setQuantityDto.assetId,
                        status: status,
                        quantity,
                        lastModifiedById: userId,
                    });
                    await queryRunner.manager.save(quantity_transfer_entity_1.QuantityTransfer, {
                        assetId: setQuantityDto.assetId,
                        fromStatus: null,
                        toStatus: status,
                        quantity,
                        transferType: quantity_transfer_entity_1.TransferType.INITIAL_STOCK,
                        reason: setQuantityDto.reason || 'Initial stock setup',
                        transferredById: userId,
                    });
                }
            }
            await queryRunner.commitTransaction();
            const updatedQuantities = await this.getAssetQuantitiesByStatus(setQuantityDto.assetId);
            return {
                success: true,
                quantities: updatedQuantities,
            };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async getTransferHistory(assetId) {
        return this.quantityTransferRepository.find({
            where: { assetId },
            relations: ['asset', 'transferredBy'],
            order: { transferredAt: 'DESC' },
        });
    }
    async getAllAssetQuantities() {
        const assets = await this.assetRepository.find({
            select: ['id', 'name'],
            where: { isActive: true },
        });
        const result = [];
        for (const asset of assets) {
            const quantities = await this.getAssetQuantitiesByStatus(asset.id);
            result.push({
                assetId: asset.id,
                assetName: asset.name,
                quantities,
            });
        }
        return result;
    }
};
exports.AssetQuantityService = AssetQuantityService;
exports.AssetQuantityService = AssetQuantityService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(asset_entity_1.Asset)),
    __param(1, (0, typeorm_1.InjectRepository)(asset_quantity_entity_1.AssetQuantity)),
    __param(2, (0, typeorm_1.InjectRepository)(quantity_transfer_entity_1.QuantityTransfer)),
    __param(3, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource])
], AssetQuantityService);
//# sourceMappingURL=asset-quantity.service.js.map