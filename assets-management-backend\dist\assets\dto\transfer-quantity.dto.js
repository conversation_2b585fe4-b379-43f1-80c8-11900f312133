"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SetAssetQuantityDto = exports.BulkTransferQuantityDto = exports.TransferQuantityDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const asset_entity_1 = require("../../entities/asset.entity");
const quantity_transfer_entity_1 = require("../../entities/quantity-transfer.entity");
class TransferQuantityDto {
    assetId;
    fromStatus;
    toStatus;
    quantity;
    reason;
    notes;
    transferType;
}
exports.TransferQuantityDto = TransferQuantityDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Asset ID to transfer quantities for',
        example: 'uuid-string',
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], TransferQuantityDto.prototype, "assetId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Source status to transfer from',
        enum: asset_entity_1.AssetStatus,
        example: asset_entity_1.AssetStatus.IN_STOCK,
    }),
    (0, class_validator_1.IsEnum)(asset_entity_1.AssetStatus),
    __metadata("design:type", String)
], TransferQuantityDto.prototype, "fromStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Destination status to transfer to',
        enum: asset_entity_1.AssetStatus,
        example: asset_entity_1.AssetStatus.IN_USE,
    }),
    (0, class_validator_1.IsEnum)(asset_entity_1.AssetStatus),
    __metadata("design:type", String)
], TransferQuantityDto.prototype, "toStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Quantity to transfer',
        example: 5,
        minimum: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], TransferQuantityDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Reason for the transfer',
        example: 'Moving assets to production environment',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TransferQuantityDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional notes',
        example: 'Transferred for Q1 deployment',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TransferQuantityDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Type of transfer',
        enum: quantity_transfer_entity_1.TransferType,
        default: quantity_transfer_entity_1.TransferType.MANUAL,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(quantity_transfer_entity_1.TransferType),
    __metadata("design:type", String)
], TransferQuantityDto.prototype, "transferType", void 0);
class BulkTransferQuantityDto {
    transfers;
    batchReason;
    batchNotes;
}
exports.BulkTransferQuantityDto = BulkTransferQuantityDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of transfer operations',
        type: [TransferQuantityDto],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => TransferQuantityDto),
    __metadata("design:type", Array)
], BulkTransferQuantityDto.prototype, "transfers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Batch reason for all transfers',
        example: 'Monthly inventory redistribution',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BulkTransferQuantityDto.prototype, "batchReason", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Batch notes',
        example: 'Bulk transfer for Q1 2024',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BulkTransferQuantityDto.prototype, "batchNotes", void 0);
class SetAssetQuantityDto {
    assetId;
    quantities;
    reason;
}
exports.SetAssetQuantityDto = SetAssetQuantityDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Asset ID to set quantities for',
        example: 'uuid-string',
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], SetAssetQuantityDto.prototype, "assetId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Quantities by status',
        example: {
            in_stock: 10,
            in_use: 5,
            maintenance: 2,
        },
    }),
    __metadata("design:type", Object)
], SetAssetQuantityDto.prototype, "quantities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Reason for setting quantities',
        example: 'Initial stock setup',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SetAssetQuantityDto.prototype, "reason", void 0);
//# sourceMappingURL=transfer-quantity.dto.js.map