"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_views_account-settings_account_index_tsx";
exports.ids = ["_rsc_src_views_account-settings_account_index_tsx"];
exports.modules = {

/***/ "(rsc)/./src/views/account-settings/account/AccountDelete.tsx":
/*!**************************************************************!*\
  !*** ./src/views/account-settings/account/AccountDelete.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @mui/material/Card */ \"(rsc)/./node_modules/@mui/material/Card/index.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_mui_material_Card__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material/CardHeader */ \"(rsc)/./node_modules/@mui/material/CardHeader/index.js\");\n/* harmony import */ var _mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_CardContent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/CardContent */ \"(rsc)/./node_modules/@mui/material/CardContent/index.js\");\n/* harmony import */ var _mui_material_CardContent__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_mui_material_CardContent__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _mui_material_FormControlLabel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/FormControlLabel */ \"(rsc)/./node_modules/@mui/material/FormControlLabel/index.js\");\n/* harmony import */ var _mui_material_FormControlLabel__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_mui_material_FormControlLabel__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_Checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/Checkbox */ \"(rsc)/./node_modules/@mui/material/Checkbox/index.js\");\n/* harmony import */ var _mui_material_Checkbox__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_material_Checkbox__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/Button */ \"(rsc)/./node_modules/@mui/material/Button/index.js\");\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_mui_material_Button__WEBPACK_IMPORTED_MODULE_6__);\n// MUI Imports\n\n\n\n\n\n\n\nconst AccountDelete = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Card__WEBPACK_IMPORTED_MODULE_1___default()), {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_2___default()), {\n                title: \"Delete Account\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\account\\\\AccountDelete.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_CardContent__WEBPACK_IMPORTED_MODULE_3___default()), {\n                className: \"flex flex-col items-start gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_FormControlLabel__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Checkbox__WEBPACK_IMPORTED_MODULE_5___default()), {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\account\\\\AccountDelete.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 36\n                        }, void 0),\n                        label: \"I confirm my account deactivation\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\account\\\\AccountDelete.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Button__WEBPACK_IMPORTED_MODULE_6___default()), {\n                        variant: \"contained\",\n                        color: \"error\",\n                        type: \"submit\",\n                        children: \"Deactivate Account\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\account\\\\AccountDelete.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\account\\\\AccountDelete.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\account\\\\AccountDelete.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AccountDelete);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/views/account-settings/account/AccountDelete.tsx\n");

/***/ }),

/***/ "(rsc)/./src/views/account-settings/account/AccountDetails.tsx":
/*!***************************************************************!*\
  !*** ./src/views/account-settings/account/AccountDetails.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Invicta\Upcoming Projects\Assets Management System\assets-management-app\src\views\account-settings\account\AccountDetails.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/views/account-settings/account/index.tsx":
/*!******************************************************!*\
  !*** ./src/views/account-settings/account/index.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mui_material_Grid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/Grid */ \"(rsc)/./node_modules/@mui/material/Grid/index.js\");\n/* harmony import */ var _mui_material_Grid__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_mui_material_Grid__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _AccountDetails__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AccountDetails */ \"(rsc)/./src/views/account-settings/account/AccountDetails.tsx\");\n/* harmony import */ var _AccountDelete__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AccountDelete */ \"(rsc)/./src/views/account-settings/account/AccountDelete.tsx\");\n// MUI Imports\n\n\n// Component Imports\n\n\nconst Account = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Grid__WEBPACK_IMPORTED_MODULE_3___default()), {\n        container: true,\n        spacing: 6,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Grid__WEBPACK_IMPORTED_MODULE_3___default()), {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AccountDetails__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\account\\\\index.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\account\\\\index.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Grid__WEBPACK_IMPORTED_MODULE_3___default()), {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AccountDelete__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\account\\\\index.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\account\\\\index.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\account\\\\index.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Account);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdmlld3MvYWNjb3VudC1zZXR0aW5ncy9hY2NvdW50L2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUEsY0FBYzs7QUFDdUI7QUFFckMsb0JBQW9CO0FBQ3lCO0FBQ0Y7QUFFM0MsTUFBTUcsVUFBVTtJQUNkLHFCQUNFLDhEQUFDSCwyREFBSUE7UUFBQ0ksU0FBUztRQUFDQyxTQUFTOzswQkFDdkIsOERBQUNMLDJEQUFJQTtnQkFBQ00sSUFBSTtnQkFBQ0MsSUFBSTswQkFDYiw0RUFBQ04sdURBQWNBOzs7Ozs7Ozs7OzBCQUVqQiw4REFBQ0QsMkRBQUlBO2dCQUFDTSxJQUFJO2dCQUFDQyxJQUFJOzBCQUNiLDRFQUFDTCxzREFBYUE7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJdEI7QUFFQSxpRUFBZUMsT0FBT0EsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLXRlc3QvLi9zcmMvdmlld3MvYWNjb3VudC1zZXR0aW5ncy9hY2NvdW50L2luZGV4LnRzeD8zYzZjIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIE1VSSBJbXBvcnRzXG5pbXBvcnQgR3JpZCBmcm9tICdAbXVpL21hdGVyaWFsL0dyaWQnXG5cbi8vIENvbXBvbmVudCBJbXBvcnRzXG5pbXBvcnQgQWNjb3VudERldGFpbHMgZnJvbSAnLi9BY2NvdW50RGV0YWlscydcbmltcG9ydCBBY2NvdW50RGVsZXRlIGZyb20gJy4vQWNjb3VudERlbGV0ZSdcblxuY29uc3QgQWNjb3VudCA9ICgpID0+IHtcbiAgcmV0dXJuIChcbiAgICA8R3JpZCBjb250YWluZXIgc3BhY2luZz17Nn0+XG4gICAgICA8R3JpZCBpdGVtIHhzPXsxMn0+XG4gICAgICAgIDxBY2NvdW50RGV0YWlscyAvPlxuICAgICAgPC9HcmlkPlxuICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9PlxuICAgICAgICA8QWNjb3VudERlbGV0ZSAvPlxuICAgICAgPC9HcmlkPlxuICAgIDwvR3JpZD5cbiAgKVxufVxuXG5leHBvcnQgZGVmYXVsdCBBY2NvdW50XG4iXSwibmFtZXMiOlsiR3JpZCIsIkFjY291bnREZXRhaWxzIiwiQWNjb3VudERlbGV0ZSIsIkFjY291bnQiLCJjb250YWluZXIiLCJzcGFjaW5nIiwiaXRlbSIsInhzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/views/account-settings/account/index.tsx\n");

/***/ })

};
;