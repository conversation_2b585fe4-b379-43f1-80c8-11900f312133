import { AssetsService, AssetSearchOptions } from './assets.service';
import { CreateAssetDto } from './dto/create-asset.dto';
import { UpdateAssetDto } from './dto/update-asset.dto';
import { AssignAssetDto } from './dto/assign-asset.dto';
import { TransferQuantityDto, BulkTransferQuantityDto, SetAssetQuantityDto } from './dto/transfer-quantity.dto';
import { AssetQuantityService } from './asset-quantity.service';
import { AssetItemService, UpdateAssetItemDto, TransferAssetItemDto } from './asset-item.service';
import { AssetStatus } from '../entities/asset.entity';
export declare class AssetsController {
    private readonly assetsService;
    private readonly assetQuantityService;
    private readonly assetItemService;
    constructor(assetsService: AssetsService, assetQuantityService: AssetQuantityService, assetItemService: AssetItemService);
    create(createAssetDto: CreateAssetDto, user: any): Promise<import("../entities/asset.entity").Asset>;
    findAll(query: AssetSearchOptions): Promise<{
        assets: import("../entities/asset.entity").Asset[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getStats(): Promise<{
        totalAssets: number;
        totalValue: number;
        statusBreakdown: Record<AssetStatus, number>;
        categoryBreakdown: Array<{
            categoryName: string;
            count: number;
            value: number;
        }>;
    }>;
    findByAssetNumber(assetNumber: string): Promise<any>;
    findOne(id: string): Promise<import("../entities/asset.entity").Asset>;
    update(id: string, updateAssetDto: UpdateAssetDto): Promise<import("../entities/asset.entity").Asset>;
    remove(id: string): Promise<void>;
    assignAsset(id: string, assignAssetDto: AssignAssetDto): Promise<import("../entities/asset.entity").Asset>;
    unassignAsset(id: string): Promise<import("../entities/asset.entity").Asset>;
    getAssetQuantities(id: string): Promise<Record<AssetStatus, number>>;
    transferQuantity(transferDto: TransferQuantityDto, user: any): Promise<{
        success: boolean;
        transfer: import("../entities/quantity-transfer.entity").QuantityTransfer;
        quantities: Record<AssetStatus, number>;
    }>;
    bulkTransferQuantity(bulkTransferDto: BulkTransferQuantityDto, user: any): Promise<{
        success: boolean;
        transfers: import("../entities/quantity-transfer.entity").QuantityTransfer[];
        batchId: string;
    }>;
    setAssetQuantities(setQuantityDto: SetAssetQuantityDto, user: any): Promise<{
        success: boolean;
        quantities: Record<AssetStatus, number>;
    }>;
    getTransferHistory(id: string): Promise<import("../entities/quantity-transfer.entity").QuantityTransfer[]>;
    getAllAssetQuantities(): Promise<{
        assetId: string;
        assetName: string;
        quantities: Record<AssetStatus, number>;
    }[]>;
    createAssetItems(assetId: string, createDto: {
        quantity: number;
    }, user: any): Promise<import("../entities/asset-item.entity").AssetItem[]>;
    getAssetItems(assetId: string): Promise<import("../entities/asset-item.entity").AssetItem[]>;
    getAssetItem(itemId: string): Promise<import("../entities/asset-item.entity").AssetItem>;
    updateAssetItem(itemId: string, updateDto: UpdateAssetItemDto, user: any): Promise<import("../entities/asset-item.entity").AssetItem>;
    transferAssetItem(transferDto: TransferAssetItemDto, user: any): Promise<import("../entities/asset-item.entity").AssetItem>;
    getAssetItemQuantities(assetId: string): Promise<Record<AssetStatus, number>>;
    getAllAssetItemQuantities(): Promise<{
        assetId: string;
        assetName: string;
        quantities: Record<AssetStatus, number>;
        totalItems: number;
    }[]>;
    deleteAssetItem(itemId: string): Promise<void>;
    uploadAssetItemImage(itemId: string, file: any, user: any): Promise<{
        imageUrl: string;
    }>;
}
