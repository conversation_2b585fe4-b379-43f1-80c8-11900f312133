import { Document } from 'mongoose';
export type AssetLogDocument = AssetLog & Document;
export declare enum LogAction {
    CREATED = "created",
    UPDATED = "updated",
    DELETED = "deleted",
    ASSIGNED = "assigned",
    UNASSIGNED = "unassigned",
    STATUS_CHANGED = "status_changed",
    LOCATION_CHANGED = "location_changed",
    MAINTENANCE = "maintenance",
    RETIRED = "retired"
}
export declare class AssetLog {
    assetId: string;
    assetNumber: string;
    action: LogAction;
    performedBy: string;
    performedByName: string;
    previousData: Record<string, any>;
    newData: Record<string, any>;
    description: string;
    ipAddress: string;
    userAgent: string;
    timestamp: Date;
}
export declare const AssetLogSchema: import("mongoose").Schema<AssetLog, import("mongoose").Model<AssetLog, any, any, any, Document<unknown, any, AssetLog, any> & AssetLog & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, AssetLog, Document<unknown, {}, import("mongoose").FlatRecord<AssetLog>, {}> & import("mongoose").FlatRecord<AssetLog> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
