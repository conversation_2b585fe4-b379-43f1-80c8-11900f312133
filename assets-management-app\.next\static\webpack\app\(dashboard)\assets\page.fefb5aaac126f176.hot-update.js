"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/assets/page",{

/***/ "(app-pages-browser)/./src/lib/services/assetLabelGenerator.ts":
/*!*************************************************!*\
  !*** ./src/lib/services/assetLabelGenerator.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssetLabelGenerator: function() { return /* binding */ AssetLabelGenerator; }\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var qrcode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! qrcode */ \"(app-pages-browser)/./node_modules/qrcode/lib/browser.js\");\n// import jsPDF from 'jspdf'\n// import QRCode from 'qrcode'\n// export interface AssetItemLabel {\n//   id: string\n//   assetNumber: string\n//   assetName: string\n//   category: string\n//   location: string\n//   status: string\n//   condition: string\n// }\n// export class AssetLabelGenerator {\n//   private doc: jsPDF\n//   constructor() {\n//     this.doc = new jsPDF({\n//       orientation: 'portrait',\n//       unit: 'mm',\n//       format: 'a4'\n//     })\n//   }\n//   /**\n//    * Generate a single asset label - very small format (1cm height)\n//    */\n//   async generateSingleLabel(item: AssetItemLabel): Promise<void> {\n//     this.doc = new jsPDF({\n//       orientation: 'landscape',\n//       unit: 'mm',\n//       format: [25, 10] // 25mm x 10mm label size (1cm height)\n//     })\n//     await this.drawMinimalLabel(item, 0, 0, 25, 10)\n//   }\n//   /**\n//    * Generate multiple asset labels on A4 sheet\n//    * Standard Avery 5160 format: 3 columns x 10 rows = 30 labels per sheet\n//    */\n//   async generateBulkLabels(items: AssetItemLabel[]): Promise<void> {\n//     this.doc = new jsPDF({\n//       orientation: 'portrait',\n//       unit: 'mm',\n//       format: 'a4'\n//     })\n//     const labelWidth = 25 // 25mm width\n//     const labelHeight = 10 // 10mm height (1cm)\n//     const marginLeft = 5\n//     const marginTop = 5\n//     const cols = 8\n//     const rows = 28\n//     const labelsPerPage = cols * rows\n//     let currentPage = 0\n//     for (let i = 0; i < items.length; i++) {\n//       const pageIndex = Math.floor(i / labelsPerPage)\n//       const positionOnPage = i % labelsPerPage\n//       // Add new page if needed\n//       if (pageIndex > currentPage) {\n//         this.doc.addPage()\n//         currentPage = pageIndex\n//       }\n//       const col = positionOnPage % cols\n//       const row = Math.floor(positionOnPage / cols)\n//       const x = marginLeft + col * labelWidth\n//       const y = marginTop + row * labelHeight\n//       await this.drawMinimalLabel(items[i], x, y, labelWidth, labelHeight)\n//     }\n//   }\n//   /**\n//    * Draw a minimal label with only QR code and asset number\n//    */\n//   private async drawMinimalLabel(\n//     item: AssetItemLabel,\n//     x: number,\n//     y: number,\n//     width: number,\n//     height: number\n//   ): Promise<void> {\n//     // Generate QR code with asset details URL\n//     // Use current host (works with both localhost and IP address)\n//     const assetDetailsUrl = `${window.location.origin}/asset-details/${item.assetNumber}`\n//     const qrCodeDataUrl = await QRCode.toDataURL(assetDetailsUrl, {\n//       width: 128,\n//       margin: 0,\n//       color: {\n//         dark: '#000000',\n//         light: '#FFFFFF'\n//       }\n//     })\n//     // Draw border\n//     this.doc.setDrawColor(0, 0, 0)\n//     this.doc.setLineWidth(0.1)\n//     this.doc.rect(x, y, width, height)\n//     // QR Code (left side, takes most of the space)\n//     const qrSize = height - 1 // Almost full height\n//     this.doc.addImage(qrCodeDataUrl, 'PNG', x + 0.5, y + 0.5, qrSize, qrSize)\n//     // Asset Number (right side, vertical text for small space)\n//     this.doc.setFont('helvetica', 'bold')\n//     this.doc.setFontSize(6)\n//     // Split asset number for better fit with new format: LOC-CAT-XXXX\n//     const assetNumber = item.assetNumber\n//     const parts = assetNumber.split('-')\n//     if (parts.length >= 3) {\n//       // For new format LOC-CAT-XXXX, show the number prominently\n//       this.doc.setFontSize(7)\n//       this.doc.text(parts[2], x + qrSize + 1, y + 3) // Show XXXX part\n//       // Show location and category prefix smaller\n//       this.doc.setFontSize(4)\n//       this.doc.text(`${parts[0]}-${parts[1]}`, x + qrSize + 1, y + 6) // Show LOC-CAT\n//     } else {\n//       // Fallback for any other format\n//       this.doc.text(assetNumber, x + qrSize + 1, y + 4)\n//     }\n//   }\n//   /**\n//    * Draw a single label at specified position (legacy method for compatibility)\n//    */\n//   private async drawLabel(item: AssetItemLabel, x: number, y: number, width: number, height: number): Promise<void> {\n//     // Generate QR code\n//     const qrCodeDataUrl = await QRCode.toDataURL(item.assetNumber, {\n//       width: 60,\n//       margin: 1,\n//       color: {\n//         dark: '#000000',\n//         light: '#FFFFFF'\n//       }\n//     })\n//     // Draw border\n//     this.doc.setDrawColor(0, 0, 0)\n//     this.doc.setLineWidth(0.1)\n//     this.doc.rect(x, y, width, height)\n//     // QR Code (left side)\n//     const qrSize = Math.min(height - 4, 15)\n//     this.doc.addImage(qrCodeDataUrl, 'PNG', x + 2, y + 2, qrSize, qrSize)\n//     // Asset Number (main text)\n//     this.doc.setFont('helvetica', 'bold')\n//     this.doc.setFontSize(8)\n//     this.doc.text(item.assetNumber, x + qrSize + 4, y + 6)\n//     // Asset Name\n//     this.doc.setFont('helvetica', 'normal')\n//     this.doc.setFontSize(6)\n//     const assetName = this.truncateText(item.assetName, 20)\n//     this.doc.text(assetName, x + qrSize + 4, y + 10)\n//     // Category and Location\n//     this.doc.setFontSize(5)\n//     this.doc.text(`Cat: ${this.truncateText(item.category, 12)}`, x + qrSize + 4, y + 14)\n//     this.doc.text(`Loc: ${this.truncateText(item.location, 12)}`, x + qrSize + 4, y + 17)\n//     // Status and Condition\n//     this.doc.setFontSize(4)\n//     this.doc.text(`${item.status.toUpperCase()} | ${item.condition.toUpperCase()}`, x + qrSize + 4, y + 21)\n//     // Company info (bottom)\n//     this.doc.setFontSize(4)\n//     this.doc.text('Asset Management System', x + 2, y + height - 2)\n//   }\n//   /**\n//    * Truncate text to fit label\n//    */\n//   private truncateText(text: string, maxLength: number): string {\n//     if (text.length <= maxLength) return text\n//     return text.substring(0, maxLength - 3) + '...'\n//   }\n//   /**\n//    * Save the PDF\n//    */\n//   save(filename: string): void {\n//     this.doc.save(filename)\n//   }\n//   /**\n//    * Open print dialog\n//    */\n//   openForPrint(): void {\n//     this.doc.autoPrint()\n//     window.open(this.doc.output('bloburl'), '_blank')\n//   }\n//   /**\n//    * Get PDF as blob\n//    */\n//   getBlob(): Blob {\n//     return this.doc.output('blob')\n//   }\n//   /**\n//    * Generate asset number labels for printing\n//    */\n//   static async generateAssetLabels(\n//     items: AssetItemLabel[],\n//     type: 'single' | 'bulk' = 'bulk'\n//   ): Promise<AssetLabelGenerator> {\n//     const generator = new AssetLabelGenerator()\n//     if (type === 'single' && items.length === 1) {\n//       await generator.generateSingleLabel(items[0])\n//     } else {\n//       await generator.generateBulkLabels(items)\n//     }\n//     return generator\n//   }\n//   /**\n//    * Quick print function for asset items\n//    */\n//   static async printAssetLabels(items: AssetItemLabel[], type: 'single' | 'bulk' = 'bulk'): Promise<void> {\n//     const generator = await AssetLabelGenerator.generateAssetLabels(items, type)\n//     generator.openForPrint()\n//   }\n//   /**\n//    * Quick save function for asset items\n//    */\n//   static async saveAssetLabels(\n//     items: AssetItemLabel[],\n//     filename?: string,\n//     type: 'single' | 'bulk' = 'bulk'\n//   ): Promise<void> {\n//     const generator = await AssetLabelGenerator.generateAssetLabels(items, type)\n//     const defaultFilename =\n//       items.length === 1\n//         ? `Asset_Label_${items[0].assetNumber}.pdf`\n//         : `Asset_Labels_${new Date().toISOString().split('T')[0]}.pdf`\n//     generator.save(filename || defaultFilename)\n//   }\n// }\n\n\nclass AssetLabelGenerator {\n    /**\n   * Generate a single asset label - Professional format (1cm height)\n   */ async generateSingleLabel(item) {\n        this.doc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            orientation: \"landscape\",\n            unit: \"mm\",\n            format: [\n                35,\n                10\n            ] // 35mm x 10mm label size for better proportions\n        });\n        await this.drawMinimalLabel(item, 0, 0, 35, 10);\n    }\n    /**\n   * Generate multiple asset labels on A4 sheet - Professional layout\n   * Optimized for 1cm height labels: 6 columns x 28 rows = 168 labels per sheet\n   */ async generateBulkLabels(items) {\n        this.doc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            orientation: \"portrait\",\n            unit: \"mm\",\n            format: \"a4\"\n        });\n        const labelWidth = 35 // 35mm width for better proportions\n        ;\n        const labelHeight = 10 // 10mm height (1cm)\n        ;\n        const marginLeft = 0;\n        const marginTop = 0;\n        const cols = 6 // 6 columns to fit A4 width (210mm)\n        ;\n        const rows = 28 // 28 rows to fit A4 height (297mm)\n        ;\n        const labelsPerPage = cols * rows;\n        let currentPage = 0;\n        for(let i = 0; i < items.length; i++){\n            const pageIndex = Math.floor(i / labelsPerPage);\n            const positionOnPage = i % labelsPerPage;\n            // Add new page if needed\n            if (pageIndex > currentPage) {\n                this.doc.addPage();\n                currentPage = pageIndex;\n            }\n            const col = positionOnPage % cols;\n            const row = Math.floor(positionOnPage / cols);\n            const x = marginLeft + col * labelWidth;\n            const y = marginTop + row * labelHeight;\n            await this.drawMinimalLabel(items[i], x, y, labelWidth, labelHeight);\n        }\n    }\n    /**\n   * Draw a minimal label with Asset Number on left and QR code on right - Professional look\n   */ async drawMinimalLabel(item, x, y, width, height) {\n        // Generate QR code with asset details URL\n        const assetDetailsUrl = \"\".concat(window.location.origin, \"/asset-details/\").concat(item.assetNumber);\n        const qrCodeDataUrl = await qrcode__WEBPACK_IMPORTED_MODULE_1__.toDataURL(assetDetailsUrl, {\n            width: 256,\n            margin: 1,\n            color: {\n                dark: \"#000000\",\n                light: \"#FFFFFF\"\n            },\n            errorCorrectionLevel: \"M\"\n        });\n        // Clean border with professional appearance\n        this.doc.setDrawColor(0, 0, 0);\n        this.doc.setLineWidth(0.2);\n        this.doc.rect(x, y, width, height);\n        // QR Code (right side) - Professional square with padding\n        const qrSize = height - 2 // Leave 1mm margin on top and bottom\n        ;\n        const qrX = x + width - qrSize - 1 // 1mm margin from right edge\n        ;\n        const qrY = y + 1 // 1mm margin from top\n        ;\n        this.doc.addImage(qrCodeDataUrl, \"PNG\", qrX, qrY, qrSize, qrSize);\n        // Asset Number (left side) - Professional typography\n        const textStartX = x + 1.5 // 1.5mm margin from left\n        ;\n        const textCenterY = y + height / 2 + 1 // Vertically centered\n        ;\n        const availableTextWidth = width - qrSize - 3 // Available space for text\n        ;\n        // Use professional font styling\n        this.doc.setFont(\"helvetica\", \"bold\");\n        this.doc.setFontSize(9);\n        const assetNumber = item.assetNumber;\n        // Check if text fits in available width\n        let fontSize = 9;\n        let textWidth = this.doc.getTextWidth(assetNumber);\n        // Dynamically adjust font size to fit available space\n        while(textWidth > availableTextWidth && fontSize > 5){\n            fontSize -= 0.5;\n            this.doc.setFontSize(fontSize);\n            textWidth = this.doc.getTextWidth(assetNumber);\n        }\n        // Draw the asset number with optimal font size\n        this.doc.text(assetNumber, textStartX, textCenterY, {\n            baseline: \"middle\"\n        });\n    }\n    /**\n   * Draw a single label at specified position (legacy method for compatibility)\n   */ async drawLabel(item, x, y, width, height) {\n        // Generate QR code\n        const qrCodeDataUrl = await qrcode__WEBPACK_IMPORTED_MODULE_1__.toDataURL(item.assetNumber, {\n            width: 60,\n            margin: 1,\n            color: {\n                dark: \"#000000\",\n                light: \"#FFFFFF\"\n            }\n        });\n        // Draw border\n        this.doc.setDrawColor(0, 0, 0);\n        this.doc.setLineWidth(0.1);\n        this.doc.rect(x, y, width, height);\n        // QR Code (left side)\n        const qrSize = Math.min(height - 4, 15);\n        this.doc.addImage(qrCodeDataUrl, \"PNG\", x + 2, y + 2, qrSize, qrSize);\n        // Asset Number (main text)\n        this.doc.setFont(\"helvetica\", \"bold\");\n        this.doc.setFontSize(8);\n        this.doc.text(item.assetNumber, x + qrSize + 4, y + 6);\n        // Asset Name\n        this.doc.setFont(\"helvetica\", \"normal\");\n        this.doc.setFontSize(6);\n        const assetName = this.truncateText(item.assetName, 20);\n        this.doc.text(assetName, x + qrSize + 4, y + 10);\n        // Category and Location\n        this.doc.setFontSize(5);\n        this.doc.text(\"Cat: \".concat(this.truncateText(item.category, 12)), x + qrSize + 4, y + 14);\n        this.doc.text(\"Loc: \".concat(this.truncateText(item.location, 12)), x + qrSize + 4, y + 17);\n        // Status and Condition\n        this.doc.setFontSize(4);\n        this.doc.text(\"\".concat(item.status.toUpperCase(), \" | \").concat(item.condition.toUpperCase()), x + qrSize + 4, y + 21);\n        // Company info (bottom)\n        this.doc.setFontSize(4);\n        this.doc.text(\"Asset Management System\", x + 2, y + height - 2);\n    }\n    /**\n   * Truncate text to fit label\n   */ truncateText(text, maxLength) {\n        if (text.length <= maxLength) return text;\n        return text.substring(0, maxLength - 3) + \"...\";\n    }\n    /**\n   * Save the PDF\n   */ save(filename) {\n        this.doc.save(filename);\n    }\n    /**\n   * Open print dialog\n   */ openForPrint() {\n        this.doc.autoPrint();\n        window.open(this.doc.output(\"bloburl\"), \"_blank\");\n    }\n    /**\n   * Get PDF as blob\n   */ getBlob() {\n        return this.doc.output(\"blob\");\n    }\n    /**\n   * Generate asset number labels for printing\n   */ static async generateAssetLabels(items) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"bulk\";\n        const generator = new AssetLabelGenerator();\n        if (type === \"single\" && items.length === 1) {\n            await generator.generateSingleLabel(items[0]);\n        } else {\n            await generator.generateBulkLabels(items);\n        }\n        return generator;\n    }\n    /**\n   * Quick print function for asset items\n   */ static async printAssetLabels(items) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"bulk\";\n        const generator = await AssetLabelGenerator.generateAssetLabels(items, type);\n        generator.openForPrint();\n    }\n    /**\n   * Quick save function for asset items\n   */ static async saveAssetLabels(items, filename) {\n        let type = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"bulk\";\n        const generator = await AssetLabelGenerator.generateAssetLabels(items, type);\n        const defaultFilename = items.length === 1 ? \"Asset_Label_\".concat(items[0].assetNumber, \".pdf\") : \"Asset_Labels_\".concat(new Date().toISOString().split(\"T\")[0], \".pdf\");\n        generator.save(filename || defaultFilename);\n    }\n    constructor(){\n        this.doc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            orientation: \"portrait\",\n            unit: \"mm\",\n            format: \"a4\"\n        });\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/assetLabelGenerator.ts\n"));

/***/ })

});