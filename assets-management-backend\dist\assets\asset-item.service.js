"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetItemService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const asset_entity_1 = require("../entities/asset.entity");
const asset_item_entity_1 = require("../entities/asset-item.entity");
const user_entity_1 = require("../entities/user.entity");
let AssetItemService = class AssetItemService {
    assetRepository;
    assetItemRepository;
    userRepository;
    dataSource;
    constructor(assetRepository, assetItemRepository, userRepository, dataSource) {
        this.assetRepository = assetRepository;
        this.assetItemRepository = assetItemRepository;
        this.userRepository = userRepository;
        this.dataSource = dataSource;
    }
    async generateUniqueAssetNumber(assetId) {
        const asset = await this.assetRepository.findOne({
            where: { id: assetId },
        });
        if (!asset) {
            throw new common_1.NotFoundException('Asset not found');
        }
        const itemCount = await this.assetItemRepository.count({
            where: { assetId },
        });
        const itemSequence = String(itemCount + 1).padStart(3, '0');
        return `${asset.assetNumber}-${itemSequence}`;
    }
    async createAssetItem(createDto, userId) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const asset = await queryRunner.manager.findOne(asset_entity_1.Asset, {
                where: { id: createDto.assetId },
            });
            if (!asset) {
                throw new common_1.NotFoundException('Asset not found');
            }
            const assetNumber = await this.generateUniqueAssetNumber(createDto.assetId);
            const assetItem = queryRunner.manager.create(asset_item_entity_1.AssetItem, {
                ...createDto,
                assetNumber,
                lastModifiedById: userId,
            });
            const savedItem = await queryRunner.manager.save(asset_item_entity_1.AssetItem, assetItem);
            await queryRunner.commitTransaction();
            return this.getAssetItemById(savedItem.id);
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async createMultipleAssetItems(assetId, quantity, userId) {
        const existingItems = await this.getAssetItemsByAssetId(assetId);
        if (existingItems.length > 0) {
            console.log(`Asset items already exist for asset ${assetId}. Existing count: ${existingItems.length}`);
            return existingItems;
        }
        const items = [];
        for (let i = 0; i < quantity; i++) {
            const item = await this.createAssetItem({
                assetId,
                status: asset_entity_1.AssetStatus.IN_STOCK,
                condition: asset_entity_1.AssetCondition.EXCELLENT,
            }, userId);
            items.push(item);
        }
        console.log(`Created ${items.length} asset items for asset ${assetId}`);
        return items;
    }
    async getAssetItemById(id) {
        const item = await this.assetItemRepository.findOne({
            where: { id },
            relations: ['asset', 'assignedTo', 'lastModifiedBy'],
        });
        if (!item) {
            throw new common_1.NotFoundException('Asset item not found');
        }
        return item;
    }
    async getAssetItemsByAssetId(assetId) {
        return this.assetItemRepository.find({
            where: { assetId, isActive: true },
            relations: ['asset', 'assignedTo', 'lastModifiedBy'],
            order: { assetNumber: 'ASC' },
        });
    }
    async findByAssetNumber(assetNumber) {
        const assetItem = await this.assetItemRepository.findOne({
            where: { assetNumber, isActive: true },
            relations: ['assignedTo'],
        });
        if (!assetItem) {
            throw new common_1.NotFoundException(`Asset item with number ${assetNumber} not found`);
        }
        const asset = await this.assetRepository.findOne({
            where: { id: assetItem.assetId },
            relations: ['category', 'location'],
        });
        if (!asset) {
            throw new common_1.NotFoundException(`Asset not found for asset item ${assetNumber}`);
        }
        return {
            ...assetItem,
            asset: {
                id: asset.id,
                name: asset.name,
                description: asset.description,
                manufacturer: asset.manufacturer,
                model: asset.model,
                unitPrice: asset.unitPrice,
                category: {
                    id: asset.category.id,
                    name: asset.category.name,
                },
                location: {
                    id: asset.location.id,
                    name: asset.location.name,
                    type: asset.location.type,
                },
            },
        };
    }
    async getAssetItemsByStatus(status) {
        return this.assetItemRepository.find({
            where: { status, isActive: true },
            relations: ['asset', 'assignedTo', 'lastModifiedBy'],
            order: { assetNumber: 'ASC' },
        });
    }
    async updateAssetItem(id, updateDto, userId) {
        const item = await this.getAssetItemById(id);
        Object.assign(item, updateDto, { lastModifiedById: userId });
        await this.assetItemRepository.save(item);
        return this.getAssetItemById(id);
    }
    async transferAssetItem(transferDto, userId) {
        const item = await this.getAssetItemById(transferDto.assetItemId);
        if (item.status !== transferDto.fromStatus) {
            throw new common_1.BadRequestException(`Asset item is not in ${transferDto.fromStatus} status`);
        }
        const updateData = {
            status: transferDto.toStatus,
            lastModifiedById: userId,
        };
        if (transferDto.toStatus === asset_entity_1.AssetStatus.IN_USE &&
            transferDto.assignedToId) {
            updateData.assignedToId = transferDto.assignedToId;
            updateData.assignedDate = new Date();
        }
        if (transferDto.fromStatus === asset_entity_1.AssetStatus.IN_USE) {
            updateData.assignedToId = undefined;
            updateData.assignedDate = undefined;
        }
        Object.assign(item, updateData);
        await this.assetItemRepository.save(item);
        return this.getAssetItemById(transferDto.assetItemId);
    }
    async getAssetQuantitiesByStatus(assetId) {
        const items = await this.getAssetItemsByAssetId(assetId);
        const quantities = {
            [asset_entity_1.AssetStatus.IN_STOCK]: 0,
            [asset_entity_1.AssetStatus.IN_USE]: 0,
            [asset_entity_1.AssetStatus.MAINTENANCE]: 0,
            [asset_entity_1.AssetStatus.RETIRED]: 0,
            [asset_entity_1.AssetStatus.LOST]: 0,
            [asset_entity_1.AssetStatus.DAMAGED]: 0,
        };
        items.forEach((item) => {
            quantities[item.status]++;
        });
        return quantities;
    }
    async getAllAssetQuantities() {
        const assets = await this.assetRepository.find({
            where: { isActive: true },
            select: ['id', 'name'],
        });
        const result = [];
        for (const asset of assets) {
            const quantities = await this.getAssetQuantitiesByStatus(asset.id);
            const totalItems = Object.values(quantities).reduce((sum, count) => sum + count, 0);
            result.push({
                assetId: asset.id,
                assetName: asset.name,
                quantities,
                totalItems,
            });
        }
        return result;
    }
    async deleteAssetItem(id) {
        const item = await this.getAssetItemById(id);
        item.isActive = false;
        await this.assetItemRepository.save(item);
    }
};
exports.AssetItemService = AssetItemService;
exports.AssetItemService = AssetItemService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(asset_entity_1.Asset)),
    __param(1, (0, typeorm_1.InjectRepository)(asset_item_entity_1.AssetItem)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource])
], AssetItemService);
//# sourceMappingURL=asset-item.service.js.map