// Type Imports
import type { ChildrenType } from '@core/types'

// Component Imports
import Providers from '@components/Providers'
import BlankLayout from '@layouts/BlankLayout'
import { AuthProvider } from '@/contexts/AuthContext'

const Layout = ({ children }: ChildrenType) => {
  // Vars
  const direction = 'ltr'

  return (
    <Providers direction={direction}>
      <AuthProvider>
        <BlankLayout>{children}</BlankLayout>
      </AuthProvider>
    </Providers>
  )
}

export default Layout
