import { Repository } from 'typeorm';
import { Asset } from '../entities/asset.entity';
import { AssetItem } from '../entities/asset-item.entity';
import { Category } from '../entities/category.entity';
import { Location } from '../entities/location.entity';
import { User } from '../entities/user.entity';
import { AuditLog } from '../entities/audit-log.entity';
export interface ReportFilters {
    startDate?: Date;
    endDate?: Date;
    categoryId?: string;
    locationId?: string;
    status?: string;
    condition?: string;
    assignedUserId?: string;
}
export interface AssetReport {
    totalAssets: number;
    totalValue: number;
    statusBreakdown: {
        status: string;
        count: number;
        percentage: number;
    }[];
    conditionBreakdown: {
        condition: string;
        count: number;
        percentage: number;
    }[];
    categoryBreakdown: {
        category: string;
        count: number;
        percentage: number;
    }[];
    locationBreakdown: {
        location: string;
        count: number;
        percentage: number;
    }[];
    assets: any[];
}
export interface UtilizationReport {
    totalAssets: number;
    inUseAssets: number;
    availableAssets: number;
    utilizationRate: number;
    categoryUtilization: {
        category: string;
        total: number;
        inUse: number;
        rate: number;
    }[];
    locationUtilization: {
        location: string;
        total: number;
        inUse: number;
        rate: number;
    }[];
}
export interface MaintenanceReport {
    assetsInMaintenance: number;
    assetsDamaged: number;
    assetsRetired: number;
    maintenanceByCategory: {
        category: string;
        count: number;
    }[];
    maintenanceByLocation: {
        location: string;
        count: number;
    }[];
    maintenanceSchedule: any[];
}
export interface ActivityReport {
    totalActivities: number;
    recentActivities: AuditLog[];
    activityByAction: {
        action: string;
        count: number;
    }[];
    activityByUser: {
        user: string;
        count: number;
    }[];
    activityTrend: {
        date: string;
        count: number;
    }[];
}
export declare class ReportsService {
    private assetRepository;
    private assetItemRepository;
    private categoryRepository;
    private locationRepository;
    private userRepository;
    private auditLogRepository;
    constructor(assetRepository: Repository<Asset>, assetItemRepository: Repository<AssetItem>, categoryRepository: Repository<Category>, locationRepository: Repository<Location>, userRepository: Repository<User>, auditLogRepository: Repository<AuditLog>);
    generateAssetReport(filters?: ReportFilters): Promise<AssetReport>;
    generateUtilizationReport(filters?: ReportFilters): Promise<UtilizationReport>;
    generateMaintenanceReport(filters?: ReportFilters): Promise<MaintenanceReport>;
    generateActivityReport(filters?: ReportFilters): Promise<ActivityReport>;
}
