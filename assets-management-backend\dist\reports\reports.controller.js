"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const reports_service_1 = require("./reports.service");
const audit_logs_service_1 = require("../audit-logs/audit-logs.service");
const audit_log_entity_1 = require("../entities/audit-log.entity");
let ReportsController = class ReportsController {
    reportsService;
    auditLogsService;
    constructor(reportsService, auditLogsService) {
        this.reportsService = reportsService;
        this.auditLogsService = auditLogsService;
    }
    async generateAssetReport(query, req) {
        const filters = {
            startDate: query.startDate ? new Date(query.startDate) : undefined,
            endDate: query.endDate ? new Date(query.endDate) : undefined,
            categoryId: query.categoryId,
            locationId: query.locationId,
            status: query.status,
            condition: query.condition,
            assignedUserId: query.assignedUserId,
        };
        await this.auditLogsService.logSystemOperation(audit_log_entity_1.AuditAction.EXPORT, req.user?.userId || null, 'Generated asset report', { filters }, req.ip, req.get('User-Agent'));
        return await this.reportsService.generateAssetReport(filters);
    }
    async generateUtilizationReport(req) {
        await this.auditLogsService.logSystemOperation(audit_log_entity_1.AuditAction.EXPORT, req.user?.userId || null, 'Generated utilization report', {}, req.ip, req.get('User-Agent'));
        return await this.reportsService.generateUtilizationReport();
    }
    async generateMaintenanceReport(req) {
        await this.auditLogsService.logSystemOperation(audit_log_entity_1.AuditAction.EXPORT, req.user?.userId || null, 'Generated maintenance report', {}, req.ip, req.get('User-Agent'));
        return await this.reportsService.generateMaintenanceReport();
    }
    async generateActivityReport(query, req) {
        const filters = {
            startDate: query.startDate ? new Date(query.startDate) : undefined,
            endDate: query.endDate ? new Date(query.endDate) : undefined,
        };
        await this.auditLogsService.logSystemOperation(audit_log_entity_1.AuditAction.EXPORT, req.user?.userId || null, 'Generated activity report', { filters }, req.ip, req.get('User-Agent'));
        return await this.reportsService.generateActivityReport(filters);
    }
    async generateSummaryReport(req) {
        await this.auditLogsService.logSystemOperation(audit_log_entity_1.AuditAction.EXPORT, req.user?.userId || null, 'Generated summary report', {}, req.ip, req.get('User-Agent'));
        const [assetReport, utilizationReport, maintenanceReport, activityReport] = await Promise.all([
            this.reportsService.generateAssetReport(),
            this.reportsService.generateUtilizationReport(),
            this.reportsService.generateMaintenanceReport(),
            this.reportsService.generateActivityReport(),
        ]);
        return {
            assets: assetReport,
            utilization: utilizationReport,
            maintenance: maintenanceReport,
            activity: activityReport,
            generatedAt: new Date(),
        };
    }
};
exports.ReportsController = ReportsController;
__decorate([
    (0, common_1.Get)('assets'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate asset report' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Asset report generated successfully',
    }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'categoryId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'locationId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'condition', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'assignedUserId', required: false }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReportsController.prototype, "generateAssetReport", null);
__decorate([
    (0, common_1.Get)('utilization'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate utilization report' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Utilization report generated successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ReportsController.prototype, "generateUtilizationReport", null);
__decorate([
    (0, common_1.Get)('maintenance'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate maintenance report' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Maintenance report generated successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ReportsController.prototype, "generateMaintenanceReport", null);
__decorate([
    (0, common_1.Get)('activity'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate activity report' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Activity report generated successfully',
    }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReportsController.prototype, "generateActivityReport", null);
__decorate([
    (0, common_1.Get)('summary'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate summary report with all key metrics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Summary report generated successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ReportsController.prototype, "generateSummaryReport", null);
exports.ReportsController = ReportsController = __decorate([
    (0, swagger_1.ApiTags)('reports'),
    (0, common_1.Controller)('reports'),
    __metadata("design:paramtypes", [reports_service_1.ReportsService,
        audit_logs_service_1.AuditLogsService])
], ReportsController);
//# sourceMappingURL=reports.controller.js.map