"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/layout",{

/***/ "(app-pages-browser)/./src/components/layout/shared/Logo.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/shared/Logo.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _emotion_styled__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @emotion/styled */ \"(app-pages-browser)/./node_modules/@emotion/styled/dist/emotion-styled.browser.development.esm.js\");\n/* harmony import */ var _configs_themeConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @configs/themeConfig */ \"(app-pages-browser)/./src/configs/themeConfig.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  color: \",\n        \";\\n  font-size: 1.25rem;\\n  line-height: 1.2;\\n  font-weight: 600;\\n  letter-spacing: 0.15px;\\n  text-transform: uppercase;\\n  margin-inline-start: 10px;\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\n// Third-party Imports\n\n// Config Imports\n\nconst LogoText = _emotion_styled__WEBPACK_IMPORTED_MODULE_2__[\"default\"].span(_templateObject(), (param)=>{\n    let { color } = param;\n    return color !== null && color !== void 0 ? color : \"var(--mui-palette-text-primary)\";\n});\n_c = LogoText;\nconst Logo = (param)=>{\n    let { color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        className: \"flex items-center min-bs-[24px]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(LogoText, {\n            color: color,\n            children: _configs_themeConfig__WEBPACK_IMPORTED_MODULE_3__[\"default\"].templateName\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\Logo.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\Logo.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = Logo;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Logo);\nvar _c, _c1;\n$RefreshReg$(_c, \"LogoText\");\n$RefreshReg$(_c1, \"Logo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2xheW91dC9zaGFyZWQvTG9nby50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLQSxzQkFBc0I7QUFDYztBQUVwQyxpQkFBaUI7QUFDNkI7QUFNOUMsTUFBTUUsV0FBV0YsdURBQU1BLENBQUNHLElBQUksb0JBQ2pCO1FBQUMsRUFBRUMsS0FBSyxFQUFFO1dBQUtBLGtCQUFBQSxtQkFBQUEsUUFBUzs7S0FEN0JGO0FBVU4sTUFBTUcsT0FBTztRQUFDLEVBQUVELEtBQUssRUFBc0M7SUFDekQscUJBQ0UsOERBQUNFO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNMO1lBQVNFLE9BQU9BO3NCQUFRSCw0REFBV0EsQ0FBQ08sWUFBWTs7Ozs7Ozs7Ozs7QUFHdkQ7TUFOTUg7QUFRTiwrREFBZUEsSUFBSUEsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvc2hhcmVkL0xvZ28udHN4P2U1ZWQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbi8vIFJlYWN0IEltcG9ydHNcbmltcG9ydCB0eXBlIHsgQ1NTUHJvcGVydGllcyB9IGZyb20gJ3JlYWN0J1xuXG4vLyBUaGlyZC1wYXJ0eSBJbXBvcnRzXG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCdcblxuLy8gQ29uZmlnIEltcG9ydHNcbmltcG9ydCB0aGVtZUNvbmZpZyBmcm9tICdAY29uZmlncy90aGVtZUNvbmZpZydcblxudHlwZSBMb2dvVGV4dFByb3BzID0ge1xuICBjb2xvcj86IENTU1Byb3BlcnRpZXNbJ2NvbG9yJ11cbn1cblxuY29uc3QgTG9nb1RleHQgPSBzdHlsZWQuc3BhbjxMb2dvVGV4dFByb3BzPmBcbiAgY29sb3I6ICR7KHsgY29sb3IgfSkgPT4gY29sb3IgPz8gJ3ZhcigtLW11aS1wYWxldHRlLXRleHQtcHJpbWFyeSknfTtcbiAgZm9udC1zaXplOiAxLjI1cmVtO1xuICBsaW5lLWhlaWdodDogMS4yO1xuICBmb250LXdlaWdodDogNjAwO1xuICBsZXR0ZXItc3BhY2luZzogMC4xNXB4O1xuICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICBtYXJnaW4taW5saW5lLXN0YXJ0OiAxMHB4O1xuYFxuXG5jb25zdCBMb2dvID0gKHsgY29sb3IgfTogeyBjb2xvcj86IENTU1Byb3BlcnRpZXNbJ2NvbG9yJ10gfSkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGl0ZW1zLWNlbnRlciBtaW4tYnMtWzI0cHhdJz5cbiAgICAgIDxMb2dvVGV4dCBjb2xvcj17Y29sb3J9Pnt0aGVtZUNvbmZpZy50ZW1wbGF0ZU5hbWV9PC9Mb2dvVGV4dD5cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG5leHBvcnQgZGVmYXVsdCBMb2dvXG4iXSwibmFtZXMiOlsic3R5bGVkIiwidGhlbWVDb25maWciLCJMb2dvVGV4dCIsInNwYW4iLCJjb2xvciIsIkxvZ28iLCJkaXYiLCJjbGFzc05hbWUiLCJ0ZW1wbGF0ZU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/shared/Logo.tsx\n"));

/***/ })

});