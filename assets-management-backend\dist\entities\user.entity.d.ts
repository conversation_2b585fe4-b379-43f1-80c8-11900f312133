export declare enum UserRole {
    ADMIN = "admin",
    MANAGER = "manager",
    VIEWER = "viewer"
}
export declare enum UserStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    SUSPENDED = "suspended"
}
export declare class User {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    password?: string;
    role: UserRole;
    status: UserStatus;
    department: string;
    phoneNumber: string;
    lastLoginAt: Date;
    assignedAssetItems: any[];
    createdAt: Date;
    updatedAt: Date;
    get fullName(): string;
}
