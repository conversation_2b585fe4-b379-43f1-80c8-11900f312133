/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_views_account-settings_notifications_index_tsx";
exports.ids = ["_rsc_src_views_account-settings_notifications_index_tsx"];
exports.modules = {

/***/ "(rsc)/./src/@core/styles/table.module.css":
/*!*******************************************!*\
  !*** ./src/@core/styles/table.module.css ***!
  \*******************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"table\": \"table_table__cB3AL\",\n\t\"cellWithInput\": \"table_cellWithInput__N6u24\"\n};\n\nmodule.exports.__checksum = \"33f278f758db\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvQGNvcmUvc3R5bGVzL3RhYmxlLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC10ZXN0Ly4vc3JjL0Bjb3JlL3N0eWxlcy90YWJsZS5tb2R1bGUuY3NzPzBhMGEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwidGFibGVcIjogXCJ0YWJsZV90YWJsZV9fY0IzQUxcIixcblx0XCJjZWxsV2l0aElucHV0XCI6IFwidGFibGVfY2VsbFdpdGhJbnB1dF9fTjZ1MjRcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiMzNmMjc4Zjc1OGRiXCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/@core/styles/table.module.css\n");

/***/ }),

/***/ "(rsc)/./src/components/Form.tsx":
/*!*********************************!*\
  !*** ./src/components/Form.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Invicta\Upcoming Projects\Assets Management System\assets-management-app\src\components\Form.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/Link.tsx":
/*!*********************************!*\
  !*** ./src/components/Link.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Invicta\Upcoming Projects\Assets Management System\assets-management-app\src\components\Link.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/views/account-settings/notifications/index.tsx":
/*!************************************************************!*\
  !*** ./src/views/account-settings/notifications/index.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/Card */ \"(rsc)/./node_modules/@mui/material/Card/index.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_mui_material_Card__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/CardHeader */ \"(rsc)/./node_modules/@mui/material/CardHeader/index.js\");\n/* harmony import */ var _mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _mui_material_CardContent__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/CardContent */ \"(rsc)/./node_modules/@mui/material/CardContent/index.js\");\n/* harmony import */ var _mui_material_CardContent__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_mui_material_CardContent__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/Typography */ \"(rsc)/./node_modules/@mui/material/Typography/index.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _mui_material_Checkbox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/Checkbox */ \"(rsc)/./node_modules/@mui/material/Checkbox/index.js\");\n/* harmony import */ var _mui_material_Checkbox__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_mui_material_Checkbox__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _mui_material_Select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/Select */ \"(rsc)/./node_modules/@mui/material/Select/index.js\");\n/* harmony import */ var _mui_material_Select__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_mui_material_Select__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _mui_material_MenuItem__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material/MenuItem */ \"(rsc)/./node_modules/@mui/material/MenuItem/index.js\");\n/* harmony import */ var _mui_material_MenuItem__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_mui_material_MenuItem__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _mui_material_Grid__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/Grid */ \"(rsc)/./node_modules/@mui/material/Grid/index.js\");\n/* harmony import */ var _mui_material_Grid__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_mui_material_Grid__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/Button */ \"(rsc)/./node_modules/@mui/material/Button/index.js\");\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_mui_material_Button__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _components_Link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @components/Link */ \"(rsc)/./src/components/Link.tsx\");\n/* harmony import */ var _components_Form__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @components/Form */ \"(rsc)/./src/components/Form.tsx\");\n/* harmony import */ var _core_styles_table_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @core/styles/table.module.css */ \"(rsc)/./src/@core/styles/table.module.css\");\n/* harmony import */ var _core_styles_table_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_core_styles_table_module_css__WEBPACK_IMPORTED_MODULE_3__);\n// MUI Imports\n\n\n\n\n\n\n\n\n\n\n// Component Imports\n\n\n// Style Imports\n\n// Vars\nconst tableData = [\n    {\n        app: true,\n        email: true,\n        browser: true,\n        type: \"New for you\"\n    },\n    {\n        app: true,\n        email: true,\n        browser: true,\n        type: \"Account activity\"\n    },\n    {\n        app: false,\n        email: true,\n        browser: true,\n        type: \"A new browser used to sign in\"\n    },\n    {\n        app: false,\n        email: true,\n        browser: false,\n        type: \"A new device is linked\"\n    }\n];\nconst Notifications = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Card__WEBPACK_IMPORTED_MODULE_4___default()), {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_5___default()), {\n                title: \"Recent Devices\",\n                subheader: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        \"We need permission from your browser to show notifications.\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"text-primary\",\n                            children: \" Request Permission\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Form__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: (_core_styles_table_module_css__WEBPACK_IMPORTED_MODULE_3___default().table),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Browser\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"App\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"border-be\",\n                                    children: tableData.map((data, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Typography__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                        color: \"text.primary\",\n                                                        children: data.type\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Checkbox__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                        defaultChecked: data.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Checkbox__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                        defaultChecked: data.browser\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                        lineNumber: 87,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Checkbox__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                        defaultChecked: data.app\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_CardContent__WEBPACK_IMPORTED_MODULE_8___default()), {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Typography__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                className: \"mbe-6 font-medium\",\n                                children: \"When should we send you notifications?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Grid__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                container: true,\n                                spacing: 6,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Grid__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Select__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                            fullWidth: true,\n                                            defaultValue: \"online\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_MenuItem__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                                    value: \"online\",\n                                                    children: \"Only when I'm online\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_MenuItem__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                                    value: \"anytime\",\n                                                    children: \"Anytime\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Grid__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                        item: true,\n                                        xs: 12,\n                                        className: \"flex gap-4 flex-wrap\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Button__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                                variant: \"contained\",\n                                                type: \"submit\",\n                                                children: \"Save Changes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_mui_material_Button__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                                variant: \"outlined\",\n                                                color: \"secondary\",\n                                                type: \"reset\",\n                                                children: \"Reset\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\views\\\\account-settings\\\\notifications\\\\index.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Notifications);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/views/account-settings/notifications/index.tsx\n");

/***/ })

};
;