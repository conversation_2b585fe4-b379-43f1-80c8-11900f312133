'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Grid,
  Box,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  LocationOn as LocationIcon,
  Business as BuildingIcon,
  Room as RoomIcon,
  Map as MapIcon
} from '@mui/icons-material'
import { SimpleTreeView } from '@mui/x-tree-view/SimpleTreeView'
import { TreeItem } from '@mui/x-tree-view/TreeItem'
import { locationsService } from '@/lib/api'
import { Location } from '@/types/api'

// Mock data for development (fallback)
const mockLocations = [
  {
    id: '1',
    name: 'Main Office',
    type: 'building',
    description: 'Main office building in downtown',
    address: '123 Main St, City, State 12345',
    isActive: true,
    parentId: null,
    children: [
      {
        id: '2',
        name: 'Floor 1',
        type: 'floor',
        description: 'Ground floor',
        address: '',
        isActive: true,
        parentId: '1',
        children: [
          {
            id: '3',
            name: 'Reception',
            type: 'room',
            description: 'Main reception area',
            address: '',
            isActive: true,
            parentId: '2',
            children: []
          },
          {
            id: '4',
            name: 'IT Department',
            type: 'room',
            description: 'IT department office',
            address: '',
            isActive: true,
            parentId: '2',
            children: []
          }
        ]
      },
      {
        id: '5',
        name: 'Floor 2',
        type: 'floor',
        description: 'Second floor',
        address: '',
        isActive: true,
        parentId: '1',
        children: [
          {
            id: '6',
            name: 'Room 201',
            type: 'room',
            description: 'Conference room',
            address: '',
            isActive: true,
            parentId: '5',
            children: []
          },
          {
            id: '7',
            name: 'Room 202',
            type: 'room',
            description: 'Office space',
            address: '',
            isActive: true,
            parentId: '5',
            children: []
          }
        ]
      }
    ]
  },
  {
    id: '8',
    name: 'Warehouse',
    type: 'building',
    description: 'Storage warehouse',
    address: '456 Industrial Ave, City, State 12345',
    isActive: true,
    parentId: null,
    children: [
      {
        id: '9',
        name: 'Section A',
        type: 'area',
        description: 'Storage section A',
        address: '',
        isActive: true,
        parentId: '8',
        children: []
      },
      {
        id: '10',
        name: 'Section B',
        type: 'area',
        description: 'Storage section B',
        address: '',
        isActive: true,
        parentId: '8',
        children: []
      }
    ]
  }
]

// Using the imported Location type from @/types/api

const locationTypes = [
  { value: 'region', label: 'Region' },
  { value: 'building', label: 'Building' },
  { value: 'floor', label: 'Floor' },
  { value: 'room', label: 'Room' },
  { value: 'area', label: 'Area' }
]

const getLocationIcon = (type: string) => {
  switch (type) {
    case 'building':
      return <BuildingIcon />
    case 'room':
      return <RoomIcon />
    case 'area':
      return <MapIcon />
    default:
      return <LocationIcon />
  }
}

export default function LocationsPage() {
  const [locations, setLocations] = useState<Location[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [viewMode, setViewMode] = useState<'tree' | 'table'>('tree')

  // Load locations from API
  useEffect(() => {
    const loadLocations = async () => {
      try {
        setLoading(true)
        setError(null)

        // Use hierarchy endpoint for proper tree structure
        const data = await locationsService.getLocationHierarchy()
        setLocations(data)
      } catch (err: any) {
        console.error('Failed to load locations:', err)
        if (err.message?.includes('401') || err.message?.includes('Unauthorized')) {
          setError('Authentication failed. Please log in again.')
          setTimeout(() => {
            window.location.href = '/login'
          }, 2000)
        } else {
          setError('Failed to load locations. Please check if the backend server is running and try again.')
        }
      } finally {
        setLoading(false)
      }
    }

    loadLocations()
  }, [])

  const [formData, setFormData] = useState({
    name: '',
    type: 'room',
    description: '',
    address: '',
    parentId: '',
    isActive: true
  })

  const handleAddLocation = () => {
    setFormData({
      name: '',
      type: 'room',
      description: '',
      address: '',
      parentId: '',
      isActive: true
    })
    setIsEditing(false)
    setDialogOpen(true)
  }

  const handleEditLocation = (location: Location) => {
    setFormData({
      name: location.name,
      type: location.type,
      description: location.description,
      address: location.address,
      parentId: location.parentId || '',
      isActive: location.isActive
    })
    setSelectedLocation(location)
    setIsEditing(true)
    setDialogOpen(true)
  }

  const handleDeleteLocation = (location: Location) => {
    setSelectedLocation(location)
    setDeleteDialogOpen(true)
  }

  const refreshLocations = async () => {
    try {
      const data = await locationsService.getLocationHierarchy()
      setLocations(data)
    } catch (err) {
      console.error('Failed to refresh locations:', err)
      setError('Failed to refresh locations. Please try again.')
    }
  }

  const handleSaveLocation = async () => {
    try {
      if (isEditing && selectedLocation) {
        // Update existing location
        await locationsService.updateLocation(selectedLocation.id, {
          name: formData.name,
          type: formData.type as any,
          description: formData.description,
          address: formData.address,
          parentId: formData.parentId || undefined,
          isActive: formData.isActive
        })
      } else {
        // Create new location
        await locationsService.createLocation({
          name: formData.name,
          type: formData.type as any,
          description: formData.description,
          address: formData.address,
          parentId: formData.parentId || undefined,
          isActive: formData.isActive
        })
      }

      // Refresh the entire hierarchy
      await refreshLocations()
      setDialogOpen(false)
      setSelectedLocation(null)
    } catch (err) {
      console.error('Failed to save location:', err)
      setError('Failed to save location. Please try again.')
    }
  }

  const confirmDelete = async () => {
    if (!selectedLocation) return

    try {
      await locationsService.deleteLocation(selectedLocation.id)
      // Refresh the entire hierarchy
      await refreshLocations()
      setDeleteDialogOpen(false)
      setSelectedLocation(null)
    } catch (err) {
      console.error('Failed to delete location:', err)
      setError('Failed to delete location. Please try again.')
    }
  }

  const getAllLocations = (locs: Location[]): Location[] => {
    let result: Location[] = []
    locs.forEach(loc => {
      result.push(loc)
      if (loc.children && loc.children.length > 0) {
        result = result.concat(getAllLocations(loc.children))
      }
    })
    return result
  }

  // Helper function to build full path for a location
  const getLocationPath = (location: Location, allLocs: Location[]): string => {
    if (!location.parentId) {
      return location.name
    }
    const parent = allLocs.find(loc => loc.id === location.parentId)
    if (parent) {
      return `${getLocationPath(parent, allLocs)} > ${location.name}`
    }
    return location.name
  }

  const renderTreeItem = (location: Location, level: number = 0) => (
      <TreeItem
        key={location.id}
        itemId={location.id}
        label={
          <Box sx={{ display: 'flex', alignItems: 'center', py: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
              {getLocationIcon(location.type)}
              <Typography variant='body1' sx={{ mx: 2, fontWeight: level === 0 ? 600 : 400 }}>
                {location.name}
              </Typography>
              <Chip
                label={locationTypes.find(t => t.value === location.type)?.label || location.type}
                size='small'
                variant='outlined'
                sx={{ mr: 1 }}
              />
              {level > 0 && (
                <Chip
                  label={`Level ${level + 1}`}
                  size='small'
                  variant='outlined'
                  color='info'
                  sx={{ mr: 1, fontSize: '0.7rem' }}
                />
              )}
              {!location.isActive && <Chip label='Inactive' size='small' color='error' sx={{ mr: 1 }} />}
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title='Edit'>
                <IconButton
                  size='small'
                  onClick={e => {
                    e.stopPropagation()
                    handleEditLocation(location)
                  }}
                >
                  <EditIcon fontSize='small' />
                </IconButton>
              </Tooltip>
              <Tooltip title='Delete'>
                <IconButton
                  size='small'
                  onClick={e => {
                    e.stopPropagation()
                    handleDeleteLocation(location)
                  }}
                >
                  <DeleteIcon fontSize='small' />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        }
      >
        {location.children?.map(child => renderTreeItem(child, level + 1))}
      </TreeItem>
    )
  )

  const flatLocations = getAllLocations(locations)

  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading locations...</Typography>
      </Box>
    )
  }

  return (
    <Box sx={{ p: 4, backgroundColor: 'grey.50', minHeight: '100vh' }}>
      {/* Error Alert */}
      {error && (
        <Alert severity='error' sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant='h4' component='h1'>
          Location Management
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControlLabel
            control={
              <Switch checked={viewMode === 'tree'} onChange={e => setViewMode(e.target.checked ? 'tree' : 'table')} />
            }
            label='Tree View'
          />
          <Button variant='contained' startIcon={<AddIcon />} onClick={handleAddLocation} color='primary'>
            Add Location
          </Button>
        </Box>
      </Box>

      {/* Locations Display */}
      <Card
        sx={{
          border: '1px solid',
          borderColor: 'divider',
          boxShadow: 'none'
        }}
      >
        <CardContent>
          {viewMode === 'tree' ? (
            <SimpleTreeView
              defaultCollapseIcon={<ExpandMoreIcon />}
              defaultExpandIcon={<ChevronRightIcon />}
              defaultExpandedItems={getAllLocations(locations).map(loc => loc.id)}
            >
              {locations.map(location => renderTreeItem(location, 0))}
            </SimpleTreeView>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Address</TableCell>
                    <TableCell>Parent</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell align='center'>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {flatLocations.map(location => (
                    <TableRow key={location.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {getLocationIcon(location.type)}
                          <Typography sx={{ ml: 1 }}>{location.name}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={locationTypes.find(t => t.value === location.type)?.label || location.type}
                          size='small'
                          variant='outlined'
                        />
                      </TableCell>
                      <TableCell>{location.description}</TableCell>
                      <TableCell>{location.address || '-'}</TableCell>
                      <TableCell>
                        {location.parentId ? flatLocations.find(l => l.id === location.parentId)?.name || '-' : 'Root'}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={location.isActive ? 'Active' : 'Inactive'}
                          color={location.isActive ? 'success' : 'error'}
                          size='small'
                        />
                      </TableCell>
                      <TableCell align='center'>
                        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                          <Tooltip title='Edit'>
                            <IconButton size='small' onClick={() => handleEditLocation(location)}>
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title='Delete'>
                            <IconButton size='small' onClick={() => handleDeleteLocation(location)}>
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Location Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth='sm' fullWidth>
        <DialogTitle>{isEditing ? 'Edit Location' : 'Add New Location'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={8}>
              <TextField
                fullWidth
                label='Location Name'
                value={formData.name}
                onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth required>
                <InputLabel>Type</InputLabel>
                <Select
                  value={formData.type}
                  label='Type'
                  onChange={e => setFormData(prev => ({ ...prev, type: e.target.value }))}
                >
                  {locationTypes.map(type => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Description'
                value={formData.description}
                onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Address'
                value={formData.address}
                onChange={e => setFormData(prev => ({ ...prev, address: e.target.value }))}
                placeholder='Full address (optional)'
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Parent Location</InputLabel>
                <Select
                  value={formData.parentId}
                  label='Parent Location'
                  onChange={e => setFormData(prev => ({ ...prev, parentId: e.target.value }))}
                >
                  <MenuItem value=''>None (Root Location)</MenuItem>
                  {flatLocations
                    .filter(loc => !isEditing || loc.id !== selectedLocation?.id)
                    .map(location => (
                      <MenuItem key={location.id} value={location.id}>
                        {getLocationPath(location, flatLocations)} (
                        {locationTypes.find(t => t.value === location.type)?.label})
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={e => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  />
                }
                label='Active'
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveLocation} variant='contained'>
            {isEditing ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete location "{selectedLocation?.name}"?
            {selectedLocation?.children && selectedLocation.children.length > 0
              ? ' This will also delete all sub-locations.'
              : ''}
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color='error' variant='contained'>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}
