"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/api/auth.ts":
/*!*****************************!*\
  !*** ./src/lib/api/auth.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: function() { return /* binding */ AuthService; },\n/* harmony export */   authService: function() { return /* binding */ authService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n\n\nclass AuthService {\n    async login(credentials) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.LOGIN, credentials);\n        // Store the token\n        (0,_config__WEBPACK_IMPORTED_MODULE_1__.setAuthToken)(response.token);\n        return response;\n    }\n    async register(userData) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.REGISTER, userData);\n        // Store the token\n        (0,_config__WEBPACK_IMPORTED_MODULE_1__.setAuthToken)(response.token);\n        return response;\n    }\n    async getProfile() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.PROFILE);\n    }\n    async verifyToken() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.VERIFY_TOKEN);\n    }\n    async forgotPassword(email) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.FORGOT_PASSWORD, {\n            email\n        });\n    }\n    async resetPassword(token, newPassword) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.RESET_PASSWORD, {\n            token,\n            newPassword\n        });\n    }\n    logout() {\n        (0,_config__WEBPACK_IMPORTED_MODULE_1__.removeAuthToken)();\n        // Redirect to login page\n        if (true) {\n            window.location.href = \"/login\";\n        }\n    }\n    isAuthenticated() {\n        if (false) {}\n        const token = localStorage.getItem(\"auth_token\");\n        return !!token;\n    }\n}\nconst authService = new AuthService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvYXBpL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFvQztBQUNnQztBQUc3RCxNQUFNSTtJQUNYLE1BQU1DLE1BQU1DLFdBQXlCLEVBQXlCO1FBQzVELE1BQU1DLFdBQVcsTUFBTVAsOENBQVNBLENBQUNRLElBQUksQ0FBZVAsK0NBQVVBLENBQUNRLFNBQVMsQ0FBQ0MsSUFBSSxDQUFDQyxLQUFLLEVBQUVMO1FBRXJGLGtCQUFrQjtRQUNsQkoscURBQVlBLENBQUNLLFNBQVNLLEtBQUs7UUFFM0IsT0FBT0w7SUFDVDtJQUVBLE1BQU1NLFNBQVNDLFFBQXlCLEVBQXlCO1FBQy9ELE1BQU1QLFdBQVcsTUFBTVAsOENBQVNBLENBQUNRLElBQUksQ0FBZVAsK0NBQVVBLENBQUNRLFNBQVMsQ0FBQ0MsSUFBSSxDQUFDSyxRQUFRLEVBQUVEO1FBRXhGLGtCQUFrQjtRQUNsQloscURBQVlBLENBQUNLLFNBQVNLLEtBQUs7UUFFM0IsT0FBT0w7SUFDVDtJQUVBLE1BQU1TLGFBQTRCO1FBQ2hDLE9BQU9oQiw4Q0FBU0EsQ0FBQ2lCLEdBQUcsQ0FBT2hCLCtDQUFVQSxDQUFDUSxTQUFTLENBQUNDLElBQUksQ0FBQ1EsT0FBTztJQUM5RDtJQUVBLE1BQU1DLGNBQWdFO1FBQ3BFLE9BQU9uQiw4Q0FBU0EsQ0FBQ1EsSUFBSSxDQUEwQ1AsK0NBQVVBLENBQUNRLFNBQVMsQ0FBQ0MsSUFBSSxDQUFDVSxZQUFZO0lBQ3ZHO0lBRUEsTUFBTUMsZUFBZUMsS0FBYSxFQUFxRDtRQUNyRixPQUFPdEIsOENBQVNBLENBQUNRLElBQUksQ0FBMkNQLCtDQUFVQSxDQUFDUSxTQUFTLENBQUNDLElBQUksQ0FBQ2EsZUFBZSxFQUFFO1lBQ3pHRDtRQUNGO0lBQ0Y7SUFFQSxNQUFNRSxjQUFjWixLQUFhLEVBQUVhLFdBQW1CLEVBQWdDO1FBQ3BGLE9BQU96Qiw4Q0FBU0EsQ0FBQ1EsSUFBSSxDQUFzQlAsK0NBQVVBLENBQUNRLFNBQVMsQ0FBQ0MsSUFBSSxDQUFDZ0IsY0FBYyxFQUFFO1lBQUVkO1lBQU9hO1FBQVk7SUFDNUc7SUFFQUUsU0FBZTtRQUNieEIsd0RBQWVBO1FBQ2YseUJBQXlCO1FBQ3pCLElBQUksSUFBa0IsRUFBYTtZQUNqQ3lCLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO1FBQ3pCO0lBQ0Y7SUFFQUMsa0JBQTJCO1FBQ3pCLElBQUksS0FBa0IsRUFBYSxFQUFPO1FBQzFDLE1BQU1uQixRQUFRb0IsYUFBYUMsT0FBTyxDQUFDO1FBQ25DLE9BQU8sQ0FBQyxDQUFDckI7SUFDWDtBQUNGO0FBRU8sTUFBTXNCLGNBQWMsSUFBSTlCLGNBQWEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi9hcGkvYXV0aC50cz84ODNjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFwaUNsaWVudCB9IGZyb20gJy4vY2xpZW50J1xuaW1wb3J0IHsgQVBJX0NPTkZJRywgc2V0QXV0aFRva2VuLCByZW1vdmVBdXRoVG9rZW4gfSBmcm9tICcuL2NvbmZpZydcbmltcG9ydCB7IExvZ2luUmVxdWVzdCwgUmVnaXN0ZXJSZXF1ZXN0LCBBdXRoUmVzcG9uc2UsIFVzZXIgfSBmcm9tICdAL3R5cGVzL2FwaSdcblxuZXhwb3J0IGNsYXNzIEF1dGhTZXJ2aWNlIHtcbiAgYXN5bmMgbG9naW4oY3JlZGVudGlhbHM6IExvZ2luUmVxdWVzdCk6IFByb21pc2U8QXV0aFJlc3BvbnNlPiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdDxBdXRoUmVzcG9uc2U+KEFQSV9DT05GSUcuRU5EUE9JTlRTLkFVVEguTE9HSU4sIGNyZWRlbnRpYWxzKVxuXG4gICAgLy8gU3RvcmUgdGhlIHRva2VuXG4gICAgc2V0QXV0aFRva2VuKHJlc3BvbnNlLnRva2VuKVxuXG4gICAgcmV0dXJuIHJlc3BvbnNlXG4gIH1cblxuICBhc3luYyByZWdpc3Rlcih1c2VyRGF0YTogUmVnaXN0ZXJSZXF1ZXN0KTogUHJvbWlzZTxBdXRoUmVzcG9uc2U+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0PEF1dGhSZXNwb25zZT4oQVBJX0NPTkZJRy5FTkRQT0lOVFMuQVVUSC5SRUdJU1RFUiwgdXNlckRhdGEpXG5cbiAgICAvLyBTdG9yZSB0aGUgdG9rZW5cbiAgICBzZXRBdXRoVG9rZW4ocmVzcG9uc2UudG9rZW4pXG5cbiAgICByZXR1cm4gcmVzcG9uc2VcbiAgfVxuXG4gIGFzeW5jIGdldFByb2ZpbGUoKTogUHJvbWlzZTxVc2VyPiB7XG4gICAgcmV0dXJuIGFwaUNsaWVudC5nZXQ8VXNlcj4oQVBJX0NPTkZJRy5FTkRQT0lOVFMuQVVUSC5QUk9GSUxFKVxuICB9XG5cbiAgYXN5bmMgdmVyaWZ5VG9rZW4oKTogUHJvbWlzZTx7IHZhbGlkOiBib29sZWFuOyB1c2VyOiBQYXJ0aWFsPFVzZXI+IH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LnBvc3Q8eyB2YWxpZDogYm9vbGVhbjsgdXNlcjogUGFydGlhbDxVc2VyPiB9PihBUElfQ09ORklHLkVORFBPSU5UUy5BVVRILlZFUklGWV9UT0tFTilcbiAgfVxuXG4gIGFzeW5jIGZvcmdvdFBhc3N3b3JkKGVtYWlsOiBzdHJpbmcpOiBQcm9taXNlPHsgbWVzc2FnZTogc3RyaW5nOyByZXNldFRva2VuPzogc3RyaW5nIH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LnBvc3Q8eyBtZXNzYWdlOiBzdHJpbmc7IHJlc2V0VG9rZW4/OiBzdHJpbmcgfT4oQVBJX0NPTkZJRy5FTkRQT0lOVFMuQVVUSC5GT1JHT1RfUEFTU1dPUkQsIHtcbiAgICAgIGVtYWlsXG4gICAgfSlcbiAgfVxuXG4gIGFzeW5jIHJlc2V0UGFzc3dvcmQodG9rZW46IHN0cmluZywgbmV3UGFzc3dvcmQ6IHN0cmluZyk6IFByb21pc2U8eyBtZXNzYWdlOiBzdHJpbmcgfT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQucG9zdDx7IG1lc3NhZ2U6IHN0cmluZyB9PihBUElfQ09ORklHLkVORFBPSU5UUy5BVVRILlJFU0VUX1BBU1NXT1JELCB7IHRva2VuLCBuZXdQYXNzd29yZCB9KVxuICB9XG5cbiAgbG9nb3V0KCk6IHZvaWQge1xuICAgIHJlbW92ZUF1dGhUb2tlbigpXG4gICAgLy8gUmVkaXJlY3QgdG8gbG9naW4gcGFnZVxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2xvZ2luJ1xuICAgIH1cbiAgfVxuXG4gIGlzQXV0aGVudGljYXRlZCgpOiBib29sZWFuIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBmYWxzZVxuICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2F1dGhfdG9rZW4nKVxuICAgIHJldHVybiAhIXRva2VuXG4gIH1cbn1cblxuZXhwb3J0IGNvbnN0IGF1dGhTZXJ2aWNlID0gbmV3IEF1dGhTZXJ2aWNlKClcbiJdLCJuYW1lcyI6WyJhcGlDbGllbnQiLCJBUElfQ09ORklHIiwic2V0QXV0aFRva2VuIiwicmVtb3ZlQXV0aFRva2VuIiwiQXV0aFNlcnZpY2UiLCJsb2dpbiIsImNyZWRlbnRpYWxzIiwicmVzcG9uc2UiLCJwb3N0IiwiRU5EUE9JTlRTIiwiQVVUSCIsIkxPR0lOIiwidG9rZW4iLCJyZWdpc3RlciIsInVzZXJEYXRhIiwiUkVHSVNURVIiLCJnZXRQcm9maWxlIiwiZ2V0IiwiUFJPRklMRSIsInZlcmlmeVRva2VuIiwiVkVSSUZZX1RPS0VOIiwiZm9yZ290UGFzc3dvcmQiLCJlbWFpbCIsIkZPUkdPVF9QQVNTV09SRCIsInJlc2V0UGFzc3dvcmQiLCJuZXdQYXNzd29yZCIsIlJFU0VUX1BBU1NXT1JEIiwibG9nb3V0Iiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwiaXNBdXRoZW50aWNhdGVkIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImF1dGhTZXJ2aWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/auth.ts\n"));

/***/ })

});