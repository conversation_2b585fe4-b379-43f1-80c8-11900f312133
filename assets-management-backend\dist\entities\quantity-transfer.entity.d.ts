import { Asset, AssetStatus } from './asset.entity';
import { User } from './user.entity';
export declare enum TransferType {
    MANUAL = "manual",
    STATUS_CHANGE = "status_change",
    BULK_TRANSFER = "bulk_transfer",
    INITIAL_STOCK = "initial_stock"
}
export declare class QuantityTransfer {
    id: string;
    asset: Asset;
    assetId: string;
    fromStatus: AssetStatus | null;
    toStatus: AssetStatus;
    quantity: number;
    transferType: TransferType;
    reason: string;
    notes: string;
    transferredBy: User;
    transferredById: string;
    transferredAt: Date;
    batchId: string;
    metadata: any;
}
