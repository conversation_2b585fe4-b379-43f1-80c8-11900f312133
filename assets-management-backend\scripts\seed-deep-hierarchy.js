const axios = require('axios');

const API_BASE = 'http://localhost:3001';

// First, login to get a token
async function login() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    return response.data.token;
  } catch (error) {
    console.error('Lo<PERSON> failed:', error.response?.data || error.message);
    throw error;
  }
}

// Create deep hierarchy test data
async function seedDeepHierarchy() {
  try {
    console.log('🔐 Logging in...');
    const token = await login();
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    console.log('📁 Creating deep category hierarchy...');
    
    // Level 1: Root Categories
    const itCategory = await axios.post(`${API_BASE}/categories`, {
      name: 'IT Equipment',
      code: 'IT',
      description: 'Information Technology equipment and devices'
    }, { headers });

    // Level 2: Hardware & Software
    const hardwareCategory = await axios.post(`${API_BASE}/categories`, {
      name: 'Hardware',
      code: 'HW',
      description: 'Physical IT equipment',
      parentId: itCategory.data.id
    }, { headers });

    const softwareCategory = await axios.post(`${API_BASE}/categories`, {
      name: 'Software',
      code: 'SW',
      description: 'Software and licenses',
      parentId: itCategory.data.id
    }, { headers });

    // Level 3: Computer Types
    const computersCategory = await axios.post(`${API_BASE}/categories`, {
      name: 'Computers',
      code: 'COMP',
      description: 'Desktop and laptop computers',
      parentId: hardwareCategory.data.id
    }, { headers });

    const peripheralsCategory = await axios.post(`${API_BASE}/categories`, {
      name: 'Peripherals',
      code: 'PERI',
      description: 'Computer peripherals and accessories',
      parentId: hardwareCategory.data.id
    }, { headers });

    // Level 4: Specific Computer Types
    const laptopsCategory = await axios.post(`${API_BASE}/categories`, {
      name: 'Laptops',
      code: 'LAP',
      description: 'Portable computers',
      parentId: computersCategory.data.id
    }, { headers });

    const desktopsCategory = await axios.post(`${API_BASE}/categories`, {
      name: 'Desktops',
      code: 'DESK',
      description: 'Desktop computers',
      parentId: computersCategory.data.id
    }, { headers });

    // Level 5: Laptop Subcategories
    const gamingLaptopsCategory = await axios.post(`${API_BASE}/categories`, {
      name: 'Gaming Laptops',
      code: 'GAMLAP',
      description: 'High-performance gaming laptops',
      parentId: laptopsCategory.data.id
    }, { headers });

    const businessLaptopsCategory = await axios.post(`${API_BASE}/categories`, {
      name: 'Business Laptops',
      code: 'BIZLAP',
      description: 'Professional business laptops',
      parentId: laptopsCategory.data.id
    }, { headers });

    // Level 6: Gaming Laptop Brands
    const dellGamingCategory = await axios.post(`${API_BASE}/categories`, {
      name: 'Dell Gaming',
      code: 'DELLGAM',
      description: 'Dell gaming laptop series',
      parentId: gamingLaptopsCategory.data.id
    }, { headers });

    console.log('🏢 Creating deep location hierarchy...');
    
    // Level 1: Company
    const company = await axios.post(`${API_BASE}/locations`, {
      name: 'TechCorp Inc.',
      type: 'region',
      description: 'Main company headquarters'
    }, { headers });

    // Level 2: Buildings
    const mainBuilding = await axios.post(`${API_BASE}/locations`, {
      name: 'Main Building',
      type: 'building',
      description: 'Primary office building',
      parentId: company.data.id
    }, { headers });

    const annexBuilding = await axios.post(`${API_BASE}/locations`, {
      name: 'Annex Building',
      type: 'building',
      description: 'Secondary office building',
      parentId: company.data.id
    }, { headers });

    // Level 3: Floors
    const floor1 = await axios.post(`${API_BASE}/locations`, {
      name: 'Floor 1',
      type: 'floor',
      description: 'Ground floor',
      parentId: mainBuilding.data.id
    }, { headers });

    const floor2 = await axios.post(`${API_BASE}/locations`, {
      name: 'Floor 2',
      type: 'floor',
      description: 'Second floor',
      parentId: mainBuilding.data.id
    }, { headers });

    // Level 4: Departments
    const itDept = await axios.post(`${API_BASE}/locations`, {
      name: 'IT Department',
      type: 'area',
      description: 'Information Technology department',
      parentId: floor2.data.id
    }, { headers });

    const hrDept = await axios.post(`${API_BASE}/locations`, {
      name: 'HR Department',
      type: 'area',
      description: 'Human Resources department',
      parentId: floor1.data.id
    }, { headers });

    // Level 5: Rooms
    const serverRoom = await axios.post(`${API_BASE}/locations`, {
      name: 'Server Room',
      type: 'room',
      description: 'Main server and networking room',
      parentId: itDept.data.id
    }, { headers });

    const devRoom = await axios.post(`${API_BASE}/locations`, {
      name: 'Development Room',
      type: 'room',
      description: 'Software development workspace',
      parentId: itDept.data.id
    }, { headers });

    // Level 6: Specific Areas
    const rack1 = await axios.post(`${API_BASE}/locations`, {
      name: 'Server Rack 1',
      type: 'area',
      description: 'First server rack',
      parentId: serverRoom.data.id
    }, { headers });

    const rack2 = await axios.post(`${API_BASE}/locations`, {
      name: 'Server Rack 2',
      type: 'area',
      description: 'Second server rack',
      parentId: serverRoom.data.id
    }, { headers });

    console.log('✅ Deep hierarchy test data created successfully!');
    console.log('📊 Created hierarchy structure:');
    console.log('   Categories: 6 levels deep (IT Equipment → Hardware → Computers → Laptops → Gaming Laptops → Dell Gaming)');
    console.log('   Locations: 6 levels deep (Company → Building → Floor → Department → Room → Rack)');
    console.log('🚀 You can now test the multi-level tree views!');
    
  } catch (error) {
    console.error('❌ Error seeding deep hierarchy data:', error.response?.data || error.message);
  }
}

seedDeepHierarchy();
