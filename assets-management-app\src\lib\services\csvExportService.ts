import { assetsService } from '@/lib/api/assets'
import { assetItemService } from '@/lib/api/assetItemService'

export interface AssetCsvRow {
  // Main Asset Information
  assetId: string
  assetNumber: string
  assetName: string
  description: string
  category: string
  location: string
  locationType: string
  manufacturer: string
  model: string
  unitPrice: number
  totalQuantity: number
  totalValue: number
  supplier: string
  invoiceNumber: string
  assetNotes: string
  assetCreatedAt: string

  // Individual Asset Item Information
  itemId: string
  itemAssetNumber: string
  itemStatus: string
  itemCondition: string
  serialNumber: string
  purchaseDate: string
  warrantyExpiry: string
  itemNotes: string
  assignedToName: string
  assignedToEmail: string
  assignedDate: string
  itemCreatedAt: string
  itemUpdatedAt: string
}

export class CsvExportService {
  /**
   * Export all assets with their individual items to CSV
   */
  static async exportAssetsWithItems(): Promise<void> {
    try {
      // Get all assets (get all pages)
      const assetsResponse = await assetsService.getAssets({ limit: 1000 }) // Get up to 1000 assets
      const assets = assetsResponse.assets
      console.log('Loaded assets for CSV export:', assets.length)

      const csvRows: AssetCsvRow[] = []

      // Process each asset and its items
      for (const asset of assets) {
        try {
          // Get individual items for this asset
          const assetItems = await assetItemService.getAssetItems(asset.id)
          console.log(`Asset ${asset.name} has ${assetItems.length} items`)

          if (assetItems.length === 0) {
            // If no individual items, create a row with just asset data
            csvRows.push({
              // Main Asset Information
              assetId: asset.id,
              assetNumber: asset.assetNumber || '',
              assetName: asset.name,
              description: asset.description || '',
              category: asset.category?.name || '',
              location: asset.location?.name || '',
              locationType: asset.location?.type || '',
              manufacturer: asset.manufacturer || '',
              model: asset.model || '',
              unitPrice: asset.unitPrice || 0,
              totalQuantity: asset.quantity || 0,
              totalValue: asset.totalValue || 0,
              supplier: asset.supplier || '',
              invoiceNumber: asset.invoiceNumber || '',
              assetNotes: asset.notes || '',
              assetCreatedAt: asset.createdAt ? new Date(asset.createdAt).toLocaleDateString() : '',

              // Empty Individual Asset Item Information
              itemId: '',
              itemAssetNumber: '',
              itemStatus: '',
              itemCondition: '',
              serialNumber: '',
              purchaseDate: '',
              warrantyExpiry: '',
              itemNotes: '',
              assignedToName: '',
              assignedToEmail: '',
              assignedDate: '',
              itemCreatedAt: '',
              itemUpdatedAt: ''
            })
          } else {
            // Create a row for each individual item
            assetItems.forEach(item => {
              csvRows.push({
                // Main Asset Information
                assetId: asset.id,
                assetNumber: asset.assetNumber || '',
                assetName: asset.name,
                description: asset.description || '',
                category: asset.category?.name || '',
                location: asset.location?.name || '',
                locationType: asset.location?.type || '',
                manufacturer: asset.manufacturer || '',
                model: asset.model || '',
                unitPrice: asset.unitPrice || 0,
                totalQuantity: asset.quantity || 0,
                totalValue: asset.totalValue || 0,
                supplier: asset.supplier || '',
                invoiceNumber: asset.invoiceNumber || '',
                assetNotes: asset.notes || '',
                assetCreatedAt: asset.createdAt ? new Date(asset.createdAt).toLocaleDateString() : '',

                // Individual Asset Item Information
                itemId: item.id,
                itemAssetNumber: item.assetNumber || '',
                itemStatus: item.status || '',
                itemCondition: item.condition || '',
                serialNumber: item.serialNumber || '',
                purchaseDate: item.purchaseDate ? new Date(item.purchaseDate).toLocaleDateString() : '',
                warrantyExpiry: item.warrantyExpiry ? new Date(item.warrantyExpiry).toLocaleDateString() : '',
                itemNotes: item.notes || '',
                assignedToName: item.assignedTo ? `${item.assignedTo.firstName} ${item.assignedTo.lastName}` : '',
                assignedToEmail: item.assignedTo?.email || '',
                assignedDate: item.assignedAt ? new Date(item.assignedAt).toLocaleDateString() : '',
                itemCreatedAt: item.createdAt ? new Date(item.createdAt).toLocaleDateString() : '',
                itemUpdatedAt: item.updatedAt ? new Date(item.updatedAt).toLocaleDateString() : ''
              })
            })
          }
        } catch (itemError) {
          console.error(`Failed to load items for asset ${asset.name}:`, itemError)
          // Still add the asset row even if items fail to load
          csvRows.push({
            // Main Asset Information
            assetId: asset.id,
            assetNumber: asset.assetNumber || '',
            assetName: asset.name,
            description: asset.description || '',
            category: asset.category?.name || '',
            location: asset.location?.name || '',
            locationType: asset.location?.type || '',
            manufacturer: asset.manufacturer || '',
            model: asset.model || '',
            unitPrice: asset.unitPrice || 0,
            totalQuantity: asset.quantity || 0,
            totalValue: asset.totalValue || 0,
            supplier: asset.supplier || '',
            invoiceNumber: asset.invoiceNumber || '',
            assetNotes: asset.notes || '',
            assetCreatedAt: asset.createdAt ? new Date(asset.createdAt).toLocaleDateString() : '',

            // Empty Individual Asset Item Information
            itemId: '',
            itemAssetNumber: '',
            itemStatus: '',
            itemCondition: '',
            serialNumber: '',
            purchaseDate: '',
            warrantyExpiry: '',
            itemNotes: '',
            assignedToName: '',
            assignedToEmail: '',
            assignedDate: '',
            itemCreatedAt: '',
            itemUpdatedAt: ''
          })
        }
      }

      console.log('Total CSV rows generated:', csvRows.length)

      // Generate and download CSV
      this.downloadCsv(csvRows)
    } catch (error) {
      console.error('Failed to export assets to CSV:', error)
      throw new Error('Failed to export assets to CSV')
    }
  }

  /**
   * Convert data to CSV format and trigger download
   */
  private static downloadCsv(data: AssetCsvRow[]): void {
    // Define CSV headers
    const headers = [
      // Main Asset Information
      'Asset ID',
      'Asset Number',
      'Asset Name',
      'Description',
      'Category',
      'Location',
      'Location Type',
      'Manufacturer',
      'Model',
      'Unit Price',
      'Total Quantity',
      'Total Value',
      'Supplier',
      'Invoice Number',
      'Asset Notes',
      'Asset Created Date',

      // Individual Asset Item Information
      'Item ID',
      'Item Asset Number',
      'Item Status',
      'Item Condition',
      'Serial Number',
      'Purchase Date',
      'Warranty Expiry',
      'Item Notes',
      'Assigned To Name',
      'Assigned To Email',
      'Assigned Date',
      'Item Created Date',
      'Item Updated Date'
    ]

    // Convert data to CSV rows
    const csvContent = [
      headers.join(','),
      ...data.map(row =>
        [
          // Main Asset Information
          this.escapeCsvValue(row.assetId),
          this.escapeCsvValue(row.assetNumber),
          this.escapeCsvValue(row.assetName),
          this.escapeCsvValue(row.description),
          this.escapeCsvValue(row.category),
          this.escapeCsvValue(row.location),
          this.escapeCsvValue(row.locationType),
          this.escapeCsvValue(row.manufacturer),
          this.escapeCsvValue(row.model),
          row.unitPrice,
          row.totalQuantity,
          row.totalValue,
          this.escapeCsvValue(row.supplier),
          this.escapeCsvValue(row.invoiceNumber),
          this.escapeCsvValue(row.assetNotes),
          this.escapeCsvValue(row.assetCreatedAt),

          // Individual Asset Item Information
          this.escapeCsvValue(row.itemId),
          this.escapeCsvValue(row.itemAssetNumber),
          this.escapeCsvValue(row.itemStatus),
          this.escapeCsvValue(row.itemCondition),
          this.escapeCsvValue(row.serialNumber),
          this.escapeCsvValue(row.purchaseDate),
          this.escapeCsvValue(row.warrantyExpiry),
          this.escapeCsvValue(row.itemNotes),
          this.escapeCsvValue(row.assignedToName),
          this.escapeCsvValue(row.assignedToEmail),
          this.escapeCsvValue(row.assignedDate),
          this.escapeCsvValue(row.itemCreatedAt),
          this.escapeCsvValue(row.itemUpdatedAt)
        ].join(',')
      )
    ].join('\n')

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)

    link.setAttribute('href', url)
    link.setAttribute('download', `assets_export_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    console.log('CSV file downloaded successfully')
  }

  /**
   * Escape CSV values to handle commas, quotes, and newlines
   */
  private static escapeCsvValue(value: string | number): string {
    if (value === null || value === undefined) return ''

    const stringValue = String(value)

    // If the value contains comma, quote, or newline, wrap in quotes and escape internal quotes
    if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
      return `"${stringValue.replace(/"/g, '""')}"`
    }

    return stringValue
  }
}
