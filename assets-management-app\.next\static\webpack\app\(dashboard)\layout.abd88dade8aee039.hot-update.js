"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/layout",{

/***/ "(app-pages-browser)/./src/components/layout/vertical/VerticalMenu.tsx":
/*!*********************************************************!*\
  !*** ./src/components/layout/vertical/VerticalMenu.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/styles */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var react_perfect_scrollbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-perfect-scrollbar */ \"(app-pages-browser)/./node_modules/react-perfect-scrollbar/lib/index.js\");\n/* harmony import */ var react_perfect_scrollbar__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_perfect_scrollbar__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _menu_vertical_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @menu/vertical-menu */ \"(app-pages-browser)/./src/@menu/vertical-menu/index.tsx\");\n/* harmony import */ var _menu_hooks_useVerticalNav__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @menu/hooks/useVerticalNav */ \"(app-pages-browser)/./src/@menu/hooks/useVerticalNav.tsx\");\n/* harmony import */ var _menu_styles_vertical_StyledVerticalNavExpandIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @menu/styles/vertical/StyledVerticalNavExpandIcon */ \"(app-pages-browser)/./src/@menu/styles/vertical/StyledVerticalNavExpandIcon.tsx\");\n/* harmony import */ var _core_styles_vertical_menuItemStyles__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @core/styles/vertical/menuItemStyles */ \"(app-pages-browser)/./src/@core/styles/vertical/menuItemStyles.ts\");\n/* harmony import */ var _core_styles_vertical_menuSectionStyles__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/styles/vertical/menuSectionStyles */ \"(app-pages-browser)/./src/@core/styles/vertical/menuSectionStyles.ts\");\n// MUI Imports\n\nvar _s = $RefreshSig$();\n\n// Third-party Imports\n\n// Component Imports\n\n// Hook Imports\n\n// Styled Component Imports\n\n// Style Imports\n\n\nconst RenderExpandIcon = (param)=>{\n    let { open, transitionDuration } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_styles_vertical_StyledVerticalNavExpandIcon__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        open: open,\n        transitionDuration: transitionDuration,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n            className: \"ri-arrow-right-s-line\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n            lineNumber: 31,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n        lineNumber: 30,\n        columnNumber: 3\n    }, undefined);\n};\n_c = RenderExpandIcon;\nconst VerticalMenu = (param)=>{\n    let { scrollMenu } = param;\n    _s();\n    // Hooks\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    const { isBreakpointReached, transitionDuration } = (0,_menu_hooks_useVerticalNav__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const ScrollWrapper = isBreakpointReached ? \"div\" : (react_perfect_scrollbar__WEBPACK_IMPORTED_MODULE_1___default());\n    return(// eslint-disable-next-line lines-around-comment\n    /* Custom scrollbar instead of browser scroll, remove if you want browser scroll only */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollWrapper, {\n        ...isBreakpointReached ? {\n            className: \"bs-full overflow-y-auto overflow-x-hidden\",\n            onScroll: (container)=>scrollMenu(container, false)\n        } : {\n            options: {\n                wheelPropagation: false,\n                suppressScrollX: true\n            },\n            onScrollY: (container)=>scrollMenu(container, true)\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_vertical_menu__WEBPACK_IMPORTED_MODULE_2__.Menu, {\n            menuItemStyles: (0,_core_styles_vertical_menuItemStyles__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(theme),\n            renderExpandIcon: (param)=>{\n                let { open } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RenderExpandIcon, {\n                    open: open,\n                    transitionDuration: transitionDuration\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 41\n                }, void 0);\n            },\n            renderExpandedMenuItemIcon: {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"ri-circle-line\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 45\n                }, void 0)\n            },\n            menuSectionStyles: (0,_core_styles_vertical_menuSectionStyles__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(theme),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_vertical_menu__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                    href: \"/dashboard\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"ri-dashboard-3-line\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 43\n                    }, void 0),\n                    children: \"Dashboard\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_vertical_menu__WEBPACK_IMPORTED_MODULE_2__.MenuSection, {\n                    label: \"Asset Management\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_vertical_menu__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                            href: \"/assets\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-database-2-line\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 42\n                            }, void 0),\n                            children: \"Assets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_vertical_menu__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                            href: \"/categories\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-folder-2-line\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 46\n                            }, void 0),\n                            children: \"Categories\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_vertical_menu__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                            href: \"/locations\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-building-line\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 45\n                            }, void 0),\n                            children: \"Locations\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_vertical_menu__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                            href: \"/users\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-team-line\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 41\n                            }, void 0),\n                            children: \"Users\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_vertical_menu__WEBPACK_IMPORTED_MODULE_2__.MenuSection, {\n                    label: \"Reports & Analytics\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_vertical_menu__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                            href: \"/reports\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-bar-chart-box-line\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 43\n                            }, void 0),\n                            children: \"Reports\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_menu_vertical_menu__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {\n                            href: \"/audit-logs\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-file-list-3-line\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 46\n                            }, void 0),\n                            children: \"Audit Logs\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\vertical\\\\VerticalMenu.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined));\n};\n_s(VerticalMenu, \"Pi/+0ZRNernJb99oKO5Q9fRQPbA=\", false, function() {\n    return [\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        _menu_hooks_useVerticalNav__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    ];\n});\n_c1 = VerticalMenu;\n/* harmony default export */ __webpack_exports__[\"default\"] = (VerticalMenu);\nvar _c, _c1;\n$RefreshReg$(_c, \"RenderExpandIcon\");\n$RefreshReg$(_c1, \"VerticalMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/vertical/VerticalMenu.tsx\n"));

/***/ })

});