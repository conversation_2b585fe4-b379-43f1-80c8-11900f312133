import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Asset, AssetStatus } from './asset.entity';
import { User } from './user.entity';

export enum TransferType {
  MANUAL = 'manual',
  STATUS_CHANGE = 'status_change',
  BULK_TRANSFER = 'bulk_transfer',
  INITIAL_STOCK = 'initial_stock',
}

@Entity('quantity_transfers')
export class QuantityTransfer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Asset, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'assetId' })
  asset: Asset;

  @Column()
  assetId: string;

  @Column({
    type: 'enum',
    enum: AssetStatus,
    nullable: true,
  })
  fromStatus: AssetStatus | null;

  @Column({
    type: 'enum',
    enum: AssetStatus,
  })
  toStatus: AssetStatus;

  @Column({ type: 'int' })
  quantity: number;

  @Column({
    type: 'enum',
    enum: TransferType,
    default: TransferType.MANUAL,
  })
  transferType: TransferType;

  @Column({ nullable: true })
  reason: string;

  @Column({ nullable: true })
  notes: string;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'transferredBy' })
  transferredBy: User;

  @Column()
  transferredById: string;

  @CreateDateColumn()
  transferredAt: Date;

  // Additional metadata for bulk transfers
  @Column({ nullable: true })
  batchId: string; // For grouping bulk transfers

  @Column({ type: 'json', nullable: true })
  metadata: any; // For storing additional transfer information
}
