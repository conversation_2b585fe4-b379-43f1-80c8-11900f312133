'use client'

import { forwardRef } from 'react'
import {
  Box,
  Typography,
  Paper,
  Grid,
  Divider
} from '@mui/material'
import QRCode from 'qrcode'
import { useEffect, useState } from 'react'

interface Asset {
  id: string
  assetNumber: string
  name: string
  category?: string
  location?: string
  assignedTo?: string
  purchaseDate?: string
}

interface PrintableAssetLabelProps {
  asset: Asset
  showQRCode?: boolean
  labelSize?: 'small' | 'medium' | 'large'
}

const PrintableAssetLabel = forwardRef<HTMLDivElement, PrintableAssetLabelProps>(
  ({ asset, showQRCode = true, labelSize = 'medium' }, ref) => {
    const [qrCodeUrl, setQrCodeUrl] = useState<string>('')

    useEffect(() => {
      if (showQRCode) {
        // Generate QR code with asset information
        const assetInfo = JSON.stringify({
          id: asset.id,
          assetNumber: asset.assetNumber,
          name: asset.name
        })

        QRCode.toDataURL(assetInfo, {
          width: 100,
          margin: 1,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        })
          .then(url => setQrCodeUrl(url))
          .catch(err => console.error('QR Code generation failed:', err))
      }
    }, [asset, showQRCode])

    const getSizeStyles = () => {
      switch (labelSize) {
        case 'small':
          return {
            width: '2.5in',
            height: '1in',
            fontSize: '8px',
            qrSize: 60
          }
        case 'large':
          return {
            width: '4in',
            height: '2in',
            fontSize: '12px',
            qrSize: 120
          }
        default: // medium
          return {
            width: '3in',
            height: '1.5in',
            fontSize: '10px',
            qrSize: 80
          }
      }
    }

    const styles = getSizeStyles()

    return (
      <Paper
        ref={ref}
        sx={{
          width: styles.width,
          height: styles.height,
          p: 1,
          border: '1px solid #000',
          fontSize: styles.fontSize,
          fontFamily: 'monospace',
          '@media print': {
            boxShadow: 'none',
            border: '1px solid #000'
          }
        }}
      >
        <Grid container spacing={1} sx={{ height: '100%' }}>
          {/* QR Code Section */}
          {showQRCode && qrCodeUrl && (
            <Grid item xs={4} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <img
                src={qrCodeUrl}
                alt="Asset QR Code"
                style={{
                  width: styles.qrSize,
                  height: styles.qrSize,
                  maxWidth: '100%',
                  maxHeight: '100%'
                }}
              />
            </Grid>
          )}

          {/* Asset Information Section */}
          <Grid item xs={showQRCode ? 8 : 12}>
            <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
              {/* Asset Number - Most Prominent */}
              <Box sx={{ textAlign: 'center', mb: 0.5 }}>
                <Typography
                  variant="h6"
                  sx={{
                    fontSize: labelSize === 'small' ? '10px' : labelSize === 'large' ? '16px' : '12px',
                    fontWeight: 'bold',
                    fontFamily: 'monospace'
                  }}
                >
                  {asset.assetNumber}
                </Typography>
              </Box>

              <Divider sx={{ my: 0.5 }} />

              {/* Asset Name */}
              <Box>
                <Typography
                  variant="body2"
                  sx={{
                    fontSize: labelSize === 'small' ? '7px' : labelSize === 'large' ? '11px' : '9px',
                    fontWeight: 'medium',
                    lineHeight: 1.2,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical'
                  }}
                >
                  {asset.name}
                </Typography>
              </Box>

              {/* Additional Info */}
              <Box sx={{ mt: 0.5 }}>
                {asset.category && (
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: labelSize === 'small' ? '6px' : labelSize === 'large' ? '9px' : '7px',
                      display: 'block',
                      lineHeight: 1.1
                    }}
                  >
                    Cat: {asset.category}
                  </Typography>
                )}
                {asset.location && (
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: labelSize === 'small' ? '6px' : labelSize === 'large' ? '9px' : '7px',
                      display: 'block',
                      lineHeight: 1.1,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    Loc: {asset.location}
                  </Typography>
                )}
                {asset.assignedTo && (
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: labelSize === 'small' ? '6px' : labelSize === 'large' ? '9px' : '7px',
                      display: 'block',
                      lineHeight: 1.1,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    Assigned: {asset.assignedTo}
                  </Typography>
                )}
              </Box>

              {/* Date */}
              <Box sx={{ mt: 'auto' }}>
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: labelSize === 'small' ? '5px' : labelSize === 'large' ? '8px' : '6px',
                    color: 'text.secondary'
                  }}
                >
                  {asset.purchaseDate ? new Date(asset.purchaseDate).toLocaleDateString() : new Date().toLocaleDateString()}
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    )
  }
)

PrintableAssetLabel.displayName = 'PrintableAssetLabel'

export default PrintableAssetLabel

// Print utility function
export const printAssetLabel = (asset: Asset, options?: { showQRCode?: boolean; labelSize?: 'small' | 'medium' | 'large' }) => {
  const printWindow = window.open('', '_blank')
  if (!printWindow) return

  const { showQRCode = true, labelSize = 'medium' } = options || {}

  // Create a temporary container
  const container = document.createElement('div')
  document.body.appendChild(container)

  // Import React and ReactDOM for rendering
  import('react').then(React => {
    import('react-dom/client').then(ReactDOM => {
      const root = ReactDOM.createRoot(container)
      
      root.render(
        React.createElement(PrintableAssetLabel, {
          asset,
          showQRCode,
          labelSize
        })
      )

      // Wait for rendering and then get the HTML
      setTimeout(() => {
        const labelHtml = container.innerHTML

        printWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Asset Label - ${asset.assetNumber}</title>
              <style>
                body {
                  margin: 0;
                  padding: 20px;
                  font-family: monospace;
                }
                @media print {
                  body {
                    margin: 0;
                    padding: 0;
                  }
                  @page {
                    margin: 0.25in;
                    size: auto;
                  }
                }
              </style>
            </head>
            <body>
              ${labelHtml}
            </body>
          </html>
        `)

        printWindow.document.close()
        printWindow.focus()
        printWindow.print()
        printWindow.close()

        // Cleanup
        document.body.removeChild(container)
      }, 1000)
    })
  })
}
