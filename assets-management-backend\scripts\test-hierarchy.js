const axios = require('axios');

const API_BASE = 'http://localhost:3001';

// First, login to get a token
async function login() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    return response.data.token;
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
    throw error;
  }
}

// Test hierarchy data
async function testHierarchy() {
  try {
    console.log('🔐 Logging in...');
    const token = await login();
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    console.log('\n📁 Testing Categories Hierarchy...');
    
    // Test categories hierarchy
    const categoriesResponse = await axios.get(`${API_BASE}/categories/hierarchy`, { headers });
    console.log('Categories Hierarchy Response:');
    console.log(JSON.stringify(categoriesResponse.data, null, 2));

    console.log('\n🏢 Testing Locations Hierarchy...');
    
    // Test locations hierarchy
    const locationsResponse = await axios.get(`${API_BASE}/locations/hierarchy`, { headers });
    console.log('Locations Hierarchy Response:');
    console.log(JSON.stringify(locationsResponse.data, null, 2));

    console.log('\n📊 Testing Flat Categories...');
    
    // Test flat categories for comparison
    const flatCategoriesResponse = await axios.get(`${API_BASE}/categories`, { headers });
    console.log('Flat Categories Response:');
    console.log(JSON.stringify(flatCategoriesResponse.data, null, 2));
    
  } catch (error) {
    console.error('❌ Error testing hierarchy:', error.response?.data || error.message);
  }
}

testHierarchy();
