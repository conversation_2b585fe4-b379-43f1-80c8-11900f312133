import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Asset, AssetStatus } from '../entities/asset.entity';
import { AssetQuantity } from '../entities/asset-quantity.entity';
import {
  QuantityTransfer,
  TransferType,
} from '../entities/quantity-transfer.entity';
import { User } from '../entities/user.entity';
import {
  TransferQuantityDto,
  BulkTransferQuantityDto,
  SetAssetQuantityDto,
} from './dto/transfer-quantity.dto';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class AssetQuantityService {
  constructor(
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(AssetQuantity)
    private assetQuantityRepository: Repository<AssetQuantity>,
    @InjectRepository(QuantityTransfer)
    private quantityTransferRepository: Repository<QuantityTransfer>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private dataSource: DataSource,
  ) {}

  async getAssetQuantities(assetId: string): Promise<AssetQuantity[]> {
    const asset = await this.assetRepository.findOne({
      where: { id: assetId },
    });
    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    return this.assetQuantityRepository.find({
      where: { assetId },
      relations: ['asset', 'lastModifiedBy'],
      order: { status: 'ASC' },
    });
  }

  async getAssetQuantitiesByStatus(
    assetId: string,
  ): Promise<Record<AssetStatus, number>> {
    const quantities = await this.getAssetQuantities(assetId);
    const result: Record<AssetStatus, number> = {} as any;

    // Initialize all statuses with 0
    Object.values(AssetStatus).forEach((status) => {
      result[status] = 0;
    });

    // Set actual quantities
    quantities.forEach((q) => {
      result[q.status] = q.quantity;
    });

    return result;
  }

  async transferQuantity(
    transferDto: TransferQuantityDto,
    userId: string,
  ): Promise<{
    success: boolean;
    transfer: QuantityTransfer;
    quantities: Record<AssetStatus, number>;
  }> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Validate asset exists
      const asset = await queryRunner.manager.findOne(Asset, {
        where: { id: transferDto.assetId },
      });
      if (!asset) {
        throw new NotFoundException('Asset not found');
      }

      // Validate user exists
      const user = await queryRunner.manager.findOne(User, {
        where: { id: userId },
      });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Get current quantities
      const fromQuantity = await queryRunner.manager.findOne(AssetQuantity, {
        where: { assetId: transferDto.assetId, status: transferDto.fromStatus },
      });

      if (!fromQuantity || fromQuantity.quantity < transferDto.quantity) {
        throw new BadRequestException(
          `Insufficient quantity in ${transferDto.fromStatus} status. Available: ${fromQuantity?.quantity || 0}, Requested: ${transferDto.quantity}`,
        );
      }

      // Update source quantity
      await queryRunner.manager.update(
        AssetQuantity,
        { assetId: transferDto.assetId, status: transferDto.fromStatus },
        {
          quantity: fromQuantity.quantity - transferDto.quantity,
          lastModifiedById: userId,
        },
      );

      // Update or create destination quantity
      const toQuantity = await queryRunner.manager.findOne(AssetQuantity, {
        where: { assetId: transferDto.assetId, status: transferDto.toStatus },
      });

      if (toQuantity) {
        await queryRunner.manager.update(
          AssetQuantity,
          { assetId: transferDto.assetId, status: transferDto.toStatus },
          {
            quantity: toQuantity.quantity + transferDto.quantity,
            lastModifiedById: userId,
          },
        );
      } else {
        await queryRunner.manager.save(AssetQuantity, {
          assetId: transferDto.assetId,
          status: transferDto.toStatus,
          quantity: transferDto.quantity,
          lastModifiedById: userId,
        });
      }

      // Create transfer record
      const transfer = await queryRunner.manager.save(QuantityTransfer, {
        assetId: transferDto.assetId,
        fromStatus: transferDto.fromStatus,
        toStatus: transferDto.toStatus,
        quantity: transferDto.quantity,
        transferType: transferDto.transferType || TransferType.MANUAL,
        reason: transferDto.reason,
        notes: transferDto.notes,
        transferredById: userId,
      });

      await queryRunner.commitTransaction();

      // Get updated quantities
      const updatedQuantities = await this.getAssetQuantitiesByStatus(
        transferDto.assetId,
      );

      return {
        success: true,
        transfer,
        quantities: updatedQuantities,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async bulkTransferQuantity(
    bulkTransferDto: BulkTransferQuantityDto,
    userId: string,
  ): Promise<{
    success: boolean;
    transfers: QuantityTransfer[];
    batchId: string;
  }> {
    const batchId = uuidv4();
    const transfers: QuantityTransfer[] = [];

    for (const transferDto of bulkTransferDto.transfers) {
      const result = await this.transferQuantity(
        {
          ...transferDto,
          reason: transferDto.reason || bulkTransferDto.batchReason,
          notes: transferDto.notes || bulkTransferDto.batchNotes,
          transferType: TransferType.BULK_TRANSFER,
        },
        userId,
      );

      // Update the transfer with batch ID
      await this.quantityTransferRepository.update(result.transfer.id, {
        batchId,
      });
      transfers.push(result.transfer);
    }

    return {
      success: true,
      transfers,
      batchId,
    };
  }

  async setAssetQuantities(
    setQuantityDto: SetAssetQuantityDto,
    userId: string,
  ): Promise<{ success: boolean; quantities: Record<AssetStatus, number> }> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Validate asset exists
      const asset = await queryRunner.manager.findOne(Asset, {
        where: { id: setQuantityDto.assetId },
      });
      if (!asset) {
        throw new NotFoundException('Asset not found');
      }

      // Clear existing quantities
      await queryRunner.manager.delete(AssetQuantity, {
        assetId: setQuantityDto.assetId,
      });

      // Set new quantities
      for (const [status, quantity] of Object.entries(
        setQuantityDto.quantities,
      )) {
        if (quantity > 0) {
          await queryRunner.manager.save(AssetQuantity, {
            assetId: setQuantityDto.assetId,
            status: status as AssetStatus,
            quantity,
            lastModifiedById: userId,
          });

          // Create transfer record for initial stock
          await queryRunner.manager.save(QuantityTransfer, {
            assetId: setQuantityDto.assetId,
            fromStatus: null,
            toStatus: status as AssetStatus,
            quantity,
            transferType: TransferType.INITIAL_STOCK,
            reason: setQuantityDto.reason || 'Initial stock setup',
            transferredById: userId,
          });
        }
      }

      await queryRunner.commitTransaction();

      const updatedQuantities = await this.getAssetQuantitiesByStatus(
        setQuantityDto.assetId,
      );

      return {
        success: true,
        quantities: updatedQuantities,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getTransferHistory(assetId: string): Promise<QuantityTransfer[]> {
    return this.quantityTransferRepository.find({
      where: { assetId },
      relations: ['asset', 'transferredBy'],
      order: { transferredAt: 'DESC' },
    });
  }

  async getAllAssetQuantities(): Promise<
    {
      assetId: string;
      assetName: string;
      quantities: Record<AssetStatus, number>;
    }[]
  > {
    const assets = await this.assetRepository.find({
      select: ['id', 'name'],
      where: { isActive: true },
    });

    const result: {
      assetId: string;
      assetName: string;
      quantities: Record<AssetStatus, number>;
    }[] = [];
    for (const asset of assets) {
      const quantities = await this.getAssetQuantitiesByStatus(asset.id);
      result.push({
        assetId: asset.id,
        assetName: asset.name,
        quantities,
      });
    }

    return result;
  }
}
