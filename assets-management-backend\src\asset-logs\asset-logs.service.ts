import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AssetLog, AssetLogDocument, LogAction } from '../schemas/asset-log.schema';
import { Asset } from '../entities/asset.entity';
import { User } from '../entities/user.entity';

export interface CreateLogOptions {
  assetId: string;
  assetNumber: string;
  action: LogAction;
  performedBy: string;
  performedByName: string;
  previousData?: Record<string, any>;
  newData?: Record<string, any>;
  description?: string;
  ipAddress?: string;
  userAgent?: string;
}

@Injectable()
export class AssetLogsService {
  constructor(
    @InjectModel(AssetLog.name) private assetLogModel: Model<AssetLogDocument>,
  ) {}

  async createLog(options: CreateLogOptions): Promise<AssetLog> {
    const log = new this.assetLogModel({
      ...options,
      timestamp: new Date(),
    });

    return log.save();
  }

  async logAssetCreated(
    asset: Asset,
    user: User,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<AssetLog> {
    return this.createLog({
      assetId: asset.id,
      assetNumber: asset.assetNumber,
      action: LogAction.CREATED,
      performedBy: user.id,
      performedByName: user.fullName,
      newData: this.sanitizeAssetData(asset),
      description: `Asset ${asset.assetNumber} created`,
      ipAddress,
      userAgent,
    });
  }

  async logAssetUpdated(
    asset: Asset,
    previousData: Partial<Asset>,
    user: User,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<AssetLog> {
    return this.createLog({
      assetId: asset.id,
      assetNumber: asset.assetNumber,
      action: LogAction.UPDATED,
      performedBy: user.id,
      performedByName: user.fullName,
      previousData: this.sanitizeAssetData(previousData),
      newData: this.sanitizeAssetData(asset),
      description: `Asset ${asset.assetNumber} updated`,
      ipAddress,
      userAgent,
    });
  }

  async logAssetDeleted(
    asset: Asset,
    user: User,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<AssetLog> {
    return this.createLog({
      assetId: asset.id,
      assetNumber: asset.assetNumber,
      action: LogAction.DELETED,
      performedBy: user.id,
      performedByName: user.fullName,
      previousData: this.sanitizeAssetData(asset),
      description: `Asset ${asset.assetNumber} deleted`,
      ipAddress,
      userAgent,
    });
  }

  async logAssetAssigned(
    asset: Asset,
    assignedTo: User,
    performedBy: User,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<AssetLog> {
    return this.createLog({
      assetId: asset.id,
      assetNumber: asset.assetNumber,
      action: LogAction.ASSIGNED,
      performedBy: performedBy.id,
      performedByName: performedBy.fullName,
      newData: {
        assignedTo: {
          id: assignedTo.id,
          name: assignedTo.fullName,
          email: assignedTo.email,
        },
        assignedAt: new Date(),
      },
      description: `Asset ${asset.assetNumber} assigned to ${assignedTo.fullName}`,
      ipAddress,
      userAgent,
    });
  }

  async logAssetUnassigned(
    asset: Asset,
    previousAssignee: User,
    performedBy: User,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<AssetLog> {
    return this.createLog({
      assetId: asset.id,
      assetNumber: asset.assetNumber,
      action: LogAction.UNASSIGNED,
      performedBy: performedBy.id,
      performedByName: performedBy.fullName,
      previousData: {
        assignedTo: {
          id: previousAssignee.id,
          name: previousAssignee.fullName,
          email: previousAssignee.email,
        },
      },
      description: `Asset ${asset.assetNumber} unassigned from ${previousAssignee.fullName}`,
      ipAddress,
      userAgent,
    });
  }

  async logStatusChange(
    asset: Asset,
    previousStatus: string,
    newStatus: string,
    user: User,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<AssetLog> {
    return this.createLog({
      assetId: asset.id,
      assetNumber: asset.assetNumber,
      action: LogAction.STATUS_CHANGED,
      performedBy: user.id,
      performedByName: user.fullName,
      previousData: { status: previousStatus },
      newData: { status: newStatus },
      description: `Asset ${asset.assetNumber} status changed from ${previousStatus} to ${newStatus}`,
      ipAddress,
      userAgent,
    });
  }

  async logLocationChange(
    asset: Asset,
    previousLocation: string,
    newLocation: string,
    user: User,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<AssetLog> {
    return this.createLog({
      assetId: asset.id,
      assetNumber: asset.assetNumber,
      action: LogAction.LOCATION_CHANGED,
      performedBy: user.id,
      performedByName: user.fullName,
      previousData: { location: previousLocation },
      newData: { location: newLocation },
      description: `Asset ${asset.assetNumber} location changed from ${previousLocation} to ${newLocation}`,
      ipAddress,
      userAgent,
    });
  }

  async getAssetLogs(
    assetId: string,
    limit: number = 50,
    skip: number = 0,
  ): Promise<AssetLog[]> {
    return this.assetLogModel
      .find({ assetId })
      .sort({ timestamp: -1 })
      .limit(limit)
      .skip(skip)
      .exec();
  }

  async getUserActivityLogs(
    userId: string,
    limit: number = 50,
    skip: number = 0,
  ): Promise<AssetLog[]> {
    return this.assetLogModel
      .find({ performedBy: userId })
      .sort({ timestamp: -1 })
      .limit(limit)
      .skip(skip)
      .exec();
  }

  async getRecentLogs(limit: number = 100): Promise<AssetLog[]> {
    return this.assetLogModel
      .find()
      .sort({ timestamp: -1 })
      .limit(limit)
      .exec();
  }

  async getLogsByDateRange(
    startDate: Date,
    endDate: Date,
    limit: number = 100,
  ): Promise<AssetLog[]> {
    return this.assetLogModel
      .find({
        timestamp: {
          $gte: startDate,
          $lte: endDate,
        },
      })
      .sort({ timestamp: -1 })
      .limit(limit)
      .exec();
  }

  async getLogsByAction(
    action: LogAction,
    limit: number = 100,
  ): Promise<AssetLog[]> {
    return this.assetLogModel
      .find({ action })
      .sort({ timestamp: -1 })
      .limit(limit)
      .exec();
  }

  private sanitizeAssetData(data: any): Record<string, any> {
    if (!data) return {};

    const sanitized = { ...data };
    
    // Remove sensitive or unnecessary fields
    delete sanitized.password;
    delete sanitized.createdAt;
    delete sanitized.updatedAt;
    
    // Convert dates to ISO strings for better storage
    Object.keys(sanitized).forEach(key => {
      if (sanitized[key] instanceof Date) {
        sanitized[key] = sanitized[key].toISOString();
      }
    });

    return sanitized;
  }
}
