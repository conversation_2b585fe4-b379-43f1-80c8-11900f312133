import { API_CONFIG, getAuthHeaders, removeAuthToken } from './config'
import { ApiError } from '@/types/api'

export class ApiClient {
  private baseURL: string

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    const config: RequestInit = {
      headers: getAuthHeaders(),
      ...options
    }

    console.log('API Client: Making request to:', url)
    console.log('API Client: Request config:', config)

    try {
      const response = await fetch(url, config)
      console.log('API Client: Response status:', response.status)
      console.log('API Client: Response ok:', response.ok)

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          // Token expired, invalid, or insufficient permissions
          removeAuthToken()
          // Redirect to login page
          if (typeof window !== 'undefined') {
            window.location.href = '/login'
          }
        }

        let errorData: Api<PERSON>rror
        try {
          errorData = await response.json()
        } catch {
          errorData = {
            message: response.statusText || 'An error occurred',
            statusCode: response.status
          }
        }

        const error = new Error(errorData.message || 'An error occurred')
        ;(error as any).status = response.status
        ;(error as any).response = { data: errorData }
        throw error
      }

      // Handle empty responses
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json()
        console.log('API Client: Response data:', data)
        return data
      } else {
        console.log('API Client: Non-JSON response, returning empty object')
        return {} as T
      }
    } catch (error) {
      if (error instanceof Error) {
        // Check if this is a network error (backend not available)
        if (error.message.includes('fetch') || error.name === 'TypeError') {
          throw new Error('Backend server not available. Please start the backend server or use demo mode.')
        }
        throw error
      }
      throw new Error('Network error occurred')
    }
  }

  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = new URL(`${this.baseURL}${endpoint}`)

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value))
        }
      })
    }

    return this.request<T>(url.pathname + url.search)
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    })
  }

  async patch<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined
    })
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'DELETE'
    })
  }

  // File upload method
  async uploadFile<T>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    const token = getAuthHeaders().Authorization
    const headers: Record<string, string> = {}
    if (token) {
      headers.Authorization = token
    }

    return this.request<T>(endpoint, {
      method: 'POST',
      headers,
      body: formData
    })
  }
}

// Create a singleton instance
export const apiClient = new ApiClient()
