import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, Like, ILike } from 'typeorm';
import { Asset, AssetStatus } from '../entities/asset.entity';
import { Category } from '../entities/category.entity';
import { Location } from '../entities/location.entity';
import { User } from '../entities/user.entity';
import { CreateAssetDto } from './dto/create-asset.dto';
import { UpdateAssetDto } from './dto/update-asset.dto';
import { AssignAssetDto } from './dto/assign-asset.dto';
import { AssetNumberService } from './asset-number.service';

export interface AssetSearchOptions {
  search?: string;
  categoryId?: string;
  locationId?: string;
  status?: AssetStatus;
  condition?: string;
  assignedToId?: string;
  manufacturer?: string;
  model?: string;
  minValue?: number;
  maxValue?: number;
  purchaseDateFrom?: string;
  purchaseDateTo?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

@Injectable()
export class AssetsService {
  constructor(
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,
    @InjectRepository(Location)
    private locationRepository: Repository<Location>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private assetNumberService: AssetNumberService,
  ) {}

  async create(createAssetDto: CreateAssetDto): Promise<Asset> {
    // Validate category exists
    const category = await this.categoryRepository.findOne({
      where: { id: createAssetDto.categoryId },
    });
    if (!category) {
      throw new NotFoundException('Category not found');
    }

    // Validate location exists
    const location = await this.locationRepository.findOne({
      where: { id: createAssetDto.locationId },
    });
    if (!location) {
      throw new NotFoundException('Location not found');
    }

    // Validate assigned user exists if provided
    if (createAssetDto.assignedToId) {
      const user = await this.userRepository.findOne({
        where: { id: createAssetDto.assignedToId },
      });
      if (!user) {
        throw new NotFoundException('Assigned user not found');
      }
    }

    // Generate unique asset number
    const assetNumber = await this.assetNumberService.generateAssetNumber();

    const asset = this.assetRepository.create(createAssetDto);
    asset.assetNumber = assetNumber;
    // Note: Individual assignment is now handled at the AssetItem level

    // Calculate total value
    asset.totalValue = asset.unitPrice * asset.quantity;

    return this.assetRepository.save(asset);
  }

  async findAll(options: AssetSearchOptions = {}): Promise<{
    assets: Asset[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      search,
      categoryId,
      locationId,
      status,
      condition,
      assignedToId,
      manufacturer,
      model,
      minValue,
      maxValue,
      purchaseDateFrom,
      purchaseDateTo,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = options;

    const query = this.assetRepository
      .createQueryBuilder('asset')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('asset.location', 'location');

    // Apply filters
    if (search) {
      query.andWhere(
        '(asset.name ILIKE :search OR asset.description ILIKE :search OR asset.assetNumber ILIKE :search OR asset.serialNumber ILIKE :search OR asset.manufacturer ILIKE :search OR asset.model ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (categoryId) {
      query.andWhere('asset.categoryId = :categoryId', { categoryId });
    }

    if (locationId) {
      query.andWhere('asset.locationId = :locationId', { locationId });
    }

    if (status) {
      query.andWhere('asset.status = :status', { status });
    }

    if (condition) {
      query.andWhere('asset.condition = :condition', { condition });
    }

    // Note: Individual assignment filtering is now handled at the AssetItem level

    if (manufacturer) {
      query.andWhere('asset.manufacturer ILIKE :manufacturer', {
        manufacturer: `%${manufacturer}%`,
      });
    }

    if (model) {
      query.andWhere('asset.model ILIKE :model', { model: `%${model}%` });
    }

    if (minValue !== undefined) {
      query.andWhere('asset.totalValue >= :minValue', { minValue });
    }

    if (maxValue !== undefined) {
      query.andWhere('asset.totalValue <= :maxValue', { maxValue });
    }

    if (purchaseDateFrom) {
      query.andWhere('asset.purchaseDate >= :purchaseDateFrom', {
        purchaseDateFrom,
      });
    }

    if (purchaseDateTo) {
      query.andWhere('asset.purchaseDate <= :purchaseDateTo', {
        purchaseDateTo,
      });
    }

    // Apply sorting
    const validSortFields = [
      'name',
      'assetNumber',
      'createdAt',
      'unitPrice',
      'status',
    ];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
    query.orderBy(`asset.${sortField}`, sortOrder);

    // Apply pagination
    const offset = (page - 1) * limit;
    query.skip(offset).take(limit);

    const [assets, total] = await query.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      assets,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOne(id: string): Promise<Asset> {
    const asset = await this.assetRepository.findOne({
      where: { id },
      relations: ['category', 'location'],
    });

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    return asset;
  }

  async findByAssetNumber(assetNumber: string): Promise<Asset> {
    const asset = await this.assetRepository.findOne({
      where: { assetNumber },
      relations: ['category', 'location'],
    });

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    return asset;
  }

  async update(id: string, updateAssetDto: UpdateAssetDto): Promise<Asset> {
    const asset = await this.assetRepository.findOne({ where: { id } });

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    // Validate category exists if being updated
    if (updateAssetDto.categoryId) {
      const category = await this.categoryRepository.findOne({
        where: { id: updateAssetDto.categoryId },
      });
      if (!category) {
        throw new NotFoundException('Category not found');
      }
    }

    // Validate location exists if being updated
    if (updateAssetDto.locationId) {
      const location = await this.locationRepository.findOne({
        where: { id: updateAssetDto.locationId },
      });
      if (!location) {
        throw new NotFoundException('Location not found');
      }
    }

    // Note: Individual assignment validation is now handled at the AssetItem level

    // Recalculate total value if price or quantity changed
    if (
      updateAssetDto.unitPrice !== undefined ||
      updateAssetDto.quantity !== undefined
    ) {
      const newUnitPrice = updateAssetDto.unitPrice ?? asset.unitPrice;
      const newQuantity = updateAssetDto.quantity ?? asset.quantity;
      updateAssetDto.totalValue = newUnitPrice * newQuantity;
    }

    await this.assetRepository.update(id, updateAssetDto);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const asset = await this.assetRepository.findOne({ where: { id } });

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    await this.assetRepository.remove(asset);
  }

  async assignAsset(
    id: string,
    assignAssetDto: AssignAssetDto,
  ): Promise<Asset> {
    const asset = await this.assetRepository.findOne({ where: { id } });

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    const user = await this.userRepository.findOne({
      where: { id: assignAssetDto.assignedToId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    await this.assetRepository.update(id, {
      // Note: Individual assignment is now handled at the AssetItem level
      // Note: Individual assignment is now handled at the AssetItem level
      // Note: Individual status is now handled at the AssetItem level
      notes: assignAssetDto.notes || asset.notes,
    });

    return this.findOne(id);
  }

  async unassignAsset(id: string): Promise<Asset> {
    const asset = await this.assetRepository.findOne({ where: { id } });

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    await this.assetRepository.update(id, {
      // Note: Individual assignment is now handled at the AssetItem level
      // Note: Individual assignment is now handled at the AssetItem level
      // Note: Individual status is now handled at the AssetItem level
    });

    return this.findOne(id);
  }

  async getAssetStats(): Promise<{
    totalAssets: number;
    totalValue: number;
    statusBreakdown: Record<AssetStatus, number>;
    categoryBreakdown: Array<{
      categoryName: string;
      count: number;
      value: number;
    }>;
  }> {
    const assets = await this.assetRepository.find({
      relations: ['category'],
    });

    const totalAssets = assets.length;
    const totalValue = assets.reduce(
      (sum, asset) => sum + (asset.totalValue || 0),
      0,
    );

    // Status breakdown
    const statusBreakdown = assets.reduce(
      (acc, asset) => {
        // Note: Status tracking is now handled at the AssetItem level
        // This method should be updated to use AssetItem statistics
        return acc;
      },
      {} as Record<AssetStatus, number>,
    );

    // Category breakdown
    const categoryMap = new Map<string, { count: number; value: number }>();
    assets.forEach((asset) => {
      const categoryName = asset.category?.name || 'Uncategorized';
      const existing = categoryMap.get(categoryName) || { count: 0, value: 0 };
      categoryMap.set(categoryName, {
        count: existing.count + 1,
        value: existing.value + (asset.totalValue || 0),
      });
    });

    const categoryBreakdown = Array.from(categoryMap.entries()).map(
      ([categoryName, data]) => ({
        categoryName,
        count: data.count,
        value: data.value,
      }),
    );

    return {
      totalAssets,
      totalValue,
      statusBreakdown,
      categoryBreakdown,
    };
  }
}
