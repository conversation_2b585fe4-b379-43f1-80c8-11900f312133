const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testAssetNumberGeneration() {
  try {
    console.log('🧪 Testing Asset Number Generation with New Format...\n');

    // First, let's create a test category with a code
    console.log('1. Creating test category...');
    const categoryResponse = await axios.post(`${BASE_URL}/categories`, {
      name: 'Client Equipment',
      code: 'CLI',
      description: 'Equipment for client use'
    });
    const categoryId = categoryResponse.data.id;
    console.log(`✅ Created category: ${categoryResponse.data.name} (Code: ${categoryResponse.data.code})`);

    // Create a test location with a code
    console.log('\n2. Creating test location...');
    const locationResponse = await axios.post(`${BASE_URL}/locations`, {
      name: 'Inventory',
      code: 'INV',
      type: 'room',
      description: 'Main inventory room'
    });
    const locationId = locationResponse.data.id;
    console.log(`✅ Created location: ${locationResponse.data.name} (Code: ${locationResponse.data.code})`);

    // Create a test asset to trigger asset number generation
    console.log('\n3. Creating test asset...');
    const assetResponse = await axios.post(`${BASE_URL}/assets`, {
      name: 'Test Laptop',
      description: 'Test laptop for asset number generation',
      unitPrice: 1000,
      quantity: 1,
      categoryId: categoryId,
      locationId: locationId
    });

    console.log(`✅ Created asset: ${assetResponse.data.name}`);
    console.log(`🎯 Generated Asset Number: ${assetResponse.data.assetNumber}`);
    
    // Verify the format
    const assetNumber = assetResponse.data.assetNumber;
    const expectedPattern = /^INV-CLI-\d{4}$/;
    
    if (expectedPattern.test(assetNumber)) {
      console.log('✅ Asset number format is correct! (INV-CLI-XXXX)');
    } else {
      console.log('❌ Asset number format is incorrect!');
      console.log(`Expected pattern: INV-CLI-XXXX`);
      console.log(`Actual: ${assetNumber}`);
    }

    // Create another asset to test sequential numbering
    console.log('\n4. Creating second asset to test sequential numbering...');
    const asset2Response = await axios.post(`${BASE_URL}/assets`, {
      name: 'Test Desktop',
      description: 'Test desktop for sequential numbering',
      unitPrice: 800,
      quantity: 1,
      categoryId: categoryId,
      locationId: locationId
    });

    console.log(`✅ Created second asset: ${asset2Response.data.name}`);
    console.log(`🎯 Generated Asset Number: ${asset2Response.data.assetNumber}`);

    // Verify sequential numbering
    const asset2Number = asset2Response.data.assetNumber;
    const firstNumber = parseInt(assetNumber.split('-')[2]);
    const secondNumber = parseInt(asset2Number.split('-')[2]);
    
    if (secondNumber === firstNumber + 1) {
      console.log('✅ Sequential numbering is working correctly!');
    } else {
      console.log('❌ Sequential numbering is not working correctly!');
      console.log(`First asset number: ${firstNumber}`);
      console.log(`Second asset number: ${secondNumber}`);
    }

    console.log('\n🎉 Asset number generation test completed successfully!');
    console.log('\nGenerated asset numbers:');
    console.log(`- ${assetNumber}`);
    console.log(`- ${asset2Number}`);

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run the test
testAssetNumberGeneration();
