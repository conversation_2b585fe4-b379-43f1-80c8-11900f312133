globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(dashboard)/dashboard/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/asset-details/[assetNumber]/page.tsx":{"*":{"id":"(ssr)/./src/app/asset-details/[assetNumber]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/asset-details/layout.tsx":{"*":{"id":"(ssr)/./src/app/asset-details/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Avatar/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Avatar/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Button/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Button/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Card/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Card/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/CardContent/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/CardContent/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/CardHeader/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/CardHeader/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Chip/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Chip/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Grid/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Grid/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/LinearProgress/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Typography/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Typography/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/@core/components/mui/Avatar.tsx":{"*":{"id":"(ssr)/./src/@core/components/mui/Avatar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/@core/components/option-menu/index.tsx":{"*":{"id":"(ssr)/./src/@core/components/option-menu/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Link.tsx":{"*":{"id":"(ssr)/./src/components/Link.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/views/dashboard/DistributedColumnChart.tsx":{"*":{"id":"(ssr)/./src/views/dashboard/DistributedColumnChart.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/views/dashboard/LineChart.tsx":{"*":{"id":"(ssr)/./src/views/dashboard/LineChart.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/views/dashboard/WeeklyOverview.tsx":{"*":{"id":"(ssr)/./src/views/dashboard/WeeklyOverview.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/@core/contexts/settingsContext.tsx":{"*":{"id":"(ssr)/./src/@core/contexts/settingsContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/@layouts/components/vertical/Footer.tsx":{"*":{"id":"(ssr)/./src/@layouts/components/vertical/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/@layouts/components/vertical/LayoutContent.tsx":{"*":{"id":"(ssr)/./src/@layouts/components/vertical/LayoutContent.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/@layouts/components/vertical/Navbar.tsx":{"*":{"id":"(ssr)/./src/@layouts/components/vertical/Navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/@layouts/LayoutWrapper.tsx":{"*":{"id":"(ssr)/./src/@layouts/LayoutWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/@menu/contexts/verticalNavContext.tsx":{"*":{"id":"(ssr)/./src/@menu/contexts/verticalNavContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AuthGuard.tsx":{"*":{"id":"(ssr)/./src/components/AuthGuard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/shared/ModeDropdown.tsx":{"*":{"id":"(ssr)/./src/components/layout/shared/ModeDropdown.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/shared/UserDropdown.tsx":{"*":{"id":"(ssr)/./src/components/layout/shared/UserDropdown.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/vertical/FooterContent.tsx":{"*":{"id":"(ssr)/./src/components/layout/vertical/FooterContent.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/vertical/Navigation.tsx":{"*":{"id":"(ssr)/./src/components/layout/vertical/Navigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/vertical/NavToggle.tsx":{"*":{"id":"(ssr)/./src/components/layout/vertical/NavToggle.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/theme/index.tsx":{"*":{"id":"(ssr)/./src/components/theme/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./src/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/assets/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/assets/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(blank-layout-pages)/login/page.tsx":{"*":{"id":"(ssr)/./src/app/(blank-layout-pages)/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/@layouts/BlankLayout.tsx":{"*":{"id":"(ssr)/./src/@layouts/BlankLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/dashboard/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\app\\asset-details\\[assetNumber]\\page.tsx":{"id":"(app-pages-browser)/./src/app/asset-details/[assetNumber]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\react-perfect-scrollbar\\dist\\css\\styles.css":{"id":"(app-pages-browser)/./node_modules/react-perfect-scrollbar/dist/css/styles.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\assets\\iconify-icons\\generated-icons.css":{"id":"(app-pages-browser)/./src/assets/iconify-icons/generated-icons.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\app\\asset-details\\layout.tsx":{"id":"(app-pages-browser)/./src/app/asset-details/layout.tsx","name":"*","chunks":[],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\@mui\\material\\Avatar\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Avatar/index.js","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\@mui\\material\\Button\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Button/index.js","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\@mui\\material\\Card\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Card/index.js","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\@mui\\material\\CardContent\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/CardContent/index.js","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\@mui\\material\\CardHeader\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/CardHeader/index.js","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\@mui\\material\\Chip\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Chip/index.js","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\@mui\\material\\Grid\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Grid/index.js","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\@mui\\material\\LinearProgress\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/index.js","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\node_modules\\@mui\\material\\Typography\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Typography/index.js","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\@core\\components\\mui\\Avatar.tsx":{"id":"(app-pages-browser)/./src/@core/components/mui/Avatar.tsx","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\@core\\components\\option-menu\\index.tsx":{"id":"(app-pages-browser)/./src/@core/components/option-menu/index.tsx","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\@core\\styles\\table.module.css":{"id":"(app-pages-browser)/./src/@core/styles/table.module.css","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\components\\Link.tsx":{"id":"(app-pages-browser)/./src/components/Link.tsx","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\views\\dashboard\\DistributedColumnChart.tsx":{"id":"(app-pages-browser)/./src/views/dashboard/DistributedColumnChart.tsx","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\views\\dashboard\\LineChart.tsx":{"id":"(app-pages-browser)/./src/views/dashboard/LineChart.tsx","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\views\\dashboard\\WeeklyOverview.tsx":{"id":"(app-pages-browser)/./src/views/dashboard/WeeklyOverview.tsx","name":"*","chunks":["app/(dashboard)/page","static/chunks/app/(dashboard)/page.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\@core\\contexts\\settingsContext.tsx":{"id":"(app-pages-browser)/./src/@core/contexts/settingsContext.tsx","name":"*","chunks":["app/(blank-layout-pages)/layout","static/chunks/app/(blank-layout-pages)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\@layouts\\components\\vertical\\Footer.tsx":{"id":"(app-pages-browser)/./src/@layouts/components/vertical/Footer.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\@layouts\\components\\vertical\\LayoutContent.tsx":{"id":"(app-pages-browser)/./src/@layouts/components/vertical/LayoutContent.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\@layouts\\components\\vertical\\Navbar.tsx":{"id":"(app-pages-browser)/./src/@layouts/components/vertical/Navbar.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\@layouts\\LayoutWrapper.tsx":{"id":"(app-pages-browser)/./src/@layouts/LayoutWrapper.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\@menu\\contexts\\verticalNavContext.tsx":{"id":"(app-pages-browser)/./src/@menu/contexts/verticalNavContext.tsx","name":"*","chunks":["app/(blank-layout-pages)/layout","static/chunks/app/(blank-layout-pages)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\components\\AuthGuard.tsx":{"id":"(app-pages-browser)/./src/components/AuthGuard.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\components\\layout\\shared\\ModeDropdown.tsx":{"id":"(app-pages-browser)/./src/components/layout/shared/ModeDropdown.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\components\\layout\\shared\\UserDropdown.tsx":{"id":"(app-pages-browser)/./src/components/layout/shared/UserDropdown.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\components\\layout\\vertical\\FooterContent.tsx":{"id":"(app-pages-browser)/./src/components/layout/vertical/FooterContent.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\components\\layout\\vertical\\Navigation.tsx":{"id":"(app-pages-browser)/./src/components/layout/vertical/Navigation.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\components\\layout\\vertical\\NavToggle.tsx":{"id":"(app-pages-browser)/./src/components/layout/vertical/NavToggle.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\components\\theme\\index.tsx":{"id":"(app-pages-browser)/./src/components/theme/index.tsx","name":"*","chunks":["app/(blank-layout-pages)/layout","static/chunks/app/(blank-layout-pages)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\contexts\\AuthContext.tsx":{"id":"(app-pages-browser)/./src/contexts/AuthContext.tsx","name":"*","chunks":["app/(blank-layout-pages)/layout","static/chunks/app/(blank-layout-pages)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\app\\(dashboard)\\assets\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/assets/page.tsx","name":"*","chunks":[],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\app\\(blank-layout-pages)\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/(blank-layout-pages)/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\@layouts\\BlankLayout.tsx":{"id":"(app-pages-browser)/./src/@layouts/BlankLayout.tsx","name":"*","chunks":["app/(blank-layout-pages)/layout","static/chunks/app/(blank-layout-pages)/layout.js"],"async":false},"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\app\\(dashboard)\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx","name":"*","chunks":["app/(dashboard)/dashboard/page","static/chunks/app/(dashboard)/dashboard/page.js"],"async":false}},"entryCSSFiles":{"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\":[],"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\app\\(dashboard)\\page":["static/css/app/(dashboard)/page.css"],"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\app\\(dashboard)\\layout":["static/css/app/(dashboard)/layout.css"],"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\app\\(blank-layout-pages)\\layout":["static/css/app/(blank-layout-pages)/layout.css"],"C:\\Invicta\\Upcoming Projects\\Assets Management System\\assets-management-app\\src\\app\\(dashboard)\\dashboard\\page":[]}}