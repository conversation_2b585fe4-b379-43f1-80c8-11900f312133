'use client'

import { useState, useRef } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  <PERSON>alogContent,
  <PERSON>alog<PERSON><PERSON>,
  Button,
  Box,
  Typography,
  Alert,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material'
import {
  Upload as UploadIcon,
  Download as DownloadIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  GetApp as GetAppIcon
} from '@mui/icons-material'

interface CSVImportExportProps {
  open: boolean
  onClose: () => void
  mode: 'import' | 'export'
  data?: any[]
  onImport?: (data: any[]) => void
  onExport?: () => void
}

interface ImportResult {
  success: boolean
  data?: any[]
  errors?: string[]
  warnings?: string[]
}

export default function CSVImportExport({
  open,
  onClose,
  mode,
  data = [],
  onImport,
  onExport
}: CSVImportExportProps) {
  const [importing, setImporting] = useState(false)
  const [exporting, setExporting] = useState(false)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [previewData, setPreviewData] = useState<any[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      setImportResult({
        success: false,
        errors: ['Please select a valid CSV file']
      })
      return
    }

    setImporting(true)
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const csv = e.target?.result as string
        const result = parseCSV(csv)
        setImportResult(result)
        if (result.success && result.data) {
          setPreviewData(result.data.slice(0, 10)) // Show first 10 rows for preview
        }
      } catch (error) {
        setImportResult({
          success: false,
          errors: ['Failed to parse CSV file']
        })
      } finally {
        setImporting(false)
      }
    }

    reader.readAsText(file)
  }

  const parseCSV = (csv: string): ImportResult => {
    const lines = csv.split('\n').filter(line => line.trim())
    if (lines.length < 2) {
      return {
        success: false,
        errors: ['CSV file must contain at least a header row and one data row']
      }
    }

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
    const requiredHeaders = ['name', 'category', 'location', 'unitPrice', 'quantity']
    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h))

    if (missingHeaders.length > 0) {
      return {
        success: false,
        errors: [`Missing required columns: ${missingHeaders.join(', ')}`]
      }
    }

    const data: any[] = []
    const errors: string[] = []
    const warnings: string[] = []

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''))
      
      if (values.length !== headers.length) {
        errors.push(`Row ${i + 1}: Column count mismatch`)
        continue
      }

      const row: any = {}
      headers.forEach((header, index) => {
        row[header] = values[index]
      })

      // Validate required fields
      if (!row.name) {
        errors.push(`Row ${i + 1}: Name is required`)
        continue
      }

      if (!row.unitPrice || isNaN(parseFloat(row.unitPrice))) {
        errors.push(`Row ${i + 1}: Valid unit price is required`)
        continue
      }

      if (!row.quantity || isNaN(parseInt(row.quantity))) {
        errors.push(`Row ${i + 1}: Valid quantity is required`)
        continue
      }

      // Convert numeric fields
      row.unitPrice = parseFloat(row.unitPrice)
      row.quantity = parseInt(row.quantity)
      row.totalValue = row.unitPrice * row.quantity

      // Add warnings for optional fields
      if (!row.description) {
        warnings.push(`Row ${i + 1}: Description is empty`)
      }

      data.push(row)
    }

    return {
      success: errors.length === 0,
      data: errors.length === 0 ? data : undefined,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined
    }
  }

  const handleImport = () => {
    if (importResult?.success && importResult.data && onImport) {
      onImport(importResult.data)
      onClose()
    }
  }

  const handleExport = () => {
    setExporting(true)
    
    // Create CSV content
    const headers = [
      'assetNumber',
      'name',
      'description',
      'category',
      'location',
      'status',
      'condition',
      'unitPrice',
      'quantity',
      'totalValue',
      'assignedTo',
      'purchaseDate',
      'warrantyExpiry',
      'manufacturer',
      'model',
      'serialNumber',
      'supplier',
      'invoiceNumber',
      'notes'
    ]

    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header] || ''
          // Escape commas and quotes
          return typeof value === 'string' && (value.includes(',') || value.includes('"'))
            ? `"${value.replace(/"/g, '""')}"`
            : value
        }).join(',')
      )
    ].join('\n')

    // Download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `assets_export_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    setExporting(false)
    if (onExport) onExport()
    onClose()
  }

  const downloadTemplate = () => {
    const templateHeaders = [
      'name',
      'description',
      'category',
      'location',
      'status',
      'condition',
      'unitPrice',
      'quantity',
      'manufacturer',
      'model',
      'serialNumber',
      'supplier',
      'invoiceNumber',
      'purchaseDate',
      'warrantyExpiry',
      'notes'
    ]

    const sampleData = [
      'Dell Laptop XPS 13',
      'High-performance laptop for development work',
      'IT Equipment',
      'Main Office > IT Department',
      'In Stock',
      'Good',
      '1299.99',
      '1',
      'Dell',
      'XPS 13 9310',
      'DL123456789',
      'Tech Solutions Inc.',
      'INV-2024-001',
      '2024-01-15',
      '2027-01-15',
      'Configured with additional RAM'
    ]

    const csvContent = [
      templateHeaders.join(','),
      sampleData.join(',')
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', 'asset_import_template.csv')
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {mode === 'import' ? 'Import Assets from CSV' : 'Export Assets to CSV'}
      </DialogTitle>
      
      <DialogContent>
        {mode === 'import' ? (
          <Box>
            {/* Import Instructions */}
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body2" gutterBottom>
                Upload a CSV file with asset data. Required columns: name, category, location, unitPrice, quantity
              </Typography>
              <Button
                size="small"
                startIcon={<GetAppIcon />}
                onClick={downloadTemplate}
                sx={{ mt: 1 }}
              >
                Download Template
              </Button>
            </Alert>

            {/* File Upload */}
            <Box sx={{ mb: 3 }}>
              <input
                type="file"
                accept=".csv"
                onChange={handleFileSelect}
                ref={fileInputRef}
                style={{ display: 'none' }}
              />
              <Button
                variant="outlined"
                startIcon={<UploadIcon />}
                onClick={() => fileInputRef.current?.click()}
                disabled={importing}
                fullWidth
                size="large"
              >
                {importing ? 'Processing...' : 'Select CSV File'}
              </Button>
            </Box>

            {importing && <LinearProgress sx={{ mb: 2 }} />}

            {/* Import Results */}
            {importResult && (
              <Box sx={{ mb: 3 }}>
                {importResult.success ? (
                  <Alert severity="success" icon={<CheckIcon />}>
                    Successfully parsed {importResult.data?.length} rows
                  </Alert>
                ) : (
                  <Alert severity="error" icon={<ErrorIcon />}>
                    <Typography variant="body2" gutterBottom>Import failed:</Typography>
                    <ul style={{ margin: 0, paddingLeft: 20 }}>
                      {importResult.errors?.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </Alert>
                )}

                {importResult.warnings && importResult.warnings.length > 0 && (
                  <Alert severity="warning" icon={<WarningIcon />} sx={{ mt: 1 }}>
                    <Typography variant="body2" gutterBottom>Warnings:</Typography>
                    <ul style={{ margin: 0, paddingLeft: 20 }}>
                      {importResult.warnings.map((warning, index) => (
                        <li key={index}>{warning}</li>
                      ))}
                    </ul>
                  </Alert>
                )}
              </Box>
            )}

            {/* Preview Data */}
            {previewData.length > 0 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Preview (First 10 rows)
                </Typography>
                <TableContainer component={Paper} sx={{ maxHeight: 300 }}>
                  <Table size="small" stickyHeader>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>Category</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell align="right">Price</TableCell>
                        <TableCell align="right">Qty</TableCell>
                        <TableCell align="right">Total</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {previewData.map((row, index) => (
                        <TableRow key={index}>
                          <TableCell>{row.name}</TableCell>
                          <TableCell>{row.category}</TableCell>
                          <TableCell>{row.location}</TableCell>
                          <TableCell align="right">${row.unitPrice}</TableCell>
                          <TableCell align="right">{row.quantity}</TableCell>
                          <TableCell align="right">${row.totalValue}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}
          </Box>
        ) : (
          <Box>
            {/* Export Information */}
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body2">
                Export {data.length} assets to CSV format. The file will include all asset information and can be used for backup or analysis.
              </Typography>
            </Alert>

            {exporting && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" gutterBottom>Preparing export...</Typography>
                <LinearProgress />
              </Box>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        {mode === 'import' ? (
          <Button
            onClick={handleImport}
            variant="contained"
            disabled={!importResult?.success || importing}
            startIcon={<UploadIcon />}
          >
            Import {importResult?.data?.length || 0} Assets
          </Button>
        ) : (
          <Button
            onClick={handleExport}
            variant="contained"
            disabled={exporting}
            startIcon={<DownloadIcon />}
          >
            Export CSV
          </Button>
        )}
      </DialogActions>
    </Dialog>
  )
}
