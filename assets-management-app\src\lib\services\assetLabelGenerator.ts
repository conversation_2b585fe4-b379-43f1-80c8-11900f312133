import jsPDF from 'jspdf'
import QRCode from 'qrcode'

export interface AssetItemLabel {
  id: string
  assetNumber: string
  assetName: string
  category: string
  location: string
  status: string
  condition: string
}

export class AssetLabelGenerator {
  private doc: jsPDF

  constructor() {
    this.doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    })
  }

  /**
   * Generate a single asset label - very small format (1cm height)
   */
  async generateSingleLabel(item: AssetItemLabel): Promise<void> {
    this.doc = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: [25, 10] // 25mm x 10mm label size (1cm height)
    })

    await this.drawMinimalLabel(item, 0, 0, 25, 10)
  }

  /**
   * Generate multiple asset labels on A4 sheet
   * Standard Avery 5160 format: 3 columns x 10 rows = 30 labels per sheet
   */
  async generateBulkLabels(items: AssetItemLabel[]): Promise<void> {
    this.doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    })

    const labelWidth = 25 // 25mm width
    const labelHeight = 10 // 10mm height (1cm)
    const marginLeft = 5
    const marginTop = 5
    const cols = 8
    const rows = 28
    const labelsPerPage = cols * rows

    let currentPage = 0

    for (let i = 0; i < items.length; i++) {
      const pageIndex = Math.floor(i / labelsPerPage)
      const positionOnPage = i % labelsPerPage

      // Add new page if needed
      if (pageIndex > currentPage) {
        this.doc.addPage()
        currentPage = pageIndex
      }

      const col = positionOnPage % cols
      const row = Math.floor(positionOnPage / cols)

      const x = marginLeft + col * labelWidth
      const y = marginTop + row * labelHeight

      await this.drawMinimalLabel(items[i], x, y, labelWidth, labelHeight)
    }
  }

  /**
   * Draw a minimal label with only QR code and asset number
   */
  private async drawMinimalLabel(
    item: AssetItemLabel,
    x: number,
    y: number,
    width: number,
    height: number
  ): Promise<void> {
    // Generate QR code with asset details URL
    // Use current host (works with both localhost and IP address)
    const assetDetailsUrl = `${window.location.origin}/asset-details/${item.assetNumber}`
    const qrCodeDataUrl = await QRCode.toDataURL(assetDetailsUrl, {
      width: 128,
      margin: 0,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })

    // Draw border
    this.doc.setDrawColor(0, 0, 0)
    this.doc.setLineWidth(0.1)
    this.doc.rect(x, y, width, height)

    // QR Code (left side, takes most of the space)
    const qrSize = height - 1 // Almost full height
    this.doc.addImage(qrCodeDataUrl, 'PNG', x + 0.5, y + 0.5, qrSize, qrSize)

    // Asset Number (right side, vertical text for small space)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setFontSize(6)

    // Split asset number for better fit
    const assetNumber = item.assetNumber
    const parts = assetNumber.split('-')

    if (parts.length >= 3) {
      // Show last part (item number) prominently
      this.doc.setFontSize(7)
      this.doc.text(parts[parts.length - 1], x + qrSize + 1, y + 3)

      // Show asset prefix smaller
      this.doc.setFontSize(4)
      this.doc.text(parts.slice(0, -1).join('-'), x + qrSize + 1, y + 6)
    } else {
      this.doc.text(assetNumber, x + qrSize + 1, y + 4)
    }
  }

  /**
   * Draw a single label at specified position (legacy method for compatibility)
   */
  private async drawLabel(item: AssetItemLabel, x: number, y: number, width: number, height: number): Promise<void> {
    // Generate QR code
    const qrCodeDataUrl = await QRCode.toDataURL(item.assetNumber, {
      width: 60,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })

    // Draw border
    this.doc.setDrawColor(0, 0, 0)
    this.doc.setLineWidth(0.1)
    this.doc.rect(x, y, width, height)

    // QR Code (left side)
    const qrSize = Math.min(height - 4, 15)
    this.doc.addImage(qrCodeDataUrl, 'PNG', x + 2, y + 2, qrSize, qrSize)

    // Asset Number (main text)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setFontSize(8)
    this.doc.text(item.assetNumber, x + qrSize + 4, y + 6)

    // Asset Name
    this.doc.setFont('helvetica', 'normal')
    this.doc.setFontSize(6)
    const assetName = this.truncateText(item.assetName, 20)
    this.doc.text(assetName, x + qrSize + 4, y + 10)

    // Category and Location
    this.doc.setFontSize(5)
    this.doc.text(`Cat: ${this.truncateText(item.category, 12)}`, x + qrSize + 4, y + 14)
    this.doc.text(`Loc: ${this.truncateText(item.location, 12)}`, x + qrSize + 4, y + 17)

    // Status and Condition
    this.doc.setFontSize(4)
    this.doc.text(`${item.status.toUpperCase()} | ${item.condition.toUpperCase()}`, x + qrSize + 4, y + 21)

    // Company info (bottom)
    this.doc.setFontSize(4)
    this.doc.text('Asset Management System', x + 2, y + height - 2)
  }

  /**
   * Truncate text to fit label
   */
  private truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength - 3) + '...'
  }

  /**
   * Save the PDF
   */
  save(filename: string): void {
    this.doc.save(filename)
  }

  /**
   * Open print dialog
   */
  openForPrint(): void {
    this.doc.autoPrint()
    window.open(this.doc.output('bloburl'), '_blank')
  }

  /**
   * Get PDF as blob
   */
  getBlob(): Blob {
    return this.doc.output('blob')
  }

  /**
   * Generate asset number labels for printing
   */
  static async generateAssetLabels(
    items: AssetItemLabel[],
    type: 'single' | 'bulk' = 'bulk'
  ): Promise<AssetLabelGenerator> {
    const generator = new AssetLabelGenerator()

    if (type === 'single' && items.length === 1) {
      await generator.generateSingleLabel(items[0])
    } else {
      await generator.generateBulkLabels(items)
    }

    return generator
  }

  /**
   * Quick print function for asset items
   */
  static async printAssetLabels(items: AssetItemLabel[], type: 'single' | 'bulk' = 'bulk'): Promise<void> {
    const generator = await AssetLabelGenerator.generateAssetLabels(items, type)
    generator.openForPrint()
  }

  /**
   * Quick save function for asset items
   */
  static async saveAssetLabels(
    items: AssetItemLabel[],
    filename?: string,
    type: 'single' | 'bulk' = 'bulk'
  ): Promise<void> {
    const generator = await AssetLabelGenerator.generateAssetLabels(items, type)
    const defaultFilename =
      items.length === 1
        ? `Asset_Label_${items[0].assetNumber}.pdf`
        : `Asset_Labels_${new Date().toISOString().split('T')[0]}.pdf`
    generator.save(filename || defaultFilename)
  }
}
