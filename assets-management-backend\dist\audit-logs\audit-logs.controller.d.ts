import { AuditLogsService } from './audit-logs.service';
import { EntityType } from '../entities/audit-log.entity';
export declare class AuditLogsController {
    private readonly auditLogsService;
    constructor(auditLogsService: AuditLogsService);
    getAuditLogs(query: any): Promise<{
        auditLogs: import("../entities/audit-log.entity").AuditLog[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getAuditStats(): Promise<{
        totalLogs: number;
        actionStats: any[];
        entityStats: any[];
        recentActivity: import("../entities/audit-log.entity").AuditLog[];
    }>;
    getEntityAuditLogs(entityType: EntityType, entityId: string): Promise<import("../entities/audit-log.entity").AuditLog[]>;
    getUserAuditLogs(userId: string, limit?: number): Promise<import("../entities/audit-log.entity").AuditLog[]>;
    getMyAuditLogs(req: any, limit?: number): Promise<import("../entities/audit-log.entity").AuditLog[]>;
    getAuditLogById(id: string): Promise<import("../entities/audit-log.entity").AuditLog | null>;
}
