"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx":
/*!************************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AssetDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Inventory.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Category.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Build.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Build,Category,CheckCircle,Inventory,LocationOn,Person,Refresh,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Warning.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst statusColors = {\n    in_use: \"primary\",\n    in_stock: \"success\",\n    maintenance: \"warning\",\n    retired: \"secondary\",\n    lost: \"error\",\n    damaged: \"error\",\n    unknown: \"default\"\n};\nconst conditionColors = {\n    excellent: \"success\",\n    good: \"info\",\n    fair: \"warning\",\n    poor: \"error\",\n    unknown: \"default\"\n};\nfunction AssetDashboard() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assets, setAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load dashboard data\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Load all data in parallel\n            const [assetsResponse, categoriesData, locationsData, usersData] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.assetsService.getAssets({\n                    limit: 1000\n                }),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.getCategories(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.locationsService.getLocations(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.usersService.getUsers()\n            ]);\n            const allAssets = assetsResponse.assets;\n            setAssets(allAssets);\n            setCategories(categoriesData);\n            setLocations(locationsData);\n            setUsers(usersData);\n            // Calculate statistics\n            const totalAssets = allAssets.length;\n            const statusCounts = allAssets.reduce((acc, asset)=>{\n                const status = asset.status || \"unknown\";\n                acc[status] = (acc[status] || 0) + 1;\n                return acc;\n            }, {});\n            const conditionCounts = allAssets.reduce((acc, asset)=>{\n                const condition = asset.condition || \"unknown\";\n                acc[condition] = (acc[condition] || 0) + 1;\n                return acc;\n            }, {});\n            const categoryCounts = allAssets.reduce((acc, asset)=>{\n                var _asset_category;\n                const categoryName = ((_asset_category = asset.category) === null || _asset_category === void 0 ? void 0 : _asset_category.name) || \"Unknown\";\n                acc[categoryName] = (acc[categoryName] || 0) + 1;\n                return acc;\n            }, {});\n            const locationCounts = allAssets.reduce((acc, asset)=>{\n                var _asset_location;\n                const locationName = ((_asset_location = asset.location) === null || _asset_location === void 0 ? void 0 : _asset_location.name) || \"Unknown\";\n                acc[locationName] = (acc[locationName] || 0) + 1;\n                return acc;\n            }, {});\n            // Create breakdown arrays\n            const categoryBreakdown = Object.entries(categoryCounts).map((param)=>{\n                let [name, count] = param;\n                return {\n                    name,\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const locationBreakdown = Object.entries(locationCounts).map((param)=>{\n                let [name, count] = param;\n                return {\n                    name,\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const statusBreakdown = Object.entries(statusCounts).map((param)=>{\n                let [status, count] = param;\n                return {\n                    status: status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const conditionBreakdown = Object.entries(conditionCounts).map((param)=>{\n                let [condition, count] = param;\n                return {\n                    condition: condition.charAt(0).toUpperCase() + condition.slice(1),\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            // Get recent assets (last 5)\n            const recentAssets = allAssets.sort((a, b)=>new Date(b.createdAt || \"\").getTime() - new Date(a.createdAt || \"\").getTime()).slice(0, 5);\n            const dashboardStats = {\n                totalAssets,\n                inUse: statusCounts.in_use || 0,\n                inStock: statusCounts.in_stock || 0,\n                maintenance: statusCounts.maintenance || 0,\n                retired: statusCounts.retired || 0,\n                lost: statusCounts.lost || 0,\n                damaged: statusCounts.damaged || 0,\n                totalCategories: categoriesData.length,\n                totalLocations: locationsData.length,\n                totalUsers: usersData.length,\n                recentAssets,\n                categoryBreakdown,\n                locationBreakdown,\n                statusBreakdown,\n                conditionBreakdown\n            };\n            setStats(dashboardStats);\n        } catch (err) {\n            console.error(\"Failed to load dashboard data:\", err);\n            setError(\"Failed to load dashboard data. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const StatCard = (param)=>{\n        let { title, value, icon, color, subtitle } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            sx: {\n                background: \"linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)\",\n                backdropFilter: \"blur(10px)\",\n                border: \"1px solid rgba(255,255,255,0.2)\",\n                // borderRadius: 3,\n                transition: \"all 0.3s ease-in-out\",\n                \"&:hover\": {\n                    transform: \"translateY(-4px)\",\n                    boxShadow: \"0 8px 25px rgba(0,0,0,0.15)\"\n                }\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    p: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    color: \"text.secondary\",\n                                    gutterBottom: true,\n                                    variant: \"body2\",\n                                    sx: {\n                                        fontWeight: 500\n                                    },\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"h3\",\n                                    component: \"div\",\n                                    color: color,\n                                    sx: {\n                                        fontWeight: 700,\n                                        mb: 0.5\n                                    },\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        fontSize: \"0.875rem\"\n                                    },\n                                    children: subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                bgcolor: \"\".concat(color, \".main\"),\n                                width: 64,\n                                height: 64,\n                                boxShadow: \"0 4px 14px rgba(0,0,0,0.15)\"\n                            },\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 216,\n            columnNumber: 5\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3,\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"60vh\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                size: 60\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 262,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 261,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    severity: \"error\",\n                    sx: {\n                        mb: 3\n                    },\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    variant: \"contained\",\n                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 48\n                    }, void 0),\n                    onClick: loadDashboardData,\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 269,\n            columnNumber: 7\n        }, this);\n    }\n    if (!stats) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                severity: \"info\",\n                children: \"No data available\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 282,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        sx: {\n            p: 4,\n            backgroundColor: \"grey.50\",\n            minHeight: \"100vh\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"h1\",\n                                gutterBottom: true,\n                                sx: {\n                                    fontWeight: 700,\n                                    color: \"text.primary\"\n                                },\n                                children: \"Asset Management Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"body1\",\n                                color: \"text.secondary\",\n                                children: \"Comprehensive overview of your organization's assets\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        variant: \"outlined\",\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 22\n                        }, void 0),\n                        onClick: loadDashboardData,\n                        sx: {\n                            borderRadius: 2,\n                            textTransform: \"none\",\n                            fontWeight: 600\n                        },\n                        children: \"Refresh Data\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Total Assets\",\n                            value: stats.totalAssets.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"primary\",\n                            subtitle: \"All registered assets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Categories\",\n                            value: stats.totalCategories.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"info\",\n                            subtitle: \"Asset categories\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Locations\",\n                            value: stats.totalLocations.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"warning\",\n                            subtitle: \"Storage locations\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Users\",\n                            value: stats.totalUsers.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"success\",\n                            subtitle: \"System users\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"In Use\",\n                            value: stats.inUse.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"primary\",\n                            subtitle: \"Currently assigned\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Available\",\n                            value: stats.inStock.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"success\",\n                            subtitle: \"Ready for assignment\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Maintenance\",\n                            value: stats.maintenance.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"warning\",\n                            subtitle: \"Under maintenance\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Retired\",\n                            value: stats.retired.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"secondary\",\n                            subtitle: \"End of life\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Lost\",\n                            value: stats.lost.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"error\",\n                            subtitle: \"Missing assets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Damaged\",\n                            value: stats.damaged.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"error\",\n                            subtitle: \"Damaged assets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Asset Status Distribution\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            mt: 2\n                                        },\n                                        children: stats.statusBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    mb: index === stats.statusBreakdown.length - 1 ? 0 : 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"space-between\",\n                                                            mb: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 500\n                                                                },\n                                                                children: item.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: [\n                                                                    item.count,\n                                                                    \" (\",\n                                                                    item.percentage,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"determinate\",\n                                                        value: item.percentage,\n                                                        sx: {\n                                                            height: 8,\n                                                            borderRadius: 4,\n                                                            backgroundColor: \"grey.200\",\n                                                            \"& .MuiLinearProgress-bar\": {\n                                                                borderRadius: 4\n                                                            }\n                                                        },\n                                                        color: item.status.toLowerCase().includes(\"use\") ? \"primary\" : item.status.toLowerCase().includes(\"stock\") ? \"success\" : item.status.toLowerCase().includes(\"maintenance\") ? \"warning\" : \"secondary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.status, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Assets by Category\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            size: \"small\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Count\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Percentage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    children: stats.categoryBreakdown.slice(0, 8).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 496,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            category.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 495,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    sx: {\n                                                                        fontWeight: 500\n                                                                    },\n                                                                    children: category.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        label: \"\".concat(category.percentage, \"%\"),\n                                                                        size: \"small\",\n                                                                        color: category.percentage > 20 ? \"primary\" : \"default\",\n                                                                        variant: category.percentage > 20 ? \"filled\" : \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 504,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, category.name, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Assets by Location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            size: \"small\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Count\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Percentage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    children: stats.locationBreakdown.slice(0, 8).map((location)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"warning\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 551,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            location.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 550,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 549,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    sx: {\n                                                                        fontWeight: 500\n                                                                    },\n                                                                    children: location.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        label: \"\".concat(location.percentage, \"%\"),\n                                                                        size: \"small\",\n                                                                        color: location.percentage > 15 ? \"warning\" : \"default\",\n                                                                        variant: location.percentage > 15 ? \"filled\" : \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, location.name, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Asset Condition Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            mt: 2\n                                        },\n                                        children: stats.conditionBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    mb: index === stats.conditionBreakdown.length - 1 ? 0 : 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"space-between\",\n                                                            mb: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 500\n                                                                },\n                                                                children: item.condition\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: [\n                                                                    item.count,\n                                                                    \" (\",\n                                                                    item.percentage,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"determinate\",\n                                                        value: item.percentage,\n                                                        sx: {\n                                                            height: 8,\n                                                            borderRadius: 4,\n                                                            backgroundColor: \"grey.200\",\n                                                            \"& .MuiLinearProgress-bar\": {\n                                                                borderRadius: 4\n                                                            }\n                                                        },\n                                                        color: item.condition.toLowerCase() === \"excellent\" ? \"success\" : item.condition.toLowerCase() === \"good\" ? \"info\" : item.condition.toLowerCase() === \"fair\" ? \"warning\" : \"error\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.condition, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Recently Added Assets\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Asset Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 645,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 647,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Condition\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 649,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Assigned To\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Date Added\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 651,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    children: stats.recentAssets.map((asset)=>{\n                                                        var _asset_category, _asset_location, _asset_assignedTo;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        fontWeight: \"medium\",\n                                                                        color: \"primary.main\",\n                                                                        children: asset.assetNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 658,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        fontWeight: \"medium\",\n                                                                        children: asset.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 663,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 669,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_category = asset.category) === null || _asset_category === void 0 ? void 0 : _asset_category.name) || \"Unknown\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 670,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 668,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"warning\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 675,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_location = asset.location) === null || _asset_location === void 0 ? void 0 : _asset_location.name) || \"Unknown\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 676,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        label: asset.status ? asset.status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase()) : \"Unknown\",\n                                                                        color: asset.status ? statusColors[asset.status] : \"default\",\n                                                                        size: \"small\",\n                                                                        variant: \"filled\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 680,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        label: asset.condition ? asset.condition.charAt(0).toUpperCase() + asset.condition.slice(1) : \"Unknown\",\n                                                                        color: asset.condition ? conditionColors[asset.condition] : \"default\",\n                                                                        size: \"small\",\n                                                                        variant: \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Build_Category_CheckCircle_Inventory_LocationOn_Person_Refresh_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"action\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 709,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_assignedTo = asset.assignedTo) === null || _asset_assignedTo === void 0 ? void 0 : _asset_assignedTo.fullName) || \"Unassigned\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 710,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 708,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 707,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        color: \"text.secondary\",\n                                                                        children: asset.createdAt ? new Date(asset.createdAt).toLocaleDateString() : \"Unknown\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 714,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, asset.id, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, this),\n                                    stats.recentAssets.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            textAlign: \"center\",\n                                            py: 4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"No recent assets found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 629,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 412,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 289,\n        columnNumber: 5\n    }, this);\n}\n_s(AssetDashboard, \"XHXyvutMjVwFwI//GM0yYOuxtd0=\");\n_c = AssetDashboard;\nvar _c;\n$RefreshReg$(_c, \"AssetDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx\n"));

/***/ })

});