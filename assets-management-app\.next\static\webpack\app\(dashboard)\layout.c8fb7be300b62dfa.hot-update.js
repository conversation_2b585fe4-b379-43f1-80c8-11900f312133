"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/layout",{

/***/ "(app-pages-browser)/./src/configs/themeConfig.ts":
/*!************************************!*\
  !*** ./src/configs/themeConfig.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/*\n * If you change the following items in the config object, you will not see any effect in the local development server\n * as these are stored in the cookie (cookie has the highest priority over the themeConfig):\n * 1. mode\n *\n * To see the effect of the above items, you can click on the reset button from the Customizer\n * which is on the top-right corner of the customizer besides the close button.\n * This will reset the cookie to the values provided in the config object below.\n *\n * Another way is to clear the cookie from the browser's Application/Storage tab and then reload the page.\n */ // Type Imports\nconst themeConfig = {\n    templateName: \"Trakspire\",\n    settingsCookieName: \"asset-management-pro-settings\",\n    mode: \"light\",\n    layoutPadding: 32,\n    compactContentWidth: 1440,\n    disableRipple: false // true, false\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (themeConfig);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/configs/themeConfig.ts\n"));

/***/ })

});