'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Alert,
  CircularProgress,
  Tooltip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material'
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material'
import { auditLogsService, AuditLog, AuditLogFilters } from '../../../services/auditLogsService'

export default function AuditLogsPage() {
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [total, setTotal] = useState(0)
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(25)
  const [filters, setFilters] = useState<AuditLogFilters>({})
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null)
  const [detailsOpen, setDetailsOpen] = useState(false)

  const loadAuditLogs = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await auditLogsService.getAuditLogs({
        ...filters,
        page: page + 1,
        limit: rowsPerPage
      })

      setAuditLogs(response.auditLogs)
      setTotal(response.total)
    } catch (err: any) {
      console.error('Failed to load audit logs:', err)
      setError('Failed to load audit logs. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadAuditLogs()
  }, [page, rowsPerPage, filters])

  const handleFilterChange = (field: keyof AuditLogFilters, value: any) => {
    setFilters(prev => ({ ...prev, [field]: value }))
    setPage(0) // Reset to first page when filtering
  }

  const handleClearFilters = () => {
    setFilters({})
    setPage(0)
  }

  const handleViewDetails = (log: AuditLog) => {
    setSelectedLog(log)
    setDetailsOpen(true)
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const renderValue = (value: any): string => {
    if (value === null || value === undefined) return 'N/A'
    if (typeof value === 'object') return JSON.stringify(value, null, 2)
    return String(value)
  }

  if (loading && auditLogs.length === 0) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress size={60} />
      </Box>
    )
  }

  return (
    <Box sx={{ p: 4, backgroundColor: 'grey.50', minHeight: '100vh' }}>
      {/* Error Alert */}
      {error && (
        <Alert severity='error' sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant='h4' component='h1' gutterBottom sx={{ fontWeight: 700, color: 'text.primary' }}>
            Audit Logs
          </Typography>
          <Typography variant='body1' color='text.secondary'>
            Track all system activities and changes
          </Typography>
        </Box>
        <Button
          variant='outlined'
          startIcon={<RefreshIcon />}
          onClick={loadAuditLogs}
          sx={{
            borderRadius: 2,
            textTransform: 'none',
            fontWeight: 600
          }}
        >
          Refresh
        </Button>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3, borderRadius: 3, border: '1px solid', borderColor: 'divider' }}>
        <CardContent>
          <Grid container spacing={3} alignItems='center'>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label='Search'
                placeholder='Search logs...'
                value={filters.search || ''}
                onChange={e => handleFilterChange('search', e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Action</InputLabel>
                <Select
                  value={filters.action || ''}
                  label='Action'
                  onChange={e => handleFilterChange('action', e.target.value)}
                >
                  <MenuItem value=''>All Actions</MenuItem>
                  <MenuItem value='CREATE'>Create</MenuItem>
                  <MenuItem value='UPDATE'>Update</MenuItem>
                  <MenuItem value='DELETE'>Delete</MenuItem>
                  <MenuItem value='LOGIN'>Login</MenuItem>
                  <MenuItem value='LOGOUT'>Logout</MenuItem>
                  <MenuItem value='EXPORT'>Export</MenuItem>
                  <MenuItem value='ASSIGN'>Assign</MenuItem>
                  <MenuItem value='UNASSIGN'>Unassign</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Entity Type</InputLabel>
                <Select
                  value={filters.entityType || ''}
                  label='Entity Type'
                  onChange={e => handleFilterChange('entityType', e.target.value)}
                >
                  <MenuItem value=''>All Types</MenuItem>
                  <MenuItem value='ASSET'>Asset</MenuItem>
                  <MenuItem value='ASSET_ITEM'>Asset Item</MenuItem>
                  <MenuItem value='CATEGORY'>Category</MenuItem>
                  <MenuItem value='LOCATION'>Location</MenuItem>
                  <MenuItem value='USER'>User</MenuItem>
                  <MenuItem value='SYSTEM'>System</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                type='date'
                label='Start Date'
                value={filters.startDate || ''}
                onChange={e => handleFilterChange('startDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                type='date'
                label='End Date'
                value={filters.endDate || ''}
                onChange={e => handleFilterChange('endDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={1}>
              <Button variant='outlined' onClick={handleClearFilters} sx={{ height: '56px', minWidth: 'auto' }}>
                Clear
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Audit Logs Table */}
      <Card sx={{ borderRadius: 3, border: '1px solid', borderColor: 'divider' }}>
        <CardContent sx={{ p: 0 }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 600 }}>Date & Time</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>User</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Action</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Entity Type</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Entity Name</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Description</TableCell>
                  <TableCell align='center' sx={{ fontWeight: 600 }}>
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} align='center' sx={{ py: 4 }}>
                      <CircularProgress size={40} />
                    </TableCell>
                  </TableRow>
                ) : auditLogs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align='center' sx={{ py: 4 }}>
                      <Typography variant='body2' color='text.secondary'>
                        No audit logs found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  auditLogs.map(log => (
                    <TableRow key={log.id} hover>
                      <TableCell>
                        <Typography variant='body2' sx={{ fontWeight: 500 }}>
                          {formatDateTime(log.createdAt)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant='body2'>{log.user?.fullName || 'System'}</Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={auditLogsService.formatAction(log.action)}
                          color={auditLogsService.getActionColor(log.action)}
                          size='small'
                          variant='filled'
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={auditLogsService.formatEntityType(log.entityType)}
                          color={auditLogsService.getEntityTypeColor(log.entityType)}
                          size='small'
                          variant='outlined'
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant='body2'>{log.entityName || '-'}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography
                          variant='body2'
                          sx={{
                            maxWidth: 200,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {log.description || '-'}
                        </Typography>
                      </TableCell>
                      <TableCell align='center'>
                        <Tooltip title='View Details'>
                          <IconButton size='small' onClick={() => handleViewDetails(log)}>
                            <ViewIcon fontSize='small' />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <TablePagination
            component='div'
            count={total}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={e => {
              setRowsPerPage(parseInt(e.target.value, 10))
              setPage(0)
            }}
            rowsPerPageOptions={[10, 25, 50, 100]}
          />
        </CardContent>
      </Card>

      {/* Details Dialog */}
      <Dialog open={detailsOpen} onClose={() => setDetailsOpen(false)} maxWidth='md' fullWidth>
        <DialogTitle>Audit Log Details</DialogTitle>
        <DialogContent>
          {selectedLog && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant='subtitle2' gutterBottom>
                    Date & Time
                  </Typography>
                  <Typography variant='body2' sx={{ mb: 2 }}>
                    {formatDateTime(selectedLog.createdAt)}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant='subtitle2' gutterBottom>
                    User
                  </Typography>
                  <Typography variant='body2' sx={{ mb: 2 }}>
                    {selectedLog.user?.fullName || 'System'} ({selectedLog.user?.email || 'N/A'})
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant='subtitle2' gutterBottom>
                    Action
                  </Typography>
                  <Chip
                    label={auditLogsService.formatAction(selectedLog.action)}
                    color={auditLogsService.getActionColor(selectedLog.action)}
                    size='small'
                    sx={{ mb: 2 }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant='subtitle2' gutterBottom>
                    Entity Type
                  </Typography>
                  <Chip
                    label={auditLogsService.formatEntityType(selectedLog.entityType)}
                    color={auditLogsService.getEntityTypeColor(selectedLog.entityType)}
                    size='small'
                    variant='outlined'
                    sx={{ mb: 2 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant='subtitle2' gutterBottom>
                    Description
                  </Typography>
                  <Typography variant='body2' sx={{ mb: 2 }}>
                    {selectedLog.description || 'No description available'}
                  </Typography>
                </Grid>

                {selectedLog.oldValues && (
                  <Grid item xs={12}>
                    <Accordion>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant='subtitle2'>Old Values</Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                          <pre style={{ margin: 0, fontSize: '0.875rem', whiteSpace: 'pre-wrap' }}>
                            {renderValue(selectedLog.oldValues)}
                          </pre>
                        </Paper>
                      </AccordionDetails>
                    </Accordion>
                  </Grid>
                )}

                {selectedLog.newValues && (
                  <Grid item xs={12}>
                    <Accordion>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant='subtitle2'>New Values</Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                          <pre style={{ margin: 0, fontSize: '0.875rem', whiteSpace: 'pre-wrap' }}>
                            {renderValue(selectedLog.newValues)}
                          </pre>
                        </Paper>
                      </AccordionDetails>
                    </Accordion>
                  </Grid>
                )}

                {selectedLog.metadata && (
                  <Grid item xs={12}>
                    <Accordion>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant='subtitle2'>Metadata</Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                          <pre style={{ margin: 0, fontSize: '0.875rem', whiteSpace: 'pre-wrap' }}>
                            {renderValue(selectedLog.metadata)}
                          </pre>
                        </Paper>
                      </AccordionDetails>
                    </Accordion>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}
