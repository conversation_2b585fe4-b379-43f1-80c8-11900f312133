import { Repository, DataSource } from 'typeorm';
import { Asset, AssetStatus } from '../entities/asset.entity';
import { AssetQuantity } from '../entities/asset-quantity.entity';
import { QuantityTransfer } from '../entities/quantity-transfer.entity';
import { User } from '../entities/user.entity';
import { TransferQuantityDto, BulkTransferQuantityDto, SetAssetQuantityDto } from './dto/transfer-quantity.dto';
export declare class AssetQuantityService {
    private assetRepository;
    private assetQuantityRepository;
    private quantityTransferRepository;
    private userRepository;
    private dataSource;
    constructor(assetRepository: Repository<Asset>, assetQuantityRepository: Repository<AssetQuantity>, quantityTransferRepository: Repository<QuantityTransfer>, userRepository: Repository<User>, dataSource: DataSource);
    getAssetQuantities(assetId: string): Promise<AssetQuantity[]>;
    getAssetQuantitiesByStatus(assetId: string): Promise<Record<AssetStatus, number>>;
    transferQuantity(transferDto: TransferQuantityDto, userId: string): Promise<{
        success: boolean;
        transfer: QuantityTransfer;
        quantities: Record<AssetStatus, number>;
    }>;
    bulkTransferQuantity(bulkTransferDto: BulkTransferQuantityDto, userId: string): Promise<{
        success: boolean;
        transfers: QuantityTransfer[];
        batchId: string;
    }>;
    setAssetQuantities(setQuantityDto: SetAssetQuantityDto, userId: string): Promise<{
        success: boolean;
        quantities: Record<AssetStatus, number>;
    }>;
    getTransferHistory(assetId: string): Promise<QuantityTransfer[]>;
    getAllAssetQuantities(): Promise<{
        assetId: string;
        assetName: string;
        quantities: Record<AssetStatus, number>;
    }[]>;
}
