import { apiClient } from './client';
import { API_CONFIG, setAuthToken, removeAuthToken } from './config';
import {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  User,
} from '@/types/api';

export class AuthService {
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>(
      API_CONFIG.ENDPOINTS.AUTH.LOGIN,
      credentials
    );

    // Store the token
    setAuthToken(response.token);

    return response;
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>(
      API_CONFIG.ENDPOINTS.AUTH.REGISTER,
      userData
    );

    // Store the token
    setAuthToken(response.token);

    return response;
  }

  async getProfile(): Promise<User> {
    return apiClient.get<User>(API_CONFIG.ENDPOINTS.AUTH.PROFILE);
  }

  async verifyToken(): Promise<{ valid: boolean; user: Partial<User> }> {
    return apiClient.post<{ valid: boolean; user: Partial<User> }>(
      API_CONFIG.ENDPOINTS.AUTH.VERIFY_TOKEN
    );
  }

  logout(): void {
    removeAuthToken();
    // Redirect to login page
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
  }

  isAuthenticated(): boolean {
    if (typeof window === 'undefined') return false;
    const token = localStorage.getItem('auth_token');
    return !!token;
  }
}

export const authService = new AuthService();
