"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetQuantity = void 0;
const typeorm_1 = require("typeorm");
const asset_entity_1 = require("./asset.entity");
const user_entity_1 = require("./user.entity");
let AssetQuantity = class AssetQuantity {
    id;
    asset;
    assetId;
    status;
    quantity;
    notes;
    lastModifiedBy;
    lastModifiedById;
    createdAt;
    updatedAt;
};
exports.AssetQuantity = AssetQuantity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AssetQuantity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => asset_entity_1.Asset, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'assetId' }),
    __metadata("design:type", asset_entity_1.Asset)
], AssetQuantity.prototype, "asset", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AssetQuantity.prototype, "assetId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: asset_entity_1.AssetStatus,
    }),
    __metadata("design:type", String)
], AssetQuantity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], AssetQuantity.prototype, "quantity", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], AssetQuantity.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true, onDelete: 'SET NULL' }),
    (0, typeorm_1.JoinColumn)({ name: 'lastModifiedBy' }),
    __metadata("design:type", user_entity_1.User)
], AssetQuantity.prototype, "lastModifiedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], AssetQuantity.prototype, "lastModifiedById", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], AssetQuantity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], AssetQuantity.prototype, "updatedAt", void 0);
exports.AssetQuantity = AssetQuantity = __decorate([
    (0, typeorm_1.Entity)('asset_quantities'),
    (0, typeorm_1.Unique)(['assetId', 'status'])
], AssetQuantity);
//# sourceMappingURL=asset-quantity.entity.js.map