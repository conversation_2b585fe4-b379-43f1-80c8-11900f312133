import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Asset } from '../entities/asset.entity';
import { AssetItem } from '../entities/asset-item.entity';
import { Category } from '../entities/category.entity';
import { Location } from '../entities/location.entity';
import { User } from '../entities/user.entity';
import { AuditLog } from '../entities/audit-log.entity';
import { AuditLogsService } from '../audit-logs/audit-logs.service';

export interface ReportFilters {
  startDate?: Date;
  endDate?: Date;
  categoryId?: string;
  locationId?: string;
  status?: string;
  condition?: string;
  assignedUserId?: string;
}

export interface AssetReport {
  totalAssets: number;
  totalValue: number;
  statusBreakdown: { status: string; count: number; percentage: number }[];
  conditionBreakdown: {
    condition: string;
    count: number;
    percentage: number;
  }[];
  categoryBreakdown: { category: string; count: number; percentage: number }[];
  locationBreakdown: { location: string; count: number; percentage: number }[];
  assets: any[];
}

export interface UtilizationReport {
  totalAssets: number;
  inUseAssets: number;
  availableAssets: number;
  utilizationRate: number;
  categoryUtilization: {
    category: string;
    total: number;
    inUse: number;
    rate: number;
  }[];
  locationUtilization: {
    location: string;
    total: number;
    inUse: number;
    rate: number;
  }[];
}

export interface MaintenanceReport {
  assetsInMaintenance: number;
  assetsDamaged: number;
  assetsRetired: number;
  maintenanceByCategory: { category: string; count: number }[];
  maintenanceByLocation: { location: string; count: number }[];
  maintenanceSchedule: any[];
}

export interface ActivityReport {
  totalActivities: number;
  recentActivities: AuditLog[];
  activityByAction: { action: string; count: number }[];
  activityByUser: { user: string; count: number }[];
  activityTrend: { date: string; count: number }[];
}

@Injectable()
export class ReportsService {
  constructor(
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(AssetItem)
    private assetItemRepository: Repository<AssetItem>,
    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,
    @InjectRepository(Location)
    private locationRepository: Repository<Location>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(AuditLog)
    private auditLogRepository: Repository<AuditLog>,
  ) {}

  async generateAssetReport(filters: ReportFilters = {}): Promise<AssetReport> {
    // Get asset items with their asset data for status and condition information
    const query = this.assetItemRepository
      .createQueryBuilder('assetItem')
      .leftJoinAndSelect('assetItem.asset', 'asset')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('asset.location', 'location')
      .leftJoinAndSelect('assetItem.assignedTo', 'assignedTo');

    // Apply filters
    if (filters.startDate) {
      query.andWhere('assetItem.createdAt >= :startDate', {
        startDate: filters.startDate,
      });
    }
    if (filters.endDate) {
      query.andWhere('assetItem.createdAt <= :endDate', {
        endDate: filters.endDate,
      });
    }
    if (filters.categoryId) {
      query.andWhere('asset.categoryId = :categoryId', {
        categoryId: filters.categoryId,
      });
    }
    if (filters.locationId) {
      query.andWhere('asset.locationId = :locationId', {
        locationId: filters.locationId,
      });
    }
    if (filters.status) {
      query.andWhere('assetItem.status = :status', { status: filters.status });
    }
    if (filters.condition) {
      query.andWhere('assetItem.condition = :condition', {
        condition: filters.condition,
      });
    }
    if (filters.assignedUserId) {
      query.andWhere('assetItem.assignedToId = :assignedUserId', {
        assignedUserId: filters.assignedUserId,
      });
    }

    const assetItems = await query.getMany();
    const totalAssets = assetItems.length;
    const totalValue = assetItems.reduce(
      (sum, item) => sum + (item.asset?.unitPrice || 0),
      0,
    );

    // Status breakdown
    const statusCounts = assetItems.reduce(
      (acc, item) => {
        acc[item.status] = (acc[item.status] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const statusBreakdown = Object.entries(statusCounts).map(
      ([status, count]) => ({
        status,
        count,
        percentage:
          totalAssets > 0 ? Math.round((count / totalAssets) * 100) : 0,
      }),
    );

    // Condition breakdown
    const conditionCounts = assetItems.reduce(
      (acc, item) => {
        acc[item.condition] = (acc[item.condition] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const conditionBreakdown = Object.entries(conditionCounts).map(
      ([condition, count]) => ({
        condition,
        count,
        percentage:
          totalAssets > 0 ? Math.round((count / totalAssets) * 100) : 0,
      }),
    );

    // Category breakdown
    const categoryCounts = assetItems.reduce(
      (acc, item) => {
        const categoryName = item.asset?.category?.name || 'Unknown';
        acc[categoryName] = (acc[categoryName] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const categoryBreakdown = Object.entries(categoryCounts).map(
      ([category, count]) => ({
        category,
        count,
        percentage:
          totalAssets > 0 ? Math.round((count / totalAssets) * 100) : 0,
      }),
    );

    // Location breakdown
    const locationCounts = assetItems.reduce(
      (acc, item) => {
        const locationName = item.asset?.location?.name || 'Unknown';
        acc[locationName] = (acc[locationName] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const locationBreakdown = Object.entries(locationCounts).map(
      ([location, count]) => ({
        location,
        count,
        percentage:
          totalAssets > 0 ? Math.round((count / totalAssets) * 100) : 0,
      }),
    );

    // Convert asset items to assets format for compatibility
    const assets = assetItems.map((item) => ({
      ...item.asset,
      status: item.status,
      condition: item.condition,
      assignedTo: item.assignedTo,
      assetNumber: item.assetNumber,
      serialNumber: item.serialNumber,
      purchaseDate: item.purchaseDate,
      warrantyExpiry: item.warrantyExpiry,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
    }));

    return {
      totalAssets,
      totalValue,
      statusBreakdown,
      conditionBreakdown,
      categoryBreakdown,
      locationBreakdown,
      assets,
    };
  }

  async generateUtilizationReport(
    filters: ReportFilters = {},
  ): Promise<UtilizationReport> {
    const assetItems = await this.assetItemRepository.find({
      relations: ['asset', 'asset.category', 'asset.location'],
    });

    const totalAssets = assetItems.length;
    const inUseAssets = assetItems.filter(
      (item) => item.status === 'in_use',
    ).length;
    const availableAssets = assetItems.filter(
      (item) => item.status === 'in_stock',
    ).length;
    const utilizationRate =
      totalAssets > 0 ? Math.round((inUseAssets / totalAssets) * 100) : 0;

    // Category utilization
    const categoryStats = assetItems.reduce(
      (acc, item) => {
        const categoryName = item.asset?.category?.name || 'Unknown';
        if (!acc[categoryName]) {
          acc[categoryName] = { total: 0, inUse: 0 };
        }
        acc[categoryName].total++;
        if (item.status === 'in_use') {
          acc[categoryName].inUse++;
        }
        return acc;
      },
      {} as Record<string, { total: number; inUse: number }>,
    );

    const categoryUtilization = Object.entries(categoryStats).map(
      ([category, stats]) => ({
        category,
        total: stats.total,
        inUse: stats.inUse,
        rate:
          stats.total > 0 ? Math.round((stats.inUse / stats.total) * 100) : 0,
      }),
    );

    // Location utilization
    const locationStats = assetItems.reduce(
      (acc, item) => {
        const locationName = item.asset?.location?.name || 'Unknown';
        if (!acc[locationName]) {
          acc[locationName] = { total: 0, inUse: 0 };
        }
        acc[locationName].total++;
        if (item.status === 'in_use') {
          acc[locationName].inUse++;
        }
        return acc;
      },
      {} as Record<string, { total: number; inUse: number }>,
    );

    const locationUtilization = Object.entries(locationStats).map(
      ([location, stats]) => ({
        location,
        total: stats.total,
        inUse: stats.inUse,
        rate:
          stats.total > 0 ? Math.round((stats.inUse / stats.total) * 100) : 0,
      }),
    );

    return {
      totalAssets,
      inUseAssets,
      availableAssets,
      utilizationRate,
      categoryUtilization,
      locationUtilization,
    };
  }

  async generateMaintenanceReport(
    filters: ReportFilters = {},
  ): Promise<MaintenanceReport> {
    const assetItems = await this.assetItemRepository.find({
      relations: ['asset', 'asset.category', 'asset.location'],
    });

    const assetsInMaintenance = assetItems.filter(
      (item) => item.status === 'maintenance',
    ).length;
    const assetsDamaged = assetItems.filter(
      (item) => item.status === 'damaged',
    ).length;
    const assetsRetired = assetItems.filter(
      (item) => item.status === 'retired',
    ).length;

    // Maintenance by category
    const maintenanceAssets = assetItems.filter(
      (item) => item.status === 'maintenance' || item.status === 'damaged',
    );

    const categoryStats = maintenanceAssets.reduce(
      (acc, item) => {
        const categoryName = item.asset?.category?.name || 'Unknown';
        acc[categoryName] = (acc[categoryName] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const maintenanceByCategory = Object.entries(categoryStats).map(
      ([category, count]) => ({
        category,
        count,
      }),
    );

    // Maintenance by location
    const locationStats = maintenanceAssets.reduce(
      (acc, item) => {
        const locationName = item.asset?.location?.name || 'Unknown';
        acc[locationName] = (acc[locationName] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const maintenanceByLocation = Object.entries(locationStats).map(
      ([location, count]) => ({
        location,
        count,
      }),
    );

    // Maintenance schedule (assets that might need maintenance soon)
    const maintenanceScheduleItems = assetItems.filter(
      (item) => item.status === 'in_use' && item.condition === 'fair',
    );

    // Convert to Asset format for compatibility
    const maintenanceSchedule = maintenanceScheduleItems.map((item) => ({
      ...item.asset,
      status: item.status,
      condition: item.condition,
      assignedTo: item.assignedTo,
      assetNumber: item.assetNumber,
      serialNumber: item.serialNumber,
      purchaseDate: item.purchaseDate,
      warrantyExpiry: item.warrantyExpiry,
    }));

    return {
      assetsInMaintenance,
      assetsDamaged,
      assetsRetired,
      maintenanceByCategory,
      maintenanceByLocation,
      maintenanceSchedule,
    };
  }

  async generateActivityReport(
    filters: ReportFilters = {},
  ): Promise<ActivityReport> {
    const query = this.auditLogRepository
      .createQueryBuilder('auditLog')
      .leftJoinAndSelect('auditLog.user', 'user');

    if (filters.startDate) {
      query.andWhere('auditLog.createdAt >= :startDate', {
        startDate: filters.startDate,
      });
    }
    if (filters.endDate) {
      query.andWhere('auditLog.createdAt <= :endDate', {
        endDate: filters.endDate,
      });
    }

    const auditLogs = await query.getMany();
    const totalActivities = auditLogs.length;

    // Recent activities
    const recentActivities = await this.auditLogRepository.find({
      relations: ['user'],
      order: { createdAt: 'DESC' },
      take: 20,
    });

    // Activity by action
    const actionStats = auditLogs.reduce(
      (acc, log) => {
        acc[log.action] = (acc[log.action] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const activityByAction = Object.entries(actionStats).map(
      ([action, count]) => ({
        action,
        count,
      }),
    );

    // Activity by user
    const userStats = auditLogs.reduce(
      (acc, log) => {
        const userName = log.user?.fullName || 'Unknown';
        acc[userName] = (acc[userName] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const activityByUser = Object.entries(userStats).map(([user, count]) => ({
      user,
      count,
    }));

    // Activity trend (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const trendData = await this.auditLogRepository
      .createQueryBuilder('auditLog')
      .select('DATE(auditLog.createdAt)', 'date')
      .addSelect('COUNT(*)', 'count')
      .where('auditLog.createdAt >= :thirtyDaysAgo', { thirtyDaysAgo })
      .groupBy('DATE(auditLog.createdAt)')
      .orderBy('DATE(auditLog.createdAt)', 'ASC')
      .getRawMany();

    const activityTrend = trendData.map((item) => ({
      date: item.date,
      count: parseInt(item.count),
    }));

    return {
      totalActivities,
      recentActivities,
      activityByAction,
      activityByUser,
      activityTrend,
    };
  }
}
