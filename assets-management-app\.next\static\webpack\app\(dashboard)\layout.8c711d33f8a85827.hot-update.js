"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/layout",{

/***/ "(app-pages-browser)/./src/components/layout/shared/UserDropdown.tsx":
/*!*******************************************************!*\
  !*** ./src/components/layout/shared/UserDropdown.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/styles */ \"(app-pages-browser)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _mui_material_Badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/Badge */ \"(app-pages-browser)/./node_modules/@mui/material/Badge/Badge.js\");\n/* harmony import */ var _mui_material_Avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/Avatar */ \"(app-pages-browser)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _mui_material_Popper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/Popper */ \"(app-pages-browser)/./node_modules/@mui/material/Popper/Popper.js\");\n/* harmony import */ var _mui_material_Fade__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/Fade */ \"(app-pages-browser)/./node_modules/@mui/material/Fade/Fade.js\");\n/* harmony import */ var _mui_material_Paper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/Paper */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _mui_material_ClickAwayListener__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/ClickAwayListener */ \"(app-pages-browser)/./node_modules/@mui/material/ClickAwayListener/ClickAwayListener.js\");\n/* harmony import */ var _mui_material_MenuList__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material/MenuList */ \"(app-pages-browser)/./node_modules/@mui/material/MenuList/MenuList.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/Typography */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_MenuItem__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material/MenuItem */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/material/Button */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// React Imports\n\n// Next Imports\n\n// Context Imports\n\n// MUI Imports\n\n\n\n\n\n\n\n\n\n\n\n\n// Styled component for badge content\nconst BadgeContentSpan = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\"span\")({\n    width: 8,\n    height: 8,\n    borderRadius: \"50%\",\n    cursor: \"pointer\",\n    backgroundColor: \"var(--mui-palette-success-main)\",\n    boxShadow: \"0 0 0 2px var(--mui-palette-background-paper)\"\n});\nconst UserDropdown = ()=>{\n    _s();\n    // States\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Refs\n    const anchorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Hooks\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const handleDropdownOpen = ()=>{\n        !open ? setOpen(true) : setOpen(false);\n    };\n    const handleDropdownClose = (event, url)=>{\n        if (url) {\n            router.push(url);\n        }\n        if (anchorRef.current && anchorRef.current.contains(event === null || event === void 0 ? void 0 : event.target)) {\n            return;\n        }\n        setOpen(false);\n    };\n    const handleLogout = ()=>{\n        logout();\n        setOpen(false);\n    };\n    // Get user initials for avatar\n    const getUserInitials = ()=>{\n        var _user_firstName, _user_lastName;\n        if (!user) return \"U\";\n        return \"\".concat(((_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName[0]) || \"\").concat(((_user_lastName = user.lastName) === null || _user_lastName === void 0 ? void 0 : _user_lastName[0]) || \"\").toUpperCase();\n    };\n    // Get user display name\n    const getUserDisplayName = ()=>{\n        if (!user) return \"User\";\n        return \"\".concat(user.firstName || \"\", \" \").concat(user.lastName || \"\").trim() || user.email;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Badge__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                ref: anchorRef,\n                overlap: \"circular\",\n                badgeContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BadgeContentSpan, {\n                    onClick: handleDropdownOpen\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 23\n                }, void 0),\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: \"right\"\n                },\n                className: \"mis-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Avatar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: anchorRef,\n                    alt: getUserDisplayName(),\n                    onClick: handleDropdownOpen,\n                    className: \"cursor-pointer bs-[38px] is-[38px]\",\n                    sx: {\n                        bgcolor: \"primary.main\",\n                        color: \"primary.contrastText\"\n                    },\n                    children: getUserInitials()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Popper__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                open: open,\n                transition: true,\n                disablePortal: true,\n                placement: \"bottom-end\",\n                anchorEl: anchorRef.current,\n                className: \"min-is-[240px] !mbs-4 z-[1]\",\n                children: (param)=>{\n                    let { TransitionProps, placement } = param;\n                    var _user_role, _user_role1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Fade__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        ...TransitionProps,\n                        style: {\n                            transformOrigin: placement === \"bottom-end\" ? \"right top\" : \"left top\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Paper__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_ClickAwayListener__WEBPACK_IMPORTED_MODULE_10__.ClickAwayListener, {\n                                onClickAway: (e)=>handleDropdownClose(e),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_MenuList__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center plb-2 pli-4 gap-2\",\n                                            tabIndex: -1,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Avatar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    alt: getUserDisplayName(),\n                                                    sx: {\n                                                        bgcolor: \"primary.main\",\n                                                        color: \"primary.contrastText\"\n                                                    },\n                                                    children: getUserInitials()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"font-medium\",\n                                                            color: \"text.primary\",\n                                                            children: getUserDisplayName()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            variant: \"caption\",\n                                                            children: (user === null || user === void 0 ? void 0 : (_user_role = user.role) === null || _user_role === void 0 ? void 0 : _user_role.charAt(0).toUpperCase()) + (user === null || user === void 0 ? void 0 : (_user_role1 = user.role) === null || _user_role1 === void 0 ? void 0 : _user_role1.slice(1)) || \"User\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"mlb-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_MenuItem__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"gap-3\",\n                                            onClick: (e)=>handleDropdownClose(e),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-user-3-line\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    color: \"text.primary\",\n                                                    children: \"My Profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center plb-2 pli-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                fullWidth: true,\n                                                variant: \"contained\",\n                                                color: \"error\",\n                                                size: \"small\",\n                                                endIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-logout-box-r-line\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                onClick: handleLogout,\n                                                sx: {\n                                                    \"& .MuiButton-endIcon\": {\n                                                        marginInlineStart: 1.5\n                                                    }\n                                                },\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, undefined);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(UserDropdown, \"Ki6AwJ+xLhf7pZqDJwJ6otUpJQc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = UserDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UserDropdown);\nvar _c;\n$RefreshReg$(_c, \"UserDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/shared/UserDropdown.tsx\n"));

/***/ })

});