'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Chip,
  Paper,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material'
import {
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Assessment as ReportIcon,
  TrendingUp as TrendingUpIcon,
  Build as MaintenanceIcon,
  Timeline as ActivityIcon
} from '@mui/icons-material'
import {
  reportsService,
  AssetReport,
  UtilizationReport,
  MaintenanceReport,
  ActivityReport,
  ReportFilters
} from '../../../services/reportsService'
import { categoriesService } from '../../../lib/api/categories'
import { locationsService } from '../../../lib/api/locations'
import { Category } from '../../../types/api'
import { Location } from '../../../types/api'

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`report-tabpanel-${index}`}
      aria-labelledby={`report-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  )
}

export default function ReportsPage() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [tabValue, setTabValue] = useState(0)
  const [filters, setFilters] = useState<ReportFilters>({})
  const [categories, setCategories] = useState<Category[]>([])
  const [locations, setLocations] = useState<Location[]>([])

  // Report data
  const [assetReport, setAssetReport] = useState<AssetReport | null>(null)
  const [utilizationReport, setUtilizationReport] = useState<UtilizationReport | null>(null)
  const [maintenanceReport, setMaintenanceReport] = useState<MaintenanceReport | null>(null)
  const [activityReport, setActivityReport] = useState<ActivityReport | null>(null)

  useEffect(() => {
    loadInitialData()
  }, [])

  const loadInitialData = async () => {
    try {
      const [categoriesData, locationsData] = await Promise.all([
        categoriesService.getCategories(),
        locationsService.getLocations()
      ])
      setCategories(categoriesData)
      setLocations(locationsData)
    } catch (err) {
      console.error('Failed to load initial data:', err)
    }
  }

  const generateReport = async (reportType: string) => {
    try {
      setLoading(true)
      setError(null)

      switch (reportType) {
        case 'assets':
          const assetData = await reportsService.generateAssetReport(filters)
          setAssetReport(assetData)
          break
        case 'utilization':
          const utilizationData = await reportsService.generateUtilizationReport()
          setUtilizationReport(utilizationData)
          break
        case 'maintenance':
          const maintenanceData = await reportsService.generateMaintenanceReport()
          setMaintenanceReport(maintenanceData)
          break
        case 'activity':
          const activityData = await reportsService.generateActivityReport({
            startDate: filters.startDate,
            endDate: filters.endDate
          })
          setActivityReport(activityData)
          break
      }
    } catch (err: any) {
      console.error(`Failed to generate ${reportType} report:`, err)
      setError(`Failed to generate ${reportType} report. Please try again.`)
    } finally {
      setLoading(false)
    }
  }

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
  }

  const handleFilterChange = (field: keyof ReportFilters, value: any) => {
    setFilters(prev => ({ ...prev, [field]: value }))
  }

  const exportReport = (reportType: string) => {
    switch (reportType) {
      case 'assets':
        if (assetReport) reportsService.exportAssetReportToCSV(assetReport)
        break
      case 'utilization':
        if (utilizationReport) reportsService.exportUtilizationReportToCSV(utilizationReport)
        break
      case 'maintenance':
        if (maintenanceReport) reportsService.exportMaintenanceReportToCSV(maintenanceReport)
        break
      case 'activity':
        if (activityReport) reportsService.exportActivityReportToCSV(activityReport)
        break
    }
  }

  return (
    <Box sx={{ p: 4, backgroundColor: 'grey.50', minHeight: '100vh' }}>
      {/* Error Alert */}
      {error && (
        <Alert severity='error' sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant='h4' component='h1' gutterBottom sx={{ fontWeight: 700, color: 'text.primary' }}>
            Reports & Analytics
          </Typography>
          <Typography variant='body1' color='text.secondary'>
            Generate comprehensive reports and analyze asset data
          </Typography>
        </Box>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3, boxShadow: 'none', border: '1px solid', borderColor: 'divider' }}>
        <CardContent>
          <Typography variant='h6' gutterBottom sx={{ fontWeight: 600 }}>
            Report Filters
          </Typography>
          <Grid container spacing={3} alignItems='center'>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                type='date'
                label='Start Date'
                value={filters.startDate || ''}
                onChange={e => handleFilterChange('startDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                type='date'
                label='End Date'
                value={filters.endDate || ''}
                onChange={e => handleFilterChange('endDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={filters.categoryId || ''}
                  label='Category'
                  onChange={e => handleFilterChange('categoryId', e.target.value)}
                >
                  <MenuItem value=''>All Categories</MenuItem>
                  {categories.map(category => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Location</InputLabel>
                <Select
                  value={filters.locationId || ''}
                  label='Location'
                  onChange={e => handleFilterChange('locationId', e.target.value)}
                >
                  <MenuItem value=''>All Locations</MenuItem>
                  {locations.map(location => (
                    <MenuItem key={location.id} value={location.id}>
                      {location.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status || ''}
                  label='Status'
                  onChange={e => handleFilterChange('status', e.target.value)}
                >
                  <MenuItem value=''>All Statuses</MenuItem>
                  <MenuItem value='in_use'>In Use</MenuItem>
                  <MenuItem value='in_stock'>In Stock</MenuItem>
                  <MenuItem value='maintenance'>Maintenance</MenuItem>
                  <MenuItem value='retired'>Retired</MenuItem>
                  <MenuItem value='lost'>Lost</MenuItem>
                  <MenuItem value='damaged'>Damaged</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Condition</InputLabel>
                <Select
                  value={filters.condition || ''}
                  label='Condition'
                  onChange={e => handleFilterChange('condition', e.target.value)}
                >
                  <MenuItem value=''>All Conditions</MenuItem>
                  <MenuItem value='excellent'>Excellent</MenuItem>
                  <MenuItem value='good'>Good</MenuItem>
                  <MenuItem value='fair'>Fair</MenuItem>
                  <MenuItem value='poor'>Poor</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Report Tabs */}
      <Card sx={{ border: '1px solid', borderColor: 'divider', boxShadow: 'none', padding: 5 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label='report tabs'>
            <Tab
              label='Asset Report'
              icon={<ReportIcon />}
              iconPosition='start'
              sx={{ textTransform: 'none', fontWeight: 600 }}
            />
            <Tab
              label='Utilization'
              icon={<TrendingUpIcon />}
              iconPosition='start'
              sx={{ textTransform: 'none', fontWeight: 600 }}
            />
            <Tab
              label='Maintenance'
              icon={<MaintenanceIcon />}
              iconPosition='start'
              sx={{ textTransform: 'none', fontWeight: 600 }}
            />
            <Tab
              label='Activity'
              icon={<ActivityIcon />}
              iconPosition='start'
              sx={{ textTransform: 'none', fontWeight: 600 }}
            />
          </Tabs>
        </Box>

        {/* Asset Report Tab */}
        <TabPanel value={tabValue} index={0}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant='h6' sx={{ fontWeight: 600 }}>
              Asset Report
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant='outlined'
                startIcon={<RefreshIcon />}
                onClick={() => generateReport('assets')}
                disabled={loading}
              >
                Generate Report
              </Button>
              {assetReport && (
                <Button variant='contained' startIcon={<DownloadIcon />} onClick={() => exportReport('assets')}>
                  Export CSV
                </Button>
              )}
            </Box>
          </Box>

          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          )}

          {assetReport && !loading && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card sx={{ p: 3 }}>
                  <Typography variant='h6' gutterBottom>
                    Summary
                  </Typography>
                  <Typography variant='h4' color='primary' gutterBottom>
                    {assetReport.totalAssets.toLocaleString()}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Total Assets
                  </Typography>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card sx={{ p: 3 }}>
                  <Typography variant='h6' gutterBottom>
                    Status Breakdown
                  </Typography>
                  {assetReport.statusBreakdown.map(item => (
                    <Box key={item.status} sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant='body2'>
                          {item.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </Typography>
                        <Typography variant='body2' sx={{ fontWeight: 600 }}>
                          {item.count} ({item.percentage}%)
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant='determinate'
                        value={item.percentage}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>
                  ))}
                </Card>
              </Grid>
            </Grid>
          )}
        </TabPanel>

        {/* Utilization Report Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant='h6' sx={{ fontWeight: 600 }}>
              Utilization Report
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant='outlined'
                startIcon={<RefreshIcon />}
                onClick={() => generateReport('utilization')}
                disabled={loading}
              >
                Generate Report
              </Button>
              {utilizationReport && (
                <Button variant='contained' startIcon={<DownloadIcon />} onClick={() => exportReport('utilization')}>
                  Export CSV
                </Button>
              )}
            </Box>
          </Box>

          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          )}

          {utilizationReport && !loading && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Card sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant='h4' color='primary' gutterBottom>
                    {utilizationReport.utilizationRate}%
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Overall Utilization Rate
                  </Typography>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant='h4' color='success.main' gutterBottom>
                    {utilizationReport.inUseAssets}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Assets In Use
                  </Typography>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant='h4' color='info.main' gutterBottom>
                    {utilizationReport.availableAssets}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Available Assets
                  </Typography>
                </Card>
              </Grid>
            </Grid>
          )}
        </TabPanel>

        {/* Maintenance Report Tab */}
        <TabPanel value={tabValue} index={2}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant='h6' sx={{ fontWeight: 600 }}>
              Maintenance Report
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant='outlined'
                startIcon={<RefreshIcon />}
                onClick={() => generateReport('maintenance')}
                disabled={loading}
              >
                Generate Report
              </Button>
              {maintenanceReport && (
                <Button variant='contained' startIcon={<DownloadIcon />} onClick={() => exportReport('maintenance')}>
                  Export CSV
                </Button>
              )}
            </Box>
          </Box>

          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          )}

          {maintenanceReport && !loading && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Card sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant='h4' color='warning.main' gutterBottom>
                    {maintenanceReport.assetsInMaintenance}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    In Maintenance
                  </Typography>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant='h4' color='error.main' gutterBottom>
                    {maintenanceReport.assetsDamaged}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Damaged Assets
                  </Typography>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant='h4' color='secondary.main' gutterBottom>
                    {maintenanceReport.assetsRetired}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Retired Assets
                  </Typography>
                </Card>
              </Grid>
            </Grid>
          )}
        </TabPanel>

        {/* Activity Report Tab */}
        <TabPanel value={tabValue} index={3}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant='h6' sx={{ fontWeight: 600 }}>
              Activity Report
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant='outlined'
                startIcon={<RefreshIcon />}
                onClick={() => generateReport('activity')}
                disabled={loading}
              >
                Generate Report
              </Button>
              {activityReport && (
                <Button variant='contained' startIcon={<DownloadIcon />} onClick={() => exportReport('activity')}>
                  Export CSV
                </Button>
              )}
            </Box>
          </Box>

          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          )}

          {activityReport && !loading && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card sx={{ p: 3 }}>
                  <Typography variant='h6' gutterBottom>
                    Total Activities
                  </Typography>
                  <Typography variant='h4' color='primary' gutterBottom>
                    {activityReport.totalActivities.toLocaleString()}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    System Activities Logged
                  </Typography>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card sx={{ p: 3 }}>
                  <Typography variant='h6' gutterBottom>
                    Recent Activities
                  </Typography>
                  <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                    {activityReport.recentActivities.slice(0, 5).map(activity => (
                      <Box key={activity.id} sx={{ mb: 2, pb: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
                        <Typography variant='body2' sx={{ fontWeight: 500 }}>
                          {activity.user?.fullName || 'System'} - {activity.action.replace('_', ' ')}
                        </Typography>
                        <Typography variant='caption' color='text.secondary'>
                          {new Date(activity.createdAt).toLocaleString()}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Card>
              </Grid>
            </Grid>
          )}
        </TabPanel>
      </Card>
    </Box>
  )
}
