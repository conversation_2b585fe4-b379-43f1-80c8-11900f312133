import { Asset, AssetStatus, AssetCondition } from './asset.entity';
import { User } from './user.entity';
export declare class AssetItem {
    id: string;
    asset: Asset;
    assetId: string;
    assetNumber: string;
    status: AssetStatus;
    condition: AssetCondition;
    serialNumber: string;
    purchaseDate: Date;
    warrantyExpiry: Date;
    notes: string;
    assignedTo: User;
    assignedToId: string;
    assignedDate: Date;
    lastModifiedBy: User;
    lastModifiedById: string;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
