import { IsUUID, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AssignAssetDto {
  @ApiProperty({
    description: 'User ID to assign the asset to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  assignedToId: string;

  @ApiProperty({
    description: 'Assignment notes',
    example: 'Assigned for remote work setup',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;
}
