'use client'

import { useEffect, useState, useCallback } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { Box, CircularProgress, Typography } from '@mui/material'
import { authService } from '@/lib/api'

interface AuthGuardProps {
  children: React.ReactNode
}

export default function AuthGuard({ children }: AuthGuardProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  const redirectToLogin = useCallback(() => {
    authService.logout()
    router.replace('/login') // Use replace instead of push to prevent back navigation
  }, [router])

  const checkAuth = useCallback(async () => {
    try {
      // Check if we have a token
      if (!authService.isAuthenticated()) {
        redirectToLogin()
        return
      }

      // Verify token with backend
      try {
        const verification = await authService.verifyToken()
        if (verification.valid) {
          setIsAuthenticated(true)
        } else {
          // Token is invalid, redirect to login
          redirectToLogin()
        }
      } catch (backendError) {
        console.error('Token verification failed:', backendError)
        // Token verification failed, redirect to login
        redirectToLogin()
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      redirectToLogin()
    } finally {
      setIsLoading(false)
    }
  }, [redirectToLogin])

  useEffect(() => {
    // Only check auth for protected routes
    const publicRoutes = ['/login', '/register', '/forgot-password']
    const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route))

    if (isPublicRoute) {
      // If user is already authenticated and tries to access login page, redirect to dashboard
      if (authService.isAuthenticated() && pathname === '/login') {
        router.replace('/dashboard')
        return
      }
      setIsLoading(false)
      setIsAuthenticated(true)
    } else {
      checkAuth()
    }
  }, [pathname, router, checkAuth])

  // Prevent back navigation to protected routes after logout
  useEffect(() => {
    const handlePopState = () => {
      const publicRoutes = ['/login', '/register', '/forgot-password']
      const isPublicRoute = publicRoutes.some(route => window.location.pathname.startsWith(route))

      if (!isPublicRoute && !authService.isAuthenticated()) {
        window.history.pushState(null, '', '/login')
      }
    }

    window.addEventListener('popstate', handlePopState)

    // Push initial state to prevent back navigation
    if (typeof window !== 'undefined' && !authService.isAuthenticated()) {
      window.history.pushState(null, '', window.location.href)
    }

    return () => {
      window.removeEventListener('popstate', handlePopState)
    }
  }, [isAuthenticated])

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2
        }}
      >
        <CircularProgress size={40} />
        <Typography variant='body1' color='text.secondary'>
          Checking authentication...
        </Typography>
      </Box>
    )
  }

  // If not authenticated and not on a public route, don't render children
  const publicRoutes = ['/login', '/register', '/forgot-password']
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route))

  if (!isAuthenticated && !isPublicRoute) {
    return null // Router will handle redirect
  }

  return <>{children}</>
}
