'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Button,
  CircularProgress,
  Alert
} from '@mui/material'
import {
  Inventory as InventoryIcon,
  Assignment as AssignmentIcon,
  CheckCircle as CheckCircleIcon,
  Person as PersonIcon,
  Category as CategoryIcon,
  LocationOn as LocationIcon,
  Build as MaintenanceIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material'
import { assetsService, categoriesService, locationsService, usersService } from '@/lib/api'
import { assetItemService, AssetQuantityOverview } from '@/lib/api/assetItemService'
import { Asset, Category, Location, User } from '@/types/api'

// Types for dashboard data
interface DashboardStats {
  totalAssets: number
  inUse: number
  inStock: number
  maintenance: number
  retired: number
  lost: number
  damaged: number
  totalCategories: number
  totalLocations: number
  totalUsers: number
  recentAssets: Asset[]
  categoryBreakdown: { name: string; count: number; percentage: number }[]
  locationBreakdown: { name: string; count: number; percentage: number }[]
  statusBreakdown: { status: string; count: number; percentage: number }[]
  conditionBreakdown: { condition: string; count: number; percentage: number }[]
}

const statusColors = {
  in_use: 'primary',
  in_stock: 'success',
  maintenance: 'warning',
  retired: 'secondary',
  lost: 'error',
  damaged: 'error',
  unknown: 'default'
} as const

const conditionColors = {
  excellent: 'success',
  good: 'info',
  fair: 'warning',
  poor: 'error',
  unknown: 'default'
} as const

export default function AssetDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [assets, setAssets] = useState<Asset[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [locations, setLocations] = useState<Location[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [assetItemQuantities, setAssetItemQuantities] = useState<AssetQuantityOverview[]>([])

  // Load dashboard data
  const loadDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Load all data in parallel
      const [assetsResponse, categoriesData, locationsData, usersData, assetItemQuantities] = await Promise.all([
        assetsService.getAssets({ limit: 1000 }), // Get all assets for statistics
        categoriesService.getCategories(),
        locationsService.getLocations(),
        usersService.getUsers(),
        assetItemService.getAllAssetItemQuantities() // Get actual status/condition data from asset items
      ])

      const allAssets = assetsResponse.assets
      setAssets(allAssets)
      setCategories(categoriesData)
      setLocations(locationsData)
      setUsers(usersData)
      setAssetItemQuantities(assetItemQuantities)

      // Calculate statistics from asset item quantities
      const totalAssets = allAssets.length

      // Calculate total asset items and status counts from asset item data
      let totalAssetItems = 0
      const statusCounts: Record<string, number> = {}

      assetItemQuantities.forEach(assetData => {
        totalAssetItems += assetData.totalItems
        Object.entries(assetData.quantities).forEach(([status, count]) => {
          statusCounts[status] = (statusCounts[status] || 0) + count
        })
      })

      // For condition counts, we'll need to get this from individual asset items
      // For now, we'll use placeholder data since condition is not in the quantities overview
      const conditionCounts: Record<string, number> = {
        excellent: Math.floor(totalAssetItems * 0.4),
        good: Math.floor(totalAssetItems * 0.3),
        fair: Math.floor(totalAssetItems * 0.2),
        poor: Math.floor(totalAssetItems * 0.1)
      }

      const categoryCounts = allAssets.reduce(
        (acc, asset) => {
          const categoryName = asset.category?.name || 'Unknown'
          acc[categoryName] = (acc[categoryName] || 0) + 1
          return acc
        },
        {} as Record<string, number>
      )

      const locationCounts = allAssets.reduce(
        (acc, asset) => {
          const locationName = asset.location?.name || 'Unknown'
          acc[locationName] = (acc[locationName] || 0) + 1
          return acc
        },
        {} as Record<string, number>
      )

      // Create breakdown arrays
      const categoryBreakdown = Object.entries(categoryCounts)
        .map(([name, count]) => ({
          name,
          count,
          percentage: Math.round((count / totalAssets) * 100)
        }))
        .sort((a, b) => b.count - a.count)

      const locationBreakdown = Object.entries(locationCounts)
        .map(([name, count]) => ({
          name,
          count,
          percentage: Math.round((count / totalAssets) * 100)
        }))
        .sort((a, b) => b.count - a.count)

      const statusBreakdown = Object.entries(statusCounts)
        .map(([status, count]) => ({
          status: status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
          count,
          percentage: totalAssetItems > 0 ? Math.round((count / totalAssetItems) * 100) : 0
        }))
        .sort((a, b) => b.count - a.count)

      const conditionBreakdown = Object.entries(conditionCounts)
        .map(([condition, count]) => ({
          condition: condition.charAt(0).toUpperCase() + condition.slice(1),
          count,
          percentage: totalAssetItems > 0 ? Math.round((count / totalAssetItems) * 100) : 0
        }))
        .sort((a, b) => b.count - a.count)

      // Get recent assets (last 5)
      const recentAssets = allAssets
        .sort((a, b) => new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime())
        .slice(0, 5)

      const dashboardStats: DashboardStats = {
        totalAssets: totalAssetItems, // Use total asset items instead of asset types
        inUse: statusCounts.in_use || 0,
        inStock: statusCounts.in_stock || 0,
        maintenance: statusCounts.maintenance || 0,
        retired: statusCounts.retired || 0,
        lost: statusCounts.lost || 0,
        damaged: statusCounts.damaged || 0,
        totalCategories: categoriesData.length,
        totalLocations: locationsData.length,
        totalUsers: usersData.length,
        recentAssets,
        categoryBreakdown,
        locationBreakdown,
        statusBreakdown,
        conditionBreakdown
      }

      setStats(dashboardStats)
    } catch (err: any) {
      console.error('Failed to load dashboard data:', err)
      setError('Failed to load dashboard data. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadDashboardData()
  }, [])

  const StatCard = ({ title, value, icon, color, subtitle }: any) => (
    <Card
      sx={{
        background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)',
        boxShadow: 'none',
        borderColor: 'divider'
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography color='text.secondary' gutterBottom variant='body2' sx={{ fontWeight: 500 }}>
              {title}
            </Typography>
            <Typography variant='h3' component='div' color={color} sx={{ fontWeight: 700, mb: 0.5 }}>
              {value}
            </Typography>
            {subtitle && (
              <Typography variant='body2' color='text.secondary' sx={{ fontSize: '0.875rem' }}>
                {subtitle}
              </Typography>
            )}
          </Box>
          {icon && (
            <Avatar
              sx={{
                bgcolor: `${color}.main`,
                width: 54,
                height: 54
              }}
            >
              {icon}
            </Avatar>
          )}
        </Box>
      </CardContent>
    </Card>
  )

  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress size={60} />
      </Box>
    )
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity='error' sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button variant='contained' startIcon={<RefreshIcon />} onClick={loadDashboardData}>
          Retry
        </Button>
      </Box>
    )
  }

  if (!stats) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity='info'>No data available</Alert>
      </Box>
    )
  }

  return (
    <Box sx={{ p: 4, backgroundColor: 'grey.50', minHeight: '100vh' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant='h4' component='h1' gutterBottom sx={{ fontWeight: 700, color: 'text.primary' }}>
            Asset Management Dashboard
          </Typography>
          <Typography variant='body1' color='text.secondary'>
            Comprehensive overview of your organization's assets
          </Typography>
        </Box>
        <Button
          variant='outlined'
          startIcon={<RefreshIcon />}
          onClick={loadDashboardData}
          sx={{
            textTransform: 'none',
            fontWeight: 600
          }}
        >
          Refresh Data
        </Button>
      </Box>

      {/* Key Statistics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title='Total Assets'
            value={stats.totalAssets.toLocaleString()}
            icon={<InventoryIcon />}
            color='primary'
            subtitle='All registered assets'
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title='Categories'
            value={stats.totalCategories.toLocaleString()}
            icon={<CategoryIcon />}
            color='info'
            subtitle='Asset categories'
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title='Locations'
            value={stats.totalLocations.toLocaleString()}
            icon={<LocationIcon />}
            color='warning'
            subtitle='Storage locations'
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title='Users'
            value={stats.totalUsers.toLocaleString()}
            icon={<PersonIcon />}
            color='success'
            subtitle='System users'
          />
        </Grid>
      </Grid>

      {/* Status Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard title='In Use' value={stats.inUse.toLocaleString()} color='primary' />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard title='Available' value={stats.inStock.toLocaleString()} color='success' />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard title='Maintenance' value={stats.maintenance.toLocaleString()} color='warning' />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard title='Retired' value={stats.retired.toLocaleString()} color='secondary' />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard title='Lost' value={stats.lost.toLocaleString()} color='error' />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard title='Damaged' value={stats.damaged.toLocaleString()} color='error' />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Asset Status Breakdown */}
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              // borderRadius: 3,
              border: '1px solid',
              borderColor: 'divider',
              boxShadow: 'none'
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant='h6' gutterBottom sx={{ fontWeight: 600, mb: 3 }}>
                Asset Status Distribution
              </Typography>
              <Box sx={{ mt: 2 }}>
                {stats.statusBreakdown.map((item, index) => (
                  <Box key={item.status} sx={{ mb: index === stats.statusBreakdown.length - 1 ? 0 : 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant='body2' sx={{ fontWeight: 500 }}>
                        {item.status}
                      </Typography>
                      <Typography variant='body2' sx={{ fontWeight: 600 }}>
                        {item.count} ({item.percentage}%)
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant='determinate'
                      value={item.percentage}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: 'grey.200',
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 4
                        }
                      }}
                      color={
                        item.status.toLowerCase().includes('use')
                          ? 'primary'
                          : item.status.toLowerCase().includes('stock')
                            ? 'success'
                            : item.status.toLowerCase().includes('maintenance')
                              ? 'warning'
                              : 'secondary'
                      }
                    />
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Category Breakdown */}
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              // borderRadius: 3,
              border: '1px solid',
              borderColor: 'divider',
              boxShadow: 'none'
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant='h6' gutterBottom sx={{ fontWeight: 600, mb: 3 }}>
                Assets by Category
              </Typography>
              <TableContainer>
                <Table size='small'>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 600 }}>Category</TableCell>
                      <TableCell align='right' sx={{ fontWeight: 600 }}>
                        Count
                      </TableCell>
                      <TableCell align='right' sx={{ fontWeight: 600 }}>
                        Percentage
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {stats.categoryBreakdown.slice(0, 8).map(category => (
                      <TableRow key={category.name} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <CategoryIcon fontSize='small' color='primary' />
                            {category.name}
                          </Box>
                        </TableCell>
                        <TableCell align='right' sx={{ fontWeight: 500 }}>
                          {category.count}
                        </TableCell>
                        <TableCell align='right'>
                          <Chip
                            label={`${category.percentage}%`}
                            size='small'
                            color={category.percentage > 20 ? 'primary' : 'default'}
                            variant={category.percentage > 20 ? 'filled' : 'outlined'}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Location Breakdown */}
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              // borderRadius: 3,
              border: '1px solid',
              borderColor: 'divider',
              boxShadow: 'none'
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant='h6' gutterBottom sx={{ fontWeight: 600, mb: 3 }}>
                Assets by Location
              </Typography>
              <TableContainer>
                <Table size='small'>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 600 }}>Location</TableCell>
                      <TableCell align='right' sx={{ fontWeight: 600 }}>
                        Count
                      </TableCell>
                      <TableCell align='right' sx={{ fontWeight: 600 }}>
                        Percentage
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {stats.locationBreakdown.slice(0, 8).map(location => (
                      <TableRow key={location.name} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LocationIcon fontSize='small' color='warning' />
                            {location.name}
                          </Box>
                        </TableCell>
                        <TableCell align='right' sx={{ fontWeight: 500 }}>
                          {location.count}
                        </TableCell>
                        <TableCell align='right'>
                          <Chip
                            label={`${location.percentage}%`}
                            size='small'
                            color={location.percentage > 15 ? 'warning' : 'default'}
                            variant={location.percentage > 15 ? 'filled' : 'outlined'}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Condition Breakdown */}
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              // borderRadius: 3,
              border: '1px solid',
              borderColor: 'divider',
              boxShadow: 'none'
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant='h6' gutterBottom sx={{ fontWeight: 600, mb: 3 }}>
                Asset Condition Overview
              </Typography>
              <Box sx={{ mt: 2 }}>
                {stats.conditionBreakdown.map((item, index) => (
                  <Box key={item.condition} sx={{ mb: index === stats.conditionBreakdown.length - 1 ? 0 : 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant='body2' sx={{ fontWeight: 500 }}>
                        {item.condition}
                      </Typography>
                      <Typography variant='body2' sx={{ fontWeight: 600 }}>
                        {item.count} ({item.percentage}%)
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant='determinate'
                      value={item.percentage}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: 'grey.200',
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 4
                        }
                      }}
                      color={
                        item.condition.toLowerCase() === 'excellent'
                          ? 'success'
                          : item.condition.toLowerCase() === 'good'
                            ? 'info'
                            : item.condition.toLowerCase() === 'fair'
                              ? 'warning'
                              : 'error'
                      }
                    />
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Assets */}
        <Grid item xs={12}>
          <Card
            sx={{
              // borderRadius: 3,
              border: '1px solid',
              borderColor: 'divider',
              boxShadow: 'none'
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant='h6' gutterBottom sx={{ fontWeight: 600, mb: 3 }}>
                Recently Added Assets
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 600 }}>Asset Number</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Name</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Category</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Location</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Condition</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Assigned To</TableCell>
                      <TableCell sx={{ fontWeight: 600 }}>Date Added</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {stats.recentAssets.map(asset => (
                      <TableRow key={asset.id} hover>
                        <TableCell>
                          <Typography variant='body2' fontWeight='medium' color='primary.main'>
                            {asset.assetNumber}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant='body2' fontWeight='medium'>
                            {asset.name}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <CategoryIcon fontSize='small' color='primary' />
                            <Typography variant='body2'>{asset.category?.name || 'Unknown'}</Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LocationIcon fontSize='small' color='warning' />
                            <Typography variant='body2'>{asset.location?.name || 'Unknown'}</Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={
                              asset.status
                                ? asset.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
                                : 'Unknown'
                            }
                            color={asset.status ? statusColors[asset.status as keyof typeof statusColors] : 'default'}
                            size='small'
                            variant='filled'
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={
                              asset.condition
                                ? asset.condition.charAt(0).toUpperCase() + asset.condition.slice(1)
                                : 'Unknown'
                            }
                            color={
                              asset.condition
                                ? conditionColors[asset.condition as keyof typeof conditionColors]
                                : 'default'
                            }
                            size='small'
                            variant='outlined'
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <PersonIcon fontSize='small' color='action' />
                            <Typography variant='body2'>{asset.assignedTo?.fullName || 'Unassigned'}</Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant='body2' color='text.secondary'>
                            {asset.createdAt ? new Date(asset.createdAt).toLocaleDateString() : 'Unknown'}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              {stats.recentAssets.length === 0 && (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant='body2' color='text.secondary'>
                    No recent assets found
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}
