'use client'

import { useState } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider
} from '@mui/material'
import {
  Inventory as InventoryIcon,
  Assignment as AssignmentIcon,
  AttachMoney as MoneyIcon,
  CheckCircle as CheckCircleIcon,
  Person as PersonIcon
} from '@mui/icons-material'

// Mock data for development
const mockStats = {
  totalAssets: 1247,
  totalValue: 2847593.45,
  inUse: 892,
  inStock: 245,
  maintenance: 67,
  retired: 43,
  recentAssets: [
    {
      id: '1',
      assetNumber: 'AST-202412-0001',
      name: 'Dell Laptop XPS 13',
      status: 'In Use',
      assignedTo: '<PERSON>',
      value: 1299.99,
      date: '2024-12-01'
    },
    {
      id: '2',
      assetNumber: 'AST-202412-0002',
      name: 'Office Chair Ergonomic',
      status: 'In Stock',
      assignedTo: null,
      value: 299.99,
      date: '2024-12-01'
    },
    {
      id: '3',
      assetNumber: 'AST-202412-0003',
      name: 'HP Printer LaserJet Pro',
      status: 'Maintenance',
      assignedTo: null,
      value: 399.99,
      date: '2024-11-30'
    }
  ],
  categoryBreakdown: [
    { name: 'IT Equipment', count: 567, value: 1245678.90, percentage: 65 },
    { name: 'Furniture', count: 234, value: 456789.12, percentage: 25 },
    { name: 'Vehicles', count: 45, value: 890123.45, percentage: 8 },
    { name: 'Office Supplies', count: 401, value: 254901.98, percentage: 2 }
  ],
  recentActivity: [
    {
      id: '1',
      action: 'Asset Created',
      description: 'Dell Laptop XPS 13 (AST-202412-0001) was created',
      user: 'Admin User',
      timestamp: '2 hours ago'
    },
    {
      id: '2',
      action: 'Asset Assigned',
      description: 'MacBook Pro assigned to Jane Smith',
      user: 'Manager User',
      timestamp: '4 hours ago'
    },
    {
      id: '3',
      action: 'Status Changed',
      description: 'Printer moved to maintenance',
      user: 'IT Support',
      timestamp: '6 hours ago'
    }
  ]
}

const statusColors = {
  'In Use': 'primary',
  'In Stock': 'success',
  'Maintenance': 'warning',
  'Retired': 'secondary'
} as const

export default function AssetDashboard() {
  const [stats] = useState(mockStats)

  const StatCard = ({ title, value, icon, color, subtitle }: any) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography color="text.secondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" color={color}>
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
          <Avatar sx={{ bgcolor: `${color}.main`, width: 56, height: 56 }}>
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  )

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Asset Management Dashboard
      </Typography>

      {/* Key Statistics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Assets"
            value={stats.totalAssets.toLocaleString()}
            icon={<InventoryIcon />}
            color="primary"
            subtitle="All registered assets"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Value"
            value={`$${stats.totalValue.toLocaleString()}`}
            icon={<MoneyIcon />}
            color="success"
            subtitle="Combined asset value"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="In Use"
            value={stats.inUse.toLocaleString()}
            icon={<AssignmentIcon />}
            color="info"
            subtitle="Currently assigned"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Available"
            value={stats.inStock.toLocaleString()}
            icon={<CheckCircleIcon />}
            color="success"
            subtitle="Ready for assignment"
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Asset Status Breakdown */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Asset Status Overview
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">In Use</Typography>
                  <Typography variant="body2">{stats.inUse}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats.inUse / stats.totalAssets) * 100} 
                  sx={{ mb: 2, height: 8, borderRadius: 4 }}
                  color="primary"
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">In Stock</Typography>
                  <Typography variant="body2">{stats.inStock}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats.inStock / stats.totalAssets) * 100} 
                  sx={{ mb: 2, height: 8, borderRadius: 4 }}
                  color="success"
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Maintenance</Typography>
                  <Typography variant="body2">{stats.maintenance}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats.maintenance / stats.totalAssets) * 100} 
                  sx={{ mb: 2, height: 8, borderRadius: 4 }}
                  color="warning"
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Retired</Typography>
                  <Typography variant="body2">{stats.retired}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats.retired / stats.totalAssets) * 100} 
                  sx={{ height: 8, borderRadius: 4 }}
                  color="secondary"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Category Breakdown */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Assets by Category
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Category</TableCell>
                      <TableCell align="right">Count</TableCell>
                      <TableCell align="right">Value</TableCell>
                      <TableCell align="right">%</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {stats.categoryBreakdown.map((category) => (
                      <TableRow key={category.name}>
                        <TableCell>{category.name}</TableCell>
                        <TableCell align="right">{category.count}</TableCell>
                        <TableCell align="right">${category.value.toLocaleString()}</TableCell>
                        <TableCell align="right">{category.percentage}%</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Assets */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Assets
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Asset Number</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Assigned To</TableCell>
                      <TableCell align="right">Value</TableCell>
                      <TableCell>Date</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {stats.recentAssets.map((asset) => (
                      <TableRow key={asset.id} hover>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {asset.assetNumber}
                          </Typography>
                        </TableCell>
                        <TableCell>{asset.name}</TableCell>
                        <TableCell>
                          <Chip
                            label={asset.status}
                            color={statusColors[asset.status as keyof typeof statusColors]}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{asset.assignedTo || 'Unassigned'}</TableCell>
                        <TableCell align="right">${asset.value.toLocaleString()}</TableCell>
                        <TableCell>{asset.date}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Activity
              </Typography>
              <List>
                {stats.recentActivity.map((activity, index) => (
                  <Box key={activity.id}>
                    <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
                          <PersonIcon fontSize="small" />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography variant="body2" fontWeight="medium">
                            {activity.action}
                          </Typography>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {activity.description}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              by {activity.user} • {activity.timestamp}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < stats.recentActivity.length - 1 && <Divider />}
                  </Box>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}
