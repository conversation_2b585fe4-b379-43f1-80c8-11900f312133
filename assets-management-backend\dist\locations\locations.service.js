"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const location_entity_1 = require("../entities/location.entity");
let LocationsService = class LocationsService {
    locationRepository;
    constructor(locationRepository) {
        this.locationRepository = locationRepository;
    }
    async create(createLocationDto) {
        if (createLocationDto.parentId) {
            const parentLocation = await this.locationRepository.findOne({
                where: { id: createLocationDto.parentId },
            });
            if (!parentLocation) {
                throw new common_1.NotFoundException('Parent location not found');
            }
        }
        const location = this.locationRepository.create(createLocationDto);
        return this.locationRepository.save(location);
    }
    async findAll() {
        return this.locationRepository.find({
            relations: ['parent', 'children'],
            order: { name: 'ASC' },
        });
    }
    async findAllActive() {
        return this.locationRepository.find({
            where: { isActive: true },
            relations: ['parent', 'children'],
            order: { name: 'ASC' },
        });
    }
    async findOne(id) {
        const location = await this.locationRepository.findOne({
            where: { id },
            relations: ['parent', 'children', 'assets'],
        });
        if (!location) {
            throw new common_1.NotFoundException('Location not found');
        }
        return location;
    }
    async update(id, updateLocationDto) {
        const location = await this.locationRepository.findOne({ where: { id } });
        if (!location) {
            throw new common_1.NotFoundException('Location not found');
        }
        if (updateLocationDto.parentId) {
            if (updateLocationDto.parentId === id) {
                throw new common_1.BadRequestException('Location cannot be its own parent');
            }
            const parentLocation = await this.locationRepository.findOne({
                where: { id: updateLocationDto.parentId },
            });
            if (!parentLocation) {
                throw new common_1.NotFoundException('Parent location not found');
            }
            const isCircular = await this.checkCircularReference(id, updateLocationDto.parentId);
            if (isCircular) {
                throw new common_1.BadRequestException('Circular reference detected');
            }
        }
        await this.locationRepository.update(id, updateLocationDto);
        return this.findOne(id);
    }
    async remove(id) {
        const location = await this.locationRepository.findOne({
            where: { id },
            relations: ['children', 'assets'],
        });
        if (!location) {
            throw new common_1.NotFoundException('Location not found');
        }
        if (location.children && location.children.length > 0) {
            throw new common_1.BadRequestException('Cannot delete location with sub-locations');
        }
        if (location.assets && location.assets.length > 0) {
            throw new common_1.BadRequestException('Cannot delete location with assets');
        }
        await this.locationRepository.remove(location);
    }
    async getHierarchy() {
        const allLocations = await this.locationRepository.find({
            order: { name: 'ASC' },
        });
        return this.buildLocationHierarchy(allLocations);
    }
    buildLocationHierarchy(allLocations) {
        const locationMap = new Map();
        const rootLocations = [];
        allLocations.forEach((location) => {
            locationMap.set(location.id, { ...location, children: [] });
        });
        allLocations.forEach((location) => {
            const locationWithChildren = locationMap.get(location.id);
            if (location.parentId) {
                const parent = locationMap.get(location.parentId);
                if (parent) {
                    parent.children = parent.children || [];
                    parent.children.push(locationWithChildren);
                }
            }
            else {
                rootLocations.push(locationWithChildren);
            }
        });
        this.sortLocationTree(rootLocations);
        return rootLocations;
    }
    sortLocationTree(locations) {
        locations.sort((a, b) => a.name.localeCompare(b.name));
        locations.forEach((location) => {
            if (location.children && location.children.length > 0) {
                this.sortLocationTree(location.children);
            }
        });
    }
    async buildLocationTree(locations) {
        for (const location of locations) {
            if (location.children && location.children.length > 0) {
                location.children = await this.buildLocationTree(location.children);
            }
        }
        return locations;
    }
    async checkCircularReference(locationId, parentId) {
        let currentParentId = parentId;
        while (currentParentId) {
            if (currentParentId === locationId) {
                return true;
            }
            const parent = await this.locationRepository.findOne({
                where: { id: currentParentId },
                relations: ['parent'],
            });
            currentParentId = parent?.parentId;
        }
        return false;
    }
};
exports.LocationsService = LocationsService;
exports.LocationsService = LocationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(location_entity_1.Location)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], LocationsService);
//# sourceMappingURL=locations.service.js.map