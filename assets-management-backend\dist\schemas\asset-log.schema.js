"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetLogSchema = exports.AssetLog = exports.LogAction = void 0;
const mongoose_1 = require("@nestjs/mongoose");
var LogAction;
(function (LogAction) {
    LogAction["CREATED"] = "created";
    LogAction["UPDATED"] = "updated";
    LogAction["DELETED"] = "deleted";
    LogAction["ASSIGNED"] = "assigned";
    LogAction["UNASSIGNED"] = "unassigned";
    LogAction["STATUS_CHANGED"] = "status_changed";
    LogAction["LOCATION_CHANGED"] = "location_changed";
    LogAction["MAINTENANCE"] = "maintenance";
    LogAction["RETIRED"] = "retired";
})(LogAction || (exports.LogAction = LogAction = {}));
let AssetLog = class AssetLog {
    assetId;
    assetNumber;
    action;
    performedBy;
    performedByName;
    previousData;
    newData;
    description;
    ipAddress;
    userAgent;
    timestamp;
};
exports.AssetLog = AssetLog;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], AssetLog.prototype, "assetId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], AssetLog.prototype, "assetNumber", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, enum: LogAction }),
    __metadata("design:type", String)
], AssetLog.prototype, "action", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], AssetLog.prototype, "performedBy", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], AssetLog.prototype, "performedByName", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object }),
    __metadata("design:type", Object)
], AssetLog.prototype, "previousData", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object }),
    __metadata("design:type", Object)
], AssetLog.prototype, "newData", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], AssetLog.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], AssetLog.prototype, "ipAddress", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], AssetLog.prototype, "userAgent", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: Date.now }),
    __metadata("design:type", Date)
], AssetLog.prototype, "timestamp", void 0);
exports.AssetLog = AssetLog = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], AssetLog);
exports.AssetLogSchema = mongoose_1.SchemaFactory.createForClass(AssetLog);
//# sourceMappingURL=asset-log.schema.js.map