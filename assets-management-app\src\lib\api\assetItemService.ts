import { apiClient } from './client'
import { AssetStatus, AssetCondition } from '@/types/api'

export interface AssetItem {
  id: string
  assetId: string
  assetNumber: string
  status: AssetStatus
  condition: AssetCondition
  serialNumber?: string
  purchaseDate?: string
  warrantyExpiry?: string
  notes?: string
  imageUrl?: string
  assignedToId?: string
  assignedDate?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  asset: {
    id: string
    name: string
    assetNumber: string
    model?: string
    manufacturer?: string
  }
  assignedTo?: {
    id: string
    fullName: string
  }
  lastModifiedBy?: {
    id: string
    fullName: string
  }
}

export interface CreateAssetItemRequest {
  quantity: number
}

export interface UpdateAssetItemRequest {
  status?: AssetStatus
  condition?: AssetCondition
  serialNumber?: string
  purchaseDate?: string
  warrantyExpiry?: string
  notes?: string
  imageUrl?: string
  assignedToId?: string
}

export interface TransferAssetItemRequest {
  assetItemId: string
  fromStatus: AssetStatus
  toStatus: AssetStatus
  reason?: string
  assignedToId?: string
}

export interface AssetItemQuantities {
  [AssetStatus.IN_STOCK]: number
  [AssetStatus.IN_USE]: number
  [AssetStatus.MAINTENANCE]: number
  [AssetStatus.RETIRED]: number
  [AssetStatus.LOST]: number
  [AssetStatus.DAMAGED]: number
}

export interface AssetQuantityOverview {
  assetId: string
  assetName: string
  quantities: AssetItemQuantities
  totalItems: number
}

export const assetItemService = {
  // Create multiple asset items for an asset
  async createAssetItems(assetId: string, data: CreateAssetItemRequest): Promise<AssetItem[]> {
    const response = await apiClient.post(`/assets/${assetId}/items`, data)
    return response
  },

  // Get all asset items for a specific asset
  async getAssetItems(assetId: string): Promise<AssetItem[]> {
    console.log('AssetItemService: Getting asset items for asset ID:', assetId)
    const response = await apiClient.get(`/assets/${assetId}/items`)
    console.log('AssetItemService: Response received:', response)
    return response
  },

  // Get asset item by ID
  async getAssetItem(itemId: string): Promise<AssetItem> {
    const response = await apiClient.get(`/assets/items/${itemId}`)
    return response
  },

  // Update asset item
  async updateAssetItem(itemId: string, data: UpdateAssetItemRequest): Promise<AssetItem> {
    const response = await apiClient.patch(`/assets/items/${itemId}`, data)
    return response
  },

  // Transfer asset item status
  async transferAssetItem(data: TransferAssetItemRequest): Promise<AssetItem> {
    const response = await apiClient.post('/assets/items/transfer', data)
    return response
  },

  // Get asset item quantities by status for a specific asset
  async getAssetItemQuantities(assetId: string): Promise<AssetItemQuantities> {
    const response = await apiClient.get(`/assets/${assetId}/items/quantities`)
    return response
  },

  // Get all asset item quantities overview
  async getAllAssetItemQuantities(): Promise<AssetQuantityOverview[]> {
    const response = await apiClient.get('/assets/items/quantities/all')
    return response
  },

  // Get asset item by asset number (Authenticated)
  async getAssetItemByAssetNumber(assetNumber: string): Promise<any> {
    const response = await apiClient.get(`/assets/by-asset-number/${assetNumber}`)
    return response
  },

  // Delete asset item
  async deleteAssetItem(itemId: string): Promise<void> {
    await apiClient.delete(`/assets/items/${itemId}`)
  },

  // Upload image for asset item
  async uploadAssetItemImage(itemId: string, file: File): Promise<{ imageUrl: string }> {
    const formData = new FormData()
    formData.append('file', file)

    const token = localStorage.getItem('token')
    const headers: Record<string, string> = {}
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/assets/items/${itemId}/upload-image`, {
      method: 'POST',
      headers,
      body: formData
    })

    if (!response.ok) {
      throw new Error('Failed to upload image')
    }

    return response.json()
  }
}
