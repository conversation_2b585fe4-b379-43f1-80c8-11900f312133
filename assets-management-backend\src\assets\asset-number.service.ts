import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Asset } from '../entities/asset.entity';

@Injectable()
export class AssetNumberService {
  constructor(
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
  ) {}

  /**
   * Generate a unique asset number in format AST-YYYYMM-XXXX
   * Where:
   * - AST is the prefix
   * - YYYY is the current year
   * - MM is the current month
   * - XXXX is a sequential number starting from 0001
   */
  async generateAssetNumber(): Promise<string> {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const prefix = `AST-${year}${month}`;

    // Find the highest existing asset number for this month
    const existingAssets = await this.assetRepository
      .createQueryBuilder('asset')
      .where('asset.assetNumber LIKE :prefix', { prefix: `${prefix}-%` })
      .orderBy('asset.assetNumber', 'DESC')
      .limit(1)
      .getOne();

    let nextSequence = 1;

    if (existingAssets) {
      // Extract the sequence number from the existing asset number
      const parts = existingAssets.assetNumber.split('-');
      if (parts.length === 3) {
        const lastSequence = parseInt(parts[2], 10);
        if (!isNaN(lastSequence)) {
          nextSequence = lastSequence + 1;
        }
      }
    }

    // Format the sequence number with leading zeros
    const sequenceStr = String(nextSequence).padStart(4, '0');
    const assetNumber = `${prefix}-${sequenceStr}`;

    // Double-check uniqueness (in case of race conditions)
    const exists = await this.assetRepository.findOne({
      where: { assetNumber },
    });

    if (exists) {
      // If somehow the number exists, try the next one
      return this.generateAssetNumber();
    }

    return assetNumber;
  }

  /**
   * Validate asset number format
   */
  validateAssetNumberFormat(assetNumber: string): boolean {
    const pattern = /^AST-\d{6}-\d{4}$/;
    return pattern.test(assetNumber);
  }

  /**
   * Check if asset number is unique
   */
  async isAssetNumberUnique(assetNumber: string, excludeId?: string): Promise<boolean> {
    const query = this.assetRepository
      .createQueryBuilder('asset')
      .where('asset.assetNumber = :assetNumber', { assetNumber });

    if (excludeId) {
      query.andWhere('asset.id != :excludeId', { excludeId });
    }

    const existing = await query.getOne();
    return !existing;
  }

  /**
   * Parse asset number to extract year, month, and sequence
   */
  parseAssetNumber(assetNumber: string): {
    year: number;
    month: number;
    sequence: number;
  } | null {
    if (!this.validateAssetNumberFormat(assetNumber)) {
      return null;
    }

    const parts = assetNumber.split('-');
    const yearMonth = parts[1];
    const sequence = parts[2];

    const year = parseInt(yearMonth.substring(0, 4), 10);
    const month = parseInt(yearMonth.substring(4, 6), 10);
    const seq = parseInt(sequence, 10);

    return { year, month, sequence: seq };
  }

  /**
   * Get asset statistics by month/year
   */
  async getAssetStatsByPeriod(year: number, month?: number): Promise<{
    totalAssets: number;
    assetNumbers: string[];
  }> {
    const yearStr = year.toString();
    const monthStr = month ? String(month).padStart(2, '0') : '';
    const prefix = `AST-${yearStr}${monthStr}`;

    const query = this.assetRepository
      .createQueryBuilder('asset')
      .select(['asset.assetNumber'])
      .where('asset.assetNumber LIKE :prefix', { prefix: `${prefix}%` })
      .orderBy('asset.assetNumber', 'ASC');

    const assets = await query.getMany();

    return {
      totalAssets: assets.length,
      assetNumbers: assets.map(asset => asset.assetNumber),
    };
  }
}
