import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Asset } from '../entities/asset.entity';
import { Category } from '../entities/category.entity';
import { Location } from '../entities/location.entity';

@Injectable()
export class AssetNumberService {
  constructor(
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,
    @InjectRepository(Location)
    private locationRepository: Repository<Location>,
  ) {}

  /**
   * Generate a unique asset number in format {LocationPrefix}-{CategoryPrefix}-{UniqueNumber}
   * Where:
   * - LocationPrefix is the parent location code (e.g., "INV")
   * - CategoryPrefix is the parent category code (e.g., "CLI")
   * - UniqueNumber is a sequential number starting from 0001
   * Example: INV-CLI-0001
   */
  async generateAssetNumber(
    categoryId: string,
    locationId: string,
  ): Promise<string> {
    // Get the root parent category code
    const categoryPrefix = await this.getRootCategoryCode(categoryId);

    // Get the root parent location code
    const locationPrefix = await this.getRootLocationCode(locationId);

    // Create the base prefix
    const basePrefix = `${locationPrefix}-${categoryPrefix}`;

    // Find the highest existing asset number with this prefix
    const existingAssets = await this.assetRepository
      .createQueryBuilder('asset')
      .where('asset.assetNumber LIKE :prefix', { prefix: `${basePrefix}-%` })
      .orderBy('asset.assetNumber', 'DESC')
      .limit(1)
      .getOne();

    let nextSequence = 1;

    if (existingAssets) {
      // Extract the sequence number from the existing asset number
      const parts = existingAssets.assetNumber.split('-');
      if (parts.length === 3) {
        const lastSequence = parseInt(parts[2], 10);
        if (!isNaN(lastSequence)) {
          nextSequence = lastSequence + 1;
        }
      }
    }

    // Format the sequence number with leading zeros
    const sequenceStr = String(nextSequence).padStart(4, '0');
    const assetNumber = `${basePrefix}-${sequenceStr}`;

    // Double-check uniqueness (in case of race conditions)
    const exists = await this.assetRepository.findOne({
      where: { assetNumber },
    });

    if (exists) {
      // If somehow the number exists, try the next one
      return this.generateAssetNumber(categoryId, locationId);
    }

    return assetNumber;
  }

  /**
   * Get the root parent category code
   */
  private async getRootCategoryCode(categoryId: string): Promise<string> {
    const category = await this.categoryRepository.findOne({
      where: { id: categoryId },
      relations: ['parent'],
    });

    if (!category) {
      return 'UNK'; // Unknown category
    }

    // If this category has a parent, recursively get the root parent
    if (category.parent) {
      return this.getRootCategoryCode(category.parent.id);
    }

    // This is the root category, return its code or generate one from name
    return category.code || this.generateCodeFromName(category.name);
  }

  /**
   * Get the root parent location code
   */
  private async getRootLocationCode(locationId: string): Promise<string> {
    const location = await this.locationRepository.findOne({
      where: { id: locationId },
      relations: ['parent'],
    });

    if (!location) {
      return 'UNK'; // Unknown location
    }

    // If this location has a parent, recursively get the root parent
    if (location.parent) {
      return this.getRootLocationCode(location.parent.id);
    }

    // This is the root location, return its code or generate one from name
    return location.code || this.generateCodeFromName(location.name);
  }

  /**
   * Generate a code from name (first 3 uppercase letters)
   */
  private generateCodeFromName(name: string): string {
    return name
      .replace(/[^a-zA-Z]/g, '') // Remove non-letters
      .toUpperCase()
      .substring(0, 3)
      .padEnd(3, 'X'); // Pad with X if less than 3 characters
  }

  /**
   * Validate asset number format
   */
  validateAssetNumberFormat(assetNumber: string): boolean {
    const pattern = /^[A-Z]{3}-[A-Z]{3}-\d{4}$/;
    return pattern.test(assetNumber);
  }

  /**
   * Check if asset number is unique
   */
  async isAssetNumberUnique(
    assetNumber: string,
    excludeId?: string,
  ): Promise<boolean> {
    const query = this.assetRepository
      .createQueryBuilder('asset')
      .where('asset.assetNumber = :assetNumber', { assetNumber });

    if (excludeId) {
      query.andWhere('asset.id != :excludeId', { excludeId });
    }

    const existing = await query.getOne();
    return !existing;
  }

  /**
   * Parse asset number to extract year, month, and sequence
   */
  parseAssetNumber(assetNumber: string): {
    year: number;
    month: number;
    sequence: number;
  } | null {
    if (!this.validateAssetNumberFormat(assetNumber)) {
      return null;
    }

    const parts = assetNumber.split('-');
    const yearMonth = parts[1];
    const sequence = parts[2];

    const year = parseInt(yearMonth.substring(0, 4), 10);
    const month = parseInt(yearMonth.substring(4, 6), 10);
    const seq = parseInt(sequence, 10);

    return { year, month, sequence: seq };
  }

  /**
   * Get asset statistics by month/year
   */
  async getAssetStatsByPeriod(
    year: number,
    month?: number,
  ): Promise<{
    totalAssets: number;
    assetNumbers: string[];
  }> {
    const yearStr = year.toString();
    const monthStr = month ? String(month).padStart(2, '0') : '';
    const prefix = `AST-${yearStr}${monthStr}`;

    const query = this.assetRepository
      .createQueryBuilder('asset')
      .select(['asset.assetNumber'])
      .where('asset.assetNumber LIKE :prefix', { prefix: `${prefix}%` })
      .orderBy('asset.assetNumber', 'ASC');

    const assets = await query.getMany();

    return {
      totalAssets: assets.length,
      assetNumbers: assets.map((asset) => asset.assetNumber),
    };
  }
}
