"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/layout",{

/***/ "(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js":
/*!*******************************************************!*\
  !*** ./node_modules/@mui/material/Divider/Divider.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! prop-types */ \"(app-pages-browser)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _mui_utils_composeClasses__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/utils/composeClasses */ \"(app-pages-browser)/./node_modules/@mui/utils/esm/composeClasses/composeClasses.js\");\n/* harmony import */ var _mui_system_colorManipulator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/system/colorManipulator */ \"(app-pages-browser)/./node_modules/@mui/system/colorManipulator.js\");\n/* harmony import */ var _styles_styled__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../styles/styled */ \"(app-pages-browser)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _DefaultPropsProvider__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../DefaultPropsProvider */ \"(app-pages-browser)/./node_modules/@mui/material/DefaultPropsProvider/DefaultPropsProvider.js\");\n/* harmony import */ var _dividerClasses__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./dividerClasses */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/dividerClasses.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nconst _excluded = [\n    \"absolute\",\n    \"children\",\n    \"className\",\n    \"component\",\n    \"flexItem\",\n    \"light\",\n    \"orientation\",\n    \"role\",\n    \"textAlign\",\n    \"variant\"\n];\n\n\n\n\n\n\n\n\n\nconst useUtilityClasses = (ownerState)=>{\n    const { absolute, children, classes, flexItem, light, orientation, textAlign, variant } = ownerState;\n    const slots = {\n        root: [\n            \"root\",\n            absolute && \"absolute\",\n            variant,\n            light && \"light\",\n            orientation === \"vertical\" && \"vertical\",\n            flexItem && \"flexItem\",\n            children && \"withChildren\",\n            children && orientation === \"vertical\" && \"withChildrenVertical\",\n            textAlign === \"right\" && orientation !== \"vertical\" && \"textAlignRight\",\n            textAlign === \"left\" && orientation !== \"vertical\" && \"textAlignLeft\"\n        ],\n        wrapper: [\n            \"wrapper\",\n            orientation === \"vertical\" && \"wrapperVertical\"\n        ]\n    };\n    return (0,_mui_utils_composeClasses__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(slots, _dividerClasses__WEBPACK_IMPORTED_MODULE_6__.getDividerUtilityClass, classes);\n};\nconst DividerRoot = (0,_styles_styled__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(\"div\", {\n    name: \"MuiDivider\",\n    slot: \"Root\",\n    overridesResolver: (props, styles)=>{\n        const { ownerState } = props;\n        return [\n            styles.root,\n            ownerState.absolute && styles.absolute,\n            styles[ownerState.variant],\n            ownerState.light && styles.light,\n            ownerState.orientation === \"vertical\" && styles.vertical,\n            ownerState.flexItem && styles.flexItem,\n            ownerState.children && styles.withChildren,\n            ownerState.children && ownerState.orientation === \"vertical\" && styles.withChildrenVertical,\n            ownerState.textAlign === \"right\" && ownerState.orientation !== \"vertical\" && styles.textAlignRight,\n            ownerState.textAlign === \"left\" && ownerState.orientation !== \"vertical\" && styles.textAlignLeft\n        ];\n    }\n})((param)=>{\n    let { theme, ownerState } = param;\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        margin: 0,\n        // Reset browser default style.\n        flexShrink: 0,\n        borderWidth: 0,\n        borderStyle: \"solid\",\n        borderColor: (theme.vars || theme).palette.divider,\n        borderBottomWidth: \"thin\"\n    }, ownerState.absolute && {\n        position: \"absolute\",\n        bottom: 0,\n        left: 0,\n        width: \"100%\"\n    }, ownerState.light && {\n        borderColor: theme.vars ? \"rgba(\".concat(theme.vars.palette.dividerChannel, \" / 0.08)\") : (0,_mui_system_colorManipulator__WEBPACK_IMPORTED_MODULE_8__.alpha)(theme.palette.divider, 0.08)\n    }, ownerState.variant === \"inset\" && {\n        marginLeft: 72\n    }, ownerState.variant === \"middle\" && ownerState.orientation === \"horizontal\" && {\n        marginLeft: theme.spacing(2),\n        marginRight: theme.spacing(2)\n    }, ownerState.variant === \"middle\" && ownerState.orientation === \"vertical\" && {\n        marginTop: theme.spacing(1),\n        marginBottom: theme.spacing(1)\n    }, ownerState.orientation === \"vertical\" && {\n        height: \"100%\",\n        borderBottomWidth: 0,\n        borderRightWidth: \"thin\"\n    }, ownerState.flexItem && {\n        alignSelf: \"stretch\",\n        height: \"auto\"\n    });\n}, (param)=>{\n    let { ownerState } = param;\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, ownerState.children && {\n        display: \"flex\",\n        whiteSpace: \"nowrap\",\n        textAlign: \"center\",\n        border: 0,\n        borderTopStyle: \"solid\",\n        borderLeftStyle: \"solid\",\n        \"&::before, &::after\": {\n            content: '\"\"',\n            alignSelf: \"center\"\n        }\n    });\n}, (param)=>{\n    let { theme, ownerState } = param;\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, ownerState.children && ownerState.orientation !== \"vertical\" && {\n        \"&::before, &::after\": {\n            width: \"100%\",\n            borderTop: \"thin solid \".concat((theme.vars || theme).palette.divider),\n            borderTopStyle: \"inherit\"\n        }\n    });\n}, (param)=>{\n    let { theme, ownerState } = param;\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, ownerState.children && ownerState.orientation === \"vertical\" && {\n        flexDirection: \"column\",\n        \"&::before, &::after\": {\n            height: \"100%\",\n            borderLeft: \"thin solid \".concat((theme.vars || theme).palette.divider),\n            borderLeftStyle: \"inherit\"\n        }\n    });\n}, (param)=>{\n    let { ownerState } = param;\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, ownerState.textAlign === \"right\" && ownerState.orientation !== \"vertical\" && {\n        \"&::before\": {\n            width: \"90%\"\n        },\n        \"&::after\": {\n            width: \"10%\"\n        }\n    }, ownerState.textAlign === \"left\" && ownerState.orientation !== \"vertical\" && {\n        \"&::before\": {\n            width: \"10%\"\n        },\n        \"&::after\": {\n            width: \"90%\"\n        }\n    });\n});\nconst DividerWrapper = (0,_styles_styled__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(\"span\", {\n    name: \"MuiDivider\",\n    slot: \"Wrapper\",\n    overridesResolver: (props, styles)=>{\n        const { ownerState } = props;\n        return [\n            styles.wrapper,\n            ownerState.orientation === \"vertical\" && styles.wrapperVertical\n        ];\n    }\n})((param)=>{\n    let { theme, ownerState } = param;\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        display: \"inline-block\",\n        paddingLeft: \"calc(\".concat(theme.spacing(1), \" * 1.2)\"),\n        paddingRight: \"calc(\".concat(theme.spacing(1), \" * 1.2)\")\n    }, ownerState.orientation === \"vertical\" && {\n        paddingTop: \"calc(\".concat(theme.spacing(1), \" * 1.2)\"),\n        paddingBottom: \"calc(\".concat(theme.spacing(1), \" * 1.2)\")\n    });\n});\nconst Divider = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(_c = _s(function Divider(inProps, ref) {\n    _s();\n    const props = (0,_DefaultPropsProvider__WEBPACK_IMPORTED_MODULE_9__.useDefaultProps)({\n        props: inProps,\n        name: \"MuiDivider\"\n    });\n    const { absolute = false, children, className, component = children ? \"div\" : \"hr\", flexItem = false, light = false, orientation = \"horizontal\", role = component !== \"hr\" ? \"separator\" : undefined, textAlign = \"center\", variant = \"fullWidth\" } = props, other = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(props, _excluded);\n    const ownerState = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, props, {\n        absolute,\n        component,\n        flexItem,\n        light,\n        orientation,\n        role,\n        textAlign,\n        variant\n    });\n    const classes = useUtilityClasses(ownerState);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(DividerRoot, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        as: component,\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(classes.root, className),\n        role: role,\n        ref: ref,\n        ownerState: ownerState\n    }, other, {\n        children: children ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(DividerWrapper, {\n            className: classes.wrapper,\n            ownerState: ownerState,\n            children: children\n        }) : null\n    }));\n}, \"CAIm94WeTMtiWHBIKb3BCV2u1bk=\", false, function() {\n    return [\n        _DefaultPropsProvider__WEBPACK_IMPORTED_MODULE_9__.useDefaultProps,\n        useUtilityClasses\n    ];\n})), \"CAIm94WeTMtiWHBIKb3BCV2u1bk=\", false, function() {\n    return [\n        _DefaultPropsProvider__WEBPACK_IMPORTED_MODULE_9__.useDefaultProps,\n        useUtilityClasses\n    ];\n});\n_c1 = Divider;\n/**\n * The following flag is used to ensure that this component isn't tabbable i.e.\n * does not get highlight/focus inside of MUI List.\n */ Divider.muiSkipListHighlight = true;\n true ? Divider.propTypes = {\n    // ┌────────────────────────────── Warning ──────────────────────────────┐\n    // │ These PropTypes are generated from the TypeScript type definitions. │\n    // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n    // └─────────────────────────────────────────────────────────────────────┘\n    /**\n   * Absolutely position the element.\n   * @default false\n   */ absolute: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().bool),\n    /**\n   * The content of the component.\n   */ children: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().node),\n    /**\n   * Override or extend the styles applied to the component.\n   */ classes: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().object),\n    /**\n   * @ignore\n   */ className: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().string),\n    /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */ component: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().elementType),\n    /**\n   * If `true`, a vertical divider will have the correct height when used in flex container.\n   * (By default, a vertical divider will have a calculated height of `0px` if it is the child of a flex container.)\n   * @default false\n   */ flexItem: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().bool),\n    /**\n   * If `true`, the divider will have a lighter color.\n   * @default false\n   * @deprecated Use <Divider sx={{ opacity: 0.6 }} /> (or any opacity or color) instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)\n   */ light: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().bool),\n    /**\n   * The component orientation.\n   * @default 'horizontal'\n   */ orientation: prop_types__WEBPACK_IMPORTED_MODULE_10___default().oneOf([\n        \"horizontal\",\n        \"vertical\"\n    ]),\n    /**\n   * @ignore\n   */ role: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().string),\n    /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */ sx: prop_types__WEBPACK_IMPORTED_MODULE_10___default().oneOfType([\n        prop_types__WEBPACK_IMPORTED_MODULE_10___default().arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_10___default().oneOfType([\n            (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func),\n            (prop_types__WEBPACK_IMPORTED_MODULE_10___default().object),\n            (prop_types__WEBPACK_IMPORTED_MODULE_10___default().bool)\n        ])),\n        (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func),\n        (prop_types__WEBPACK_IMPORTED_MODULE_10___default().object)\n    ]),\n    /**\n   * The text alignment.\n   * @default 'center'\n   */ textAlign: prop_types__WEBPACK_IMPORTED_MODULE_10___default().oneOf([\n        \"center\",\n        \"left\",\n        \"right\"\n    ]),\n    /**\n   * The variant to use.\n   * @default 'fullWidth'\n   */ variant: prop_types__WEBPACK_IMPORTED_MODULE_10___default().oneOfType([\n        prop_types__WEBPACK_IMPORTED_MODULE_10___default().oneOf([\n            \"fullWidth\",\n            \"inset\",\n            \"middle\"\n        ]),\n        (prop_types__WEBPACK_IMPORTED_MODULE_10___default().string)\n    ])\n} : 0;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Divider);\nvar _c, _c1;\n$RefreshReg$(_c, \"Divider$React.forwardRef\");\n$RefreshReg$(_c1, \"Divider\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@mui/material/Divider/dividerClasses.js":
/*!**************************************************************!*\
  !*** ./node_modules/@mui/material/Divider/dividerClasses.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDividerUtilityClass: function() { return /* binding */ getDividerUtilityClass; }\n/* harmony export */ });\n/* harmony import */ var _mui_utils_generateUtilityClasses__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @mui/utils/generateUtilityClasses */ \"(app-pages-browser)/./node_modules/@mui/utils/esm/generateUtilityClasses/generateUtilityClasses.js\");\n/* harmony import */ var _mui_utils_generateUtilityClass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mui/utils/generateUtilityClass */ \"(app-pages-browser)/./node_modules/@mui/utils/esm/generateUtilityClass/generateUtilityClass.js\");\n\n\nfunction getDividerUtilityClass(slot) {\n    return (0,_mui_utils_generateUtilityClass__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"MuiDivider\", slot);\n}\nconst dividerClasses = (0,_mui_utils_generateUtilityClasses__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"MuiDivider\", [\n    \"root\",\n    \"absolute\",\n    \"fullWidth\",\n    \"inset\",\n    \"middle\",\n    \"flexItem\",\n    \"light\",\n    \"vertical\",\n    \"withChildren\",\n    \"withChildrenVertical\",\n    \"textAlignRight\",\n    \"textAlignLeft\",\n    \"wrapper\",\n    \"wrapperVertical\"\n]);\n/* harmony default export */ __webpack_exports__[\"default\"] = (dividerClasses);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AbXVpL21hdGVyaWFsL0RpdmlkZXIvZGl2aWRlckNsYXNzZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVFO0FBQ0o7QUFDNUQsU0FBU0UsdUJBQXVCQyxJQUFJO0lBQ3pDLE9BQU9GLDJFQUFvQkEsQ0FBQyxjQUFjRTtBQUM1QztBQUNBLE1BQU1DLGlCQUFpQkosNkVBQXNCQSxDQUFDLGNBQWM7SUFBQztJQUFRO0lBQVk7SUFBYTtJQUFTO0lBQVU7SUFBWTtJQUFTO0lBQVk7SUFBZ0I7SUFBd0I7SUFBa0I7SUFBaUI7SUFBVztDQUFrQjtBQUMxUCwrREFBZUksY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQG11aS9tYXRlcmlhbC9EaXZpZGVyL2RpdmlkZXJDbGFzc2VzLmpzPzFmMjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGdlbmVyYXRlVXRpbGl0eUNsYXNzZXMgZnJvbSAnQG11aS91dGlscy9nZW5lcmF0ZVV0aWxpdHlDbGFzc2VzJztcbmltcG9ydCBnZW5lcmF0ZVV0aWxpdHlDbGFzcyBmcm9tICdAbXVpL3V0aWxzL2dlbmVyYXRlVXRpbGl0eUNsYXNzJztcbmV4cG9ydCBmdW5jdGlvbiBnZXREaXZpZGVyVXRpbGl0eUNsYXNzKHNsb3QpIHtcbiAgcmV0dXJuIGdlbmVyYXRlVXRpbGl0eUNsYXNzKCdNdWlEaXZpZGVyJywgc2xvdCk7XG59XG5jb25zdCBkaXZpZGVyQ2xhc3NlcyA9IGdlbmVyYXRlVXRpbGl0eUNsYXNzZXMoJ011aURpdmlkZXInLCBbJ3Jvb3QnLCAnYWJzb2x1dGUnLCAnZnVsbFdpZHRoJywgJ2luc2V0JywgJ21pZGRsZScsICdmbGV4SXRlbScsICdsaWdodCcsICd2ZXJ0aWNhbCcsICd3aXRoQ2hpbGRyZW4nLCAnd2l0aENoaWxkcmVuVmVydGljYWwnLCAndGV4dEFsaWduUmlnaHQnLCAndGV4dEFsaWduTGVmdCcsICd3cmFwcGVyJywgJ3dyYXBwZXJWZXJ0aWNhbCddKTtcbmV4cG9ydCBkZWZhdWx0IGRpdmlkZXJDbGFzc2VzOyJdLCJuYW1lcyI6WyJnZW5lcmF0ZVV0aWxpdHlDbGFzc2VzIiwiZ2VuZXJhdGVVdGlsaXR5Q2xhc3MiLCJnZXREaXZpZGVyVXRpbGl0eUNsYXNzIiwic2xvdCIsImRpdmlkZXJDbGFzc2VzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@mui/material/Divider/dividerClasses.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/shared/UserDropdown.tsx":
/*!*******************************************************!*\
  !*** ./src/components/layout/shared/UserDropdown.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/styles */ \"(app-pages-browser)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _mui_material_Badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/Badge */ \"(app-pages-browser)/./node_modules/@mui/material/Badge/Badge.js\");\n/* harmony import */ var _mui_material_Avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/Avatar */ \"(app-pages-browser)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _mui_material_Popper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/Popper */ \"(app-pages-browser)/./node_modules/@mui/material/Popper/Popper.js\");\n/* harmony import */ var _mui_material_Fade__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/Fade */ \"(app-pages-browser)/./node_modules/@mui/material/Fade/Fade.js\");\n/* harmony import */ var _mui_material_Paper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/Paper */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _mui_material_ClickAwayListener__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/ClickAwayListener */ \"(app-pages-browser)/./node_modules/@mui/material/ClickAwayListener/ClickAwayListener.js\");\n/* harmony import */ var _mui_material_MenuList__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material/MenuList */ \"(app-pages-browser)/./node_modules/@mui/material/MenuList/MenuList.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/Typography */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material/Button */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// React Imports\n\n// Next Imports\n\n// Context Imports\n\n// MUI Imports\n\n\n\n\n\n\n\n\n\n\n\n// Styled component for badge content\nconst BadgeContentSpan = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\"span\")({\n    width: 8,\n    height: 8,\n    borderRadius: \"50%\",\n    cursor: \"pointer\",\n    backgroundColor: \"var(--mui-palette-success-main)\",\n    boxShadow: \"0 0 0 2px var(--mui-palette-background-paper)\"\n});\nconst UserDropdown = ()=>{\n    _s();\n    // States\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Refs\n    const anchorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Hooks\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const handleDropdownOpen = ()=>{\n        !open ? setOpen(true) : setOpen(false);\n    };\n    const handleDropdownClose = (event, url)=>{\n        if (url) {\n            router.push(url);\n        }\n        if (anchorRef.current && anchorRef.current.contains(event === null || event === void 0 ? void 0 : event.target)) {\n            return;\n        }\n        setOpen(false);\n    };\n    const handleLogout = ()=>{\n        logout();\n        setOpen(false);\n    };\n    // Get user initials for avatar\n    const getUserInitials = ()=>{\n        var _user_firstName, _user_lastName;\n        if (!user) return \"U\";\n        return \"\".concat(((_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName[0]) || \"\").concat(((_user_lastName = user.lastName) === null || _user_lastName === void 0 ? void 0 : _user_lastName[0]) || \"\").toUpperCase();\n    };\n    // Get user display name\n    const getUserDisplayName = ()=>{\n        if (!user) return \"User\";\n        return \"\".concat(user.firstName || \"\", \" \").concat(user.lastName || \"\").trim() || user.email;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Badge__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                ref: anchorRef,\n                overlap: \"circular\",\n                badgeContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BadgeContentSpan, {\n                    onClick: handleDropdownOpen\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 23\n                }, void 0),\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: \"right\"\n                },\n                className: \"mis-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Avatar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: anchorRef,\n                    alt: getUserDisplayName(),\n                    onClick: handleDropdownOpen,\n                    className: \"cursor-pointer bs-[38px] is-[38px]\",\n                    sx: {\n                        bgcolor: \"primary.main\",\n                        color: \"primary.contrastText\"\n                    },\n                    children: getUserInitials()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Popper__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                open: open,\n                transition: true,\n                disablePortal: true,\n                placement: \"bottom-end\",\n                anchorEl: anchorRef.current,\n                className: \"min-is-[240px] !mbs-4 z-[1]\",\n                children: (param)=>{\n                    let { TransitionProps, placement } = param;\n                    var _user_role, _user_role1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Fade__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        ...TransitionProps,\n                        style: {\n                            transformOrigin: placement === \"bottom-end\" ? \"right top\" : \"left top\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Paper__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_ClickAwayListener__WEBPACK_IMPORTED_MODULE_10__.ClickAwayListener, {\n                                onClickAway: (e)=>handleDropdownClose(e),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_MenuList__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center plb-2 pli-4 gap-2\",\n                                            tabIndex: -1,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Avatar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    alt: getUserDisplayName(),\n                                                    sx: {\n                                                        bgcolor: \"primary.main\",\n                                                        color: \"primary.contrastText\"\n                                                    },\n                                                    children: getUserInitials()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"font-medium\",\n                                                            color: \"text.primary\",\n                                                            children: getUserDisplayName()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            variant: \"caption\",\n                                                            children: (user === null || user === void 0 ? void 0 : (_user_role = user.role) === null || _user_role === void 0 ? void 0 : _user_role.charAt(0).toUpperCase()) + (user === null || user === void 0 ? void 0 : (_user_role1 = user.role) === null || _user_role1 === void 0 ? void 0 : _user_role1.slice(1)) || \"User\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"mlb-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center plb-2 pli-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                fullWidth: true,\n                                                variant: \"contained\",\n                                                color: \"error\",\n                                                size: \"small\",\n                                                endIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-logout-box-r-line\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                onClick: handleLogout,\n                                                sx: {\n                                                    \"& .MuiButton-endIcon\": {\n                                                        marginInlineStart: 1.5\n                                                    }\n                                                },\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, undefined);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(UserDropdown, \"Ki6AwJ+xLhf7pZqDJwJ6otUpJQc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = UserDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UserDropdown);\nvar _c;\n$RefreshReg$(_c, \"UserDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/shared/UserDropdown.tsx\n"));

/***/ })

});