'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  <PERSON><PERSON>graphy,
  Button,
  TextField,
  Grid,
  Box,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  Tooltip,
  Paper,
  Alert,
  Collapse,
  Divider,
  Slider,
  Autocomplete,
  InputAdornment,
  Checkbox
} from '@mui/material'
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Assignment as AssignIcon,
  Print as PrintIcon,
  MoreVert as MoreVertIcon,
  Download as DownloadIcon,
  Label as LabelIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Clear as ClearIcon,
  Settings as TuneIcon,
  CalendarToday as CalendarIcon,
  AttachMoney as MoneyIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material'
import {
  assetsService,
  assetQuantityService,
  assetItemService,
  categoriesService,
  locationsService,
  usersService
} from '@/lib/api'
import { Asset, Category, Location, User } from '@/types/api'
import { AssetLabelGenerator, AssetItemLabel } from '@/lib/services/assetLabelGenerator'
import { CsvExportService } from '@/lib/services/csvExportService'

// Types for component state
interface AssetWithDetails extends Asset {
  categoryName?: string
  locationName?: string
  assignedToName?: string
}

const statusColors = {
  in_stock: 'success',
  in_use: 'primary',
  maintenance: 'warning',
  retired: 'secondary',
  lost: 'error',
  damaged: 'error'
} as const

const conditionColors = {
  excellent: 'success',
  good: 'info',
  fair: 'warning',
  poor: 'error'
} as const

export default function AssetsPage() {
  const [assets, setAssets] = useState<AssetWithDetails[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [locations, setLocations] = useState<Location[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalAssets, setTotalAssets] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('')
  const [filterCategory, setFilterCategory] = useState('')
  const [filterLocation, setFilterLocation] = useState('')
  const [filterCondition, setFilterCondition] = useState('')
  const [filterAssignedTo, setFilterAssignedTo] = useState('')
  const [filterDateFrom, setFilterDateFrom] = useState('')
  const [filterDateTo, setFilterDateTo] = useState('')
  const [filterValueRange, setFilterValueRange] = useState<number[]>([0, 10000])
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(10)

  // Add Asset Form State
  const [newAsset, setNewAsset] = useState({
    name: '',
    description: '',
    categoryId: '',
    locationId: '',
    status: 'in_stock',
    condition: 'excellent',
    unitPrice: '',
    quantity: '1',
    purchaseDate: '',
    warrantyExpiry: '',
    manufacturer: '',
    model: ''
  })
  const [selectedAsset, setSelectedAsset] = useState<AssetWithDetails | null>(null)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [addDialogOpen, setAddDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null)

  // Edit Asset Form State
  const [editAsset, setEditAsset] = useState({
    name: '',
    description: '',
    categoryId: '',
    locationId: '',
    status: 'in_stock',
    condition: 'excellent',
    unitPrice: '',
    quantity: '1',
    purchaseDate: '',
    warrantyExpiry: '',
    manufacturer: '',
    model: '',
    supplier: '',
    invoiceNumber: '',
    notes: '',
    assignedToId: ''
  })

  // Quantity Management State
  const [quantityAdjustment, setQuantityAdjustment] = useState({
    fromStatus: '',
    toStatus: '',
    adjustQuantity: 0,
    reason: ''
  })
  const [showQuantityAdjustment, setShowQuantityAdjustment] = useState(false)

  // Asset Item Management State
  const [transferDialogOpen, setTransferDialogOpen] = useState(false)
  const [selectedAssetForTransfer, setSelectedAssetForTransfer] = useState<string>('')
  const [assetItems, setAssetItems] = useState<any[]>([])
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [transferData, setTransferData] = useState({
    toStatus: 'in_use' as any,
    reason: '',
    assignedToId: ''
  })

  // Asset Item Edit State
  const [editItemDialogOpen, setEditItemDialogOpen] = useState(false)
  const [selectedAssetItem, setSelectedAssetItem] = useState<any>(null)
  const [editItemData, setEditItemData] = useState({
    status: '',
    condition: '',
    serialNumber: '',
    assignedToId: '',
    notes: ''
  })

  // Load initial data and search assets
  const loadAssets = async (searchParams: any = {}) => {
    try {
      setLoading(true)
      setError(null)

      // Build search parameters
      const params = {
        page: page + 1, // API uses 1-based pagination
        limit: rowsPerPage,
        search: searchTerm || undefined,
        categoryId: filterCategory || undefined,
        locationId: filterLocation || undefined,
        status: filterStatus || undefined,
        condition: filterCondition || undefined,
        assignedToId: filterAssignedTo || undefined,
        minValue: filterValueRange[0] > 0 ? filterValueRange[0] : undefined,
        maxValue: filterValueRange[1] < 10000 ? filterValueRange[1] : undefined,
        purchaseDateFrom: filterDateFrom || undefined,
        purchaseDateTo: filterDateTo || undefined,
        ...searchParams
      }

      // Remove undefined values
      Object.keys(params).forEach(key => params[key] === undefined && delete params[key])

      const assetsResponse = await assetsService.getAssets(params)

      // Enhance assets with category, location, and user names
      const enhancedAssets: AssetWithDetails[] = assetsResponse.assets.map(asset => ({
        ...asset,
        categoryName: asset.category?.name || 'Unknown',
        locationName: asset.location?.name || 'Unknown',
        assignedToName: asset.assignedTo?.fullName || undefined
      }))

      setAssets(enhancedAssets)
      setTotalAssets(assetsResponse.total)
    } catch (err: any) {
      console.error('Failed to load assets:', err)
      if (err.message?.includes('401') || err.message?.includes('Unauthorized')) {
        setError('Authentication failed. Please log in again.')
        setTimeout(() => {
          window.location.href = '/login'
        }, 2000)
      } else {
        setError('Failed to load assets. Please check if the backend server is running and try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  // Load reference data (categories, locations, users)
  useEffect(() => {
    const loadReferenceData = async () => {
      try {
        const [categoriesData, locationsData, usersData] = await Promise.all([
          categoriesService.getCategories(),
          locationsService.getLocations(),
          usersService.getUsers()
        ])

        setCategories(categoriesData)
        setLocations(locationsData)
        setUsers(usersData)

        // Set initial value range
        setFilterValueRange([0, 10000])
      } catch (err) {
        console.error('Failed to load reference data:', err)
      }
    }

    loadReferenceData()
  }, [])

  // Load assets when filters change
  useEffect(() => {
    loadAssets()
  }, [
    page,
    rowsPerPage,
    searchTerm,
    filterStatus,
    filterCategory,
    filterLocation,
    filterCondition,
    filterAssignedTo,
    filterDateFrom,
    filterDateTo,
    filterValueRange
  ])

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value)
  }

  const clearAllFilters = () => {
    setSearchTerm('')
    setFilterStatus('')
    setFilterCategory('')
    setFilterLocation('')
    setFilterCondition('')
    setFilterAssignedTo('')
    setFilterDateFrom('')
    setFilterDateTo('')
    setFilterValueRange([0, 10000])
    setPage(0)
  }

  const handleViewAsset = (asset: any) => {
    setSelectedAsset(asset)
    setViewDialogOpen(true)
  }

  const handleDeleteAsset = (asset: any) => {
    setSelectedAsset(asset)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!selectedAsset) return

    try {
      await assetsService.deleteAsset(selectedAsset.id)
      setAssets(assets.filter(asset => asset.id !== selectedAsset.id))
      setDeleteDialogOpen(false)
      setSelectedAsset(null)
    } catch (err) {
      console.error('Failed to delete asset:', err)
      setError('Failed to delete asset. Please try again.')
    }
  }

  const handleAddAsset = () => {
    setNewAsset({
      name: '',
      description: '',
      categoryId: '',
      locationId: '',
      status: 'in_stock',
      condition: 'excellent',
      unitPrice: '',
      quantity: '1',
      purchaseDate: '',
      warrantyExpiry: '',
      manufacturer: '',
      model: ''
    })
    setAddDialogOpen(true)
  }

  const handleEditAsset = (asset: AssetWithDetails) => {
    setSelectedAsset(asset)
    setEditAsset({
      name: asset.name,
      description: asset.description || '',
      categoryId: asset.categoryId || '',
      locationId: asset.locationId || '',
      status: asset.status,
      condition: asset.condition,
      unitPrice: asset.unitPrice.toString(),
      quantity: asset.quantity.toString(),
      purchaseDate: asset.purchaseDate || '',
      warrantyExpiry: asset.warrantyExpiry || '',
      manufacturer: asset.manufacturer || '',
      model: asset.model || '',
      supplier: asset.supplier || '',
      invoiceNumber: asset.invoiceNumber || '',
      notes: asset.notes || '',
      assignedToId: asset.assignedToId || ''
    })
    // Reset quantity adjustment state
    setQuantityAdjustment({
      fromStatus: asset.status,
      toStatus: asset.status,
      adjustQuantity: 0,
      reason: ''
    })
    setShowQuantityAdjustment(false)
    setEditDialogOpen(true)
  }

  const handleSaveAsset = async () => {
    try {
      const assetData = {
        ...newAsset,
        status: newAsset.status as any,
        condition: newAsset.condition as any,
        unitPrice: parseFloat(newAsset.unitPrice) || 0,
        quantity: parseInt(newAsset.quantity) || 1,
        purchaseDate: newAsset.purchaseDate || undefined,
        warrantyExpiry: newAsset.warrantyExpiry || undefined
      }

      const createdAsset = await assetsService.createAsset(assetData)

      // Asset items are automatically created by the backend when asset is created
      console.log(
        `Asset created successfully: ${createdAsset.name} with ${createdAsset.quantity} quantity (individual items auto-generated by backend)`
      )

      // Add the new asset to the list with enhanced data
      const enhancedAsset: AssetWithDetails = {
        ...createdAsset,
        categoryName: categories.find(c => c.id === createdAsset.categoryId)?.name || 'Unknown',
        locationName: locations.find(l => l.id === createdAsset.locationId)?.name || 'Unknown',
        assignedToName: undefined
      }

      setAssets([enhancedAsset, ...assets])
      setAddDialogOpen(false)

      // Show success message with quantity info
      console.log(`Asset created successfully: ${createdAsset.name} with ${createdAsset.quantity} individual items`)
    } catch (err) {
      console.error('Failed to create asset:', err)
      setError('Failed to create asset. Please try again.')
    }
  }

  const handleStatusChange = (newStatus: string) => {
    const oldStatus = selectedAsset?.status || editAsset.status

    setEditAsset({ ...editAsset, status: newStatus })

    // Show quantity adjustment if status changed
    if (oldStatus !== newStatus) {
      setQuantityAdjustment({
        fromStatus: oldStatus,
        toStatus: newStatus,
        adjustQuantity: parseInt(editAsset.quantity) || 1,
        reason: `Status change from ${oldStatus.replace('_', ' ')} to ${newStatus.replace('_', ' ')}`
      })
      setShowQuantityAdjustment(true)
    } else {
      setShowQuantityAdjustment(false)
    }
  }

  const handleOpenTransferDialog = () => {
    setTransferDialogOpen(true)
  }

  const handleLoadAssetItems = async (assetId: string) => {
    try {
      console.log('Loading asset items for asset ID:', assetId)
      const items = await assetItemService.getAssetItems(assetId)
      console.log('Loaded asset items:', items)
      console.log('Number of items:', items?.length || 0)
      setAssetItems(items)
    } catch (err) {
      console.error('Failed to load asset items:', err)
      console.error('Error details:', err)
      setError('Failed to load asset items. Please try again.')
    }
  }

  const handleTransferAssetItems = async () => {
    try {
      // Transfer each selected item
      for (const itemId of selectedItems) {
        const item = assetItems.find(i => i.id === itemId)
        if (item) {
          await assetItemService.transferAssetItem({
            assetItemId: itemId,
            fromStatus: item.status,
            toStatus: transferData.toStatus,
            reason: transferData.reason,
            assignedToId: transferData.assignedToId || undefined
          })
        }
      }

      // Refresh data
      await loadAssets()
      if (selectedAssetForTransfer) {
        await handleLoadAssetItems(selectedAssetForTransfer)
      }

      // Reset selection
      setSelectedItems([])
      setTransferData({
        toStatus: 'in_use',
        reason: '',
        assignedToId: ''
      })
    } catch (err) {
      console.error('Failed to transfer asset items:', err)
      setError('Failed to transfer asset items. Please try again.')
    }
  }

  const handleEditAssetItem = (item: any) => {
    setSelectedAssetItem(item)
    setEditItemData({
      status: item.status,
      condition: item.condition,
      serialNumber: item.serialNumber || '',
      assignedToId: item.assignedToId || '',
      notes: item.notes || ''
    })
    setEditItemDialogOpen(true)
  }

  const handleSaveAssetItem = async () => {
    if (!selectedAssetItem) return

    try {
      await assetItemService.updateAssetItem(selectedAssetItem.id, {
        status: editItemData.status as any,
        condition: editItemData.condition as any,
        serialNumber: editItemData.serialNumber || undefined,
        assignedToId: editItemData.assignedToId || undefined,
        notes: editItemData.notes || undefined
      })

      // Refresh data
      await loadAssets()
      if (selectedAssetForTransfer) {
        await handleLoadAssetItems(selectedAssetForTransfer)
      }

      setEditItemDialogOpen(false)
      setSelectedAssetItem(null)
    } catch (err) {
      console.error('Failed to update asset item:', err)
      setError('Failed to update asset item. Please try again.')
    }
  }

  const handleDeleteAssetItem = async (itemId: string) => {
    if (!confirm('Are you sure you want to delete this asset item? This action cannot be undone.')) {
      return
    }

    try {
      await assetItemService.deleteAssetItem(itemId)

      // Refresh data
      await loadAssets()
      if (selectedAssetForTransfer) {
        await handleLoadAssetItems(selectedAssetForTransfer)
      }
    } catch (err) {
      console.error('Failed to delete asset item:', err)
      setError('Failed to delete asset item. Please try again.')
    }
  }

  const handlePrintAssetLabel = async (item: any) => {
    try {
      const asset = assets.find(a => a.id === item.assetId)
      if (!asset) return

      const labelData: AssetItemLabel = {
        id: item.id,
        assetNumber: item.assetNumber,
        assetName: asset.name,
        category: asset.categoryName || 'Unknown',
        location: asset.locationName || 'Unknown',
        status: item.status,
        condition: item.condition
      }

      await AssetLabelGenerator.printAssetLabels([labelData], 'single')
      console.log('Asset label printed successfully')
    } catch (err) {
      console.error('Failed to print asset label:', err)
      setError('Failed to print asset label. Please try again.')
    }
  }

  const handlePrintSelectedLabels = async () => {
    try {
      if (selectedItems.length === 0) {
        setError('Please select at least one asset item to print labels.')
        return
      }

      const selectedAssetItems = assetItems.filter(item => selectedItems.includes(item.id))
      const asset = assets.find(a => a.id === selectedAssetItems[0]?.assetId)
      if (!asset) return

      const labelData: AssetItemLabel[] = selectedAssetItems.map(item => ({
        id: item.id,
        assetNumber: item.assetNumber,
        assetName: asset.name,
        category: asset.categoryName || 'Unknown',
        location: asset.locationName || 'Unknown',
        status: item.status,
        condition: item.condition
      }))

      await AssetLabelGenerator.printAssetLabels(labelData, 'bulk')
      console.log(`Printed ${selectedItems.length} asset labels successfully`)
    } catch (err) {
      console.error('Failed to print asset labels:', err)
      setError('Failed to print asset labels. Please try again.')
    }
  }

  const handleSaveEditAsset = async () => {
    if (!selectedAsset) return

    // Basic validation
    if (!editAsset.name.trim()) {
      setError('Asset name is required')
      return
    }

    if (!editAsset.categoryId) {
      setError('Category is required')
      return
    }

    if (!editAsset.locationId) {
      setError('Location is required')
      return
    }

    try {
      setLoading(true)

      // Calculate final quantity based on adjustment
      let finalQuantity = parseInt(editAsset.quantity) || 1
      if (showQuantityAdjustment && quantityAdjustment.adjustQuantity !== 0) {
        finalQuantity = quantityAdjustment.adjustQuantity
      }

      const assetData = {
        name: editAsset.name,
        description: editAsset.description,
        categoryId: editAsset.categoryId,
        locationId: editAsset.locationId,
        manufacturer: editAsset.manufacturer,
        model: editAsset.model,
        unitPrice: parseFloat(editAsset.unitPrice) || 0,
        quantity: finalQuantity,
        supplier: editAsset.supplier,
        invoiceNumber: editAsset.invoiceNumber,
        notes: editAsset.notes,
        purchaseDate: editAsset.purchaseDate || undefined,
        warrantyExpiry: editAsset.warrantyExpiry || undefined,
        assignedToId: editAsset.assignedToId || undefined
      }

      const updatedAsset = await assetsService.updateAsset(selectedAsset.id, assetData)

      // Update the asset in the list with enhanced data
      const enhancedAsset: AssetWithDetails = {
        ...updatedAsset,
        categoryName: categories.find(c => c.id === updatedAsset.categoryId)?.name || 'Unknown',
        locationName: locations.find(l => l.id === updatedAsset.locationId)?.name || 'Unknown',
        assignedToName: users.find(u => u.id === updatedAsset.assignedToId)?.fullName || undefined
      }

      setAssets(assets.map(asset => (asset.id === selectedAsset.id ? enhancedAsset : asset)))
      setEditDialogOpen(false)
      setSelectedAsset(null)
      setShowQuantityAdjustment(false)
    } catch (err) {
      console.error('Failed to update asset:', err)
      setError(`Failed to update asset: ${err instanceof Error ? err.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const handleExportCsv = async () => {
    try {
      console.log('Exporting assets to CSV...')
      setLoading(true)

      await CsvExportService.exportAssetsWithItems()

      console.log('CSV export completed successfully')
    } catch (err) {
      console.error('Failed to export CSV:', err)
      setError(`Failed to export CSV: ${err instanceof Error ? err.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const handlePrintLabel = async (asset: AssetWithDetails) => {
    try {
      console.log('Printing label for asset:', asset.name, asset.assetNumber)

      const reportGenerator = new ReportGenerator()
      await reportGenerator.generateAssetLabel(asset)
      reportGenerator.openForPrint()

      console.log('Label opened for printing')
    } catch (err) {
      console.error('Failed to print label:', err)
      setError(`Failed to print label: ${err instanceof Error ? err.message : 'Unknown error'}`)
    }
  }

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, asset: any) => {
    setMenuAnchor(event.currentTarget)
    setSelectedAsset(asset)
  }

  const handleMenuClose = () => {
    setMenuAnchor(null)
    setSelectedAsset(null)
  }

  // Assets are already filtered by the server, so we just use them directly
  const filteredAssets = assets

  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <Typography>Loading assets...</Typography>
      </Box>
    )
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity='error' sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button variant='contained' onClick={() => window.location.reload()}>
          Retry
        </Button>
      </Box>
    )
  }

  // Calculate statistics
  const totalValue = assets.reduce((sum, asset) => sum + (asset.totalValue || 0), 0)
  const statusStats = assets.reduce(
    (acc, asset) => {
      acc[asset.status] = (acc[asset.status] || 0) + 1
      return acc
    },
    {} as Record<string, number>
  )

  // Calculate quantity statistics by status
  const quantityStats = assets.reduce(
    (acc, asset) => {
      acc[asset.status] = (acc[asset.status] || 0) + asset.quantity
      return acc
    },
    {} as Record<string, number>
  )

  const totalQuantity = assets.reduce((sum, asset) => sum + asset.quantity, 0)

  return (
    <Box sx={{ p: 4, backgroundColor: 'grey.50', minHeight: '100vh' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography
            variant='h4'
            component='h1'
            sx={{
              fontWeight: 700,
              color: 'text.primary',
              mb: 1
            }}
          >
            Asset Management
          </Typography>
          <Typography variant='body1' color='text.secondary'>
            Manage and track your organization's assets efficiently
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant='contained'
            startIcon={<AddIcon />}
            color='primary'
            onClick={handleAddAsset}
            sx={{
              borderRadius: 2,
              fontWeight: 600,
              textTransform: 'none',
              px: 3,
              py: 1.5,
              boxShadow: 2,
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: 4
              }
            }}
          >
            Add Asset
          </Button>
          <Button
            variant='outlined'
            startIcon={<DownloadIcon />}
            onClick={handleExportCsv}
            disabled={loading}
            sx={{
              borderRadius: 2,
              fontWeight: 600,
              textTransform: 'none',
              px: 3,
              py: 1.5,
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: 2
              }
            }}
          >
            {loading ? 'Exporting...' : 'Export CSV'}
          </Button>
        </Box>
      </Box>

      {/* Dashboard Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            elevation={0}
            sx={{
              p: 3,
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 2,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <Box sx={{ position: 'relative', zIndex: 2 }}>
              <Typography variant='h6' sx={{ fontWeight: 600, mb: 1 }}>
                Total Assets
              </Typography>
              <Typography variant='h3' sx={{ fontWeight: 700, mb: 1 }}>
                {totalAssets.toLocaleString()}
              </Typography>
              <Typography variant='body2' sx={{ opacity: 0.9 }}>
                Active inventory items
              </Typography>
            </Box>
            <Box
              sx={{
                position: 'absolute',
                top: -20,
                right: -20,
                width: 80,
                height: 80,
                borderRadius: '50%',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <i className='ri-database-2-line' style={{ fontSize: '2rem', opacity: 0.7 }} />
            </Box>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            elevation={0}
            sx={{
              p: 3,
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 2,
              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
              color: 'white',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <Box sx={{ position: 'relative', zIndex: 2 }}>
              <Typography variant='h6' sx={{ fontWeight: 600, mb: 1 }}>
                Total Value
              </Typography>
              <Typography variant='h3' sx={{ fontWeight: 700, mb: 1 }}>
                LKR {totalValue.toLocaleString()}
              </Typography>
              <Typography variant='body2' sx={{ opacity: 0.9 }}>
                Portfolio worth
              </Typography>
            </Box>
            <Box
              sx={{
                position: 'absolute',
                top: -20,
                right: -20,
                width: 80,
                height: 80,
                borderRadius: '50%',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <i className='ri-money-dollar-circle-line' style={{ fontSize: '2rem', opacity: 0.7 }} />
            </Box>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            elevation={0}
            sx={{
              p: 3,
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 2,
              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
              color: 'white',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <Box sx={{ position: 'relative', zIndex: 2 }}>
              <Typography variant='h6' sx={{ fontWeight: 600, mb: 1 }}>
                In Use
              </Typography>
              <Typography variant='h3' sx={{ fontWeight: 700, mb: 1 }}>
                {statusStats.in_use || 0}
              </Typography>
              <Typography variant='body2' sx={{ opacity: 0.9 }}>
                Currently deployed
              </Typography>
            </Box>
            <Box
              sx={{
                position: 'absolute',
                top: -20,
                right: -20,
                width: 80,
                height: 80,
                borderRadius: '50%',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <i className='ri-play-circle-line' style={{ fontSize: '2rem', opacity: 0.7 }} />
            </Box>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            elevation={0}
            sx={{
              p: 3,
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 2,
              background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
              color: 'white',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <Box sx={{ position: 'relative', zIndex: 2 }}>
              <Typography variant='h6' sx={{ fontWeight: 600, mb: 1 }}>
                Available
              </Typography>
              <Typography variant='h3' sx={{ fontWeight: 700, mb: 1 }}>
                {statusStats.in_stock || 0}
              </Typography>
              <Typography variant='body2' sx={{ opacity: 0.9 }}>
                Ready for deployment
              </Typography>
            </Box>
            <Box
              sx={{
                position: 'absolute',
                top: -20,
                right: -20,
                width: 80,
                height: 80,
                borderRadius: '50%',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <i className='ri-check-circle-line' style={{ fontSize: '2rem', opacity: 0.7 }} />
            </Box>
          </Card>
        </Grid>
      </Grid>

      {/* Quantity Management Dashboard */}
      <Card
        elevation={0}
        sx={{
          mb: 3,
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 2
        }}
      >
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant='h6' sx={{ display: 'flex', alignItems: 'center' }}>
              <i className='ri-stack-line' style={{ marginRight: '8px' }} />
              Asset Quantity by Status
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant='outlined'
                size='small'
                onClick={() => loadAssets()}
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                <i className='ri-refresh-line' style={{ marginRight: '4px' }} />
                Refresh
              </Button>
              <Button
                variant='outlined'
                size='small'
                onClick={() => handleOpenTransferDialog()}
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                <i className='ri-exchange-line' style={{ marginRight: '4px' }} />
                Manage Quantities
              </Button>
            </Box>
          </Box>
          <Grid container spacing={3}>
            {Object.entries(quantityStats).map(([status, quantity]) => {
              const statusLabel = status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
              const color = statusColors[status as keyof typeof statusColors]
              const colorMap = {
                success: '#4caf50',
                primary: '#2196f3',
                warning: '#ff9800',
                secondary: '#9e9e9e',
                error: '#f44336'
              }

              return (
                <Grid item xs={12} sm={6} md={2} key={status}>
                  <Box
                    sx={{
                      p: 2,
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 2,
                      textAlign: 'center',
                      backgroundColor: 'background.paper',
                      transition: 'all 0.2s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 2,
                        borderColor: colorMap[color as keyof typeof colorMap]
                      }
                    }}
                  >
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: colorMap[color as keyof typeof colorMap],
                        mx: 'auto',
                        mb: 1
                      }}
                    />
                    <Typography variant='h4' sx={{ fontWeight: 700, color: 'text.primary', mb: 0.5 }}>
                      {quantity}
                    </Typography>
                    <Typography variant='body2' color='text.secondary' sx={{ fontSize: '0.75rem' }}>
                      {statusLabel}
                    </Typography>
                    <Typography variant='caption' color='text.secondary'>
                      {((quantity / totalQuantity) * 100).toFixed(1)}% of total
                    </Typography>
                  </Box>
                </Grid>
              )
            })}
            <Grid item xs={12} sm={6} md={2}>
              <Box
                sx={{
                  p: 2,
                  border: '2px solid',
                  borderColor: 'primary.main',
                  borderRadius: 2,
                  textAlign: 'center',
                  backgroundColor: 'primary.50',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: 2
                  }
                }}
              >
                <Box
                  sx={{
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    backgroundColor: 'primary.main',
                    mx: 'auto',
                    mb: 1
                  }}
                />
                <Typography variant='h4' sx={{ fontWeight: 700, color: 'primary.main', mb: 0.5 }}>
                  {totalQuantity}
                </Typography>
                <Typography variant='body2' color='primary.main' sx={{ fontSize: '0.75rem', fontWeight: 600 }}>
                  Total Quantity
                </Typography>
                <Typography variant='caption' color='text.secondary'>
                  All assets combined
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Search and Filters */}
      <Card
        elevation={0}
        sx={{
          mb: 3,
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 2
        }}
      >
        <CardContent sx={{ p: 4 }}>
          {/* Main Search and Quick Filters */}
          <Grid container spacing={3} alignItems='center' sx={{ mb: 3 }}>
            <Grid item xs={12} md={5}>
              <TextField
                fullWidth
                placeholder='Search assets by name, number, category, location, manufacturer, model, serial number...'
                value={searchTerm}
                onChange={handleSearch}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    backgroundColor: 'background.paper',
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main'
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderWidth: 2
                    }
                  }
                }}
                InputProps={{
                  startAdornment: (
                    <Box sx={{ display: 'flex', alignItems: 'center', mr: 1 }}>
                      <SearchIcon sx={{ color: 'text.secondary', fontSize: '1.25rem' }} />
                    </Box>
                  )
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    backgroundColor: 'background.paper',
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main'
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderWidth: 2
                    }
                  }
                }}
              >
                <InputLabel>Status</InputLabel>
                <Select value={filterStatus} label='Status' onChange={e => setFilterStatus(e.target.value)}>
                  <MenuItem value=''>All Statuses</MenuItem>
                  <MenuItem value='in_stock'>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: 'success.main' }} />
                      In Stock
                    </Box>
                  </MenuItem>
                  <MenuItem value='in_use'>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: 'info.main' }} />
                      In Use
                    </Box>
                  </MenuItem>
                  <MenuItem value='maintenance'>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: 'warning.main' }} />
                      Maintenance
                    </Box>
                  </MenuItem>
                  <MenuItem value='retired'>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: 'error.main' }} />
                      Retired
                    </Box>
                  </MenuItem>
                  <MenuItem value='lost'>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: 'error.main' }} />
                      Lost
                    </Box>
                  </MenuItem>
                  <MenuItem value='damaged'>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: 'error.main' }} />
                      Damaged
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    backgroundColor: 'background.paper',
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main'
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderWidth: 2
                    }
                  }
                }}
              >
                <InputLabel>Category</InputLabel>
                <Select value={filterCategory} label='Category' onChange={e => setFilterCategory(e.target.value)}>
                  <MenuItem value=''>All Categories</MenuItem>
                  {categories.map(category => (
                    <MenuItem key={category.id} value={category.id}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <i
                          className='ri-folder-2-line'
                          style={{ fontSize: '1rem', color: 'var(--mui-palette-primary-main)' }}
                        />
                        {category.name}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={1.5}>
              <Button
                fullWidth
                variant={showAdvancedFilters ? 'contained' : 'outlined'}
                startIcon={showAdvancedFilters ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                sx={{
                  borderRadius: 2,
                  fontWeight: 600,
                  textTransform: 'none',
                  py: 1.5,
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    boxShadow: 2
                  }
                }}
              >
                Filters
              </Button>
            </Grid>
            <Grid item xs={12} md={1.5}>
              <Button
                fullWidth
                variant='outlined'
                startIcon={<ClearIcon />}
                onClick={clearAllFilters}
                color='secondary'
                sx={{
                  borderRadius: 2,
                  fontWeight: 600,
                  textTransform: 'none',
                  py: 1.5,
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    boxShadow: 2
                  }
                }}
              >
                Clear All
              </Button>
            </Grid>
          </Grid>

          {/* Advanced Filters */}
          <Collapse in={showAdvancedFilters}>
            <Divider sx={{ mb: 3 }} />
            <Typography variant='h6' sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
              <TuneIcon sx={{ mr: 1 }} />
              Advanced Filters
            </Typography>

            <Grid container spacing={3}>
              {/* Location Filter */}
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Location</InputLabel>
                  <Select value={filterLocation} label='Location' onChange={e => setFilterLocation(e.target.value)}>
                    <MenuItem value=''>All Locations</MenuItem>
                    {locations.map(location => (
                      <MenuItem key={location.id} value={location.id}>
                        {location.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Condition Filter */}
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Condition</InputLabel>
                  <Select value={filterCondition} label='Condition' onChange={e => setFilterCondition(e.target.value)}>
                    <MenuItem value=''>All Conditions</MenuItem>
                    <MenuItem value='excellent'>Excellent</MenuItem>
                    <MenuItem value='good'>Good</MenuItem>
                    <MenuItem value='fair'>Fair</MenuItem>
                    <MenuItem value='poor'>Poor</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              {/* Assigned To Filter */}
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Assigned To</InputLabel>
                  <Select
                    value={filterAssignedTo}
                    label='Assigned To'
                    onChange={e => setFilterAssignedTo(e.target.value)}
                  >
                    <MenuItem value=''>All Users</MenuItem>
                    {users.map(user => (
                      <MenuItem key={user.id} value={user.id}>
                        {user.fullName}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Value Range Filter */}
              <Grid item xs={12} md={3}>
                <Typography gutterBottom>
                  <MoneyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Value Range: LKR {filterValueRange[0].toLocaleString()} - LKR {filterValueRange[1].toLocaleString()}
                </Typography>
                <Slider
                  value={filterValueRange}
                  onChange={(_, newValue) => setFilterValueRange(newValue as number[])}
                  valueLabelDisplay='auto'
                  min={0}
                  max={assets.length > 0 ? Math.max(...assets.map(asset => asset.totalValue || 0)) : 10000}
                  step={100}
                  valueLabelFormat={value => `LKR ${value.toLocaleString()}`}
                />
              </Grid>

              {/* Date Range Filters */}
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label='Purchase Date From'
                  type='date'
                  value={filterDateFrom}
                  onChange={e => setFilterDateFrom(e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position='start'>
                        <CalendarIcon />
                      </InputAdornment>
                    )
                  }}
                />
              </Grid>

              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label='Purchase Date To'
                  type='date'
                  value={filterDateTo}
                  onChange={e => setFilterDateTo(e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position='start'>
                        <CalendarIcon />
                      </InputAdornment>
                    )
                  }}
                />
              </Grid>

              {/* Filter Summary */}
              <Grid item xs={12} md={6}>
                <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant='body2' color='text.secondary'>
                    <strong>
                      Showing {assets.length} of {totalAssets} assets
                    </strong>
                    {searchTerm && ` • Search: "${searchTerm}"`}
                    {filterStatus && ` • Status: ${filterStatus}`}
                    {filterCategory &&
                      ` • Category: ${categories.find(c => c.id === filterCategory)?.name || filterCategory}`}
                    {filterLocation &&
                      ` • Location: ${locations.find(l => l.id === filterLocation)?.name || filterLocation}`}
                    {filterCondition && ` • Condition: ${filterCondition}`}
                    {filterAssignedTo &&
                      ` • Assigned to: ${users.find(u => u.id === filterAssignedTo)?.fullName || filterAssignedTo}`}
                    {(filterDateFrom || filterDateTo) && ` • Date range applied`}
                    {(filterValueRange[0] > 0 ||
                      filterValueRange[1] <
                        (assets.length > 0 ? Math.max(...assets.map(asset => asset.totalValue || 0)) : 10000)) &&
                      ` • Value range applied`}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Collapse>
        </CardContent>
      </Card>

      {/* Assets Table */}
      <Card
        elevation={0}
        sx={{
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 2,
          overflow: 'hidden'
        }}
      >
        <TableContainer sx={{ backgroundColor: 'background.paper' }}>
          <Table sx={{ minWidth: 650 }}>
            <TableHead>
              <TableRow
                sx={{
                  backgroundColor: 'grey.50',
                  '& .MuiTableCell-head': {
                    fontWeight: 600,
                    fontSize: '0.875rem',
                    color: 'text.primary',
                    borderBottom: '2px solid',
                    borderBottomColor: 'divider',
                    py: 2.5
                  }
                }}
              >
                <TableCell>Asset Number</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Category</TableCell>
                <TableCell>Location</TableCell>
                <TableCell>Quantity</TableCell>
                <TableCell>Base Number</TableCell>
                <TableCell>Assigned To</TableCell>
                <TableCell align='right'>Value</TableCell>
                <TableCell align='center' width={120}>
                  Actions
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredAssets.map((asset, index) => (
                <TableRow
                  key={asset.id}
                  hover
                  sx={{
                    '&:hover': {
                      backgroundColor: 'action.hover',
                      transform: 'scale(1.001)',
                      transition: 'all 0.2s ease-in-out'
                    },
                    '& .MuiTableCell-root': {
                      borderBottom: '1px solid',
                      borderBottomColor: 'divider',
                      py: 2
                    },
                    ...(index % 2 === 0 && {
                      backgroundColor: 'grey.25'
                    })
                  }}
                >
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          backgroundColor: 'primary.main'
                        }}
                      />
                      <Typography variant='body2' fontWeight='600' color='text.primary'>
                        {asset.assetNumber}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant='body2' fontWeight='500' color='text.primary'>
                      {asset.name}
                    </Typography>
                    <Typography variant='caption' color='text.secondary'>
                      {asset.manufacturer} {asset.model}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant='body2' color='text.primary'>
                      {asset.categoryName}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant='body2' color='text.primary'>
                      {asset.locationName}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                      <Typography variant='body2' fontWeight='600' color='text.primary'>
                        {asset.quantity} units
                      </Typography>
                      <Typography variant='caption' color='text.secondary'>
                        Individual tracking
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                      <Typography variant='body2' fontWeight='600' color='text.primary'>
                        {asset.assetNumber}
                      </Typography>
                      <Typography variant='caption' color='text.secondary'>
                        Base number
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant='body2' color='text.primary'>
                      {asset.assignedToName || (
                        <Typography component='span' color='text.secondary' fontStyle='italic'>
                          Unassigned
                        </Typography>
                      )}
                    </Typography>
                  </TableCell>
                  <TableCell align='right'>
                    <Typography variant='body2' fontWeight='600' color='text.primary'>
                      LKR {(asset.totalValue || 0).toLocaleString()}
                    </Typography>
                  </TableCell>
                  <TableCell align='center'>
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5 }}>
                      <Tooltip title='View Details' arrow>
                        <IconButton
                          size='small'
                          onClick={() => handleViewAsset(asset)}
                          sx={{
                            color: 'primary.main',
                            '&:hover': {
                              backgroundColor: 'primary.light',
                              color: 'primary.contrastText'
                            }
                          }}
                        >
                          <ViewIcon fontSize='small' />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Print Label' arrow>
                        <IconButton
                          size='small'
                          onClick={() => handlePrintLabel(asset)}
                          sx={{
                            color: 'secondary.main',
                            '&:hover': {
                              backgroundColor: 'secondary.light',
                              color: 'secondary.contrastText'
                            }
                          }}
                        >
                          <LabelIcon fontSize='small' />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='More Actions' arrow>
                        <IconButton
                          size='small'
                          onClick={e => handleMenuClick(e, asset)}
                          sx={{
                            color: 'text.secondary',
                            '&:hover': {
                              backgroundColor: 'action.hover',
                              color: 'text.primary'
                            }
                          }}
                        >
                          <MoreVertIcon fontSize='small' />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <Box
          sx={{
            borderTop: '1px solid',
            borderTopColor: 'divider',
            backgroundColor: 'grey.25'
          }}
        >
          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component='div'
            count={totalAssets}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            onRowsPerPageChange={e => {
              setRowsPerPage(parseInt(e.target.value, 10))
              setPage(0)
            }}
            sx={{
              '& .MuiTablePagination-toolbar': {
                paddingX: 3,
                paddingY: 2
              },
              '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
                fontWeight: 500,
                color: 'text.primary'
              }
            }}
          />
        </Box>
      </Card>

      {/* Action Menu */}
      <Menu anchorEl={menuAnchor} open={Boolean(menuAnchor)} onClose={handleMenuClose}>
        <MenuItem
          onClick={() => {
            handleEditAsset(selectedAsset!)
            handleMenuClose()
          }}
        >
          <EditIcon sx={{ mr: 1 }} /> Edit
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <AssignIcon sx={{ mr: 1 }} /> Assign
        </MenuItem>
        <MenuItem
          onClick={() => {
            handleDeleteAsset(selectedAsset)
            handleMenuClose()
          }}
        >
          <DeleteIcon sx={{ mr: 1 }} /> Delete
        </MenuItem>
      </Menu>

      {/* View Asset Dialog */}
      <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth='md' fullWidth>
        <DialogTitle>Asset Details</DialogTitle>
        <DialogContent>
          {selectedAsset && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Asset Number</Typography>
                <Typography variant='body1'>{selectedAsset.assetNumber}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Name</Typography>
                <Typography variant='body1'>{selectedAsset.name}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Category</Typography>
                <Typography variant='body1'>{selectedAsset.categoryName}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Location</Typography>
                <Typography variant='body1'>{selectedAsset.locationName}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Quantity</Typography>
                <Typography variant='body1'>{selectedAsset.quantity} units</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Individual Tracking</Typography>
                <Typography variant='body1'>Each unit has unique asset number</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Unit Price</Typography>
                <Typography variant='body1'>LKR {selectedAsset.unitPrice}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Quantity</Typography>
                <Typography variant='body1'>{selectedAsset.quantity}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Total Value</Typography>
                <Typography variant='body1'>LKR {selectedAsset.totalValue}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Assigned To</Typography>
                <Typography variant='body1'>{selectedAsset.assignedToName || 'Unassigned'}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Purchase Date</Typography>
                <Typography variant='body1'>{selectedAsset.purchaseDate}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Warranty Expiry</Typography>
                <Typography variant='body1'>{selectedAsset.warrantyExpiry}</Typography>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
          <Button
            variant='contained'
            startIcon={<LabelIcon />}
            onClick={() => selectedAsset && handlePrintLabel(selectedAsset)}
          >
            Print Label
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete asset "{selectedAsset?.name}" ({selectedAsset?.assetNumber})? This action
            cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color='error' variant='contained'>
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add Asset Dialog */}
      <Dialog open={addDialogOpen} onClose={() => setAddDialogOpen(false)} maxWidth='md' fullWidth>
        <DialogTitle>Add New Asset</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Asset Name'
                value={newAsset.name}
                onChange={e => setNewAsset({ ...newAsset, name: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Category</InputLabel>
                <Select
                  value={newAsset.categoryId}
                  label='Category'
                  onChange={e => setNewAsset({ ...newAsset, categoryId: e.target.value })}
                >
                  {categories.map(category => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Location</InputLabel>
                <Select
                  value={newAsset.locationId}
                  label='Location'
                  onChange={e => setNewAsset({ ...newAsset, locationId: e.target.value })}
                >
                  {locations.map(location => (
                    <MenuItem key={location.id} value={location.id}>
                      {location.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Initial Status for Items</InputLabel>
                <Select
                  value={newAsset.status || 'in_stock'}
                  label='Initial Status for Items'
                  onChange={e => setNewAsset({ ...newAsset, status: e.target.value })}
                >
                  <MenuItem value='in_stock'>In Stock</MenuItem>
                  <MenuItem value='in_use'>In Use</MenuItem>
                  <MenuItem value='maintenance'>Maintenance</MenuItem>
                  <MenuItem value='retired'>Retired</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Initial Condition for Items</InputLabel>
                <Select
                  value={newAsset.condition || 'excellent'}
                  label='Initial Condition for Items'
                  onChange={e => setNewAsset({ ...newAsset, condition: e.target.value })}
                >
                  <MenuItem value='excellent'>Excellent</MenuItem>
                  <MenuItem value='good'>Good</MenuItem>
                  <MenuItem value='fair'>Fair</MenuItem>
                  <MenuItem value='poor'>Poor</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Unit Price'
                type='number'
                value={newAsset.unitPrice}
                onChange={e => setNewAsset({ ...newAsset, unitPrice: e.target.value })}
                InputProps={{
                  startAdornment: <Typography sx={{ mr: 1 }}>LKR</Typography>
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Quantity'
                type='number'
                value={newAsset.quantity}
                onChange={e => setNewAsset({ ...newAsset, quantity: e.target.value })}
                inputProps={{ min: 1 }}
                helperText='Number of units for this asset'
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Manufacturer'
                value={newAsset.manufacturer}
                onChange={e => setNewAsset({ ...newAsset, manufacturer: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Model'
                value={newAsset.model}
                onChange={e => setNewAsset({ ...newAsset, model: e.target.value })}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Purchase Date'
                type='date'
                value={newAsset.purchaseDate}
                onChange={e => setNewAsset({ ...newAsset, purchaseDate: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Warranty Expiry'
                type='date'
                value={newAsset.warrantyExpiry}
                onChange={e => setNewAsset({ ...newAsset, warrantyExpiry: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Description'
                multiline
                rows={3}
                value={newAsset.description}
                onChange={e => setNewAsset({ ...newAsset, description: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveAsset} variant='contained' color='primary'>
            Create Asset
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Asset Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth='md' fullWidth>
        <DialogTitle>Edit Asset</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Asset Name'
                value={editAsset.name}
                onChange={e => setEditAsset({ ...editAsset, name: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Category</InputLabel>
                <Select
                  value={editAsset.categoryId}
                  label='Category'
                  onChange={e => setEditAsset({ ...editAsset, categoryId: e.target.value })}
                >
                  {categories.map(category => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Location</InputLabel>
                <Select
                  value={editAsset.locationId}
                  label='Location'
                  onChange={e => setEditAsset({ ...editAsset, locationId: e.target.value })}
                >
                  {locations.map(location => (
                    <MenuItem key={location.id} value={location.id}>
                      {location.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant='subtitle2' sx={{ mb: 1 }}>
                  Individual Item Management
                </Typography>
                <Typography variant='body2' color='text.secondary'>
                  Status and condition are now managed at the individual asset item level. Use the "Manage Items" button
                  to view and update individual asset items.
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant='subtitle2' sx={{ mb: 1 }}>
                  Asset Quantity: {selectedAsset?.quantity || 0} units
                </Typography>
                <Typography variant='body2' color='text.secondary'>
                  Each unit has a unique asset number for individual tracking and labeling.
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Unit Price'
                type='number'
                value={editAsset.unitPrice}
                onChange={e => setEditAsset({ ...editAsset, unitPrice: e.target.value })}
                InputProps={{
                  startAdornment: <Typography sx={{ mr: 1 }}>LKR</Typography>
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Quantity'
                type='number'
                value={editAsset.quantity}
                onChange={e => setEditAsset({ ...editAsset, quantity: e.target.value })}
                inputProps={{ min: 1 }}
                helperText={`Current: ${selectedAsset?.quantity || 0} units • Individual tracking enabled`}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Manufacturer'
                value={editAsset.manufacturer}
                onChange={e => setEditAsset({ ...editAsset, manufacturer: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Model'
                value={editAsset.model}
                onChange={e => setEditAsset({ ...editAsset, model: e.target.value })}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Purchase Date'
                type='date'
                value={editAsset.purchaseDate}
                onChange={e => setEditAsset({ ...editAsset, purchaseDate: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Warranty Expiry'
                type='date'
                value={editAsset.warrantyExpiry}
                onChange={e => setEditAsset({ ...editAsset, warrantyExpiry: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Assigned To</InputLabel>
                <Select
                  value={editAsset.assignedToId}
                  label='Assigned To'
                  onChange={e => setEditAsset({ ...editAsset, assignedToId: e.target.value })}
                >
                  <MenuItem value=''>Unassigned</MenuItem>
                  {users.map(user => (
                    <MenuItem key={user.id} value={user.id}>
                      {user.fullName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Description'
                multiline
                rows={3}
                value={editAsset.description}
                onChange={e => setEditAsset({ ...editAsset, description: e.target.value })}
              />
            </Grid>

            {/* Status Change Alert */}
            {selectedAsset && selectedAsset.status && editAsset.status !== selectedAsset.status && (
              <Grid item xs={12}>
                <Alert severity='info' sx={{ mt: 2 }}>
                  <Typography variant='body2'>
                    <strong>Status Change:</strong> {selectedAsset.status.replace('_', ' ').toUpperCase()} →{' '}
                    {editAsset.status.replace('_', ' ').toUpperCase()}
                  </Typography>
                  <Typography variant='body2' sx={{ mt: 1 }}>
                    This will update the asset quantity tracking for both statuses.
                  </Typography>
                </Alert>
              </Grid>
            )}

            {/* Quantity Adjustment Section */}
            {showQuantityAdjustment && (
              <Grid item xs={12}>
                <Card sx={{ p: 3, backgroundColor: 'primary.50', border: '1px solid', borderColor: 'primary.main' }}>
                  <Typography variant='h6' sx={{ mb: 2, color: 'primary.main' }}>
                    <i className='ri-exchange-line' style={{ marginRight: '8px' }} />
                    Quantity Management
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label='Adjust Quantity'
                        type='number'
                        value={quantityAdjustment.adjustQuantity}
                        onChange={e =>
                          setQuantityAdjustment({
                            ...quantityAdjustment,
                            adjustQuantity: parseInt(e.target.value) || 0
                          })
                        }
                        inputProps={{ min: 0 }}
                        helperText={`Moving from ${quantityAdjustment.fromStatus.replace('_', ' ')} to ${quantityAdjustment.toStatus.replace('_', ' ')}`}
                      />
                    </Grid>
                    <Grid item xs={12} md={8}>
                      <TextField
                        fullWidth
                        label='Reason for Change'
                        value={quantityAdjustment.reason}
                        onChange={e =>
                          setQuantityAdjustment({
                            ...quantityAdjustment,
                            reason: e.target.value
                          })
                        }
                        placeholder='Enter reason for quantity adjustment...'
                      />
                    </Grid>
                  </Grid>
                  <Box sx={{ mt: 2, p: 2, backgroundColor: 'background.paper', borderRadius: 1 }}>
                    <Typography variant='body2' color='text.secondary'>
                      <strong>Summary:</strong> Moving {quantityAdjustment.adjustQuantity} units from{' '}
                      <strong>{quantityAdjustment.fromStatus.replace('_', ' ').toUpperCase()}</strong> to{' '}
                      <strong>{quantityAdjustment.toStatus.replace('_', ' ').toUpperCase()}</strong>
                    </Typography>
                  </Box>
                </Card>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveEditAsset} variant='contained' color='primary' disabled={loading}>
            {loading ? 'Updating...' : 'Update Asset'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Asset Item Management Dialog */}
      <Dialog open={transferDialogOpen} onClose={() => setTransferDialogOpen(false)} maxWidth='lg' fullWidth>
        <DialogTitle>Asset Item Management</DialogTitle>
        <DialogContent>
          <Typography variant='body2' color='text.secondary' sx={{ mb: 3 }}>
            Select an asset to view and manage individual asset items. Each item has a unique asset number that can be
            printed and attached to the physical asset.
          </Typography>

          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Select Asset</InputLabel>
                <Select
                  value={selectedAssetForTransfer}
                  label='Select Asset'
                  onChange={async e => {
                    const assetId = e.target.value as string
                    setSelectedAssetForTransfer(assetId)
                    if (assetId) {
                      await handleLoadAssetItems(assetId)
                    } else {
                      setAssetItems([])
                    }
                    setSelectedItems([])
                  }}
                >
                  {assets.map(asset => (
                    <MenuItem key={asset.id} value={asset.id}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                        <Typography variant='body1' sx={{ fontWeight: 600 }}>
                          {asset.name}
                        </Typography>
                        <Typography variant='body2' color='text.secondary'>
                          ({asset.assetNumber})
                        </Typography>
                        <Box sx={{ ml: 'auto' }}>
                          <Typography variant='caption' color='text.secondary'>
                            Total: {asset.quantity} units
                          </Typography>
                        </Box>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {selectedAssetForTransfer && (
              <>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant='h6'>Individual Asset Items</Typography>
                    <Button
                      variant='outlined'
                      size='small'
                      onClick={() => handleLoadAssetItems(selectedAssetForTransfer)}
                      startIcon={<RefreshIcon />}
                    >
                      Refresh
                    </Button>
                  </Box>
                </Grid>

                {!assetItems || assetItems.length === 0 ? (
                  <Grid item xs={12}>
                    <Box sx={{ p: 3, bgcolor: 'grey.50', borderRadius: 2, textAlign: 'center' }}>
                      <Typography variant='body1' color='text.secondary' sx={{ mb: 2 }}>
                        No individual asset items found for this asset.
                      </Typography>
                      <Typography variant='body2' color='text.secondary' sx={{ mb: 2 }}>
                        Individual asset items are created automatically when you create an asset with quantity &gt; 0.
                      </Typography>
                      <Button
                        variant='contained'
                        color='primary'
                        onClick={async () => {
                          try {
                            // Create asset items for the selected asset
                            const asset = assets.find(a => a.id === selectedAssetForTransfer)
                            if (asset) {
                              await assetItemService.createAssetItems(asset.id, {
                                quantity: asset.quantity
                              })
                              await handleLoadAssetItems(selectedAssetForTransfer)
                            }
                          } catch (err) {
                            console.error('Failed to create asset items:', err)
                            setError('Failed to create asset items. Please try again.')
                          }
                        }}
                      >
                        Create Asset Items
                      </Button>
                    </Box>
                  </Grid>
                ) : (
                  <>
                    <Grid item xs={12}>
                      <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
                        <Table stickyHeader size='small'>
                          <TableHead>
                            <TableRow>
                              <TableCell padding='checkbox'>
                                <Checkbox
                                  indeterminate={
                                    selectedItems.length > 0 && selectedItems.length < (assetItems?.length || 0)
                                  }
                                  checked={
                                    (assetItems?.length || 0) > 0 && selectedItems.length === (assetItems?.length || 0)
                                  }
                                  onChange={e => {
                                    if (e.target.checked) {
                                      setSelectedItems(assetItems?.map(item => item.id) || [])
                                    } else {
                                      setSelectedItems([])
                                    }
                                  }}
                                />
                              </TableCell>
                              <TableCell>Asset Number</TableCell>
                              <TableCell>Status</TableCell>
                              <TableCell>Condition</TableCell>
                              <TableCell>Assigned To</TableCell>
                              <TableCell>Serial Number</TableCell>
                              <TableCell>Actions</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {(assetItems || []).map(item => (
                              <TableRow key={item.id} hover>
                                <TableCell padding='checkbox'>
                                  <Checkbox
                                    checked={selectedItems.includes(item.id)}
                                    onChange={e => {
                                      if (e.target.checked) {
                                        setSelectedItems([...selectedItems, item.id])
                                      } else {
                                        setSelectedItems(selectedItems.filter(id => id !== item.id))
                                      }
                                    }}
                                  />
                                </TableCell>
                                <TableCell>
                                  <Typography variant='body2' fontWeight='600'>
                                    {item.assetNumber}
                                  </Typography>
                                </TableCell>
                                <TableCell>
                                  <Chip
                                    label={item.status.replace('_', ' ').toUpperCase()}
                                    color={statusColors[item.status as keyof typeof statusColors]}
                                    size='small'
                                    sx={{ fontWeight: 600, fontSize: '0.75rem' }}
                                  />
                                </TableCell>
                                <TableCell>
                                  <Chip
                                    label={item.condition.toUpperCase()}
                                    color={conditionColors[item.condition as keyof typeof conditionColors]}
                                    size='small'
                                    variant='outlined'
                                    sx={{ fontWeight: 500, fontSize: '0.75rem' }}
                                  />
                                </TableCell>
                                <TableCell>
                                  {item.assignedTo ? (
                                    <Typography variant='body2'>
                                      {item.assignedTo.firstName} {item.assignedTo.lastName}
                                    </Typography>
                                  ) : (
                                    <Typography variant='body2' color='text.secondary'>
                                      Unassigned
                                    </Typography>
                                  )}
                                </TableCell>
                                <TableCell>
                                  <Typography variant='body2' color='text.secondary'>
                                    {item.serialNumber || '-'}
                                  </Typography>
                                </TableCell>
                                <TableCell>
                                  <Box sx={{ display: 'flex', gap: 1 }}>
                                    <IconButton
                                      size='small'
                                      onClick={() => handleEditAssetItem(item)}
                                      title='Edit Asset Item'
                                    >
                                      <EditIcon fontSize='small' />
                                    </IconButton>
                                    <IconButton
                                      size='small'
                                      color='primary'
                                      onClick={() => handlePrintAssetLabel(item)}
                                      title='Print Asset Label'
                                    >
                                      <PrintIcon fontSize='small' />
                                    </IconButton>
                                    <IconButton
                                      size='small'
                                      color='error'
                                      onClick={() => handleDeleteAssetItem(item.id)}
                                      title='Delete Asset Item'
                                    >
                                      <DeleteIcon fontSize='small' />
                                    </IconButton>
                                  </Box>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Grid>

                    {selectedItems.length > 0 && (
                      <Grid item xs={12}>
                        <Paper sx={{ p: 2, bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.200' }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                            <Typography variant='subtitle2'>
                              Bulk Operations ({selectedItems.length} items selected)
                            </Typography>
                            <Button
                              variant='outlined'
                              size='small'
                              startIcon={<PrintIcon />}
                              onClick={handlePrintSelectedLabels}
                              sx={{ ml: 2 }}
                            >
                              Print Labels
                            </Button>
                          </Box>
                          <Typography variant='body2' sx={{ mb: 2, color: 'text.secondary' }}>
                            Transfer Status
                          </Typography>
                          <Grid container spacing={2} alignItems='center'>
                            <Grid item xs={12} md={3}>
                              <FormControl fullWidth size='small'>
                                <InputLabel>To Status</InputLabel>
                                <Select
                                  value={transferData.toStatus}
                                  label='To Status'
                                  onChange={e => setTransferData({ ...transferData, toStatus: e.target.value })}
                                >
                                  <MenuItem value='in_stock'>In Stock</MenuItem>
                                  <MenuItem value='in_use'>In Use</MenuItem>
                                  <MenuItem value='maintenance'>Maintenance</MenuItem>
                                  <MenuItem value='retired'>Retired</MenuItem>
                                  <MenuItem value='lost'>Lost</MenuItem>
                                  <MenuItem value='damaged'>Damaged</MenuItem>
                                </Select>
                              </FormControl>
                            </Grid>
                            <Grid item xs={12} md={3}>
                              <FormControl fullWidth size='small'>
                                <InputLabel>Assign To User</InputLabel>
                                <Select
                                  value={transferData.assignedToId}
                                  label='Assign To User'
                                  onChange={e => setTransferData({ ...transferData, assignedToId: e.target.value })}
                                >
                                  <MenuItem value=''>Unassigned</MenuItem>
                                  {users.map(user => (
                                    <MenuItem key={user.id} value={user.id}>
                                      {user.firstName} {user.lastName}
                                    </MenuItem>
                                  ))}
                                </Select>
                              </FormControl>
                            </Grid>
                            <Grid item xs={12} md={4}>
                              <TextField
                                fullWidth
                                size='small'
                                label='Reason'
                                value={transferData.reason}
                                onChange={e => setTransferData({ ...transferData, reason: e.target.value })}
                                placeholder='Enter reason for transfer...'
                              />
                            </Grid>
                            <Grid item xs={12} md={2}>
                              <Button
                                fullWidth
                                variant='contained'
                                color='primary'
                                onClick={handleTransferAssetItems}
                                disabled={selectedItems.length === 0}
                              >
                                Transfer
                              </Button>
                            </Grid>
                          </Grid>
                        </Paper>
                      </Grid>
                    )}
                  </>
                )}
              </>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTransferDialogOpen(false)}>Close</Button>
          {selectedAssetForTransfer && (!assetItems || assetItems.length === 0) && (
            <Button
              variant='contained'
              color='primary'
              onClick={async () => {
                try {
                  // Create asset items for the selected asset
                  const asset = assets.find(a => a.id === selectedAssetForTransfer)
                  if (asset) {
                    await assetItemService.createAssetItems(asset.id, {
                      quantity: asset.quantity
                    })
                    await handleLoadAssetItems(selectedAssetForTransfer)
                  }
                } catch (err) {
                  console.error('Failed to create asset items:', err)
                  setError('Failed to create asset items. Please try again.')
                }
              }}
            >
              Create Asset Items
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Edit Asset Item Dialog */}
      <Dialog open={editItemDialogOpen} onClose={() => setEditItemDialogOpen(false)} maxWidth='md' fullWidth>
        <DialogTitle>Edit Asset Item</DialogTitle>
        <DialogContent>
          {selectedAssetItem && (
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label='Asset Number'
                  value={selectedAssetItem.assetNumber}
                  disabled
                  helperText='Asset number cannot be changed'
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label='Serial Number'
                  value={editItemData.serialNumber}
                  onChange={e => setEditItemData({ ...editItemData, serialNumber: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={editItemData.status}
                    label='Status'
                    onChange={e => setEditItemData({ ...editItemData, status: e.target.value })}
                  >
                    <MenuItem value='in_stock'>In Stock</MenuItem>
                    <MenuItem value='in_use'>In Use</MenuItem>
                    <MenuItem value='maintenance'>Maintenance</MenuItem>
                    <MenuItem value='retired'>Retired</MenuItem>
                    <MenuItem value='lost'>Lost</MenuItem>
                    <MenuItem value='damaged'>Damaged</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Condition</InputLabel>
                  <Select
                    value={editItemData.condition}
                    label='Condition'
                    onChange={e => setEditItemData({ ...editItemData, condition: e.target.value })}
                  >
                    <MenuItem value='excellent'>Excellent</MenuItem>
                    <MenuItem value='good'>Good</MenuItem>
                    <MenuItem value='fair'>Fair</MenuItem>
                    <MenuItem value='poor'>Poor</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Assigned To</InputLabel>
                  <Select
                    value={editItemData.assignedToId}
                    label='Assigned To'
                    onChange={e => setEditItemData({ ...editItemData, assignedToId: e.target.value })}
                  >
                    <MenuItem value=''>Unassigned</MenuItem>
                    {users.map(user => (
                      <MenuItem key={user.id} value={user.id}>
                        {user.firstName} {user.lastName}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label='Notes'
                  multiline
                  rows={3}
                  value={editItemData.notes}
                  onChange={e => setEditItemData({ ...editItemData, notes: e.target.value })}
                  placeholder='Enter any notes about this asset item...'
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditItemDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveAssetItem} variant='contained' color='primary'>
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}
