'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON>graphy,
  Button,
  TextField,
  Grid,
  Box,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  Tooltip,
  Avatar,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress
} from '@mui/material'
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  MoreVert as MoreVertIcon,
  Person as PersonIcon,
  AdminPanelSettings as AdminIcon,
  ManageAccounts as ManagerIcon,
  RemoveRedEye as ViewerIcon
} from '@mui/icons-material'
import { usersService } from '@/lib/api'
import { User } from '@/types/api'
import { useAuth } from '../../../contexts/AuthContext'

// Mock data for development (fallback)
const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    status: 'active',
    department: 'IT',
    phoneNumber: '+**********',
    lastLoginAt: '2024-12-01T10:30:00Z',
    createdAt: '2024-01-15T09:00:00Z',
    assignedAssets: 3
  },
  {
    id: '2',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    role: 'manager',
    status: 'active',
    department: 'Operations',
    phoneNumber: '+**********',
    lastLoginAt: '2024-12-01T08:15:00Z',
    createdAt: '2024-02-01T10:00:00Z',
    assignedAssets: 5
  },
  {
    id: '3',
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Smith',
    role: 'viewer',
    status: 'active',
    department: 'HR',
    phoneNumber: '+1234567892',
    lastLoginAt: '2024-11-30T16:45:00Z',
    createdAt: '2024-03-10T11:30:00Z',
    assignedAssets: 2
  },
  {
    id: '4',
    email: '<EMAIL>',
    firstName: 'Mike',
    lastName: 'Johnson',
    role: 'viewer',
    status: 'inactive',
    department: 'Finance',
    phoneNumber: '+1234567893',
    lastLoginAt: '2024-11-25T14:20:00Z',
    createdAt: '2024-04-05T13:15:00Z',
    assignedAssets: 0
  }
]

const roleColors = {
  admin: 'error',
  manager: 'warning',
  viewer: 'info'
} as const

const statusColors = {
  active: 'success',
  inactive: 'secondary',
  suspended: 'error'
} as const

const getRoleIcon = (role: string) => {
  switch (role) {
    case 'admin':
      return <AdminIcon />
    case 'manager':
      return <ManagerIcon />
    case 'viewer':
      return <ViewerIcon />
    default:
      return <PersonIcon />
  }
}

export default function UsersPage() {
  const { user: currentUser } = useAuth()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterRole, setFilterRole] = useState('')
  const [filterStatus, setFilterStatus] = useState('')
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(10)

  // Load users from API
  useEffect(() => {
    const loadUsers = async () => {
      try {
        setLoading(true)
        setError(null)

        const data = await usersService.getUsers()
        setUsers(data)
      } catch (err: any) {
        console.error('Failed to load users:', err)
        if (err.message?.includes('401') || err.message?.includes('Unauthorized')) {
          setError('Authentication failed. Please log in again.')
          setTimeout(() => {
            window.location.href = '/login'
          }, 2000)
        } else {
          setError('Failed to load users. Please check if the backend server is running and try again.')
        }
      } finally {
        setLoading(false)
      }
    }

    loadUsers()
  }, [])
  const [selectedUser, setSelectedUser] = useState<any>(null)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [addDialogOpen, setAddDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [changePasswordDialogOpen, setChangePasswordDialogOpen] = useState(false)
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null)

  // Form states
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    password: '',
    confirmPassword: '',
    role: 'viewer',
    status: 'active',
    department: '',
    phoneNumber: ''
  })

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  const [formErrors, setFormErrors] = useState<Record<string, string>>({})

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value)
  }

  const resetForm = () => {
    setFormData({
      email: '',
      firstName: '',
      lastName: '',
      password: '',
      confirmPassword: '',
      role: 'viewer',
      status: 'active',
      department: '',
      phoneNumber: ''
    })
    setFormErrors({})
  }

  const resetPasswordForm = () => {
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
    setFormErrors({})
  }

  const validateForm = () => {
    const errors: Record<string, string> = {}

    if (!formData.email) errors.email = 'Email is required'
    else if (!/\S+@\S+\.\S+/.test(formData.email)) errors.email = 'Email is invalid'

    if (!formData.firstName) errors.firstName = 'First name is required'
    if (!formData.lastName) errors.lastName = 'Last name is required'

    // Only validate password fields when adding a new user (not editing)
    if (addDialogOpen && !editDialogOpen) {
      if (!formData.password) errors.password = 'Password is required'
      else if (formData.password.length < 6) errors.password = 'Password must be at least 6 characters'

      if (formData.password !== formData.confirmPassword) {
        errors.confirmPassword = 'Passwords do not match'
      }
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const validatePasswordForm = () => {
    const errors: Record<string, string> = {}

    // Check if current user is admin and changing another user's password
    const isAdminChangingOtherUser = currentUser?.role === 'admin' && selectedUser && currentUser.id !== selectedUser.id

    // Only require current password if not admin changing another user's password
    if (!isAdminChangingOtherUser && !passwordData.currentPassword) {
      errors.currentPassword = 'Current password is required'
    }

    if (!passwordData.newPassword) errors.newPassword = 'New password is required'
    else if (passwordData.newPassword.length < 6) errors.newPassword = 'Password must be at least 6 characters'

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleAddUser = () => {
    resetForm()
    setAddDialogOpen(true)
  }

  const handleEditUser = (user: any) => {
    setSelectedUser(user)
    setFormData({
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      password: '', // Keep for form state but won't be sent to backend
      confirmPassword: '', // Keep for form state but won't be sent to backend
      role: user.role,
      status: user.status,
      department: user.department || '',
      phoneNumber: user.phoneNumber || ''
    })
    setEditDialogOpen(true)
  }

  const handleChangePassword = (user: any) => {
    setSelectedUser(user)
    resetPasswordForm()
    setChangePasswordDialogOpen(true)
  }

  const handleViewUser = (user: any) => {
    setSelectedUser(user)
    setViewDialogOpen(true)
  }

  const handleDeleteUser = (user: any) => {
    setSelectedUser(user)
    setDeleteDialogOpen(true)
  }

  const handleSaveUser = async () => {
    if (!validateForm()) return

    console.log('Frontend: handleSaveUser called')
    console.log('Frontend: editDialogOpen:', editDialogOpen)
    console.log('Frontend: addDialogOpen:', addDialogOpen)
    console.log('Frontend: selectedUser:', selectedUser)

    try {
      if (editDialogOpen && selectedUser) {
        // Update existing user
        const updateData = {
          email: formData.email,
          firstName: formData.firstName,
          lastName: formData.lastName,
          role: formData.role as any,
          status: formData.status as any,
          department: formData.department || undefined,
          phoneNumber: formData.phoneNumber || undefined
        }
        console.log('Frontend: Sending update data:', updateData)
        const updatedUser = await usersService.updateUser(selectedUser.id, updateData)

        setUsers(users.map(user => (user.id === selectedUser.id ? updatedUser : user)))
        setEditDialogOpen(false)
        setSelectedUser(null)
      } else {
        // Create new user
        const newUser = await usersService.createUser({
          email: formData.email,
          firstName: formData.firstName,
          lastName: formData.lastName,
          password: formData.password,
          role: formData.role as any,
          status: formData.status as any,
          department: formData.department || undefined,
          phoneNumber: formData.phoneNumber || undefined
        })

        setUsers([...users, newUser])
        setAddDialogOpen(false)
      }

      setSelectedUser(null)
      resetForm()
    } catch (err: any) {
      console.error('Failed to save user:', err)
      setError('Failed to save user. Please try again.')
    }
  }

  const handleSavePassword = async () => {
    if (!validatePasswordForm() || !selectedUser) return

    try {
      // Check if current user is admin and changing another user's password
      const isAdminChangingOtherUser = currentUser?.role === 'admin' && currentUser.id !== selectedUser.id

      const changePasswordData: any = {
        newPassword: passwordData.newPassword
      }

      // Only include current password if not admin changing another user's password
      if (!isAdminChangingOtherUser) {
        changePasswordData.currentPassword = passwordData.currentPassword
      }

      await usersService.changePassword(selectedUser.id, changePasswordData)

      setChangePasswordDialogOpen(false)
      setSelectedUser(null)
      resetPasswordForm()
    } catch (err: any) {
      console.error('Failed to change password:', err)
      setError('Failed to change password. Please try again.')
    }
  }

  const confirmDelete = async () => {
    if (!selectedUser) return

    try {
      await usersService.deleteUser(selectedUser.id)
      setUsers(users.filter(user => user.id !== selectedUser.id))
      setDeleteDialogOpen(false)
      setSelectedUser(null)
    } catch (err: any) {
      console.error('Failed to delete user:', err)
      setError('Failed to delete user. Please try again.')
    }
  }

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, user: any) => {
    setMenuAnchor(event.currentTarget)
    setSelectedUser(user)
  }

  const handleMenuClose = () => {
    setMenuAnchor(null)
    // Don't clear selectedUser if a dialog is open
    if (!editDialogOpen && !changePasswordDialogOpen && !deleteDialogOpen && !viewDialogOpen) {
      setSelectedUser(null)
    }
  }

  const toggleUserStatus = (userId: string) => {
    setUsers(
      users.map(user =>
        user.id === userId ? { ...user, status: user.status === 'active' ? 'inactive' : 'active' } : user
      )
    )
  }

  const filteredUsers = users.filter(user => {
    const matchesSearch =
      user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.department && user.department.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesRole = !filterRole || user.role === filterRole
    const matchesStatus = !filterStatus || user.status === filterStatus

    return matchesSearch && matchesRole && matchesStatus
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const formatLastLogin = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 24) {
      return `${diffInHours} hours ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays} days ago`
    }
  }

  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading users...</Typography>
      </Box>
    )
  }

  return (
    <Box sx={{ p: 4, backgroundColor: 'grey.50', minHeight: '100vh' }}>
      {/* Error Alert */}
      {error && (
        <Alert severity='error' sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant='h4' component='h1'>
          User Management
        </Typography>
        <Button variant='contained' startIcon={<AddIcon />} color='primary' onClick={handleAddUser}>
          Add User
        </Button>
      </Box>

      {/* Search and Filters */}
      <Card
        sx={{
          border: '1px solid',
          borderColor: 'divider',
          boxShadow: 'none',
          mb: 3
        }}
      >
        <CardContent>
          <Grid container spacing={3} alignItems='center'>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder='Search users...'
                value={searchTerm}
                onChange={handleSearch}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select value={filterRole} label='Role' onChange={e => setFilterRole(e.target.value)}>
                  <MenuItem value=''>All Roles</MenuItem>
                  <MenuItem value='admin'>Admin</MenuItem>
                  <MenuItem value='manager'>Manager</MenuItem>
                  <MenuItem value='viewer'>Viewer</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select value={filterStatus} label='Status' onChange={e => setFilterStatus(e.target.value)}>
                  <MenuItem value=''>All Statuses</MenuItem>
                  <MenuItem value='active'>Active</MenuItem>
                  <MenuItem value='inactive'>Inactive</MenuItem>
                  <MenuItem value='suspended'>Suspended</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant='outlined'
                onClick={() => {
                  setFilterRole('')
                  setFilterStatus('')
                  setSearchTerm('')
                }}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card
        sx={{
          border: '1px solid',
          borderColor: 'divider',
          boxShadow: 'none'
        }}
      >
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>User</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Role</TableCell>
                <TableCell>Department</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Assets</TableCell>
                <TableCell>Last Login</TableCell>
                <TableCell align='center'>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredUsers.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map(user => (
                <TableRow key={user.id} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box>
                        <Typography variant='body2' fontWeight='medium'>
                          {user.firstName} {user.lastName}
                        </Typography>
                        <Typography variant='caption' color='text.secondary'>
                          ID: {user.id}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {getRoleIcon(user.role)}
                      <Chip
                        label={user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                        color={roleColors[user.role as keyof typeof roleColors]}
                        size='small'
                        sx={{ ml: 1 }}
                      />
                    </Box>
                  </TableCell>
                  <TableCell>{user.department || 'N/A'}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Chip
                        label={user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                        color={statusColors[user.status as keyof typeof statusColors]}
                        size='small'
                      />
                      <Switch
                        checked={user.status === 'active'}
                        onChange={() => toggleUserStatus(user.id)}
                        size='small'
                        sx={{ ml: 1 }}
                      />
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant='body2'>{user.assignedAssets} assets</Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant='body2'>{formatLastLogin(user.lastLoginAt)}</Typography>
                  </TableCell>
                  <TableCell align='center'>
                    <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                      <Tooltip title='View Details'>
                        <IconButton size='small' onClick={() => handleViewUser(user)}>
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      <IconButton size='small' onClick={e => handleMenuClick(e, user)}>
                        <MoreVertIcon />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component='div'
          count={filteredUsers.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(_, newPage) => setPage(newPage)}
          onRowsPerPageChange={e => {
            setRowsPerPage(parseInt(e.target.value, 10))
            setPage(0)
          }}
        />
      </Card>

      {/* Action Menu */}
      <Menu anchorEl={menuAnchor} open={Boolean(menuAnchor)} onClose={handleMenuClose}>
        <MenuItem
          onClick={() => {
            handleEditUser(selectedUser)
            handleMenuClose()
          }}
        >
          <EditIcon sx={{ mr: 1 }} /> Edit
        </MenuItem>
        {/* Only show Change Password option for admin users */}
        {currentUser?.role === 'admin' && (
          <MenuItem
            onClick={() => {
              handleChangePassword(selectedUser)
              handleMenuClose()
            }}
          >
            <PersonIcon sx={{ mr: 1 }} /> Change Password
          </MenuItem>
        )}
        <MenuItem
          onClick={() => {
            handleDeleteUser(selectedUser)
            handleMenuClose()
          }}
        >
          <DeleteIcon sx={{ mr: 1 }} /> Delete
        </MenuItem>
      </Menu>

      {/* View User Dialog */}
      <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth='md' fullWidth>
        <DialogTitle>User Details</DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ mr: 2, width: 64, height: 64, bgcolor: 'primary.main' }}>
                  {selectedUser.firstName[0]}
                  {selectedUser.lastName[0]}
                </Avatar>
                <Box>
                  <Typography variant='h6'>
                    {selectedUser.firstName} {selectedUser.lastName}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    {selectedUser.email}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Role</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {getRoleIcon(selectedUser.role)}
                  <Chip
                    label={selectedUser.role.charAt(0).toUpperCase() + selectedUser.role.slice(1)}
                    color={roleColors[selectedUser.role as keyof typeof roleColors]}
                    size='small'
                    sx={{ ml: 1 }}
                  />
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Status</Typography>
                <Chip
                  label={selectedUser.status.charAt(0).toUpperCase() + selectedUser.status.slice(1)}
                  color={statusColors[selectedUser.status as keyof typeof statusColors]}
                  size='small'
                />
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Department</Typography>
                <Typography variant='body1'>{selectedUser.department || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Phone Number</Typography>
                <Typography variant='body1'>{selectedUser.phoneNumber || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Assigned Assets</Typography>
                <Typography variant='body1'>{selectedUser.assignedAssets} assets</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Last Login</Typography>
                <Typography variant='body1'>{formatLastLogin(selectedUser.lastLoginAt)}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant='subtitle2'>Member Since</Typography>
                <Typography variant='body1'>{formatDate(selectedUser.createdAt)}</Typography>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Add User Dialog */}
      <Dialog open={addDialogOpen} onClose={() => setAddDialogOpen(false)} maxWidth='md' fullWidth>
        <DialogTitle>Add New User</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label='First Name'
                value={formData.firstName}
                onChange={e => setFormData({ ...formData, firstName: e.target.value })}
                error={!!formErrors.firstName}
                helperText={formErrors.firstName}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label='Last Name'
                value={formData.lastName}
                onChange={e => setFormData({ ...formData, lastName: e.target.value })}
                error={!!formErrors.lastName}
                helperText={formErrors.lastName}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Email'
                type='email'
                value={formData.email}
                onChange={e => setFormData({ ...formData, email: e.target.value })}
                error={!!formErrors.email}
                helperText={formErrors.email}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label='Password'
                type='password'
                value={formData.password}
                onChange={e => setFormData({ ...formData, password: e.target.value })}
                error={!!formErrors.password}
                helperText={formErrors.password}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label='Confirm Password'
                type='password'
                value={formData.confirmPassword}
                onChange={e => setFormData({ ...formData, confirmPassword: e.target.value })}
                error={!!formErrors.confirmPassword}
                helperText={formErrors.confirmPassword}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  value={formData.role}
                  label='Role'
                  onChange={e => setFormData({ ...formData, role: e.target.value })}
                >
                  <MenuItem value='admin'>Admin</MenuItem>
                  <MenuItem value='manager'>Manager</MenuItem>
                  <MenuItem value='viewer'>Viewer</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  label='Status'
                  onChange={e => setFormData({ ...formData, status: e.target.value })}
                >
                  <MenuItem value='active'>Active</MenuItem>
                  <MenuItem value='inactive'>Inactive</MenuItem>
                  <MenuItem value='suspended'>Suspended</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label='Department'
                value={formData.department}
                onChange={e => setFormData({ ...formData, department: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label='Phone Number'
                value={formData.phoneNumber}
                onChange={e => setFormData({ ...formData, phoneNumber: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveUser} variant='contained'>
            Add User
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={() => {
          setEditDialogOpen(false)
          setSelectedUser(null)
        }}
        maxWidth='md'
        fullWidth
      >
        <DialogTitle>Edit User</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label='First Name'
                value={formData.firstName}
                onChange={e => setFormData({ ...formData, firstName: e.target.value })}
                error={!!formErrors.firstName}
                helperText={formErrors.firstName}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label='Last Name'
                value={formData.lastName}
                onChange={e => setFormData({ ...formData, lastName: e.target.value })}
                error={!!formErrors.lastName}
                helperText={formErrors.lastName}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Email'
                type='email'
                value={formData.email}
                onChange={e => setFormData({ ...formData, email: e.target.value })}
                error={!!formErrors.email}
                helperText={formErrors.email}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  value={formData.role}
                  label='Role'
                  onChange={e => setFormData({ ...formData, role: e.target.value })}
                >
                  <MenuItem value='admin'>Admin</MenuItem>
                  <MenuItem value='manager'>Manager</MenuItem>
                  <MenuItem value='viewer'>Viewer</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  label='Status'
                  onChange={e => setFormData({ ...formData, status: e.target.value })}
                >
                  <MenuItem value='active'>Active</MenuItem>
                  <MenuItem value='inactive'>Inactive</MenuItem>
                  <MenuItem value='suspended'>Suspended</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label='Department'
                value={formData.department}
                onChange={e => setFormData({ ...formData, department: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label='Phone Number'
                value={formData.phoneNumber}
                onChange={e => setFormData({ ...formData, phoneNumber: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveUser} variant='contained'>
            Update User
          </Button>
        </DialogActions>
      </Dialog>

      {/* Change Password Dialog */}
      <Dialog
        open={changePasswordDialogOpen}
        onClose={() => setChangePasswordDialogOpen(false)}
        maxWidth='sm'
        fullWidth
      >
        <DialogTitle>Change Password</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <Typography variant='body2' color='text.secondary' sx={{ mb: 2 }}>
                Changing password for: {selectedUser?.firstName} {selectedUser?.lastName}
              </Typography>
              {currentUser?.role === 'admin' && selectedUser && currentUser.id !== selectedUser.id && (
                <Typography variant='body2' color='primary' sx={{ mb: 2, fontWeight: 'medium' }}>
                  As an admin, you can change this user's password without knowing their current password.
                </Typography>
              )}
            </Grid>
            {/* Only show current password field if not admin changing another user's password */}
            {!(currentUser?.role === 'admin' && selectedUser && currentUser.id !== selectedUser.id) && (
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label='Current Password'
                  type='password'
                  value={passwordData.currentPassword}
                  onChange={e => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
                  error={!!formErrors.currentPassword}
                  helperText={formErrors.currentPassword}
                  required
                />
              </Grid>
            )}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='New Password'
                type='password'
                value={passwordData.newPassword}
                onChange={e => setPasswordData({ ...passwordData, newPassword: e.target.value })}
                error={!!formErrors.newPassword}
                helperText={formErrors.newPassword}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Confirm New Password'
                type='password'
                value={passwordData.confirmPassword}
                onChange={e => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
                error={!!formErrors.confirmPassword}
                helperText={formErrors.confirmPassword}
                required
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setChangePasswordDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSavePassword} variant='contained'>
            Change Password
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete user "{selectedUser?.firstName} {selectedUser?.lastName}"? This action
            cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color='error' variant='contained'>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}
