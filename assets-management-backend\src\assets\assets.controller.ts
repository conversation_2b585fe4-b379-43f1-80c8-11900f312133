import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ParseU<PERSON><PERSON>ipe,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { AssetsService, AssetSearchOptions } from './assets.service';
import { CreateAssetDto } from './dto/create-asset.dto';
import { UpdateAssetDto } from './dto/update-asset.dto';
import { AssignAssetDto } from './dto/assign-asset.dto';
import {
  TransferQuantityDto,
  BulkTransferQuantityDto,
  SetAssetQuantityDto,
} from './dto/transfer-quantity.dto';
import { AssetQuantityService } from './asset-quantity.service';
import {
  AssetItemService,
  CreateAssetItemDto,
  UpdateAssetItemDto,
  TransferAssetItemDto,
} from './asset-item.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { UserRole } from '../entities/user.entity';
import { AssetStatus } from '../entities/asset.entity';

@ApiTags('Assets')
@Controller('assets')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class AssetsController {
  constructor(
    private readonly assetsService: AssetsService,
    private readonly assetQuantityService: AssetQuantityService,
    private readonly assetItemService: AssetItemService,
  ) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Create a new asset (Admin/Manager only)' })
  @ApiResponse({
    status: 201,
    description: 'Asset successfully created',
  })
  @ApiResponse({
    status: 404,
    description: 'Category or Location not found',
  })
  async create(
    @Body() createAssetDto: CreateAssetDto,
    @CurrentUser() user: any,
  ) {
    const asset = await this.assetsService.create(createAssetDto);

    // Automatically create individual asset items if quantity > 0
    if (asset.quantity > 0) {
      try {
        await this.assetItemService.createMultipleAssetItems(
          asset.id,
          asset.quantity,
          user.id,
        );
      } catch (error) {
        console.error('Failed to create asset items:', error);
        // Don't fail the asset creation if item creation fails
      }
    }

    return asset;
  }

  @Get()
  @ApiOperation({ summary: 'Get all assets with filtering and pagination' })
  @ApiQuery({
    name: 'search',
    required: false,
    description:
      'Search term for name, description, asset number, serial number, manufacturer, model',
  })
  @ApiQuery({
    name: 'categoryId',
    required: false,
    description: 'Filter by category ID',
  })
  @ApiQuery({
    name: 'locationId',
    required: false,
    description: 'Filter by location ID',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: AssetStatus,
    description: 'Filter by status',
  })
  @ApiQuery({
    name: 'condition',
    required: false,
    description: 'Filter by condition (excellent, good, fair, poor)',
  })
  @ApiQuery({
    name: 'assignedToId',
    required: false,
    description: 'Filter by assigned user ID',
  })
  @ApiQuery({
    name: 'manufacturer',
    required: false,
    description: 'Filter by manufacturer',
  })
  @ApiQuery({ name: 'model', required: false, description: 'Filter by model' })
  @ApiQuery({
    name: 'minValue',
    required: false,
    type: Number,
    description: 'Minimum total value',
  })
  @ApiQuery({
    name: 'maxValue',
    required: false,
    type: Number,
    description: 'Maximum total value',
  })
  @ApiQuery({
    name: 'purchaseDateFrom',
    required: false,
    description: 'Purchase date from (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'purchaseDateTo',
    required: false,
    description: 'Purchase date to (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    description: 'Sort field (default: createdAt)',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort order (default: DESC)',
  })
  @ApiResponse({
    status: 200,
    description: 'Assets retrieved successfully',
  })
  findAll(@Query() query: AssetSearchOptions) {
    return this.assetsService.findAll(query);
  }

  @Get('stats')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get asset statistics (Admin/Manager only)' })
  @ApiResponse({
    status: 200,
    description: 'Asset statistics retrieved successfully',
  })
  getStats() {
    return this.assetsService.getAssetStats();
  }

  @Get('by-asset-number/:assetNumber')
  @ApiOperation({
    summary: 'Get asset item by asset number (Public - No Auth Required)',
  })
  @ApiResponse({
    status: 200,
    description: 'Asset item found',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset item not found',
  })
  async findByAssetNumber(@Param('assetNumber') assetNumber: string) {
    return this.assetItemService.findByAssetNumber(assetNumber);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get asset by ID' })
  @ApiResponse({
    status: 200,
    description: 'Asset retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset not found',
  })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.assetsService.findOne(id);
  }

  @Patch(':id')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Update asset (Admin/Manager only)' })
  @ApiResponse({
    status: 200,
    description: 'Asset updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset not found',
  })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateAssetDto: UpdateAssetDto,
  ) {
    return this.assetsService.update(id, updateAssetDto);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Delete asset (Admin/Manager only)' })
  @ApiResponse({
    status: 200,
    description: 'Asset deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset not found',
  })
  remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.assetsService.remove(id);
  }

  @Post(':id/assign')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Assign asset to user (Admin/Manager only)' })
  @ApiResponse({
    status: 200,
    description: 'Asset assigned successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset or User not found',
  })
  assignAsset(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() assignAssetDto: AssignAssetDto,
  ) {
    return this.assetsService.assignAsset(id, assignAssetDto);
  }

  @Post(':id/unassign')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Unassign asset from user (Admin/Manager only)' })
  @ApiResponse({
    status: 200,
    description: 'Asset unassigned successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset not found',
  })
  unassignAsset(@Param('id', ParseUUIDPipe) id: string) {
    return this.assetsService.unassignAsset(id);
  }

  // Quantity Management Endpoints

  @Get(':id/quantities')
  @ApiOperation({ summary: 'Get asset quantities by status' })
  @ApiResponse({
    status: 200,
    description: 'Asset quantities retrieved successfully',
  })
  getAssetQuantities(@Param('id', ParseUUIDPipe) id: string) {
    return this.assetQuantityService.getAssetQuantitiesByStatus(id);
  }

  @Post('quantities/transfer')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({
    summary: 'Transfer asset quantities between statuses (Admin/Manager only)',
  })
  @ApiResponse({
    status: 200,
    description: 'Quantity transfer completed successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Insufficient quantity or invalid transfer',
  })
  transferQuantity(
    @Body() transferDto: TransferQuantityDto,
    @CurrentUser() user: any,
  ) {
    return this.assetQuantityService.transferQuantity(transferDto, user.id);
  }

  @Post('quantities/bulk-transfer')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({
    summary: 'Bulk transfer asset quantities (Admin/Manager only)',
  })
  @ApiResponse({
    status: 200,
    description: 'Bulk quantity transfer completed successfully',
  })
  bulkTransferQuantity(
    @Body() bulkTransferDto: BulkTransferQuantityDto,
    @CurrentUser() user: any,
  ) {
    return this.assetQuantityService.bulkTransferQuantity(
      bulkTransferDto,
      user.id,
    );
  }

  @Post('quantities/set')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({
    summary: 'Set asset quantities by status (Admin/Manager only)',
  })
  @ApiResponse({
    status: 200,
    description: 'Asset quantities set successfully',
  })
  setAssetQuantities(
    @Body() setQuantityDto: SetAssetQuantityDto,
    @CurrentUser() user: any,
  ) {
    return this.assetQuantityService.setAssetQuantities(
      setQuantityDto,
      user.id,
    );
  }

  @Get(':id/transfer-history')
  @ApiOperation({ summary: 'Get asset quantity transfer history' })
  @ApiResponse({
    status: 200,
    description: 'Transfer history retrieved successfully',
  })
  getTransferHistory(@Param('id', ParseUUIDPipe) id: string) {
    return this.assetQuantityService.getTransferHistory(id);
  }

  @Get('quantities/all')
  @ApiOperation({ summary: 'Get all asset quantities overview' })
  @ApiResponse({
    status: 200,
    description: 'All asset quantities retrieved successfully',
  })
  getAllAssetQuantities() {
    return this.assetQuantityService.getAllAssetQuantities();
  }

  // Asset Item Management Endpoints

  @Post(':id/items')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({
    summary: 'Create asset items for an asset (Admin/Manager only)',
  })
  @ApiResponse({
    status: 201,
    description: 'Asset items created successfully',
  })
  createAssetItems(
    @Param('id', ParseUUIDPipe) assetId: string,
    @Body() createDto: { quantity: number },
    @CurrentUser() user: any,
  ) {
    return this.assetItemService.createMultipleAssetItems(
      assetId,
      createDto.quantity,
      user.id,
    );
  }

  @Get(':id/items')
  @ApiOperation({ summary: 'Get all asset items for an asset' })
  @ApiResponse({
    status: 200,
    description: 'Asset items retrieved successfully',
  })
  getAssetItems(@Param('id', ParseUUIDPipe) assetId: string) {
    return this.assetItemService.getAssetItemsByAssetId(assetId);
  }

  @Get('items/:itemId')
  @ApiOperation({ summary: 'Get asset item by ID' })
  @ApiResponse({
    status: 200,
    description: 'Asset item retrieved successfully',
  })
  getAssetItem(@Param('itemId', ParseUUIDPipe) itemId: string) {
    return this.assetItemService.getAssetItemById(itemId);
  }

  @Patch('items/:itemId')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Update asset item (Admin/Manager only)' })
  @ApiResponse({
    status: 200,
    description: 'Asset item updated successfully',
  })
  updateAssetItem(
    @Param('itemId', ParseUUIDPipe) itemId: string,
    @Body() updateDto: UpdateAssetItemDto,
    @CurrentUser() user: any,
  ) {
    return this.assetItemService.updateAssetItem(itemId, updateDto, user.id);
  }

  @Post('items/transfer')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Transfer asset item status (Admin/Manager only)' })
  @ApiResponse({
    status: 200,
    description: 'Asset item transferred successfully',
  })
  transferAssetItem(
    @Body() transferDto: TransferAssetItemDto,
    @CurrentUser() user: any,
  ) {
    return this.assetItemService.transferAssetItem(transferDto, user.id);
  }

  @Get(':id/items/quantities')
  @ApiOperation({ summary: 'Get asset item quantities by status' })
  @ApiResponse({
    status: 200,
    description: 'Asset item quantities retrieved successfully',
  })
  getAssetItemQuantities(@Param('id', ParseUUIDPipe) assetId: string) {
    return this.assetItemService.getAssetQuantitiesByStatus(assetId);
  }

  @Get('items/quantities/all')
  @ApiOperation({ summary: 'Get all asset item quantities overview' })
  @ApiResponse({
    status: 200,
    description: 'All asset item quantities retrieved successfully',
  })
  getAllAssetItemQuantities() {
    return this.assetItemService.getAllAssetQuantities();
  }

  @Delete('items/:itemId')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete asset item (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Asset item deleted successfully',
  })
  deleteAssetItem(@Param('itemId', ParseUUIDPipe) itemId: string) {
    return this.assetItemService.deleteAssetItem(itemId);
  }
}
