'use client'

import { useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import {
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Box,
  Alert,
  InputAdornment,
  IconButton,
  Link,
  Divider,
  Checkbox,
  FormControlLabel
} from '@mui/material'
import {
  Visibility,
  VisibilityOff,
  Email as EmailIcon,
  Lock as LockIcon,
  Business as BusinessIcon
} from '@mui/icons-material'
import { authService } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'

export default function LoginPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { login, isLoading, error, clearError } = useAuth()
  const returnUrl = searchParams.get('returnUrl')
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  })
  const [showPassword, setShowPassword] = useState(false)

  const handleInputChange = (field: string) => (event: any) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
    if (error) clearError()
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()

    try {
      await login({
        email: formData.email,
        password: formData.password
      })

      // Redirect to return URL if provided, otherwise to dashboard
      if (returnUrl) {
        router.push(decodeURIComponent(returnUrl))
      } else {
        router.push('/dashboard')
      }
    } catch (err: any) {
      console.error('Login error:', err)
      // Error is handled by the AuthContext
    }
  }

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'grey.50',
        p: 3
      }}
    >
      <Card sx={{ maxWidth: 400, width: '100%' }}>
        <CardContent sx={{ p: 4 }}>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <BusinessIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
            <Typography variant='h4' component='h1' gutterBottom>
              Asset Management
            </Typography>
            <Typography variant='body2' color='text.secondary'>
              Sign in to your account
            </Typography>
          </Box>

          {/* Return URL Alert */}
          {returnUrl && (
            <Alert severity='info' sx={{ mb: 3 }}>
              <Typography variant='body2'>You will be redirected to the asset details page after login.</Typography>
            </Alert>
          )}

          {/* Error Alert */}
          {error && (
            <Alert severity='error' sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Login Credentials */}
          <Alert severity='info' sx={{ mb: 3 }}>
            <Typography variant='body2' fontWeight='medium'>
              Login Credentials:
            </Typography>
            <Typography variant='body2'>Email: <EMAIL></Typography>
            <Typography variant='body2'>Password: password123</Typography>
          </Alert>

          {/* Login Form */}
          <form onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label='Email'
              type='email'
              value={formData.email}
              onChange={handleInputChange('email')}
              required
              sx={{ mb: 3 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position='start'>
                    <EmailIcon color='action' />
                  </InputAdornment>
                )
              }}
            />

            <TextField
              fullWidth
              label='Password'
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleInputChange('password')}
              required
              sx={{ mb: 3 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position='start'>
                    <LockIcon color='action' />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position='end'>
                    <IconButton onClick={() => setShowPassword(!showPassword)} edge='end'>
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />

            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <FormControlLabel
                control={
                  <Checkbox checked={formData.rememberMe} onChange={handleInputChange('rememberMe')} size='small' />
                }
                label='Remember me'
              />
              <Link href='/forgot-password' variant='body2'>
                Forgot password?
              </Link>
            </Box>

            <Button type='submit' fullWidth variant='contained' size='large' disabled={isLoading} sx={{ mb: 3 }}>
              {isLoading ? 'Signing in...' : 'Sign In'}
            </Button>

            <Divider sx={{ mb: 3 }}>
              <Typography variant='body2' color='text.secondary'>
                or
              </Typography>
            </Divider>

            <Box sx={{ textAlign: 'center' }}>
              <Typography variant='body2' color='text.secondary'>
                Don't have an account?{' '}
                <Link href='/register' variant='body2'>
                  Sign up
                </Link>
              </Typography>
            </Box>
          </form>
        </CardContent>
      </Card>
    </Box>
  )
}
