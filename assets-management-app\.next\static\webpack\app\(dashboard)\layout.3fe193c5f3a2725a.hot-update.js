"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/layout",{

/***/ "(app-pages-browser)/./src/configs/primaryColorConfig.ts":
/*!*******************************************!*\
  !*** ./src/configs/primaryColorConfig.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// Primary color config object\nconst primaryColorConfig = [\n    {\n        name: \"professional-blue\",\n        light: \"#5A9FD4\",\n        main: \"#4169E1\",\n        dark: \"#1E5A96\"\n    }\n];\n/* harmony default export */ __webpack_exports__[\"default\"] = (primaryColorConfig);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb25maWdzL3ByaW1hcnlDb2xvckNvbmZpZy50cyIsIm1hcHBpbmdzIjoiO0FBT0EsOEJBQThCO0FBQzlCLE1BQU1BLHFCQUEyQztJQUMvQztRQUNFQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxNQUFNO0lBQ1I7Q0FDRDtBQUVELCtEQUFlSixrQkFBa0JBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbmZpZ3MvcHJpbWFyeUNvbG9yQ29uZmlnLnRzP2VjMzMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHR5cGUgUHJpbWFyeUNvbG9yQ29uZmlnID0ge1xuICBuYW1lPzogc3RyaW5nXG4gIGxpZ2h0Pzogc3RyaW5nXG4gIG1haW46IHN0cmluZ1xuICBkYXJrPzogc3RyaW5nXG59XG5cbi8vIFByaW1hcnkgY29sb3IgY29uZmlnIG9iamVjdFxuY29uc3QgcHJpbWFyeUNvbG9yQ29uZmlnOiBQcmltYXJ5Q29sb3JDb25maWdbXSA9IFtcbiAge1xuICAgIG5hbWU6ICdwcm9mZXNzaW9uYWwtYmx1ZScsXG4gICAgbGlnaHQ6ICcjNUE5RkQ0JyxcbiAgICBtYWluOiAnIzQxNjlFMScsXG4gICAgZGFyazogJyMxRTVBOTYnXG4gIH1cbl1cblxuZXhwb3J0IGRlZmF1bHQgcHJpbWFyeUNvbG9yQ29uZmlnXG4iXSwibmFtZXMiOlsicHJpbWFyeUNvbG9yQ29uZmlnIiwibmFtZSIsImxpZ2h0IiwibWFpbiIsImRhcmsiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/configs/primaryColorConfig.ts\n"));

/***/ })

});