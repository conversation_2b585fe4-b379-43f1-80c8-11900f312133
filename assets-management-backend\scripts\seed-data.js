const axios = require('axios');

const API_BASE = 'http://localhost:3001';

// First, login to get a token
async function login() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    return response.data.token;
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
    throw error;
  }
}

// Create sample data
async function seedData() {
  try {
    console.log('🔐 Logging in...');
    const token = await login();
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    console.log('📁 Creating categories...');
    
    // Create categories
    const itCategory = await axios.post(`${API_BASE}/categories`, {
      name: 'IT Equipment',
      code: 'IT',
      description: 'Information Technology equipment and devices'
    }, { headers });

    const furnitureCategory = await axios.post(`${API_BASE}/categories`, {
      name: 'Furniture',
      code: 'FURN',
      description: 'Office furniture and fixtures'
    }, { headers });

    const vehicleCategory = await axios.post(`${API_BASE}/categories`, {
      name: 'Vehicles',
      code: 'VEH',
      description: 'Company vehicles and transportation'
    }, { headers });

    console.log('🏢 Creating locations...');
    
    // Create locations
    const mainOffice = await axios.post(`${API_BASE}/locations`, {
      name: 'Main Office',
      type: 'building',
      description: 'Main office building'
    }, { headers });

    const itDept = await axios.post(`${API_BASE}/locations`, {
      name: 'IT Department',
      type: 'department',
      description: 'Information Technology department',
      parentId: mainOffice.data.id
    }, { headers });

    const confRoom = await axios.post(`${API_BASE}/locations`, {
      name: 'Conference Room A',
      type: 'room',
      description: 'Main conference room',
      parentId: mainOffice.data.id
    }, { headers });

    console.log('💻 Creating sample assets...');
    
    // Create sample assets
    await axios.post(`${API_BASE}/assets`, {
      name: 'Dell Laptop XPS 13',
      description: 'High-performance laptop for development work',
      categoryId: itCategory.data.id,
      locationId: itDept.data.id,
      status: 'in_use',
      condition: 'good',
      unitPrice: 1299.99,
      quantity: 1,
      purchaseDate: '2024-01-15',
      warrantyExpiry: '2027-01-15',
      manufacturer: 'Dell',
      model: 'XPS 13',
      serialNumber: 'DL123456789'
    }, { headers });

    await axios.post(`${API_BASE}/assets`, {
      name: 'Office Chair Ergonomic',
      description: 'Comfortable ergonomic office chair',
      categoryId: furnitureCategory.data.id,
      locationId: confRoom.data.id,
      status: 'in_stock',
      condition: 'excellent',
      unitPrice: 299.99,
      quantity: 5,
      purchaseDate: '2024-02-01',
      warrantyExpiry: '2026-02-01',
      manufacturer: 'Herman Miller',
      model: 'Aeron'
    }, { headers });

    await axios.post(`${API_BASE}/assets`, {
      name: 'HP Printer LaserJet Pro',
      description: 'Professional laser printer',
      categoryId: itCategory.data.id,
      locationId: itDept.data.id,
      status: 'maintenance',
      condition: 'fair',
      unitPrice: 399.99,
      quantity: 1,
      purchaseDate: '2023-06-10',
      warrantyExpiry: '2025-06-10',
      manufacturer: 'HP',
      model: 'LaserJet Pro M404n',
      serialNumber: 'HP987654321'
    }, { headers });

    console.log('✅ Sample data created successfully!');
    console.log('🚀 You can now login with:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');
    
  } catch (error) {
    console.error('❌ Error seeding data:', error.response?.data || error.message);
  }
}

seedData();
