"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/asset-details/[assetNumber]/page",{

/***/ "(app-pages-browser)/./src/app/asset-details/[assetNumber]/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/asset-details/[assetNumber]/page.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AssetDetailsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/QrCode.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Info.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Category.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CalendarToday.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarToday,Category,Info,LocationOn,Person,Print,QrCode!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Print.js\");\n/* harmony import */ var _lib_api_assetItemService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/assetItemService */ \"(app-pages-browser)/./src/lib/api/assetItemService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AssetDetailsPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const assetNumber = params.assetNumber;\n    const [assetItem, setAssetItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Authentication hooks\n    const { user, isAuthenticated, isLoading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Role checking\n    const isAdmin = ()=>(user === null || user === void 0 ? void 0 : user.role) === \"admin\";\n    // Check authentication first\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!authLoading) {\n            if (!isAuthenticated || !isAdmin()) {\n                // Redirect to login with return URL\n                const currentUrl = \"/asset-details/\".concat(assetNumber);\n                const returnUrl = encodeURIComponent(currentUrl);\n                router.push(\"/login?returnUrl=\".concat(returnUrl));\n                return;\n            }\n        }\n    }, [\n        authLoading,\n        isAuthenticated,\n        isAdmin,\n        assetNumber,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAssetDetails = async ()=>{\n            // Don't load data if not authenticated\n            if (!isAuthenticated || !isAdmin()) {\n                return;\n            }\n            try {\n                setLoading(true);\n                setError(null);\n                console.log(\"Loading asset details for asset number:\", assetNumber);\n                // Use the new direct API endpoint\n                const assetItemData = await _lib_api_assetItemService__WEBPACK_IMPORTED_MODULE_4__.assetItemService.getAssetItemByAssetNumber(assetNumber);\n                console.log(\"Loaded asset item data:\", assetItemData);\n                setAssetItem(assetItemData);\n            } catch (err) {\n                console.error(\"Failed to load asset details:\", err);\n                setError(\"Failed to load asset details: \".concat(err instanceof Error ? err.message : \"Unknown error\"));\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (assetNumber && !authLoading && isAuthenticated && isAdmin()) {\n            loadAssetDetails();\n        }\n    }, [\n        assetNumber,\n        authLoading,\n        isAuthenticated,\n        isAdmin\n    ]);\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case \"in_stock\":\n                return \"success\";\n            case \"in_use\":\n                return \"primary\";\n            case \"maintenance\":\n                return \"warning\";\n            case \"retired\":\n                return \"error\";\n            default:\n                return \"default\";\n        }\n    };\n    const getConditionColor = (condition)=>{\n        switch(condition.toLowerCase()){\n            case \"excellent\":\n                return \"success\";\n            case \"good\":\n                return \"info\";\n            case \"fair\":\n                return \"warning\";\n            case \"poor\":\n                return \"error\";\n            default:\n                return \"default\";\n        }\n    };\n    const formatStatus = (status)=>{\n        return status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase());\n    };\n    const formatCondition = (condition)=>{\n        return condition.charAt(0).toUpperCase() + condition.slice(1).toLowerCase();\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        }).format(amount);\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"Not set\";\n        return new Date(dateString).toLocaleDateString();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            maxWidth: \"md\",\n            sx: {\n                py: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"50vh\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !assetItem) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            maxWidth: \"md\",\n            sx: {\n                py: 4\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    severity: \"error\",\n                    sx: {\n                        mb: 2\n                    },\n                    children: error || \"Asset not found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    variant: \"contained\",\n                    href: \"/assets\",\n                    children: \"Back to Assets\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        maxWidth: \"md\",\n        sx: {\n            py: 4\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    mb: 4,\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        sx: {\n                            mx: \"auto\",\n                            mb: 2,\n                            bgcolor: \"primary.main\",\n                            width: 64,\n                            height: 64\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            sx: {\n                                fontSize: 32\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        variant: \"h4\",\n                        component: \"h1\",\n                        gutterBottom: true,\n                        children: assetItem.asset.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        variant: \"h6\",\n                        color: \"text.secondary\",\n                        gutterBottom: true,\n                        children: [\n                            \"Asset Number: \",\n                            assetItem.assetNumber\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            gap: 1,\n                            mb: 2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                label: formatStatus(assetItem.status),\n                                color: getStatusColor(assetItem.status),\n                                variant: \"filled\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                label: formatCondition(assetItem.condition),\n                                color: getConditionColor(assetItem.condition),\n                                variant: \"outlined\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    mb: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"h6\",\n                            gutterBottom: true,\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    color: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                \"Asset Information\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.asset.description || \"No description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Unit Price\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: formatCurrency(assetItem.asset.unitPrice)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Manufacturer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.asset.manufacturer || \"Not specified\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Model\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.asset.model || \"Not specified\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                container: true,\n                spacing: 2,\n                sx: {\n                    mb: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                color: \"primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Category\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"body1\",\n                                        children: assetItem.asset.category.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                color: \"primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Location\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"body1\",\n                                        children: assetItem.asset.location.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"body2\",\n                                        color: \"text.secondary\",\n                                        children: [\n                                            \"Type: \",\n                                            assetItem.asset.location.type\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    mb: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"h6\",\n                            gutterBottom: true,\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    color: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                \"Item Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Serial Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.serialNumber || \"Not set\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Purchase Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: formatDate(assetItem.purchaseDate)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Warranty Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: formatDate(assetItem.warrantyDate)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this),\n                                assetItem.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"Notes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.notes\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this),\n            assetItem.assignedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    mb: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"h6\",\n                            gutterBottom: true,\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    color: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this),\n                                \"Assigned To\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    sx: {\n                                        bgcolor: \"primary.main\"\n                                    },\n                                    children: assetItem.assignedUser.name.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body1\",\n                                            children: assetItem.assignedUser.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: assetItem.assignedUser.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    textAlign: \"center\",\n                    mt: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        variant: \"contained\",\n                        href: \"/assets\",\n                        sx: {\n                            mr: 2\n                        },\n                        children: \"Back to Assets\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        variant: \"outlined\",\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarToday_Category_Info_LocationOn_Person_Print_QrCode_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 47\n                        }, void 0),\n                        onClick: ()=>window.print(),\n                        children: \"Print Details\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\[assetNumber]\\\\page.tsx\",\n        lineNumber: 198,\n        columnNumber: 5\n    }, this);\n}\n_s(AssetDetailsPage, \"81/RgpDyeDsJ0Vl8HgLWOdUea0k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = AssetDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"AssetDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXNzZXQtZGV0YWlscy9bYXNzZXROdW1iZXJdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUM0QjtBQUN2QjtBQWMxQjtBQVNNO0FBQ2lDO0FBb0M5QyxTQUFTZ0M7O0lBQ3RCLE1BQU1DLFNBQVMvQiwwREFBU0E7SUFDeEIsTUFBTWdDLFNBQVMvQiwwREFBU0E7SUFDeEIsTUFBTWdDLGVBQWUvQixnRUFBZUE7SUFDcEMsTUFBTWdDLGNBQWNILE9BQU9HLFdBQVc7SUFDdEMsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdyQywrQ0FBUUEsQ0FBMEI7SUFDcEUsTUFBTSxDQUFDc0MsU0FBU0MsV0FBVyxHQUFHdkMsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDd0MsT0FBT0MsU0FBUyxHQUFHekMsK0NBQVFBLENBQWdCO0lBRWxELHVCQUF1QjtJQUN2QixNQUFNLEVBQUUwQyxJQUFJLEVBQUVDLGVBQWUsRUFBRUMsV0FBV0MsV0FBVyxFQUFFLEdBQUd6Qyw4REFBT0E7SUFFakUsZ0JBQWdCO0lBQ2hCLE1BQU0wQyxVQUFVLElBQU1KLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTUssSUFBSSxNQUFLO0lBRXJDLDZCQUE2QjtJQUM3QmhELGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxDQUFDOEMsYUFBYTtZQUNoQixJQUFJLENBQUNGLG1CQUFtQixDQUFDRyxXQUFXO2dCQUNsQyxvQ0FBb0M7Z0JBQ3BDLE1BQU1FLGFBQWEsa0JBQThCLE9BQVpiO2dCQUNyQyxNQUFNYyxZQUFZQyxtQkFBbUJGO2dCQUNyQ2YsT0FBT2tCLElBQUksQ0FBQyxvQkFBOEIsT0FBVkY7Z0JBQ2hDO1lBQ0Y7UUFDRjtJQUNGLEdBQUc7UUFBQ0o7UUFBYUY7UUFBaUJHO1FBQVNYO1FBQWFGO0tBQU87SUFFL0RsQyxnREFBU0EsQ0FBQztRQUNSLE1BQU1xRCxtQkFBbUI7WUFDdkIsdUNBQXVDO1lBQ3ZDLElBQUksQ0FBQ1QsbUJBQW1CLENBQUNHLFdBQVc7Z0JBQ2xDO1lBQ0Y7WUFFQSxJQUFJO2dCQUNGUCxXQUFXO2dCQUNYRSxTQUFTO2dCQUVUWSxRQUFRQyxHQUFHLENBQUMsMkNBQTJDbkI7Z0JBRXZELGtDQUFrQztnQkFDbEMsTUFBTW9CLGdCQUFnQixNQUFNekIsdUVBQWdCQSxDQUFDMEIseUJBQXlCLENBQUNyQjtnQkFDdkVrQixRQUFRQyxHQUFHLENBQUMsMkJBQTJCQztnQkFFdkNsQixhQUFha0I7WUFDZixFQUFFLE9BQU9FLEtBQUs7Z0JBQ1pKLFFBQVFiLEtBQUssQ0FBQyxpQ0FBaUNpQjtnQkFDL0NoQixTQUFTLGlDQUFzRixPQUFyRGdCLGVBQWVDLFFBQVFELElBQUlFLE9BQU8sR0FBRztZQUNqRixTQUFVO2dCQUNScEIsV0FBVztZQUNiO1FBQ0Y7UUFFQSxJQUFJSixlQUFlLENBQUNVLGVBQWVGLG1CQUFtQkcsV0FBVztZQUMvRE07UUFDRjtJQUNGLEdBQUc7UUFBQ2pCO1FBQWFVO1FBQWFGO1FBQWlCRztLQUFRO0lBRXZELE1BQU1jLGlCQUFpQixDQUFDQztRQUN0QixPQUFRQSxPQUFPQyxXQUFXO1lBQ3hCLEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNUO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTUMsb0JBQW9CLENBQUNDO1FBQ3pCLE9BQVFBLFVBQVVGLFdBQVc7WUFDM0IsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1Q7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxNQUFNRyxlQUFlLENBQUNKO1FBQ3BCLE9BQU9BLE9BQU9LLE9BQU8sQ0FBQyxLQUFLLEtBQUtBLE9BQU8sQ0FBQyxTQUFTQyxDQUFBQSxJQUFLQSxFQUFFQyxXQUFXO0lBQ3JFO0lBRUEsTUFBTUMsa0JBQWtCLENBQUNMO1FBQ3ZCLE9BQU9BLFVBQVVNLE1BQU0sQ0FBQyxHQUFHRixXQUFXLEtBQUtKLFVBQVVPLEtBQUssQ0FBQyxHQUFHVCxXQUFXO0lBQzNFO0lBRUEsTUFBTVUsaUJBQWlCLENBQUNDO1FBQ3RCLE9BQU8sSUFBSUMsS0FBS0MsWUFBWSxDQUFDLFNBQVM7WUFDcENDLE9BQU87WUFDUEMsVUFBVTtRQUNaLEdBQUdDLE1BQU0sQ0FBQ0w7SUFDWjtJQUVBLE1BQU1NLGFBQWEsQ0FBQ0M7UUFDbEIsSUFBSSxDQUFDQSxZQUFZLE9BQU87UUFDeEIsT0FBTyxJQUFJQyxLQUFLRCxZQUFZRSxrQkFBa0I7SUFDaEQ7SUFFQSxJQUFJNUMsU0FBUztRQUNYLHFCQUNFLDhEQUFDdkIsb0tBQVNBO1lBQUNvRSxVQUFTO1lBQUtDLElBQUk7Z0JBQUVDLElBQUk7WUFBRTtzQkFDbkMsNEVBQUNoRixvS0FBR0E7Z0JBQUNpRixTQUFRO2dCQUFPQyxnQkFBZTtnQkFBU0MsWUFBVztnQkFBU0MsV0FBVTswQkFDeEUsNEVBQUM1RSxvS0FBZ0JBOzs7Ozs7Ozs7Ozs7Ozs7SUFJekI7SUFFQSxJQUFJMkIsU0FBUyxDQUFDSixXQUFXO1FBQ3ZCLHFCQUNFLDhEQUFDckIsb0tBQVNBO1lBQUNvRSxVQUFTO1lBQUtDLElBQUk7Z0JBQUVDLElBQUk7WUFBRTs7OEJBQ25DLDhEQUFDdkUsb0tBQUtBO29CQUFDNEUsVUFBUztvQkFBUU4sSUFBSTt3QkFBRU8sSUFBSTtvQkFBRTs4QkFDakNuRCxTQUFTOzs7Ozs7OEJBRVosOERBQUM1QixvS0FBTUE7b0JBQUNnRixTQUFRO29CQUFZQyxNQUFLOzhCQUFVOzs7Ozs7Ozs7Ozs7SUFLakQ7SUFFQSxxQkFDRSw4REFBQzlFLG9LQUFTQTtRQUFDb0UsVUFBUztRQUFLQyxJQUFJO1lBQUVDLElBQUk7UUFBRTs7MEJBRW5DLDhEQUFDaEYsb0tBQUdBO2dCQUFDK0UsSUFBSTtvQkFBRU8sSUFBSTtvQkFBR0csV0FBVztnQkFBUzs7a0NBQ3BDLDhEQUFDbkYscUtBQU1BO3dCQUFDeUUsSUFBSTs0QkFBRVcsSUFBSTs0QkFBUUosSUFBSTs0QkFBR0ssU0FBUzs0QkFBZ0JDLE9BQU87NEJBQUlDLFFBQVE7d0JBQUc7a0NBQzlFLDRFQUFDakYsNklBQVVBOzRCQUFDbUUsSUFBSTtnQ0FBRWUsVUFBVTs0QkFBRzs7Ozs7Ozs7Ozs7a0NBRWpDLDhEQUFDM0YscUtBQVVBO3dCQUFDb0YsU0FBUTt3QkFBS1EsV0FBVTt3QkFBS0MsWUFBWTtrQ0FDakRqRSxVQUFVa0UsS0FBSyxDQUFDQyxJQUFJOzs7Ozs7a0NBRXZCLDhEQUFDL0YscUtBQVVBO3dCQUFDb0YsU0FBUTt3QkFBS1ksT0FBTTt3QkFBaUJILFlBQVk7OzRCQUFDOzRCQUM1Q2pFLFVBQVVELFdBQVc7Ozs7Ozs7a0NBRXRDLDhEQUFDOUIsb0tBQUdBO3dCQUFDK0UsSUFBSTs0QkFBRUUsU0FBUzs0QkFBUUMsZ0JBQWdCOzRCQUFVa0IsS0FBSzs0QkFBR2QsSUFBSTt3QkFBRTs7MENBQ2xFLDhEQUFDbEYscUtBQUlBO2dDQUNIaUcsT0FBT3pDLGFBQWE3QixVQUFVeUIsTUFBTTtnQ0FDcEMyQyxPQUFPNUMsZUFBZXhCLFVBQVV5QixNQUFNO2dDQUN0QytCLFNBQVE7Ozs7OzswQ0FFViw4REFBQ25GLHFLQUFJQTtnQ0FDSGlHLE9BQU9yQyxnQkFBZ0JqQyxVQUFVNEIsU0FBUztnQ0FDMUN3QyxPQUFPekMsa0JBQWtCM0IsVUFBVTRCLFNBQVM7Z0NBQzVDNEIsU0FBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1kLDhEQUFDdEYscUtBQUlBO2dCQUFDOEUsSUFBSTtvQkFBRU8sSUFBSTtnQkFBRTswQkFDaEIsNEVBQUNwRixxS0FBV0E7O3NDQUNWLDhEQUFDQyxxS0FBVUE7NEJBQUNvRixTQUFROzRCQUFLUyxZQUFZOzRCQUFDakIsSUFBSTtnQ0FBRUUsU0FBUztnQ0FBUUUsWUFBWTtnQ0FBVWlCLEtBQUs7NEJBQUU7OzhDQUN4Riw4REFBQzlFLDZJQUFRQTtvQ0FBQzZFLE9BQU07Ozs7OztnQ0FBWTs7Ozs7OztzQ0FHOUIsOERBQUM5RixxS0FBSUE7NEJBQUNpRyxTQUFTOzRCQUFDQyxTQUFTOzs4Q0FDdkIsOERBQUNsRyxxS0FBSUE7b0NBQUNtRyxJQUFJO29DQUFDQyxJQUFJO29DQUFJQyxJQUFJOztzREFDckIsOERBQUN2RyxxS0FBVUE7NENBQUNvRixTQUFROzRDQUFRWSxPQUFNO3NEQUFpQjs7Ozs7O3NEQUduRCw4REFBQ2hHLHFLQUFVQTs0Q0FBQ29GLFNBQVE7c0RBQVN4RCxVQUFVa0UsS0FBSyxDQUFDVSxXQUFXLElBQUk7Ozs7Ozs7Ozs7Ozs4Q0FFOUQsOERBQUN0RyxxS0FBSUE7b0NBQUNtRyxJQUFJO29DQUFDQyxJQUFJO29DQUFJQyxJQUFJOztzREFDckIsOERBQUN2RyxxS0FBVUE7NENBQUNvRixTQUFROzRDQUFRWSxPQUFNO3NEQUFpQjs7Ozs7O3NEQUduRCw4REFBQ2hHLHFLQUFVQTs0Q0FBQ29GLFNBQVE7c0RBQVNwQixlQUFlcEMsVUFBVWtFLEtBQUssQ0FBQ1csU0FBUzs7Ozs7Ozs7Ozs7OzhDQUV2RSw4REFBQ3ZHLHFLQUFJQTtvQ0FBQ21HLElBQUk7b0NBQUNDLElBQUk7b0NBQUlDLElBQUk7O3NEQUNyQiw4REFBQ3ZHLHFLQUFVQTs0Q0FBQ29GLFNBQVE7NENBQVFZLE9BQU07c0RBQWlCOzs7Ozs7c0RBR25ELDhEQUFDaEcscUtBQVVBOzRDQUFDb0YsU0FBUTtzREFBU3hELFVBQVVrRSxLQUFLLENBQUNZLFlBQVksSUFBSTs7Ozs7Ozs7Ozs7OzhDQUUvRCw4REFBQ3hHLHFLQUFJQTtvQ0FBQ21HLElBQUk7b0NBQUNDLElBQUk7b0NBQUlDLElBQUk7O3NEQUNyQiw4REFBQ3ZHLHFLQUFVQTs0Q0FBQ29GLFNBQVE7NENBQVFZLE9BQU07c0RBQWlCOzs7Ozs7c0RBR25ELDhEQUFDaEcscUtBQVVBOzRDQUFDb0YsU0FBUTtzREFBU3hELFVBQVVrRSxLQUFLLENBQUNhLEtBQUssSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTzlELDhEQUFDekcscUtBQUlBO2dCQUFDaUcsU0FBUztnQkFBQ0MsU0FBUztnQkFBR3hCLElBQUk7b0JBQUVPLElBQUk7Z0JBQUU7O2tDQUN0Qyw4REFBQ2pGLHFLQUFJQTt3QkFBQ21HLElBQUk7d0JBQUNDLElBQUk7d0JBQUlDLElBQUk7a0NBQ3JCLDRFQUFDekcscUtBQUlBO3NDQUNILDRFQUFDQyxxS0FBV0E7O2tEQUNWLDhEQUFDQyxxS0FBVUE7d0NBQUNvRixTQUFRO3dDQUFLUyxZQUFZO3dDQUFDakIsSUFBSTs0Q0FBRUUsU0FBUzs0Q0FBUUUsWUFBWTs0Q0FBVWlCLEtBQUs7d0NBQUU7OzBEQUN4Riw4REFBQ3RGLDZJQUFZQTtnREFBQ3FGLE9BQU07Ozs7Ozs0Q0FBWTs7Ozs7OztrREFHbEMsOERBQUNoRyxxS0FBVUE7d0NBQUNvRixTQUFRO2tEQUFTeEQsVUFBVWtFLEtBQUssQ0FBQ2MsUUFBUSxDQUFDYixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUloRSw4REFBQzdGLHFLQUFJQTt3QkFBQ21HLElBQUk7d0JBQUNDLElBQUk7d0JBQUlDLElBQUk7a0NBQ3JCLDRFQUFDekcscUtBQUlBO3NDQUNILDRFQUFDQyxxS0FBV0E7O2tEQUNWLDhEQUFDQyxxS0FBVUE7d0NBQUNvRixTQUFRO3dDQUFLUyxZQUFZO3dDQUFDakIsSUFBSTs0Q0FBRUUsU0FBUzs0Q0FBUUUsWUFBWTs0Q0FBVWlCLEtBQUs7d0NBQUU7OzBEQUN4Riw4REFBQ3BGLDZJQUFZQTtnREFBQ21GLE9BQU07Ozs7Ozs0Q0FBWTs7Ozs7OztrREFHbEMsOERBQUNoRyxxS0FBVUE7d0NBQUNvRixTQUFRO2tEQUFTeEQsVUFBVWtFLEtBQUssQ0FBQ2UsUUFBUSxDQUFDZCxJQUFJOzs7Ozs7a0RBQzFELDhEQUFDL0YscUtBQVVBO3dDQUFDb0YsU0FBUTt3Q0FBUVksT0FBTTs7NENBQWlCOzRDQUMxQ3BFLFVBQVVrRSxLQUFLLENBQUNlLFFBQVEsQ0FBQ0MsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUTlDLDhEQUFDaEgscUtBQUlBO2dCQUFDOEUsSUFBSTtvQkFBRU8sSUFBSTtnQkFBRTswQkFDaEIsNEVBQUNwRixxS0FBV0E7O3NDQUNWLDhEQUFDQyxxS0FBVUE7NEJBQUNvRixTQUFROzRCQUFLUyxZQUFZOzRCQUFDakIsSUFBSTtnQ0FBRUUsU0FBUztnQ0FBUUUsWUFBWTtnQ0FBVWlCLEtBQUs7NEJBQUU7OzhDQUN4Riw4REFBQ2hGLDZJQUFZQTtvQ0FBQytFLE9BQU07Ozs7OztnQ0FBWTs7Ozs7OztzQ0FHbEMsOERBQUM5RixxS0FBSUE7NEJBQUNpRyxTQUFTOzRCQUFDQyxTQUFTOzs4Q0FDdkIsOERBQUNsRyxxS0FBSUE7b0NBQUNtRyxJQUFJO29DQUFDQyxJQUFJO29DQUFJQyxJQUFJOztzREFDckIsOERBQUN2RyxxS0FBVUE7NENBQUNvRixTQUFROzRDQUFRWSxPQUFNO3NEQUFpQjs7Ozs7O3NEQUduRCw4REFBQ2hHLHFLQUFVQTs0Q0FBQ29GLFNBQVE7c0RBQVN4RCxVQUFVbUYsWUFBWSxJQUFJOzs7Ozs7Ozs7Ozs7OENBRXpELDhEQUFDN0cscUtBQUlBO29DQUFDbUcsSUFBSTtvQ0FBQ0MsSUFBSTtvQ0FBSUMsSUFBSTs7c0RBQ3JCLDhEQUFDdkcscUtBQVVBOzRDQUFDb0YsU0FBUTs0Q0FBUVksT0FBTTtzREFBaUI7Ozs7OztzREFHbkQsOERBQUNoRyxxS0FBVUE7NENBQUNvRixTQUFRO3NEQUFTYixXQUFXM0MsVUFBVW9GLFlBQVk7Ozs7Ozs7Ozs7Ozs4Q0FFaEUsOERBQUM5RyxxS0FBSUE7b0NBQUNtRyxJQUFJO29DQUFDQyxJQUFJO29DQUFJQyxJQUFJOztzREFDckIsOERBQUN2RyxxS0FBVUE7NENBQUNvRixTQUFROzRDQUFRWSxPQUFNO3NEQUFpQjs7Ozs7O3NEQUduRCw4REFBQ2hHLHFLQUFVQTs0Q0FBQ29GLFNBQVE7c0RBQVNiLFdBQVczQyxVQUFVcUYsWUFBWTs7Ozs7Ozs7Ozs7O2dDQUUvRHJGLFVBQVVzRixLQUFLLGtCQUNkLDhEQUFDaEgscUtBQUlBO29DQUFDbUcsSUFBSTtvQ0FBQ0MsSUFBSTs7c0RBQ2IsOERBQUN0RyxxS0FBVUE7NENBQUNvRixTQUFROzRDQUFRWSxPQUFNO3NEQUFpQjs7Ozs7O3NEQUduRCw4REFBQ2hHLHFLQUFVQTs0Q0FBQ29GLFNBQVE7c0RBQVN4RCxVQUFVc0YsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFRckR0RixVQUFVdUYsWUFBWSxrQkFDckIsOERBQUNySCxxS0FBSUE7Z0JBQUM4RSxJQUFJO29CQUFFTyxJQUFJO2dCQUFFOzBCQUNoQiw0RUFBQ3BGLHFLQUFXQTs7c0NBQ1YsOERBQUNDLHFLQUFVQTs0QkFBQ29GLFNBQVE7NEJBQUtTLFlBQVk7NEJBQUNqQixJQUFJO2dDQUFFRSxTQUFTO2dDQUFRRSxZQUFZO2dDQUFVaUIsS0FBSzs0QkFBRTs7OENBQ3hGLDhEQUFDbEYsNklBQVVBO29DQUFDaUYsT0FBTTs7Ozs7O2dDQUFZOzs7Ozs7O3NDQUdoQyw4REFBQ25HLG9LQUFHQTs0QkFBQytFLElBQUk7Z0NBQUVFLFNBQVM7Z0NBQVFFLFlBQVk7Z0NBQVVpQixLQUFLOzRCQUFFOzs4Q0FDdkQsOERBQUM5RixxS0FBTUE7b0NBQUN5RSxJQUFJO3dDQUFFWSxTQUFTO29DQUFlOzhDQUFJNUQsVUFBVXVGLFlBQVksQ0FBQ3BCLElBQUksQ0FBQ2pDLE1BQU0sQ0FBQyxHQUFHRixXQUFXOzs7Ozs7OENBQzNGLDhEQUFDL0Qsb0tBQUdBOztzREFDRiw4REFBQ0cscUtBQVVBOzRDQUFDb0YsU0FBUTtzREFBU3hELFVBQVV1RixZQUFZLENBQUNwQixJQUFJOzs7Ozs7c0RBQ3hELDhEQUFDL0YscUtBQVVBOzRDQUFDb0YsU0FBUTs0Q0FBUVksT0FBTTtzREFDL0JwRSxVQUFVdUYsWUFBWSxDQUFDQyxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTekMsOERBQUN2SCxvS0FBR0E7Z0JBQUMrRSxJQUFJO29CQUFFVSxXQUFXO29CQUFVK0IsSUFBSTtnQkFBRTs7a0NBQ3BDLDhEQUFDakgsb0tBQU1BO3dCQUFDZ0YsU0FBUTt3QkFBWUMsTUFBSzt3QkFBVVQsSUFBSTs0QkFBRTBDLElBQUk7d0JBQUU7a0NBQUc7Ozs7OztrQ0FHMUQsOERBQUNsSCxvS0FBTUE7d0JBQUNnRixTQUFRO3dCQUFXbUMseUJBQVcsOERBQUNsRyw2SUFBU0E7Ozs7O3dCQUFLbUcsU0FBUyxJQUFNQyxPQUFPQyxLQUFLO2tDQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNNUY7R0F0U3dCbkc7O1FBQ1A5QixzREFBU0E7UUFDVEMsc0RBQVNBO1FBQ0hDLDREQUFlQTtRQU9zQkMsMERBQU9BOzs7S0FWM0MyQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2Fzc2V0LWRldGFpbHMvW2Fzc2V0TnVtYmVyXS9wYWdlLnRzeD9mNmNkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VQYXJhbXMsIHVzZVJvdXRlciwgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnXG5pbXBvcnQge1xuICBCb3gsXG4gIENhcmQsXG4gIENhcmRDb250ZW50LFxuICBUeXBvZ3JhcGh5LFxuICBDaGlwLFxuICBHcmlkLFxuICBBdmF0YXIsXG4gIERpdmlkZXIsXG4gIEJ1dHRvbixcbiAgQ2lyY3VsYXJQcm9ncmVzcyxcbiAgQWxlcnQsXG4gIENvbnRhaW5lclxufSBmcm9tICdAbXVpL21hdGVyaWFsJ1xuaW1wb3J0IHtcbiAgUXJDb2RlIGFzIFFyQ29kZUljb24sXG4gIENhdGVnb3J5IGFzIENhdGVnb3J5SWNvbixcbiAgTG9jYXRpb25PbiBhcyBMb2NhdGlvbkljb24sXG4gIFBlcnNvbiBhcyBQZXJzb25JY29uLFxuICBDYWxlbmRhclRvZGF5IGFzIENhbGVuZGFySWNvbixcbiAgSW5mbyBhcyBJbmZvSWNvbixcbiAgUHJpbnQgYXMgUHJpbnRJY29uXG59IGZyb20gJ0BtdWkvaWNvbnMtbWF0ZXJpYWwnXG5pbXBvcnQgeyBhc3NldEl0ZW1TZXJ2aWNlIH0gZnJvbSAnQC9saWIvYXBpL2Fzc2V0SXRlbVNlcnZpY2UnXG5pbXBvcnQgeyBhc3NldHNTZXJ2aWNlIH0gZnJvbSAnQC9saWIvYXBpL2Fzc2V0cydcblxuaW50ZXJmYWNlIEFzc2V0SXRlbURldGFpbHMge1xuICBpZDogc3RyaW5nXG4gIGFzc2V0TnVtYmVyOiBzdHJpbmdcbiAgc3RhdHVzOiBzdHJpbmdcbiAgY29uZGl0aW9uOiBzdHJpbmdcbiAgc2VyaWFsTnVtYmVyPzogc3RyaW5nXG4gIHB1cmNoYXNlRGF0ZT86IHN0cmluZ1xuICB3YXJyYW50eURhdGU/OiBzdHJpbmdcbiAgbm90ZXM/OiBzdHJpbmdcbiAgYXNzaWduZWRVc2VyPzoge1xuICAgIGlkOiBzdHJpbmdcbiAgICBuYW1lOiBzdHJpbmdcbiAgICBlbWFpbDogc3RyaW5nXG4gIH1cbiAgYXNzZXQ6IHtcbiAgICBpZDogc3RyaW5nXG4gICAgbmFtZTogc3RyaW5nXG4gICAgZGVzY3JpcHRpb24/OiBzdHJpbmdcbiAgICBtYW51ZmFjdHVyZXI/OiBzdHJpbmdcbiAgICBtb2RlbD86IHN0cmluZ1xuICAgIHVuaXRQcmljZTogbnVtYmVyXG4gICAgY2F0ZWdvcnk6IHtcbiAgICAgIGlkOiBzdHJpbmdcbiAgICAgIG5hbWU6IHN0cmluZ1xuICAgIH1cbiAgICBsb2NhdGlvbjoge1xuICAgICAgaWQ6IHN0cmluZ1xuICAgICAgbmFtZTogc3RyaW5nXG4gICAgICB0eXBlOiBzdHJpbmdcbiAgICB9XG4gIH1cbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXNzZXREZXRhaWxzUGFnZSgpIHtcbiAgY29uc3QgcGFyYW1zID0gdXNlUGFyYW1zKClcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcbiAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKClcbiAgY29uc3QgYXNzZXROdW1iZXIgPSBwYXJhbXMuYXNzZXROdW1iZXIgYXMgc3RyaW5nXG4gIGNvbnN0IFthc3NldEl0ZW0sIHNldEFzc2V0SXRlbV0gPSB1c2VTdGF0ZTxBc3NldEl0ZW1EZXRhaWxzIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuXG4gIC8vIEF1dGhlbnRpY2F0aW9uIGhvb2tzXG4gIGNvbnN0IHsgdXNlciwgaXNBdXRoZW50aWNhdGVkLCBpc0xvYWRpbmc6IGF1dGhMb2FkaW5nIH0gPSB1c2VBdXRoKClcblxuICAvLyBSb2xlIGNoZWNraW5nXG4gIGNvbnN0IGlzQWRtaW4gPSAoKSA9PiB1c2VyPy5yb2xlID09PSAnYWRtaW4nXG5cbiAgLy8gQ2hlY2sgYXV0aGVudGljYXRpb24gZmlyc3RcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWF1dGhMb2FkaW5nKSB7XG4gICAgICBpZiAoIWlzQXV0aGVudGljYXRlZCB8fCAhaXNBZG1pbigpKSB7XG4gICAgICAgIC8vIFJlZGlyZWN0IHRvIGxvZ2luIHdpdGggcmV0dXJuIFVSTFxuICAgICAgICBjb25zdCBjdXJyZW50VXJsID0gYC9hc3NldC1kZXRhaWxzLyR7YXNzZXROdW1iZXJ9YFxuICAgICAgICBjb25zdCByZXR1cm5VcmwgPSBlbmNvZGVVUklDb21wb25lbnQoY3VycmVudFVybClcbiAgICAgICAgcm91dGVyLnB1c2goYC9sb2dpbj9yZXR1cm5Vcmw9JHtyZXR1cm5Vcmx9YClcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG4gICAgfVxuICB9LCBbYXV0aExvYWRpbmcsIGlzQXV0aGVudGljYXRlZCwgaXNBZG1pbiwgYXNzZXROdW1iZXIsIHJvdXRlcl0pXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBsb2FkQXNzZXREZXRhaWxzID0gYXN5bmMgKCkgPT4ge1xuICAgICAgLy8gRG9uJ3QgbG9hZCBkYXRhIGlmIG5vdCBhdXRoZW50aWNhdGVkXG4gICAgICBpZiAoIWlzQXV0aGVudGljYXRlZCB8fCAhaXNBZG1pbigpKSB7XG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICB0cnkge1xuICAgICAgICBzZXRMb2FkaW5nKHRydWUpXG4gICAgICAgIHNldEVycm9yKG51bGwpXG5cbiAgICAgICAgY29uc29sZS5sb2coJ0xvYWRpbmcgYXNzZXQgZGV0YWlscyBmb3IgYXNzZXQgbnVtYmVyOicsIGFzc2V0TnVtYmVyKVxuXG4gICAgICAgIC8vIFVzZSB0aGUgbmV3IGRpcmVjdCBBUEkgZW5kcG9pbnRcbiAgICAgICAgY29uc3QgYXNzZXRJdGVtRGF0YSA9IGF3YWl0IGFzc2V0SXRlbVNlcnZpY2UuZ2V0QXNzZXRJdGVtQnlBc3NldE51bWJlcihhc3NldE51bWJlcilcbiAgICAgICAgY29uc29sZS5sb2coJ0xvYWRlZCBhc3NldCBpdGVtIGRhdGE6JywgYXNzZXRJdGVtRGF0YSlcblxuICAgICAgICBzZXRBc3NldEl0ZW0oYXNzZXRJdGVtRGF0YSBhcyBBc3NldEl0ZW1EZXRhaWxzKVxuICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGFzc2V0IGRldGFpbHM6JywgZXJyKVxuICAgICAgICBzZXRFcnJvcihgRmFpbGVkIHRvIGxvYWQgYXNzZXQgZGV0YWlsczogJHtlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InfWApXG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgICAgfVxuICAgIH1cblxuICAgIGlmIChhc3NldE51bWJlciAmJiAhYXV0aExvYWRpbmcgJiYgaXNBdXRoZW50aWNhdGVkICYmIGlzQWRtaW4oKSkge1xuICAgICAgbG9hZEFzc2V0RGV0YWlscygpXG4gICAgfVxuICB9LCBbYXNzZXROdW1iZXIsIGF1dGhMb2FkaW5nLCBpc0F1dGhlbnRpY2F0ZWQsIGlzQWRtaW5dKVxuXG4gIGNvbnN0IGdldFN0YXR1c0NvbG9yID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMudG9Mb3dlckNhc2UoKSkge1xuICAgICAgY2FzZSAnaW5fc3RvY2snOlxuICAgICAgICByZXR1cm4gJ3N1Y2Nlc3MnXG4gICAgICBjYXNlICdpbl91c2UnOlxuICAgICAgICByZXR1cm4gJ3ByaW1hcnknXG4gICAgICBjYXNlICdtYWludGVuYW5jZSc6XG4gICAgICAgIHJldHVybiAnd2FybmluZydcbiAgICAgIGNhc2UgJ3JldGlyZWQnOlxuICAgICAgICByZXR1cm4gJ2Vycm9yJ1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuICdkZWZhdWx0J1xuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdldENvbmRpdGlvbkNvbG9yID0gKGNvbmRpdGlvbjogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChjb25kaXRpb24udG9Mb3dlckNhc2UoKSkge1xuICAgICAgY2FzZSAnZXhjZWxsZW50JzpcbiAgICAgICAgcmV0dXJuICdzdWNjZXNzJ1xuICAgICAgY2FzZSAnZ29vZCc6XG4gICAgICAgIHJldHVybiAnaW5mbydcbiAgICAgIGNhc2UgJ2ZhaXInOlxuICAgICAgICByZXR1cm4gJ3dhcm5pbmcnXG4gICAgICBjYXNlICdwb29yJzpcbiAgICAgICAgcmV0dXJuICdlcnJvcidcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiAnZGVmYXVsdCdcbiAgICB9XG4gIH1cblxuICBjb25zdCBmb3JtYXRTdGF0dXMgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gc3RhdHVzLnJlcGxhY2UoJ18nLCAnICcpLnJlcGxhY2UoL1xcYlxcdy9nLCBsID0+IGwudG9VcHBlckNhc2UoKSlcbiAgfVxuXG4gIGNvbnN0IGZvcm1hdENvbmRpdGlvbiA9IChjb25kaXRpb246IHN0cmluZykgPT4ge1xuICAgIHJldHVybiBjb25kaXRpb24uY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBjb25kaXRpb24uc2xpY2UoMSkudG9Mb3dlckNhc2UoKVxuICB9XG5cbiAgY29uc3QgZm9ybWF0Q3VycmVuY3kgPSAoYW1vdW50OiBudW1iZXIpID0+IHtcbiAgICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KCdlbi1VUycsIHtcbiAgICAgIHN0eWxlOiAnY3VycmVuY3knLFxuICAgICAgY3VycmVuY3k6ICdVU0QnXG4gICAgfSkuZm9ybWF0KGFtb3VudClcbiAgfVxuXG4gIGNvbnN0IGZvcm1hdERhdGUgPSAoZGF0ZVN0cmluZz86IHN0cmluZykgPT4ge1xuICAgIGlmICghZGF0ZVN0cmluZykgcmV0dXJuICdOb3Qgc2V0J1xuICAgIHJldHVybiBuZXcgRGF0ZShkYXRlU3RyaW5nKS50b0xvY2FsZURhdGVTdHJpbmcoKVxuICB9XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPENvbnRhaW5lciBtYXhXaWR0aD0nbWQnIHN4PXt7IHB5OiA0IH19PlxuICAgICAgICA8Qm94IGRpc3BsYXk9J2ZsZXgnIGp1c3RpZnlDb250ZW50PSdjZW50ZXInIGFsaWduSXRlbXM9J2NlbnRlcicgbWluSGVpZ2h0PSc1MHZoJz5cbiAgICAgICAgICA8Q2lyY3VsYXJQcm9ncmVzcyAvPlxuICAgICAgICA8L0JveD5cbiAgICAgIDwvQ29udGFpbmVyPlxuICAgIClcbiAgfVxuXG4gIGlmIChlcnJvciB8fCAhYXNzZXRJdGVtKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxDb250YWluZXIgbWF4V2lkdGg9J21kJyBzeD17eyBweTogNCB9fT5cbiAgICAgICAgPEFsZXJ0IHNldmVyaXR5PSdlcnJvcicgc3g9e3sgbWI6IDIgfX0+XG4gICAgICAgICAge2Vycm9yIHx8ICdBc3NldCBub3QgZm91bmQnfVxuICAgICAgICA8L0FsZXJ0PlxuICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9J2NvbnRhaW5lZCcgaHJlZj0nL2Fzc2V0cyc+XG4gICAgICAgICAgQmFjayB0byBBc3NldHNcbiAgICAgICAgPC9CdXR0b24+XG4gICAgICA8L0NvbnRhaW5lcj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxDb250YWluZXIgbWF4V2lkdGg9J21kJyBzeD17eyBweTogNCB9fT5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8Qm94IHN4PXt7IG1iOiA0LCB0ZXh0QWxpZ246ICdjZW50ZXInIH19PlxuICAgICAgICA8QXZhdGFyIHN4PXt7IG14OiAnYXV0bycsIG1iOiAyLCBiZ2NvbG9yOiAncHJpbWFyeS5tYWluJywgd2lkdGg6IDY0LCBoZWlnaHQ6IDY0IH19PlxuICAgICAgICAgIDxRckNvZGVJY29uIHN4PXt7IGZvbnRTaXplOiAzMiB9fSAvPlxuICAgICAgICA8L0F2YXRhcj5cbiAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0naDQnIGNvbXBvbmVudD0naDEnIGd1dHRlckJvdHRvbT5cbiAgICAgICAgICB7YXNzZXRJdGVtLmFzc2V0Lm5hbWV9XG4gICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0naDYnIGNvbG9yPSd0ZXh0LnNlY29uZGFyeScgZ3V0dGVyQm90dG9tPlxuICAgICAgICAgIEFzc2V0IE51bWJlcjoge2Fzc2V0SXRlbS5hc3NldE51bWJlcn1cbiAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLCBnYXA6IDEsIG1iOiAyIH19PlxuICAgICAgICAgIDxDaGlwXG4gICAgICAgICAgICBsYWJlbD17Zm9ybWF0U3RhdHVzKGFzc2V0SXRlbS5zdGF0dXMpfVxuICAgICAgICAgICAgY29sb3I9e2dldFN0YXR1c0NvbG9yKGFzc2V0SXRlbS5zdGF0dXMpIGFzIGFueX1cbiAgICAgICAgICAgIHZhcmlhbnQ9J2ZpbGxlZCdcbiAgICAgICAgICAvPlxuICAgICAgICAgIDxDaGlwXG4gICAgICAgICAgICBsYWJlbD17Zm9ybWF0Q29uZGl0aW9uKGFzc2V0SXRlbS5jb25kaXRpb24pfVxuICAgICAgICAgICAgY29sb3I9e2dldENvbmRpdGlvbkNvbG9yKGFzc2V0SXRlbS5jb25kaXRpb24pIGFzIGFueX1cbiAgICAgICAgICAgIHZhcmlhbnQ9J291dGxpbmVkJ1xuICAgICAgICAgIC8+XG4gICAgICAgIDwvQm94PlxuICAgICAgPC9Cb3g+XG5cbiAgICAgIHsvKiBBc3NldCBJbmZvcm1hdGlvbiAqL31cbiAgICAgIDxDYXJkIHN4PXt7IG1iOiAzIH19PlxuICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0naDYnIGd1dHRlckJvdHRvbSBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6IDEgfX0+XG4gICAgICAgICAgICA8SW5mb0ljb24gY29sb3I9J3ByaW1hcnknIC8+XG4gICAgICAgICAgICBBc3NldCBJbmZvcm1hdGlvblxuICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICA8R3JpZCBjb250YWluZXIgc3BhY2luZz17Mn0+XG4gICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9PlxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5MicgY29sb3I9J3RleHQuc2Vjb25kYXJ5Jz5cbiAgICAgICAgICAgICAgICBEZXNjcmlwdGlvblxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2JvZHkxJz57YXNzZXRJdGVtLmFzc2V0LmRlc2NyaXB0aW9uIHx8ICdObyBkZXNjcmlwdGlvbid9PC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgPC9HcmlkPlxuICAgICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fT5cbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTInIGNvbG9yPSd0ZXh0LnNlY29uZGFyeSc+XG4gICAgICAgICAgICAgICAgVW5pdCBQcmljZVxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2JvZHkxJz57Zm9ybWF0Q3VycmVuY3koYXNzZXRJdGVtLmFzc2V0LnVuaXRQcmljZSl9PC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgPC9HcmlkPlxuICAgICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fT5cbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTInIGNvbG9yPSd0ZXh0LnNlY29uZGFyeSc+XG4gICAgICAgICAgICAgICAgTWFudWZhY3R1cmVyXG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTEnPnthc3NldEl0ZW0uYXNzZXQubWFudWZhY3R1cmVyIHx8ICdOb3Qgc3BlY2lmaWVkJ308L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9PlxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5MicgY29sb3I9J3RleHQuc2Vjb25kYXJ5Jz5cbiAgICAgICAgICAgICAgICBNb2RlbFxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2JvZHkxJz57YXNzZXRJdGVtLmFzc2V0Lm1vZGVsIHx8ICdOb3Qgc3BlY2lmaWVkJ308L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgICAgPC9HcmlkPlxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuXG4gICAgICB7LyogQ2F0ZWdvcnkgJiBMb2NhdGlvbiAqL31cbiAgICAgIDxHcmlkIGNvbnRhaW5lciBzcGFjaW5nPXsyfSBzeD17eyBtYjogMyB9fT5cbiAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fT5cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0naDYnIGd1dHRlckJvdHRvbSBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6IDEgfX0+XG4gICAgICAgICAgICAgICAgPENhdGVnb3J5SWNvbiBjb2xvcj0ncHJpbWFyeScgLz5cbiAgICAgICAgICAgICAgICBDYXRlZ29yeVxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2JvZHkxJz57YXNzZXRJdGVtLmFzc2V0LmNhdGVnb3J5Lm5hbWV9PC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvR3JpZD5cbiAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fT5cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0naDYnIGd1dHRlckJvdHRvbSBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6IDEgfX0+XG4gICAgICAgICAgICAgICAgPExvY2F0aW9uSWNvbiBjb2xvcj0ncHJpbWFyeScgLz5cbiAgICAgICAgICAgICAgICBMb2NhdGlvblxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2JvZHkxJz57YXNzZXRJdGVtLmFzc2V0LmxvY2F0aW9uLm5hbWV9PC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5MicgY29sb3I9J3RleHQuc2Vjb25kYXJ5Jz5cbiAgICAgICAgICAgICAgICBUeXBlOiB7YXNzZXRJdGVtLmFzc2V0LmxvY2F0aW9uLnR5cGV9XG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L0dyaWQ+XG4gICAgICA8L0dyaWQ+XG5cbiAgICAgIHsvKiBJdGVtIERldGFpbHMgKi99XG4gICAgICA8Q2FyZCBzeD17eyBtYjogMyB9fT5cbiAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2g2JyBndXR0ZXJCb3R0b20gc3g9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAxIH19PlxuICAgICAgICAgICAgPENhbGVuZGFySWNvbiBjb2xvcj0ncHJpbWFyeScgLz5cbiAgICAgICAgICAgIEl0ZW0gRGV0YWlsc1xuICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICA8R3JpZCBjb250YWluZXIgc3BhY2luZz17Mn0+XG4gICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9PlxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5MicgY29sb3I9J3RleHQuc2Vjb25kYXJ5Jz5cbiAgICAgICAgICAgICAgICBTZXJpYWwgTnVtYmVyXG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTEnPnthc3NldEl0ZW0uc2VyaWFsTnVtYmVyIHx8ICdOb3Qgc2V0J308L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9PlxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5MicgY29sb3I9J3RleHQuc2Vjb25kYXJ5Jz5cbiAgICAgICAgICAgICAgICBQdXJjaGFzZSBEYXRlXG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTEnPntmb3JtYXREYXRlKGFzc2V0SXRlbS5wdXJjaGFzZURhdGUpfTwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgIDwvR3JpZD5cbiAgICAgICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17Nn0+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2JvZHkyJyBjb2xvcj0ndGV4dC5zZWNvbmRhcnknPlxuICAgICAgICAgICAgICAgIFdhcnJhbnR5IERhdGVcbiAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5MSc+e2Zvcm1hdERhdGUoYXNzZXRJdGVtLndhcnJhbnR5RGF0ZSl9PC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgPC9HcmlkPlxuICAgICAgICAgICAge2Fzc2V0SXRlbS5ub3RlcyAmJiAoXG4gICAgICAgICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfT5cbiAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5MicgY29sb3I9J3RleHQuc2Vjb25kYXJ5Jz5cbiAgICAgICAgICAgICAgICAgIE5vdGVzXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2JvZHkxJz57YXNzZXRJdGVtLm5vdGVzfTwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPC9HcmlkPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIHsvKiBBc3NpZ25lZCBVc2VyICovfVxuICAgICAge2Fzc2V0SXRlbS5hc3NpZ25lZFVzZXIgJiYgKFxuICAgICAgICA8Q2FyZCBzeD17eyBtYjogMyB9fT5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdoNicgZ3V0dGVyQm90dG9tIHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogMSB9fT5cbiAgICAgICAgICAgICAgPFBlcnNvbkljb24gY29sb3I9J3ByaW1hcnknIC8+XG4gICAgICAgICAgICAgIEFzc2lnbmVkIFRvXG4gICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogMiB9fT5cbiAgICAgICAgICAgICAgPEF2YXRhciBzeD17eyBiZ2NvbG9yOiAncHJpbWFyeS5tYWluJyB9fT57YXNzZXRJdGVtLmFzc2lnbmVkVXNlci5uYW1lLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpfTwvQXZhdGFyPlxuICAgICAgICAgICAgICA8Qm94PlxuICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2JvZHkxJz57YXNzZXRJdGVtLmFzc2lnbmVkVXNlci5uYW1lfTwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5MicgY29sb3I9J3RleHQuc2Vjb25kYXJ5Jz5cbiAgICAgICAgICAgICAgICAgIHthc3NldEl0ZW0uYXNzaWduZWRVc2VyLmVtYWlsfVxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICA8L0JveD5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICApfVxuXG4gICAgICB7LyogQWN0aW9ucyAqL31cbiAgICAgIDxCb3ggc3g9e3sgdGV4dEFsaWduOiAnY2VudGVyJywgbXQ6IDQgfX0+XG4gICAgICAgIDxCdXR0b24gdmFyaWFudD0nY29udGFpbmVkJyBocmVmPScvYXNzZXRzJyBzeD17eyBtcjogMiB9fT5cbiAgICAgICAgICBCYWNrIHRvIEFzc2V0c1xuICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPEJ1dHRvbiB2YXJpYW50PSdvdXRsaW5lZCcgc3RhcnRJY29uPXs8UHJpbnRJY29uIC8+fSBvbkNsaWNrPXsoKSA9PiB3aW5kb3cucHJpbnQoKX0+XG4gICAgICAgICAgUHJpbnQgRGV0YWlsc1xuICAgICAgICA8L0J1dHRvbj5cbiAgICAgIDwvQm94PlxuICAgIDwvQ29udGFpbmVyPlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VQYXJhbXMiLCJ1c2VSb3V0ZXIiLCJ1c2VTZWFyY2hQYXJhbXMiLCJ1c2VBdXRoIiwiQm94IiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiVHlwb2dyYXBoeSIsIkNoaXAiLCJHcmlkIiwiQXZhdGFyIiwiQnV0dG9uIiwiQ2lyY3VsYXJQcm9ncmVzcyIsIkFsZXJ0IiwiQ29udGFpbmVyIiwiUXJDb2RlIiwiUXJDb2RlSWNvbiIsIkNhdGVnb3J5IiwiQ2F0ZWdvcnlJY29uIiwiTG9jYXRpb25PbiIsIkxvY2F0aW9uSWNvbiIsIlBlcnNvbiIsIlBlcnNvbkljb24iLCJDYWxlbmRhclRvZGF5IiwiQ2FsZW5kYXJJY29uIiwiSW5mbyIsIkluZm9JY29uIiwiUHJpbnQiLCJQcmludEljb24iLCJhc3NldEl0ZW1TZXJ2aWNlIiwiQXNzZXREZXRhaWxzUGFnZSIsInBhcmFtcyIsInJvdXRlciIsInNlYXJjaFBhcmFtcyIsImFzc2V0TnVtYmVyIiwiYXNzZXRJdGVtIiwic2V0QXNzZXRJdGVtIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwidXNlciIsImlzQXV0aGVudGljYXRlZCIsImlzTG9hZGluZyIsImF1dGhMb2FkaW5nIiwiaXNBZG1pbiIsInJvbGUiLCJjdXJyZW50VXJsIiwicmV0dXJuVXJsIiwiZW5jb2RlVVJJQ29tcG9uZW50IiwicHVzaCIsImxvYWRBc3NldERldGFpbHMiLCJjb25zb2xlIiwibG9nIiwiYXNzZXRJdGVtRGF0YSIsImdldEFzc2V0SXRlbUJ5QXNzZXROdW1iZXIiLCJlcnIiLCJFcnJvciIsIm1lc3NhZ2UiLCJnZXRTdGF0dXNDb2xvciIsInN0YXR1cyIsInRvTG93ZXJDYXNlIiwiZ2V0Q29uZGl0aW9uQ29sb3IiLCJjb25kaXRpb24iLCJmb3JtYXRTdGF0dXMiLCJyZXBsYWNlIiwibCIsInRvVXBwZXJDYXNlIiwiZm9ybWF0Q29uZGl0aW9uIiwiY2hhckF0Iiwic2xpY2UiLCJmb3JtYXRDdXJyZW5jeSIsImFtb3VudCIsIkludGwiLCJOdW1iZXJGb3JtYXQiLCJzdHlsZSIsImN1cnJlbmN5IiwiZm9ybWF0IiwiZm9ybWF0RGF0ZSIsImRhdGVTdHJpbmciLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwibWF4V2lkdGgiLCJzeCIsInB5IiwiZGlzcGxheSIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsIm1pbkhlaWdodCIsInNldmVyaXR5IiwibWIiLCJ2YXJpYW50IiwiaHJlZiIsInRleHRBbGlnbiIsIm14IiwiYmdjb2xvciIsIndpZHRoIiwiaGVpZ2h0IiwiZm9udFNpemUiLCJjb21wb25lbnQiLCJndXR0ZXJCb3R0b20iLCJhc3NldCIsIm5hbWUiLCJjb2xvciIsImdhcCIsImxhYmVsIiwiY29udGFpbmVyIiwic3BhY2luZyIsIml0ZW0iLCJ4cyIsInNtIiwiZGVzY3JpcHRpb24iLCJ1bml0UHJpY2UiLCJtYW51ZmFjdHVyZXIiLCJtb2RlbCIsImNhdGVnb3J5IiwibG9jYXRpb24iLCJ0eXBlIiwic2VyaWFsTnVtYmVyIiwicHVyY2hhc2VEYXRlIiwid2FycmFudHlEYXRlIiwibm90ZXMiLCJhc3NpZ25lZFVzZXIiLCJlbWFpbCIsIm10IiwibXIiLCJzdGFydEljb24iLCJvbkNsaWNrIiwid2luZG93IiwicHJpbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/asset-details/[assetNumber]/page.tsx\n"));

/***/ })

});