"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const asset_entity_1 = require("../entities/asset.entity");
const asset_item_entity_1 = require("../entities/asset-item.entity");
const category_entity_1 = require("../entities/category.entity");
const location_entity_1 = require("../entities/location.entity");
const user_entity_1 = require("../entities/user.entity");
const audit_log_entity_1 = require("../entities/audit-log.entity");
let ReportsService = class ReportsService {
    assetRepository;
    assetItemRepository;
    categoryRepository;
    locationRepository;
    userRepository;
    auditLogRepository;
    constructor(assetRepository, assetItemRepository, categoryRepository, locationRepository, userRepository, auditLogRepository) {
        this.assetRepository = assetRepository;
        this.assetItemRepository = assetItemRepository;
        this.categoryRepository = categoryRepository;
        this.locationRepository = locationRepository;
        this.userRepository = userRepository;
        this.auditLogRepository = auditLogRepository;
    }
    async generateAssetReport(filters = {}) {
        const query = this.assetItemRepository
            .createQueryBuilder('assetItem')
            .leftJoinAndSelect('assetItem.asset', 'asset')
            .leftJoinAndSelect('asset.category', 'category')
            .leftJoinAndSelect('asset.location', 'location')
            .leftJoinAndSelect('assetItem.assignedTo', 'assignedTo');
        if (filters.startDate) {
            query.andWhere('assetItem.createdAt >= :startDate', {
                startDate: filters.startDate,
            });
        }
        if (filters.endDate) {
            query.andWhere('assetItem.createdAt <= :endDate', {
                endDate: filters.endDate,
            });
        }
        if (filters.categoryId) {
            query.andWhere('asset.categoryId = :categoryId', {
                categoryId: filters.categoryId,
            });
        }
        if (filters.locationId) {
            query.andWhere('asset.locationId = :locationId', {
                locationId: filters.locationId,
            });
        }
        if (filters.status) {
            query.andWhere('assetItem.status = :status', { status: filters.status });
        }
        if (filters.condition) {
            query.andWhere('assetItem.condition = :condition', {
                condition: filters.condition,
            });
        }
        if (filters.assignedUserId) {
            query.andWhere('assetItem.assignedToId = :assignedUserId', {
                assignedUserId: filters.assignedUserId,
            });
        }
        const assetItems = await query.getMany();
        const totalAssets = assetItems.length;
        const totalValue = assetItems.reduce((sum, item) => sum + (item.asset?.unitPrice || 0), 0);
        const statusCounts = assetItems.reduce((acc, item) => {
            acc[item.status] = (acc[item.status] || 0) + 1;
            return acc;
        }, {});
        const statusBreakdown = Object.entries(statusCounts).map(([status, count]) => ({
            status,
            count,
            percentage: totalAssets > 0 ? Math.round((count / totalAssets) * 100) : 0,
        }));
        const conditionCounts = assetItems.reduce((acc, item) => {
            acc[item.condition] = (acc[item.condition] || 0) + 1;
            return acc;
        }, {});
        const conditionBreakdown = Object.entries(conditionCounts).map(([condition, count]) => ({
            condition,
            count,
            percentage: totalAssets > 0 ? Math.round((count / totalAssets) * 100) : 0,
        }));
        const categoryCounts = assetItems.reduce((acc, item) => {
            const categoryName = item.asset?.category?.name || 'Unknown';
            acc[categoryName] = (acc[categoryName] || 0) + 1;
            return acc;
        }, {});
        const categoryBreakdown = Object.entries(categoryCounts).map(([category, count]) => ({
            category,
            count,
            percentage: totalAssets > 0 ? Math.round((count / totalAssets) * 100) : 0,
        }));
        const locationCounts = assetItems.reduce((acc, item) => {
            const locationName = item.asset?.location?.name || 'Unknown';
            acc[locationName] = (acc[locationName] || 0) + 1;
            return acc;
        }, {});
        const locationBreakdown = Object.entries(locationCounts).map(([location, count]) => ({
            location,
            count,
            percentage: totalAssets > 0 ? Math.round((count / totalAssets) * 100) : 0,
        }));
        const assets = assetItems.map((item) => ({
            ...item.asset,
            status: item.status,
            condition: item.condition,
            assignedTo: item.assignedTo,
            assetNumber: item.assetNumber,
            serialNumber: item.serialNumber,
            purchaseDate: item.purchaseDate,
            warrantyExpiry: item.warrantyExpiry,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
        }));
        return {
            totalAssets,
            totalValue,
            statusBreakdown,
            conditionBreakdown,
            categoryBreakdown,
            locationBreakdown,
            assets,
        };
    }
    async generateUtilizationReport(filters = {}) {
        const assetItems = await this.assetItemRepository.find({
            relations: ['asset', 'asset.category', 'asset.location'],
        });
        const totalAssets = assetItems.length;
        const inUseAssets = assetItems.filter((item) => item.status === 'in_use').length;
        const availableAssets = assetItems.filter((item) => item.status === 'in_stock').length;
        const utilizationRate = totalAssets > 0 ? Math.round((inUseAssets / totalAssets) * 100) : 0;
        const categoryStats = assetItems.reduce((acc, item) => {
            const categoryName = item.asset?.category?.name || 'Unknown';
            if (!acc[categoryName]) {
                acc[categoryName] = { total: 0, inUse: 0 };
            }
            acc[categoryName].total++;
            if (item.status === 'in_use') {
                acc[categoryName].inUse++;
            }
            return acc;
        }, {});
        const categoryUtilization = Object.entries(categoryStats).map(([category, stats]) => ({
            category,
            total: stats.total,
            inUse: stats.inUse,
            rate: stats.total > 0 ? Math.round((stats.inUse / stats.total) * 100) : 0,
        }));
        const locationStats = assetItems.reduce((acc, item) => {
            const locationName = item.asset?.location?.name || 'Unknown';
            if (!acc[locationName]) {
                acc[locationName] = { total: 0, inUse: 0 };
            }
            acc[locationName].total++;
            if (item.status === 'in_use') {
                acc[locationName].inUse++;
            }
            return acc;
        }, {});
        const locationUtilization = Object.entries(locationStats).map(([location, stats]) => ({
            location,
            total: stats.total,
            inUse: stats.inUse,
            rate: stats.total > 0 ? Math.round((stats.inUse / stats.total) * 100) : 0,
        }));
        return {
            totalAssets,
            inUseAssets,
            availableAssets,
            utilizationRate,
            categoryUtilization,
            locationUtilization,
        };
    }
    async generateMaintenanceReport(filters = {}) {
        const assetItems = await this.assetItemRepository.find({
            relations: ['asset', 'asset.category', 'asset.location'],
        });
        const assetsInMaintenance = assetItems.filter((item) => item.status === 'maintenance').length;
        const assetsDamaged = assetItems.filter((item) => item.status === 'damaged').length;
        const assetsRetired = assetItems.filter((item) => item.status === 'retired').length;
        const maintenanceAssets = assetItems.filter((item) => item.status === 'maintenance' || item.status === 'damaged');
        const categoryStats = maintenanceAssets.reduce((acc, item) => {
            const categoryName = item.asset?.category?.name || 'Unknown';
            acc[categoryName] = (acc[categoryName] || 0) + 1;
            return acc;
        }, {});
        const maintenanceByCategory = Object.entries(categoryStats).map(([category, count]) => ({
            category,
            count,
        }));
        const locationStats = maintenanceAssets.reduce((acc, item) => {
            const locationName = item.asset?.location?.name || 'Unknown';
            acc[locationName] = (acc[locationName] || 0) + 1;
            return acc;
        }, {});
        const maintenanceByLocation = Object.entries(locationStats).map(([location, count]) => ({
            location,
            count,
        }));
        const maintenanceScheduleItems = assetItems.filter((item) => item.status === 'in_use' && item.condition === 'fair');
        const maintenanceSchedule = maintenanceScheduleItems.map((item) => ({
            ...item.asset,
            status: item.status,
            condition: item.condition,
            assignedTo: item.assignedTo,
            assetNumber: item.assetNumber,
            serialNumber: item.serialNumber,
            purchaseDate: item.purchaseDate,
            warrantyExpiry: item.warrantyExpiry,
        }));
        return {
            assetsInMaintenance,
            assetsDamaged,
            assetsRetired,
            maintenanceByCategory,
            maintenanceByLocation,
            maintenanceSchedule,
        };
    }
    async generateActivityReport(filters = {}) {
        const query = this.auditLogRepository
            .createQueryBuilder('auditLog')
            .leftJoinAndSelect('auditLog.user', 'user');
        if (filters.startDate) {
            query.andWhere('auditLog.createdAt >= :startDate', {
                startDate: filters.startDate,
            });
        }
        if (filters.endDate) {
            query.andWhere('auditLog.createdAt <= :endDate', {
                endDate: filters.endDate,
            });
        }
        const auditLogs = await query.getMany();
        const totalActivities = auditLogs.length;
        const recentActivities = await this.auditLogRepository.find({
            relations: ['user'],
            order: { createdAt: 'DESC' },
            take: 20,
        });
        const actionStats = auditLogs.reduce((acc, log) => {
            acc[log.action] = (acc[log.action] || 0) + 1;
            return acc;
        }, {});
        const activityByAction = Object.entries(actionStats).map(([action, count]) => ({
            action,
            count,
        }));
        const userStats = auditLogs.reduce((acc, log) => {
            const userName = log.user?.fullName || 'Unknown';
            acc[userName] = (acc[userName] || 0) + 1;
            return acc;
        }, {});
        const activityByUser = Object.entries(userStats).map(([user, count]) => ({
            user,
            count,
        }));
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const trendData = await this.auditLogRepository
            .createQueryBuilder('auditLog')
            .select('DATE(auditLog.createdAt)', 'date')
            .addSelect('COUNT(*)', 'count')
            .where('auditLog.createdAt >= :thirtyDaysAgo', { thirtyDaysAgo })
            .groupBy('DATE(auditLog.createdAt)')
            .orderBy('DATE(auditLog.createdAt)', 'ASC')
            .getRawMany();
        const activityTrend = trendData.map((item) => ({
            date: item.date,
            count: parseInt(item.count),
        }));
        return {
            totalActivities,
            recentActivities,
            activityByAction,
            activityByUser,
            activityTrend,
        };
    }
};
exports.ReportsService = ReportsService;
exports.ReportsService = ReportsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(asset_entity_1.Asset)),
    __param(1, (0, typeorm_1.InjectRepository)(asset_item_entity_1.AssetItem)),
    __param(2, (0, typeorm_1.InjectRepository)(category_entity_1.Category)),
    __param(3, (0, typeorm_1.InjectRepository)(location_entity_1.Location)),
    __param(4, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(5, (0, typeorm_1.InjectRepository)(audit_log_entity_1.AuditLog)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ReportsService);
//# sourceMappingURL=reports.service.js.map