/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/asset-details/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Capp%5C%5Casset-details%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Capp%5C%5Casset-details%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/asset-details/layout.tsx */ \"(app-pages-browser)/./src/app/asset-details/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q0ludmljdGElNUMlNUNVcGNvbWluZyUyMFByb2plY3RzJTVDJTVDQXNzZXRzJTIwTWFuYWdlbWVudCUyMFN5c3RlbSU1QyU1Q2Fzc2V0cy1tYW5hZ2VtZW50LWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Fzc2V0LWRldGFpbHMlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQXlKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/NDE0NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXEludmljdGFcXFxcVXBjb21pbmcgUHJvamVjdHNcXFxcQXNzZXRzIE1hbmFnZW1lbnQgU3lzdGVtXFxcXGFzc2V0cy1tYW5hZ2VtZW50LWFwcFxcXFxzcmNcXFxcYXBwXFxcXGFzc2V0LWRldGFpbHNcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Capp%5C%5Casset-details%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9uYXZpZ2F0aW9uLmpzPzcwNjMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb25cIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/asset-details/layout.tsx":
/*!******************************************!*\
  !*** ./src/app/asset-details/layout.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst AssetDetailsLayout = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\asset-details\\\\layout.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AssetDetailsLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AssetDetailsLayout);\nvar _c;\n$RefreshReg$(_c, \"AssetDetailsLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXNzZXQtZGV0YWlscy9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7QUFFcUQ7QUFHckQsTUFBTUMscUJBQXFCO1FBQUMsRUFBRUMsUUFBUSxFQUFnQjtJQUNwRCxxQkFDRSw4REFBQ0YsK0RBQVlBO2tCQUNWRTs7Ozs7O0FBR1A7S0FOTUQ7QUFRTiwrREFBZUEsa0JBQWtCQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvYXNzZXQtZGV0YWlscy9sYXlvdXQudHN4Pzg3NDEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnXG5pbXBvcnQgdHlwZSB7IENoaWxkcmVuVHlwZSB9IGZyb20gJ0Bjb3JlL3R5cGVzJ1xuXG5jb25zdCBBc3NldERldGFpbHNMYXlvdXQgPSAoeyBjaGlsZHJlbiB9OiBDaGlsZHJlblR5cGUpID0+IHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvQXV0aFByb3ZpZGVyPlxuICApXG59XG5cbmV4cG9ydCBkZWZhdWx0IEFzc2V0RGV0YWlsc0xheW91dFxuIl0sIm5hbWVzIjpbIkF1dGhQcm92aWRlciIsIkFzc2V0RGV0YWlsc0xheW91dCIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/asset-details/layout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api/index.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAuthenticated = !!user && _lib_api__WEBPACK_IMPORTED_MODULE_3__.authService.isAuthenticated();\n    const clearError = ()=>setError(null);\n    const refreshUser = async ()=>{\n        if (!_lib_api__WEBPACK_IMPORTED_MODULE_3__.authService.isAuthenticated()) {\n            setUser(null);\n            setIsLoading(false);\n            return;\n        }\n        try {\n            setIsLoading(true);\n            const userData = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authService.getProfile();\n            setUser(userData);\n            setError(null);\n        } catch (error) {\n            console.error(\"Failed to refresh user:\", error);\n            setUser(null);\n            setError(error instanceof Error ? error.message : \"Failed to refresh user\");\n            _lib_api__WEBPACK_IMPORTED_MODULE_3__.authService.logout();\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const login = async (credentials)=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authService.login(credentials);\n            setUser(response.user);\n            // Redirect to dashboard after successful login\n            router.replace(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            setError(error instanceof Error ? error.message : \"Login failed\");\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authService.register(userData);\n            setUser(response.user);\n            // Redirect to dashboard after successful registration\n            router.replace(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Registration failed:\", error);\n            setError(error instanceof Error ? error.message : \"Registration failed\");\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = ()=>{\n        _lib_api__WEBPACK_IMPORTED_MODULE_3__.authService.logout();\n        setUser(null);\n        setError(null);\n        router.replace(\"/login\");\n    };\n    // Initialize auth state on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        refreshUser();\n    }, []);\n    // Listen for storage changes (logout from another tab)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleStorageChange = (e)=>{\n            if (e.key === \"auth_token\" && !e.newValue) {\n                // Token was removed, user logged out\n                setUser(null);\n                setError(null);\n                router.replace(\"/login\");\n            }\n        };\n        window.addEventListener(\"storage\", handleStorageChange);\n        return ()=>window.removeEventListener(\"storage\", handleStorageChange);\n    }, [\n        router\n    ]);\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        error,\n        login,\n        register,\n        logout,\n        refreshUser,\n        clearError\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 135,\n        columnNumber: 10\n    }, this);\n}\n_s(AuthProvider, \"EAPRY10XFg762l0zG7+zEIjP2Fw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/asset-logs.ts":
/*!***********************************!*\
  !*** ./src/lib/api/asset-logs.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssetLogsService: function() { return /* binding */ AssetLogsService; },\n/* harmony export */   assetLogsService: function() { return /* binding */ assetLogsService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n/* harmony import */ var _types_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/api */ \"(app-pages-browser)/./src/types/api.ts\");\n\n\n\nclass AssetLogsService {\n    async getRecentLogs(limit) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.RECENT, {\n            limit\n        });\n    }\n    async getAssetLogs(assetId, params) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.BY_ASSET(assetId), params);\n    }\n    async getUserActivityLogs(userId, params) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.BY_USER(userId), params);\n    }\n    async getLogsByAction(action, limit) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.BY_ACTION(action), {\n            limit\n        });\n    }\n    async getLogsByDateRange(params) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSET_LOGS.DATE_RANGE, params);\n    }\n    // Helper methods for common log queries\n    async getAssetCreationLogs(limit) {\n        return this.getLogsByAction(_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.CREATED, limit);\n    }\n    async getAssetAssignmentLogs(limit) {\n        return this.getLogsByAction(_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.ASSIGNED, limit);\n    }\n    async getAssetStatusChangeLogs(limit) {\n        return this.getLogsByAction(_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.STATUS_CHANGED, limit);\n    }\n    async getTodaysLogs() {\n        const today = new Date();\n        const startOfDay = new Date(today.setHours(0, 0, 0, 0));\n        const endOfDay = new Date(today.setHours(23, 59, 59, 999));\n        return this.getLogsByDateRange({\n            startDate: startOfDay.toISOString(),\n            endDate: endOfDay.toISOString(),\n            limit: 100\n        });\n    }\n    async getWeeklyLogs() {\n        const today = new Date();\n        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);\n        return this.getLogsByDateRange({\n            startDate: weekAgo.toISOString(),\n            endDate: today.toISOString(),\n            limit: 500\n        });\n    }\n    async getMonthlyLogs() {\n        const today = new Date();\n        const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);\n        return this.getLogsByDateRange({\n            startDate: monthAgo.toISOString(),\n            endDate: today.toISOString(),\n            limit: 1000\n        });\n    }\n    // Format log action for display\n    formatLogAction(action) {\n        const actionMap = {\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.CREATED]: \"Created\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.UPDATED]: \"Updated\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.DELETED]: \"Deleted\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.ASSIGNED]: \"Assigned\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.UNASSIGNED]: \"Unassigned\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.STATUS_CHANGED]: \"Status Changed\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.LOCATION_CHANGED]: \"Location Changed\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.MAINTENANCE]: \"Maintenance\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.RETIRED]: \"Retired\"\n        };\n        return actionMap[action] || action;\n    }\n    // Get log action color for UI\n    getLogActionColor(action) {\n        const colorMap = {\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.CREATED]: \"success\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.UPDATED]: \"info\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.DELETED]: \"error\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.ASSIGNED]: \"primary\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.UNASSIGNED]: \"warning\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.STATUS_CHANGED]: \"info\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.LOCATION_CHANGED]: \"info\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.MAINTENANCE]: \"warning\",\n            [_types_api__WEBPACK_IMPORTED_MODULE_2__.LogAction.RETIRED]: \"secondary\"\n        };\n        return colorMap[action] || \"default\";\n    }\n}\nconst assetLogsService = new AssetLogsService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/asset-logs.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/assetItemService.ts":
/*!*****************************************!*\
  !*** ./src/lib/api/assetItemService.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assetItemService: function() { return /* binding */ assetItemService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\nconst assetItemService = {\n    // Create multiple asset items for an asset\n    async createAssetItems (assetId, data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/\".concat(assetId, \"/items\"), data);\n        return response;\n    },\n    // Get all asset items for a specific asset\n    async getAssetItems (assetId) {\n        console.log(\"AssetItemService: Getting asset items for asset ID:\", assetId);\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/\".concat(assetId, \"/items\"));\n        console.log(\"AssetItemService: Response received:\", response);\n        return response;\n    },\n    // Get asset item by ID\n    async getAssetItem (itemId) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/items/\".concat(itemId));\n        return response;\n    },\n    // Update asset item\n    async updateAssetItem (itemId, data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(\"/assets/items/\".concat(itemId), data);\n        return response;\n    },\n    // Transfer asset item status\n    async transferAssetItem (data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/items/transfer\", data);\n        return response;\n    },\n    // Get asset item quantities by status for a specific asset\n    async getAssetItemQuantities (assetId) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/\".concat(assetId, \"/items/quantities\"));\n        return response;\n    },\n    // Get all asset item quantities overview\n    async getAllAssetItemQuantities () {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/items/quantities/all\");\n        return response;\n    },\n    // Get asset item by asset number (Authenticated)\n    async getAssetItemByAssetNumber (assetNumber) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/by-asset-number/\".concat(assetNumber));\n        return response;\n    },\n    // Delete asset item\n    async deleteAssetItem (itemId) {\n        await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/assets/items/\".concat(itemId));\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/assetItemService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/assetQuantityService.ts":
/*!*********************************************!*\
  !*** ./src/lib/api/assetQuantityService.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assetQuantityService: function() { return /* binding */ assetQuantityService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\nconst assetQuantityService = {\n    // Get asset quantities by status\n    async getAssetQuantities (assetId) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/\".concat(assetId, \"/quantities\"));\n        return response.data;\n    },\n    // Transfer quantities between statuses\n    async transferQuantity (data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/quantities/transfer\", data);\n        return response.data;\n    },\n    // Bulk transfer quantities\n    async bulkTransferQuantity (data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/quantities/bulk-transfer\", data);\n        return response.data;\n    },\n    // Set asset quantities by status\n    async setAssetQuantities (data) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/assets/quantities/set\", data);\n        return response.data;\n    },\n    // Get transfer history for an asset\n    async getTransferHistory (assetId) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/\".concat(assetId, \"/transfer-history\"));\n        return response.data;\n    },\n    // Get all asset quantities overview\n    async getAllAssetQuantities () {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/assets/quantities/all\");\n        return response.data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/assetQuantityService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/assets.ts":
/*!*******************************!*\
  !*** ./src/lib/api/assets.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssetsService: function() { return /* binding */ AssetsService; },\n/* harmony export */   assetsService: function() { return /* binding */ assetsService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n\n\nclass AssetsService {\n    async getAssets(params) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BASE, params);\n    }\n    async getAssetById(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BY_ID(id));\n    }\n    async getAssetByNumber(assetNumber) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BY_ASSET_NUMBER(assetNumber));\n    }\n    async createAsset(data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BASE, data);\n    }\n    async updateAsset(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BY_ID(id), data);\n    }\n    async deleteAsset(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.BY_ID(id));\n    }\n    async assignAsset(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.ASSIGN(id), data);\n    }\n    async unassignAsset(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.UNASSIGN(id));\n    }\n    async getAssetStats() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.ASSETS.STATS);\n    }\n    // Search assets with debouncing support\n    async searchAssets(searchTerm, filters) {\n        return this.getAssets({\n            search: searchTerm,\n            ...filters\n        });\n    }\n    // Get assets by category\n    async getAssetsByCategory(categoryId) {\n        return this.getAssets({\n            categoryId\n        });\n    }\n    // Get assets by location\n    async getAssetsByLocation(locationId) {\n        return this.getAssets({\n            locationId\n        });\n    }\n    // Get assets assigned to a user\n    async getAssetsByUser(userId) {\n        return this.getAssets({\n            assignedToId: userId\n        });\n    }\n    // Upload asset image\n    async uploadAssetImage(assetId, file) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.uploadFile(\"/assets/\".concat(assetId, \"/upload-image\"), file);\n    }\n}\nconst assetsService = new AssetsService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/assets.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/auth.ts":
/*!*****************************!*\
  !*** ./src/lib/api/auth.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: function() { return /* binding */ AuthService; },\n/* harmony export */   authService: function() { return /* binding */ authService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n\n\nclass AuthService {\n    async login(credentials) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.LOGIN, credentials);\n        // Store the token\n        (0,_config__WEBPACK_IMPORTED_MODULE_1__.setAuthToken)(response.token);\n        return response;\n    }\n    async register(userData) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.REGISTER, userData);\n        // Store the token\n        (0,_config__WEBPACK_IMPORTED_MODULE_1__.setAuthToken)(response.token);\n        return response;\n    }\n    async getProfile() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.PROFILE);\n    }\n    async verifyToken() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.AUTH.VERIFY_TOKEN);\n    }\n    logout() {\n        (0,_config__WEBPACK_IMPORTED_MODULE_1__.removeAuthToken)();\n        // Redirect to login page\n        if (true) {\n            window.location.href = \"/login\";\n        }\n    }\n    isAuthenticated() {\n        if (false) {}\n        const token = localStorage.getItem(\"auth_token\");\n        return !!token;\n    }\n}\nconst authService = new AuthService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/auth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/categories.ts":
/*!***********************************!*\
  !*** ./src/lib/api/categories.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoriesService: function() { return /* binding */ CategoriesService; },\n/* harmony export */   categoriesService: function() { return /* binding */ categoriesService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n\n\nclass CategoriesService {\n    async getCategories() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BASE);\n    }\n    async getActiveCategories() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.ACTIVE);\n    }\n    async getCategoryHierarchy() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.HIERARCHY);\n    }\n    async getCategoryById(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BY_ID(id));\n    }\n    async createCategory(data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BASE, data);\n    }\n    async updateCategory(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BY_ID(id), data);\n    }\n    async deleteCategory(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.CATEGORIES.BY_ID(id));\n    }\n    // Helper method to get categories as options for forms\n    async getCategoryOptions() {\n        const categories = await this.getActiveCategories();\n        return categories.map((category)=>({\n                value: category.id,\n                label: category.fullPath || category.name\n            }));\n    }\n    // Get root categories (no parent)\n    async getRootCategories() {\n        const categories = await this.getCategories();\n        return categories.filter((category)=>!category.parentId);\n    }\n    // Get subcategories of a parent category\n    async getSubcategories(parentId) {\n        const categories = await this.getCategories();\n        return categories.filter((category)=>category.parentId === parentId);\n    }\n}\nconst categoriesService = new CategoriesService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/categories.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/client.ts":
/*!*******************************!*\
  !*** ./src/lib/api/client.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: function() { return /* binding */ ApiClient; },\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; }\n/* harmony export */ });\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n\nclass ApiClient {\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        const config = {\n            headers: (0,_config__WEBPACK_IMPORTED_MODULE_0__.getAuthHeaders)(),\n            ...options\n        };\n        console.log(\"API Client: Making request to:\", url);\n        console.log(\"API Client: Request config:\", config);\n        try {\n            const response = await fetch(url, config);\n            console.log(\"API Client: Response status:\", response.status);\n            console.log(\"API Client: Response ok:\", response.ok);\n            if (!response.ok) {\n                if (response.status === 401 || response.status === 403) {\n                    // Token expired, invalid, or insufficient permissions\n                    (0,_config__WEBPACK_IMPORTED_MODULE_0__.removeAuthToken)();\n                    // Redirect to login page\n                    if (true) {\n                        window.location.href = \"/login\";\n                    }\n                }\n                let errorData;\n                try {\n                    errorData = await response.json();\n                } catch (e) {\n                    errorData = {\n                        message: response.statusText || \"An error occurred\",\n                        statusCode: response.status\n                    };\n                }\n                const error = new Error(errorData.message || \"An error occurred\");\n                error.status = response.status;\n                error.response = {\n                    data: errorData\n                };\n                throw error;\n            }\n            // Handle empty responses\n            const contentType = response.headers.get(\"content-type\");\n            if (contentType && contentType.includes(\"application/json\")) {\n                const data = await response.json();\n                console.log(\"API Client: Response data:\", data);\n                return data;\n            } else {\n                console.log(\"API Client: Non-JSON response, returning empty object\");\n                return {};\n            }\n        } catch (error) {\n            if (error instanceof Error) {\n                // Check if this is a network error (backend not available)\n                if (error.message.includes(\"fetch\") || error.name === \"TypeError\") {\n                    throw new Error(\"Backend server not available. Please start the backend server or use demo mode.\");\n                }\n                throw error;\n            }\n            throw new Error(\"Network error occurred\");\n        }\n    }\n    async get(endpoint, params) {\n        const url = new URL(\"\".concat(this.baseURL).concat(endpoint));\n        if (params) {\n            Object.entries(params).forEach((param)=>{\n                let [key, value] = param;\n                if (value !== undefined && value !== null) {\n                    url.searchParams.append(key, String(value));\n                }\n            });\n        }\n        return this.request(url.pathname + url.search);\n    }\n    async post(endpoint, data) {\n        return this.request(endpoint, {\n            method: \"POST\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async put(endpoint, data) {\n        return this.request(endpoint, {\n            method: \"PUT\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async patch(endpoint, data) {\n        return this.request(endpoint, {\n            method: \"PATCH\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    async delete(endpoint) {\n        return this.request(endpoint, {\n            method: \"DELETE\"\n        });\n    }\n    // File upload method\n    async uploadFile(endpoint, file, additionalData) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        if (additionalData) {\n            Object.entries(additionalData).forEach((param)=>{\n                let [key, value] = param;\n                formData.append(key, String(value));\n            });\n        }\n        const token = (0,_config__WEBPACK_IMPORTED_MODULE_0__.getAuthHeaders)().Authorization;\n        const headers = {};\n        if (token) {\n            headers.Authorization = token;\n        }\n        return this.request(endpoint, {\n            method: \"POST\",\n            headers,\n            body: formData\n        });\n    }\n    constructor(){\n        this.baseURL = _config__WEBPACK_IMPORTED_MODULE_0__.API_CONFIG.BASE_URL;\n    }\n}\n// Create a singleton instance\nconst apiClient = new ApiClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/client.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/config.ts":
/*!*******************************!*\
  !*** ./src/lib/api/config.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: function() { return /* binding */ API_CONFIG; },\n/* harmony export */   getAuthHeaders: function() { return /* binding */ getAuthHeaders; },\n/* harmony export */   getAuthToken: function() { return /* binding */ getAuthToken; },\n/* harmony export */   removeAuthToken: function() { return /* binding */ removeAuthToken; },\n/* harmony export */   setAuthToken: function() { return /* binding */ setAuthToken; }\n/* harmony export */ });\nconst API_CONFIG = {\n    BASE_URL: \"http://192.168.1.67:3001\" || 0,\n    TIMEOUT: 10000,\n    ENDPOINTS: {\n        // Auth\n        AUTH: {\n            LOGIN: \"/auth/login\",\n            REGISTER: \"/auth/register\",\n            PROFILE: \"/auth/profile\",\n            VERIFY_TOKEN: \"/auth/verify-token\"\n        },\n        // Users\n        USERS: {\n            BASE: \"/users\",\n            BY_ID: (id)=>\"/users/\".concat(id),\n            CHANGE_PASSWORD: (id)=>\"/users/\".concat(id, \"/change-password\")\n        },\n        // Assets\n        ASSETS: {\n            BASE: \"/assets\",\n            BY_ID: (id)=>\"/assets/\".concat(id),\n            BY_ASSET_NUMBER: (assetNumber)=>\"/assets/asset-number/\".concat(assetNumber),\n            ASSIGN: (id)=>\"/assets/\".concat(id, \"/assign\"),\n            UNASSIGN: (id)=>\"/assets/\".concat(id, \"/unassign\"),\n            STATS: \"/assets/stats\"\n        },\n        // Categories\n        CATEGORIES: {\n            BASE: \"/categories\",\n            BY_ID: (id)=>\"/categories/\".concat(id),\n            ACTIVE: \"/categories/active\",\n            HIERARCHY: \"/categories/hierarchy\"\n        },\n        // Locations\n        LOCATIONS: {\n            BASE: \"/locations\",\n            BY_ID: (id)=>\"/locations/\".concat(id),\n            ACTIVE: \"/locations/active\",\n            HIERARCHY: \"/locations/hierarchy\"\n        },\n        // Asset Logs\n        ASSET_LOGS: {\n            RECENT: \"/asset-logs/recent\",\n            BY_ASSET: (assetId)=>\"/asset-logs/asset/\".concat(assetId),\n            BY_USER: (userId)=>\"/asset-logs/user/\".concat(userId),\n            BY_ACTION: (action)=>\"/asset-logs/action/\".concat(action),\n            DATE_RANGE: \"/asset-logs/date-range\"\n        }\n    }\n};\nconst getAuthToken = ()=>{\n    if (false) {}\n    return localStorage.getItem(\"auth_token\");\n};\nconst setAuthToken = (token)=>{\n    if (false) {}\n    localStorage.setItem(\"auth_token\", token);\n};\nconst removeAuthToken = ()=>{\n    if (false) {}\n    localStorage.removeItem(\"auth_token\");\n};\nconst getAuthHeaders = ()=>{\n    const token = getAuthToken();\n    console.log(\"getAuthHeaders: Token retrieved:\", !!token);\n    console.log(\"getAuthHeaders: Token value:\", token ? \"\".concat(token.substring(0, 20), \"...\") : \"null\");\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...token && {\n            Authorization: \"Bearer \".concat(token)\n        }\n    };\n    console.log(\"getAuthHeaders: Headers created:\", Object.keys(headers));\n    return headers;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/config.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/index.ts":
/*!******************************!*\
  !*** ./src/lib/api/index.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: function() { return /* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_9__.API_CONFIG; },\n/* harmony export */   AssetCondition: function() { return /* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.AssetCondition; },\n/* harmony export */   AssetStatus: function() { return /* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.AssetStatus; },\n/* harmony export */   LocationType: function() { return /* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.LocationType; },\n/* harmony export */   LogAction: function() { return /* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.LogAction; },\n/* harmony export */   UserRole: function() { return /* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.UserRole; },\n/* harmony export */   UserStatus: function() { return /* reexport safe */ _types_api__WEBPACK_IMPORTED_MODULE_10__.UserStatus; },\n/* harmony export */   apiClient: function() { return /* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_8__.apiClient; },\n/* harmony export */   assetItemService: function() { return /* reexport safe */ _assetItemService__WEBPACK_IMPORTED_MODULE_3__.assetItemService; },\n/* harmony export */   assetLogsService: function() { return /* reexport safe */ _asset_logs__WEBPACK_IMPORTED_MODULE_7__.assetLogsService; },\n/* harmony export */   assetQuantityService: function() { return /* reexport safe */ _assetQuantityService__WEBPACK_IMPORTED_MODULE_2__.assetQuantityService; },\n/* harmony export */   assetsService: function() { return /* reexport safe */ _assets__WEBPACK_IMPORTED_MODULE_1__.assetsService; },\n/* harmony export */   authService: function() { return /* reexport safe */ _auth__WEBPACK_IMPORTED_MODULE_0__.authService; },\n/* harmony export */   categoriesService: function() { return /* reexport safe */ _categories__WEBPACK_IMPORTED_MODULE_4__.categoriesService; },\n/* harmony export */   getAuthToken: function() { return /* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_9__.getAuthToken; },\n/* harmony export */   locationsService: function() { return /* reexport safe */ _locations__WEBPACK_IMPORTED_MODULE_5__.locationsService; },\n/* harmony export */   removeAuthToken: function() { return /* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_9__.removeAuthToken; },\n/* harmony export */   setAuthToken: function() { return /* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_9__.setAuthToken; },\n/* harmony export */   usersService: function() { return /* reexport safe */ _users__WEBPACK_IMPORTED_MODULE_6__.usersService; }\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(app-pages-browser)/./src/lib/api/auth.ts\");\n/* harmony import */ var _assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./assets */ \"(app-pages-browser)/./src/lib/api/assets.ts\");\n/* harmony import */ var _assetQuantityService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./assetQuantityService */ \"(app-pages-browser)/./src/lib/api/assetQuantityService.ts\");\n/* harmony import */ var _assetItemService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./assetItemService */ \"(app-pages-browser)/./src/lib/api/assetItemService.ts\");\n/* harmony import */ var _categories__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./categories */ \"(app-pages-browser)/./src/lib/api/categories.ts\");\n/* harmony import */ var _locations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./locations */ \"(app-pages-browser)/./src/lib/api/locations.ts\");\n/* harmony import */ var _users__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./users */ \"(app-pages-browser)/./src/lib/api/users.ts\");\n/* harmony import */ var _asset_logs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./asset-logs */ \"(app-pages-browser)/./src/lib/api/asset-logs.ts\");\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n/* harmony import */ var _types_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/types/api */ \"(app-pages-browser)/./src/types/api.ts\");\n// Export all API services\n\n\n\n\n\n\n\n\n// Export API client and config\n\n\n// Export types\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvYXBpL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDBCQUEwQjtBQUNVO0FBQ0k7QUFDcUI7QUFDUjtBQUNMO0FBQ0Y7QUFDUjtBQUNTO0FBRS9DLCtCQUErQjtBQUNLO0FBQzhDO0FBRWxGLGVBQWU7QUFDWSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvbGliL2FwaS9pbmRleC50cz9lNjkzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydCBhbGwgQVBJIHNlcnZpY2VzXG5leHBvcnQgeyBhdXRoU2VydmljZSB9IGZyb20gJy4vYXV0aCdcbmV4cG9ydCB7IGFzc2V0c1NlcnZpY2UgfSBmcm9tICcuL2Fzc2V0cydcbmV4cG9ydCB7IGFzc2V0UXVhbnRpdHlTZXJ2aWNlIH0gZnJvbSAnLi9hc3NldFF1YW50aXR5U2VydmljZSdcbmV4cG9ydCB7IGFzc2V0SXRlbVNlcnZpY2UgfSBmcm9tICcuL2Fzc2V0SXRlbVNlcnZpY2UnXG5leHBvcnQgeyBjYXRlZ29yaWVzU2VydmljZSB9IGZyb20gJy4vY2F0ZWdvcmllcydcbmV4cG9ydCB7IGxvY2F0aW9uc1NlcnZpY2UgfSBmcm9tICcuL2xvY2F0aW9ucydcbmV4cG9ydCB7IHVzZXJzU2VydmljZSB9IGZyb20gJy4vdXNlcnMnXG5leHBvcnQgeyBhc3NldExvZ3NTZXJ2aWNlIH0gZnJvbSAnLi9hc3NldC1sb2dzJ1xuXG4vLyBFeHBvcnQgQVBJIGNsaWVudCBhbmQgY29uZmlnXG5leHBvcnQgeyBhcGlDbGllbnQgfSBmcm9tICcuL2NsaWVudCdcbmV4cG9ydCB7IEFQSV9DT05GSUcsIGdldEF1dGhUb2tlbiwgc2V0QXV0aFRva2VuLCByZW1vdmVBdXRoVG9rZW4gfSBmcm9tICcuL2NvbmZpZydcblxuLy8gRXhwb3J0IHR5cGVzXG5leHBvcnQgKiBmcm9tICdAL3R5cGVzL2FwaSdcbiJdLCJuYW1lcyI6WyJhdXRoU2VydmljZSIsImFzc2V0c1NlcnZpY2UiLCJhc3NldFF1YW50aXR5U2VydmljZSIsImFzc2V0SXRlbVNlcnZpY2UiLCJjYXRlZ29yaWVzU2VydmljZSIsImxvY2F0aW9uc1NlcnZpY2UiLCJ1c2Vyc1NlcnZpY2UiLCJhc3NldExvZ3NTZXJ2aWNlIiwiYXBpQ2xpZW50IiwiQVBJX0NPTkZJRyIsImdldEF1dGhUb2tlbiIsInNldEF1dGhUb2tlbiIsInJlbW92ZUF1dGhUb2tlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/locations.ts":
/*!**********************************!*\
  !*** ./src/lib/api/locations.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LocationsService: function() { return /* binding */ LocationsService; },\n/* harmony export */   locationsService: function() { return /* binding */ locationsService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n\n\nclass LocationsService {\n    async getLocations() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BASE);\n    }\n    async getActiveLocations() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.ACTIVE);\n    }\n    async getLocationHierarchy() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.HIERARCHY);\n    }\n    async getLocationById(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BY_ID(id));\n    }\n    async createLocation(data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BASE, data);\n    }\n    async updateLocation(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BY_ID(id), data);\n    }\n    async deleteLocation(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.LOCATIONS.BY_ID(id));\n    }\n    // Helper method to get locations as options for forms\n    async getLocationOptions() {\n        const locations = await this.getActiveLocations();\n        return locations.map((location)=>({\n                value: location.id,\n                label: location.fullPath || location.name\n            }));\n    }\n    // Get root locations (no parent)\n    async getRootLocations() {\n        const locations = await this.getLocations();\n        return locations.filter((location)=>!location.parentId);\n    }\n    // Get sub-locations of a parent location\n    async getSublocations(parentId) {\n        const locations = await this.getLocations();\n        return locations.filter((location)=>location.parentId === parentId);\n    }\n    // Get locations by type\n    async getLocationsByType(type) {\n        const locations = await this.getLocations();\n        return locations.filter((location)=>location.type === type);\n    }\n    // Parse coordinates if they exist\n    parseCoordinates(coordinates) {\n        if (!coordinates) return null;\n        try {\n            const parsed = JSON.parse(coordinates);\n            if (parsed.lat && parsed.lng) {\n                return {\n                    lat: parsed.lat,\n                    lng: parsed.lng\n                };\n            }\n        } catch (error) {\n            console.error(\"Failed to parse coordinates:\", error);\n        }\n        return null;\n    }\n    // Format coordinates for storage\n    formatCoordinates(lat, lng) {\n        return JSON.stringify({\n            lat,\n            lng\n        });\n    }\n}\nconst locationsService = new LocationsService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/locations.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/users.ts":
/*!******************************!*\
  !*** ./src/lib/api/users.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UsersService: function() { return /* binding */ UsersService; },\n/* harmony export */   usersService: function() { return /* binding */ usersService; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/api/config.ts\");\n\n\nclass UsersService {\n    async getUsers() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BASE);\n    }\n    async getUserById(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BY_ID(id));\n    }\n    async createUser(data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BASE, data);\n    }\n    async updateUser(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BY_ID(id), data);\n    }\n    async deleteUser(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.BY_ID(id));\n    }\n    async changePassword(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(_config__WEBPACK_IMPORTED_MODULE_1__.API_CONFIG.ENDPOINTS.USERS.CHANGE_PASSWORD(id), data);\n    }\n    // Helper method to get users as options for forms\n    async getUserOptions() {\n        const users = await this.getUsers();\n        return users.map((user)=>({\n                value: user.id,\n                label: user.fullName\n            }));\n    }\n    // Get users by role\n    async getUsersByRole(role) {\n        const users = await this.getUsers();\n        return users.filter((user)=>user.role === role);\n    }\n    // Get active users only\n    async getActiveUsers() {\n        const users = await this.getUsers();\n        return users.filter((user)=>user.status === \"active\");\n    }\n    // Get users by department\n    async getUsersByDepartment(department) {\n        const users = await this.getUsers();\n        return users.filter((user)=>user.department === department);\n    }\n    // Search users by name or email\n    async searchUsers(searchTerm) {\n        const users = await this.getUsers();\n        const term = searchTerm.toLowerCase();\n        return users.filter((user)=>user.fullName.toLowerCase().includes(term) || user.email.toLowerCase().includes(term) || user.firstName.toLowerCase().includes(term) || user.lastName.toLowerCase().includes(term));\n    }\n}\nconst usersService = new UsersService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/users.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/api.ts":
/*!**************************!*\
  !*** ./src/types/api.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssetCondition: function() { return /* binding */ AssetCondition; },\n/* harmony export */   AssetStatus: function() { return /* binding */ AssetStatus; },\n/* harmony export */   LocationType: function() { return /* binding */ LocationType; },\n/* harmony export */   LogAction: function() { return /* binding */ LogAction; },\n/* harmony export */   UserRole: function() { return /* binding */ UserRole; },\n/* harmony export */   UserStatus: function() { return /* binding */ UserStatus; }\n/* harmony export */ });\n// User Types\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"ADMIN\"] = \"admin\";\n    UserRole[\"MANAGER\"] = \"manager\";\n    UserRole[\"VIEWER\"] = \"viewer\";\n})(UserRole || (UserRole = {}));\nvar UserStatus;\n(function(UserStatus) {\n    UserStatus[\"ACTIVE\"] = \"active\";\n    UserStatus[\"INACTIVE\"] = \"inactive\";\n    UserStatus[\"SUSPENDED\"] = \"suspended\";\n})(UserStatus || (UserStatus = {}));\nvar AssetStatus;\n(function(AssetStatus) {\n    AssetStatus[\"IN_STOCK\"] = \"in_stock\";\n    AssetStatus[\"IN_USE\"] = \"in_use\";\n    AssetStatus[\"MAINTENANCE\"] = \"maintenance\";\n    AssetStatus[\"RETIRED\"] = \"retired\";\n    AssetStatus[\"LOST\"] = \"lost\";\n    AssetStatus[\"DAMAGED\"] = \"damaged\";\n})(AssetStatus || (AssetStatus = {}));\nvar AssetCondition;\n(function(AssetCondition) {\n    AssetCondition[\"EXCELLENT\"] = \"excellent\";\n    AssetCondition[\"GOOD\"] = \"good\";\n    AssetCondition[\"FAIR\"] = \"fair\";\n    AssetCondition[\"POOR\"] = \"poor\";\n})(AssetCondition || (AssetCondition = {}));\nvar LocationType;\n(function(LocationType) {\n    LocationType[\"REGION\"] = \"region\";\n    LocationType[\"BUILDING\"] = \"building\";\n    LocationType[\"FLOOR\"] = \"floor\";\n    LocationType[\"ROOM\"] = \"room\";\n    LocationType[\"AREA\"] = \"area\";\n})(LocationType || (LocationType = {}));\nvar LogAction;\n(function(LogAction) {\n    LogAction[\"CREATED\"] = \"created\";\n    LogAction[\"UPDATED\"] = \"updated\";\n    LogAction[\"DELETED\"] = \"deleted\";\n    LogAction[\"ASSIGNED\"] = \"assigned\";\n    LogAction[\"UNASSIGNED\"] = \"unassigned\";\n    LogAction[\"STATUS_CHANGED\"] = \"status_changed\";\n    LogAction[\"LOCATION_CHANGED\"] = \"location_changed\";\n    LogAction[\"MAINTENANCE\"] = \"maintenance\";\n    LogAction[\"RETIRED\"] = \"retired\";\n})(LogAction || (LogAction = {}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\n\nvar REACT_CONSUMER_TYPE = Symbol.for('react.consumer');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\nvar enableRenderableContext = false;\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false;\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nvar REACT_CLIENT_REFERENCE$2 = Symbol.for('react.client.reference'); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  if (typeof type === 'function') {\n    if (type.$$typeof === REACT_CLIENT_REFERENCE$2) {\n      // TODO: Create a convention for naming client references with debug info.\n      return null;\n    }\n\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    {\n      if (typeof type.tag === 'number') {\n        error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n      }\n    }\n\n    switch (type.$$typeof) {\n      case REACT_PROVIDER_TYPE:\n        {\n          var provider = type;\n          return getContextName(provider._context) + '.Provider';\n        }\n\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n\n        {\n          return getContextName(context) + '.Consumer';\n        }\n\n      case REACT_CONSUMER_TYPE:\n        {\n          return null;\n        }\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar assign = Object.assign;\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || enableRenderableContext  || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    {\n      var warnAboutAccessingRef = function () {\n        if (!specialPropRefWarningShown) {\n          specialPropRefWarningShown = true;\n\n          error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, _ref, self, source, owner, props) {\n  var ref;\n\n  {\n    ref = _ref;\n  }\n\n  var element;\n\n  {\n    // In prod, `ref` is a regular property. It will be removed in a\n    // future release.\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      // Built-in properties that belong on the element\n      type: type,\n      key: key,\n      ref: ref,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: owner\n    };\n  }\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\nvar didWarnAboutKeySpread = {};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, isStaticChildren, source, self) {\n  {\n    if (!isValidElementType(type)) {\n      // This is an invalid element type.\n      //\n      // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    } else {\n      // This is a valid element type.\n      // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing\n      // errors. We don't want exception behavior to differ between dev and\n      // prod. (Rendering will throw with a helpful message and as soon as the\n      // type is fixed, the key warnings will appear.)\n      var children = config.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    } // Warn about key spread regardless of whether the type is valid.\n\n\n    if (hasOwnProperty.call(config, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      {\n        ref = config.ref;\n      }\n\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && // Skip over reserved prop names\n      propName !== 'key' && (propName !== 'ref')) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    var element = ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    }\n\n    return element;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nvar ownerHasKeyUseWarning = {};\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = getComponentNameFromType(parentType);\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  // TODO: Move this to render phase instead of at element creation.\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar jsxDEV = jsxDEV$1 ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/MTM2MCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CInvicta%5C%5CUpcoming%20Projects%5C%5CAssets%20Management%20System%5C%5Cassets-management-app%5C%5Csrc%5C%5Capp%5C%5Casset-details%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);