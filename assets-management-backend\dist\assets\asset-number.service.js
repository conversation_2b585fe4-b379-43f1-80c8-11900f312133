"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetNumberService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const asset_entity_1 = require("../entities/asset.entity");
let AssetNumberService = class AssetNumberService {
    assetRepository;
    constructor(assetRepository) {
        this.assetRepository = assetRepository;
    }
    async generateAssetNumber() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const prefix = `AST-${year}${month}`;
        const existingAssets = await this.assetRepository
            .createQueryBuilder('asset')
            .where('asset.assetNumber LIKE :prefix', { prefix: `${prefix}-%` })
            .orderBy('asset.assetNumber', 'DESC')
            .limit(1)
            .getOne();
        let nextSequence = 1;
        if (existingAssets) {
            const parts = existingAssets.assetNumber.split('-');
            if (parts.length === 3) {
                const lastSequence = parseInt(parts[2], 10);
                if (!isNaN(lastSequence)) {
                    nextSequence = lastSequence + 1;
                }
            }
        }
        const sequenceStr = String(nextSequence).padStart(4, '0');
        const assetNumber = `${prefix}-${sequenceStr}`;
        const exists = await this.assetRepository.findOne({
            where: { assetNumber },
        });
        if (exists) {
            return this.generateAssetNumber();
        }
        return assetNumber;
    }
    validateAssetNumberFormat(assetNumber) {
        const pattern = /^AST-\d{6}-\d{4}$/;
        return pattern.test(assetNumber);
    }
    async isAssetNumberUnique(assetNumber, excludeId) {
        const query = this.assetRepository
            .createQueryBuilder('asset')
            .where('asset.assetNumber = :assetNumber', { assetNumber });
        if (excludeId) {
            query.andWhere('asset.id != :excludeId', { excludeId });
        }
        const existing = await query.getOne();
        return !existing;
    }
    parseAssetNumber(assetNumber) {
        if (!this.validateAssetNumberFormat(assetNumber)) {
            return null;
        }
        const parts = assetNumber.split('-');
        const yearMonth = parts[1];
        const sequence = parts[2];
        const year = parseInt(yearMonth.substring(0, 4), 10);
        const month = parseInt(yearMonth.substring(4, 6), 10);
        const seq = parseInt(sequence, 10);
        return { year, month, sequence: seq };
    }
    async getAssetStatsByPeriod(year, month) {
        const yearStr = year.toString();
        const monthStr = month ? String(month).padStart(2, '0') : '';
        const prefix = `AST-${yearStr}${monthStr}`;
        const query = this.assetRepository
            .createQueryBuilder('asset')
            .select(['asset.assetNumber'])
            .where('asset.assetNumber LIKE :prefix', { prefix: `${prefix}%` })
            .orderBy('asset.assetNumber', 'ASC');
        const assets = await query.getMany();
        return {
            totalAssets: assets.length,
            assetNumbers: assets.map(asset => asset.assetNumber),
        };
    }
};
exports.AssetNumberService = AssetNumberService;
exports.AssetNumberService = AssetNumberService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(asset_entity_1.Asset)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AssetNumberService);
//# sourceMappingURL=asset-number.service.js.map