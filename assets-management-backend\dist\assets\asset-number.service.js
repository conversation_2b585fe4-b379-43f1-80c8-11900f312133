"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetNumberService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const asset_entity_1 = require("../entities/asset.entity");
const category_entity_1 = require("../entities/category.entity");
const location_entity_1 = require("../entities/location.entity");
let AssetNumberService = class AssetNumberService {
    assetRepository;
    categoryRepository;
    locationRepository;
    constructor(assetRepository, categoryRepository, locationRepository) {
        this.assetRepository = assetRepository;
        this.categoryRepository = categoryRepository;
        this.locationRepository = locationRepository;
    }
    async generateAssetNumber(categoryId, locationId) {
        const categoryPrefix = await this.getRootCategoryCode(categoryId);
        const locationPrefix = await this.getRootLocationCode(locationId);
        const basePrefix = `${locationPrefix}-${categoryPrefix}`;
        const existingAssets = await this.assetRepository
            .createQueryBuilder('asset')
            .where('asset.assetNumber LIKE :prefix', { prefix: `${basePrefix}-%` })
            .orderBy('asset.assetNumber', 'DESC')
            .limit(1)
            .getOne();
        let nextSequence = 1;
        if (existingAssets) {
            const parts = existingAssets.assetNumber.split('-');
            if (parts.length === 3) {
                const lastSequence = parseInt(parts[2], 10);
                if (!isNaN(lastSequence)) {
                    nextSequence = lastSequence + 1;
                }
            }
        }
        const sequenceStr = String(nextSequence).padStart(4, '0');
        const assetNumber = `${basePrefix}-${sequenceStr}`;
        const exists = await this.assetRepository.findOne({
            where: { assetNumber },
        });
        if (exists) {
            return this.generateAssetNumber(categoryId, locationId);
        }
        return assetNumber;
    }
    async getRootCategoryCode(categoryId) {
        const category = await this.categoryRepository.findOne({
            where: { id: categoryId },
            relations: ['parent'],
        });
        if (!category) {
            return 'UNK';
        }
        if (category.parent) {
            return this.getRootCategoryCode(category.parent.id);
        }
        return category.code || this.generateCodeFromName(category.name);
    }
    async getRootLocationCode(locationId) {
        const location = await this.locationRepository.findOne({
            where: { id: locationId },
            relations: ['parent'],
        });
        if (!location) {
            return 'UNK';
        }
        if (location.parent) {
            return this.getRootLocationCode(location.parent.id);
        }
        return location.code || this.generateCodeFromName(location.name);
    }
    generateCodeFromName(name) {
        return name
            .replace(/[^a-zA-Z]/g, '')
            .toUpperCase()
            .substring(0, 3)
            .padEnd(3, 'X');
    }
    validateAssetNumberFormat(assetNumber) {
        const pattern = /^[A-Z]{3}-[A-Z]{3}-\d{4}$/;
        return pattern.test(assetNumber);
    }
    async isAssetNumberUnique(assetNumber, excludeId) {
        const query = this.assetRepository
            .createQueryBuilder('asset')
            .where('asset.assetNumber = :assetNumber', { assetNumber });
        if (excludeId) {
            query.andWhere('asset.id != :excludeId', { excludeId });
        }
        const existing = await query.getOne();
        return !existing;
    }
    parseAssetNumber(assetNumber) {
        if (!this.validateAssetNumberFormat(assetNumber)) {
            return null;
        }
        const parts = assetNumber.split('-');
        const yearMonth = parts[1];
        const sequence = parts[2];
        const year = parseInt(yearMonth.substring(0, 4), 10);
        const month = parseInt(yearMonth.substring(4, 6), 10);
        const seq = parseInt(sequence, 10);
        return { year, month, sequence: seq };
    }
    async getAssetStatsByPeriod(year, month) {
        const yearStr = year.toString();
        const monthStr = month ? String(month).padStart(2, '0') : '';
        const prefix = `AST-${yearStr}${monthStr}`;
        const query = this.assetRepository
            .createQueryBuilder('asset')
            .select(['asset.assetNumber'])
            .where('asset.assetNumber LIKE :prefix', { prefix: `${prefix}%` })
            .orderBy('asset.assetNumber', 'ASC');
        const assets = await query.getMany();
        return {
            totalAssets: assets.length,
            assetNumbers: assets.map((asset) => asset.assetNumber),
        };
    }
};
exports.AssetNumberService = AssetNumberService;
exports.AssetNumberService = AssetNumberService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(asset_entity_1.Asset)),
    __param(1, (0, typeorm_1.InjectRepository)(category_entity_1.Category)),
    __param(2, (0, typeorm_1.InjectRepository)(location_entity_1.Location)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], AssetNumberService);
//# sourceMappingURL=asset-number.service.js.map