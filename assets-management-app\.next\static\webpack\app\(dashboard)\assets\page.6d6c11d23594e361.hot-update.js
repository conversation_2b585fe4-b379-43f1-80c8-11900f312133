"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/assets/page",{

/***/ "(app-pages-browser)/./src/lib/services/assetLabelGenerator.ts":
/*!*************************************************!*\
  !*** ./src/lib/services/assetLabelGenerator.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssetLabelGenerator: function() { return /* binding */ AssetLabelGenerator; }\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var qrcode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! qrcode */ \"(app-pages-browser)/./node_modules/qrcode/lib/browser.js\");\n// import jsPDF from 'jspdf'\n// import QRCode from 'qrcode'\n// export interface AssetItemLabel {\n//   id: string\n//   assetNumber: string\n//   assetName: string\n//   category: string\n//   location: string\n//   status: string\n//   condition: string\n// }\n// export class AssetLabelGenerator {\n//   private doc: jsPDF\n//   constructor() {\n//     this.doc = new jsPDF({\n//       orientation: 'portrait',\n//       unit: 'mm',\n//       format: 'a4'\n//     })\n//   }\n//   /**\n//    * Generate a single asset label - very small format (1cm height)\n//    */\n//   async generateSingleLabel(item: AssetItemLabel): Promise<void> {\n//     this.doc = new jsPDF({\n//       orientation: 'landscape',\n//       unit: 'mm',\n//       format: [25, 10] // 25mm x 10mm label size (1cm height)\n//     })\n//     await this.drawMinimalLabel(item, 0, 0, 25, 10)\n//   }\n//   /**\n//    * Generate multiple asset labels on A4 sheet\n//    * Standard Avery 5160 format: 3 columns x 10 rows = 30 labels per sheet\n//    */\n//   async generateBulkLabels(items: AssetItemLabel[]): Promise<void> {\n//     this.doc = new jsPDF({\n//       orientation: 'portrait',\n//       unit: 'mm',\n//       format: 'a4'\n//     })\n//     const labelWidth = 25 // 25mm width\n//     const labelHeight = 10 // 10mm height (1cm)\n//     const marginLeft = 5\n//     const marginTop = 5\n//     const cols = 8\n//     const rows = 28\n//     const labelsPerPage = cols * rows\n//     let currentPage = 0\n//     for (let i = 0; i < items.length; i++) {\n//       const pageIndex = Math.floor(i / labelsPerPage)\n//       const positionOnPage = i % labelsPerPage\n//       // Add new page if needed\n//       if (pageIndex > currentPage) {\n//         this.doc.addPage()\n//         currentPage = pageIndex\n//       }\n//       const col = positionOnPage % cols\n//       const row = Math.floor(positionOnPage / cols)\n//       const x = marginLeft + col * labelWidth\n//       const y = marginTop + row * labelHeight\n//       await this.drawMinimalLabel(items[i], x, y, labelWidth, labelHeight)\n//     }\n//   }\n//   /**\n//    * Draw a minimal label with only QR code and asset number\n//    */\n//   private async drawMinimalLabel(\n//     item: AssetItemLabel,\n//     x: number,\n//     y: number,\n//     width: number,\n//     height: number\n//   ): Promise<void> {\n//     // Generate QR code with asset details URL\n//     // Use current host (works with both localhost and IP address)\n//     const assetDetailsUrl = `${window.location.origin}/asset-details/${item.assetNumber}`\n//     const qrCodeDataUrl = await QRCode.toDataURL(assetDetailsUrl, {\n//       width: 128,\n//       margin: 0,\n//       color: {\n//         dark: '#000000',\n//         light: '#FFFFFF'\n//       }\n//     })\n//     // Draw border\n//     this.doc.setDrawColor(0, 0, 0)\n//     this.doc.setLineWidth(0.1)\n//     this.doc.rect(x, y, width, height)\n//     // QR Code (left side, takes most of the space)\n//     const qrSize = height - 1 // Almost full height\n//     this.doc.addImage(qrCodeDataUrl, 'PNG', x + 0.5, y + 0.5, qrSize, qrSize)\n//     // Asset Number (right side, vertical text for small space)\n//     this.doc.setFont('helvetica', 'bold')\n//     this.doc.setFontSize(6)\n//     // Split asset number for better fit with new format: LOC-CAT-XXXX\n//     const assetNumber = item.assetNumber\n//     const parts = assetNumber.split('-')\n//     if (parts.length >= 3) {\n//       // For new format LOC-CAT-XXXX, show the number prominently\n//       this.doc.setFontSize(7)\n//       this.doc.text(parts[2], x + qrSize + 1, y + 3) // Show XXXX part\n//       // Show location and category prefix smaller\n//       this.doc.setFontSize(4)\n//       this.doc.text(`${parts[0]}-${parts[1]}`, x + qrSize + 1, y + 6) // Show LOC-CAT\n//     } else {\n//       // Fallback for any other format\n//       this.doc.text(assetNumber, x + qrSize + 1, y + 4)\n//     }\n//   }\n//   /**\n//    * Draw a single label at specified position (legacy method for compatibility)\n//    */\n//   private async drawLabel(item: AssetItemLabel, x: number, y: number, width: number, height: number): Promise<void> {\n//     // Generate QR code\n//     const qrCodeDataUrl = await QRCode.toDataURL(item.assetNumber, {\n//       width: 60,\n//       margin: 1,\n//       color: {\n//         dark: '#000000',\n//         light: '#FFFFFF'\n//       }\n//     })\n//     // Draw border\n//     this.doc.setDrawColor(0, 0, 0)\n//     this.doc.setLineWidth(0.1)\n//     this.doc.rect(x, y, width, height)\n//     // QR Code (left side)\n//     const qrSize = Math.min(height - 4, 15)\n//     this.doc.addImage(qrCodeDataUrl, 'PNG', x + 2, y + 2, qrSize, qrSize)\n//     // Asset Number (main text)\n//     this.doc.setFont('helvetica', 'bold')\n//     this.doc.setFontSize(8)\n//     this.doc.text(item.assetNumber, x + qrSize + 4, y + 6)\n//     // Asset Name\n//     this.doc.setFont('helvetica', 'normal')\n//     this.doc.setFontSize(6)\n//     const assetName = this.truncateText(item.assetName, 20)\n//     this.doc.text(assetName, x + qrSize + 4, y + 10)\n//     // Category and Location\n//     this.doc.setFontSize(5)\n//     this.doc.text(`Cat: ${this.truncateText(item.category, 12)}`, x + qrSize + 4, y + 14)\n//     this.doc.text(`Loc: ${this.truncateText(item.location, 12)}`, x + qrSize + 4, y + 17)\n//     // Status and Condition\n//     this.doc.setFontSize(4)\n//     this.doc.text(`${item.status.toUpperCase()} | ${item.condition.toUpperCase()}`, x + qrSize + 4, y + 21)\n//     // Company info (bottom)\n//     this.doc.setFontSize(4)\n//     this.doc.text('Asset Management System', x + 2, y + height - 2)\n//   }\n//   /**\n//    * Truncate text to fit label\n//    */\n//   private truncateText(text: string, maxLength: number): string {\n//     if (text.length <= maxLength) return text\n//     return text.substring(0, maxLength - 3) + '...'\n//   }\n//   /**\n//    * Save the PDF\n//    */\n//   save(filename: string): void {\n//     this.doc.save(filename)\n//   }\n//   /**\n//    * Open print dialog\n//    */\n//   openForPrint(): void {\n//     this.doc.autoPrint()\n//     window.open(this.doc.output('bloburl'), '_blank')\n//   }\n//   /**\n//    * Get PDF as blob\n//    */\n//   getBlob(): Blob {\n//     return this.doc.output('blob')\n//   }\n//   /**\n//    * Generate asset number labels for printing\n//    */\n//   static async generateAssetLabels(\n//     items: AssetItemLabel[],\n//     type: 'single' | 'bulk' = 'bulk'\n//   ): Promise<AssetLabelGenerator> {\n//     const generator = new AssetLabelGenerator()\n//     if (type === 'single' && items.length === 1) {\n//       await generator.generateSingleLabel(items[0])\n//     } else {\n//       await generator.generateBulkLabels(items)\n//     }\n//     return generator\n//   }\n//   /**\n//    * Quick print function for asset items\n//    */\n//   static async printAssetLabels(items: AssetItemLabel[], type: 'single' | 'bulk' = 'bulk'): Promise<void> {\n//     const generator = await AssetLabelGenerator.generateAssetLabels(items, type)\n//     generator.openForPrint()\n//   }\n//   /**\n//    * Quick save function for asset items\n//    */\n//   static async saveAssetLabels(\n//     items: AssetItemLabel[],\n//     filename?: string,\n//     type: 'single' | 'bulk' = 'bulk'\n//   ): Promise<void> {\n//     const generator = await AssetLabelGenerator.generateAssetLabels(items, type)\n//     const defaultFilename =\n//       items.length === 1\n//         ? `Asset_Label_${items[0].assetNumber}.pdf`\n//         : `Asset_Labels_${new Date().toISOString().split('T')[0]}.pdf`\n//     generator.save(filename || defaultFilename)\n//   }\n// }\n\n\nclass AssetLabelGenerator {\n    /**\n   * Generate a single asset label - very small format (1cm height)\n   */ async generateSingleLabel(item) {\n        this.doc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            orientation: \"landscape\",\n            unit: \"mm\",\n            format: [\n                30,\n                10\n            ] // 30mm x 10mm label size (1cm height)\n        });\n        await this.drawMinimalLabel(item, 0, 0, 30, 10);\n    }\n    /**\n   * Generate multiple asset labels on A4 sheet\n   * Optimized for 1cm height labels: 7 columns x 28 rows = 196 labels per sheet\n   */ async generateBulkLabels(items) {\n        this.doc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            orientation: \"portrait\",\n            unit: \"mm\",\n            format: \"a4\"\n        });\n        const labelWidth = 30 // 30mm width\n        ;\n        const labelHeight = 10 // 10mm height (1cm)\n        ;\n        const marginLeft = 0;\n        const marginTop = 0;\n        const cols = 7 // 7 columns to fit A4 width (210mm)\n        ;\n        const rows = 28 // 28 rows to fit A4 height (297mm)\n        ;\n        const labelsPerPage = cols * rows;\n        let currentPage = 0;\n        for(let i = 0; i < items.length; i++){\n            const pageIndex = Math.floor(i / labelsPerPage);\n            const positionOnPage = i % labelsPerPage;\n            // Add new page if needed\n            if (pageIndex > currentPage) {\n                this.doc.addPage();\n                currentPage = pageIndex;\n            }\n            const col = positionOnPage % cols;\n            const row = Math.floor(positionOnPage / cols);\n            const x = marginLeft + col * labelWidth;\n            const y = marginTop + row * labelHeight;\n            await this.drawMinimalLabel(items[i], x, y, labelWidth, labelHeight);\n        }\n    }\n    /**\n   * Draw a minimal label with Asset Number on left and QR code on right\n   */ async drawMinimalLabel(item, x, y, width, height) {\n        // Generate QR code with asset details URL\n        // Use current host (works with both localhost and IP address)\n        const assetDetailsUrl = \"\".concat(window.location.origin, \"/asset-details/\").concat(item.assetNumber);\n        const qrCodeDataUrl = await qrcode__WEBPACK_IMPORTED_MODULE_1__.toDataURL(assetDetailsUrl, {\n            width: 128,\n            margin: 0,\n            color: {\n                dark: \"#000000\",\n                light: \"#FFFFFF\"\n            }\n        });\n        // Draw border\n        this.doc.setDrawColor(0, 0, 0);\n        this.doc.setLineWidth(0.1);\n        this.doc.rect(x, y, width, height);\n        // QR Code (right side, square size based on height)\n        const qrSize = height - 1 // Almost full height with small margin\n        ;\n        const qrX = x + width - qrSize - 0.5 // Position on right side\n        ;\n        this.doc.addImage(qrCodeDataUrl, \"PNG\", qrX, y + 0.5, qrSize, qrSize);\n        // Asset Number (left side)\n        this.doc.setFont(\"helvetica\", \"bold\");\n        // Split asset number for better display\n        const assetNumber = item.assetNumber;\n        const parts = assetNumber.split(\"-\");\n        const textStartX = x + 0.5;\n        const availableTextWidth = width - qrSize - 1.5 // Space available for text\n        ;\n        if (parts.length >= 3) {\n            // For format LOC-CAT-XXXX, display in multiple lines if needed\n            this.doc.setFontSize(8);\n            // Main number (XXXX part) - most prominent\n            this.doc.setFont(\"helvetica\", \"bold\");\n            this.doc.text(parts[2], textStartX, y + 4);\n            // Location and category prefix\n            this.doc.setFontSize(6);\n            this.doc.setFont(\"helvetica\", \"normal\");\n            this.doc.text(\"\".concat(parts[0], \"-\").concat(parts[1]), textStartX, y + 7.5);\n        } else {\n            // Fallback for other formats - single line\n            this.doc.setFontSize(7);\n            // Check if text fits, if not reduce font size\n            const textWidth = this.doc.getTextWidth(assetNumber);\n            if (textWidth > availableTextWidth) {\n                this.doc.setFontSize(5);\n            }\n            this.doc.text(assetNumber, textStartX, y + height / 2 + 1);\n        }\n    }\n    /**\n   * Draw a single label at specified position (legacy method for compatibility)\n   */ async drawLabel(item, x, y, width, height) {\n        // Generate QR code\n        const qrCodeDataUrl = await qrcode__WEBPACK_IMPORTED_MODULE_1__.toDataURL(item.assetNumber, {\n            width: 60,\n            margin: 1,\n            color: {\n                dark: \"#000000\",\n                light: \"#FFFFFF\"\n            }\n        });\n        // Draw border\n        this.doc.setDrawColor(0, 0, 0);\n        this.doc.setLineWidth(0.1);\n        this.doc.rect(x, y, width, height);\n        // QR Code (left side)\n        const qrSize = Math.min(height - 4, 15);\n        this.doc.addImage(qrCodeDataUrl, \"PNG\", x + 2, y + 2, qrSize, qrSize);\n        // Asset Number (main text)\n        this.doc.setFont(\"helvetica\", \"bold\");\n        this.doc.setFontSize(8);\n        this.doc.text(item.assetNumber, x + qrSize + 4, y + 6);\n        // Asset Name\n        this.doc.setFont(\"helvetica\", \"normal\");\n        this.doc.setFontSize(6);\n        const assetName = this.truncateText(item.assetName, 20);\n        this.doc.text(assetName, x + qrSize + 4, y + 10);\n        // Category and Location\n        this.doc.setFontSize(5);\n        this.doc.text(\"Cat: \".concat(this.truncateText(item.category, 12)), x + qrSize + 4, y + 14);\n        this.doc.text(\"Loc: \".concat(this.truncateText(item.location, 12)), x + qrSize + 4, y + 17);\n        // Status and Condition\n        this.doc.setFontSize(4);\n        this.doc.text(\"\".concat(item.status.toUpperCase(), \" | \").concat(item.condition.toUpperCase()), x + qrSize + 4, y + 21);\n        // Company info (bottom)\n        this.doc.setFontSize(4);\n        this.doc.text(\"Asset Management System\", x + 2, y + height - 2);\n    }\n    /**\n   * Truncate text to fit label\n   */ truncateText(text, maxLength) {\n        if (text.length <= maxLength) return text;\n        return text.substring(0, maxLength - 3) + \"...\";\n    }\n    /**\n   * Save the PDF\n   */ save(filename) {\n        this.doc.save(filename);\n    }\n    /**\n   * Open print dialog\n   */ openForPrint() {\n        this.doc.autoPrint();\n        window.open(this.doc.output(\"bloburl\"), \"_blank\");\n    }\n    /**\n   * Get PDF as blob\n   */ getBlob() {\n        return this.doc.output(\"blob\");\n    }\n    /**\n   * Generate asset number labels for printing\n   */ static async generateAssetLabels(items) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"bulk\";\n        const generator = new AssetLabelGenerator();\n        if (type === \"single\" && items.length === 1) {\n            await generator.generateSingleLabel(items[0]);\n        } else {\n            await generator.generateBulkLabels(items);\n        }\n        return generator;\n    }\n    /**\n   * Quick print function for asset items\n   */ static async printAssetLabels(items) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"bulk\";\n        const generator = await AssetLabelGenerator.generateAssetLabels(items, type);\n        generator.openForPrint();\n    }\n    /**\n   * Quick save function for asset items\n   */ static async saveAssetLabels(items, filename) {\n        let type = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"bulk\";\n        const generator = await AssetLabelGenerator.generateAssetLabels(items, type);\n        const defaultFilename = items.length === 1 ? \"Asset_Label_\".concat(items[0].assetNumber, \".pdf\") : \"Asset_Labels_\".concat(new Date().toISOString().split(\"T\")[0], \".pdf\");\n        generator.save(filename || defaultFilename);\n    }\n    constructor(){\n        this.doc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            orientation: \"portrait\",\n            unit: \"mm\",\n            format: \"a4\"\n        });\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/assetLabelGenerator.ts\n"));

/***/ })

});