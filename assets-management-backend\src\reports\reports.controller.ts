import {
  Controller,
  Get,
  Query,
  UseGuards,
  Request,
  Response,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { ReportsService, ReportFilters } from './reports.service';
// import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
// import { RolesGuard } from '../auth/guards/roles.guard';
// import { Roles } from '../auth/decorators/roles.decorator';
// import { UserRole } from '../entities/user.entity';
import { AuditLogsService } from '../audit-logs/audit-logs.service';
import { AuditAction, EntityType } from '../entities/audit-log.entity';

@ApiTags('reports')
@Controller('reports')
// @UseGuards(JwtAuthGuard, RolesGuard)
export class ReportsController {
  constructor(
    private readonly reportsService: ReportsService,
    private readonly auditLogsService: AuditLogsService,
  ) {}

  @Get('assets')
  // @Roles(UserRole.ADMIN, UserRole.MANAGER, UserRole.VIEWER)
  @ApiOperation({ summary: 'Generate asset report' })
  @ApiResponse({
    status: 200,
    description: 'Asset report generated successfully',
  })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiQuery({ name: 'categoryId', required: false })
  @ApiQuery({ name: 'locationId', required: false })
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'condition', required: false })
  @ApiQuery({ name: 'assignedUserId', required: false })
  async generateAssetReport(@Query() query: any, @Request() req) {
    const filters: ReportFilters = {
      startDate: query.startDate ? new Date(query.startDate) : undefined,
      endDate: query.endDate ? new Date(query.endDate) : undefined,
      categoryId: query.categoryId,
      locationId: query.locationId,
      status: query.status,
      condition: query.condition,
      assignedUserId: query.assignedUserId,
    };

    // Log the report generation
    await this.auditLogsService.logSystemOperation(
      AuditAction.EXPORT,
      req.user?.userId || null,
      'Generated asset report',
      { filters },
      req.ip,
      req.get('User-Agent'),
    );

    return await this.reportsService.generateAssetReport(filters);
  }

  @Get('utilization')
  // @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Generate utilization report' })
  @ApiResponse({
    status: 200,
    description: 'Utilization report generated successfully',
  })
  async generateUtilizationReport(@Request() req) {
    // Log the report generation
    await this.auditLogsService.logSystemOperation(
      AuditAction.EXPORT,
      req.user?.userId || null,
      'Generated utilization report',
      {},
      req.ip,
      req.get('User-Agent'),
    );

    return await this.reportsService.generateUtilizationReport();
  }

  @Get('maintenance')
  // @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Generate maintenance report' })
  @ApiResponse({
    status: 200,
    description: 'Maintenance report generated successfully',
  })
  async generateMaintenanceReport(@Request() req) {
    // Log the report generation
    await this.auditLogsService.logSystemOperation(
      AuditAction.EXPORT,
      req.user?.userId || null,
      'Generated maintenance report',
      {},
      req.ip,
      req.get('User-Agent'),
    );

    return await this.reportsService.generateMaintenanceReport();
  }

  @Get('activity')
  // @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Generate activity report' })
  @ApiResponse({
    status: 200,
    description: 'Activity report generated successfully',
  })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  async generateActivityReport(@Query() query: any, @Request() req) {
    const filters: ReportFilters = {
      startDate: query.startDate ? new Date(query.startDate) : undefined,
      endDate: query.endDate ? new Date(query.endDate) : undefined,
    };

    // Log the report generation
    await this.auditLogsService.logSystemOperation(
      AuditAction.EXPORT,
      req.user?.userId || null,
      'Generated activity report',
      { filters },
      req.ip,
      req.get('User-Agent'),
    );

    return await this.reportsService.generateActivityReport(filters);
  }

  @Get('summary')
  // @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Generate summary report with all key metrics' })
  @ApiResponse({
    status: 200,
    description: 'Summary report generated successfully',
  })
  async generateSummaryReport(@Request() req) {
    // Log the report generation
    await this.auditLogsService.logSystemOperation(
      AuditAction.EXPORT,
      req.user?.userId || null,
      'Generated summary report',
      {},
      req.ip,
      req.get('User-Agent'),
    );

    const [assetReport, utilizationReport, maintenanceReport, activityReport] =
      await Promise.all([
        this.reportsService.generateAssetReport(),
        this.reportsService.generateUtilizationReport(),
        this.reportsService.generateMaintenanceReport(),
        this.reportsService.generateActivityReport(),
      ]);

    return {
      assets: assetReport,
      utilization: utilizationReport,
      maintenance: maintenanceReport,
      activity: activityReport,
      generatedAt: new Date(),
    };
  }
}
