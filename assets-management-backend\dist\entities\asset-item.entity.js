"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetItem = void 0;
const typeorm_1 = require("typeorm");
const asset_entity_1 = require("./asset.entity");
const user_entity_1 = require("./user.entity");
let AssetItem = class AssetItem {
    id;
    asset;
    assetId;
    assetNumber;
    status;
    condition;
    serialNumber;
    purchaseDate;
    warrantyExpiry;
    notes;
    imageUrl;
    assignedTo;
    assignedToId;
    assignedDate;
    lastModifiedBy;
    lastModifiedById;
    isActive;
    createdAt;
    updatedAt;
};
exports.AssetItem = AssetItem;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AssetItem.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => asset_entity_1.Asset, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'assetId' }),
    __metadata("design:type", asset_entity_1.Asset)
], AssetItem.prototype, "asset", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AssetItem.prototype, "assetId", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], AssetItem.prototype, "assetNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: asset_entity_1.AssetStatus,
        default: asset_entity_1.AssetStatus.IN_STOCK,
    }),
    __metadata("design:type", String)
], AssetItem.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: asset_entity_1.AssetCondition,
        default: asset_entity_1.AssetCondition.EXCELLENT,
    }),
    __metadata("design:type", String)
], AssetItem.prototype, "condition", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], AssetItem.prototype, "serialNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], AssetItem.prototype, "purchaseDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], AssetItem.prototype, "warrantyExpiry", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], AssetItem.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], AssetItem.prototype, "imageUrl", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true, onDelete: 'SET NULL' }),
    (0, typeorm_1.JoinColumn)({ name: 'assignedToId' }),
    __metadata("design:type", user_entity_1.User)
], AssetItem.prototype, "assignedTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], AssetItem.prototype, "assignedToId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], AssetItem.prototype, "assignedDate", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true, onDelete: 'SET NULL' }),
    (0, typeorm_1.JoinColumn)({ name: 'lastModifiedBy' }),
    __metadata("design:type", user_entity_1.User)
], AssetItem.prototype, "lastModifiedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], AssetItem.prototype, "lastModifiedById", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], AssetItem.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], AssetItem.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], AssetItem.prototype, "updatedAt", void 0);
exports.AssetItem = AssetItem = __decorate([
    (0, typeorm_1.Entity)('asset_items'),
    (0, typeorm_1.Index)(['assetNumber'], { unique: true })
], AssetItem);
//# sourceMappingURL=asset-item.entity.js.map