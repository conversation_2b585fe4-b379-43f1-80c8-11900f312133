import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { Category } from './category.entity';
import { Location } from './location.entity';
import { User } from './user.entity';

export enum AssetStatus {
  IN_STOCK = 'in_stock',
  IN_USE = 'in_use',
  MAINTENANCE = 'maintenance',
  RETIRED = 'retired',
  LOST = 'lost',
  DAMAGED = 'damaged',
}

export enum AssetCondition {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor',
}

@Entity('assets')
export class Asset {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  assetNumber: string; // Auto-generated: AST-YYYYMM-XXXX

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  model: string;

  @Column({ nullable: true })
  manufacturer: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  unitPrice: number;

  @Column({ type: 'int', default: 1 })
  quantity: number; // Total quantity of this asset type

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  totalValue: number; // unitPrice * quantity

  @Column({ nullable: true })
  supplier: string;

  @Column({ nullable: true })
  invoiceNumber: string;

  @Column({ nullable: true })
  notes: string;

  @Column({ nullable: true })
  imageUrl: string;

  @Column({ default: true })
  isActive: boolean;

  // Relationships
  @ManyToOne(() => Category, (category) => category.assets, {
    eager: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'categoryId' })
  category: Category;

  @Column({ nullable: true })
  categoryId: string;

  @ManyToOne(() => Location, (location) => location.assets, {
    eager: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'locationId' })
  location: Location;

  @Column({ nullable: true })
  locationId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @BeforeInsert()
  calculateTotalValue() {
    if (this.unitPrice && this.quantity) {
      this.totalValue = this.unitPrice * this.quantity;
    }
  }

  // Generate asset number in format AST-YYYYMM-XXXX
  static generateAssetNumber(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const random = Math.floor(Math.random() * 9999)
      .toString()
      .padStart(4, '0');
    return `AST-${year}${month}-${random}`;
  }
}
