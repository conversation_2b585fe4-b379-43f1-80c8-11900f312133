import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  AuditLog,
  AuditAction,
  EntityType,
} from '../entities/audit-log.entity';
import { User } from '../entities/user.entity';

export interface CreateAuditLogDto {
  action: AuditAction;
  entityType: EntityType;
  entityId?: string;
  entityName?: string;
  description?: string;
  oldValues?: any;
  newValues?: any;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
  userId?: string;
}

export interface AuditLogFilters {
  action?: AuditAction;
  entityType?: EntityType;
  entityId?: string;
  userId?: string;
  startDate?: Date;
  endDate?: Date;
  search?: string;
  page?: number;
  limit?: number;
}

@Injectable()
export class AuditLogsService {
  constructor(
    @InjectRepository(AuditLog)
    private auditLogRepository: Repository<AuditLog>,
  ) {}

  async createAuditLog(data: CreateAuditLogDto): Promise<AuditLog> {
    const auditLog = this.auditLogRepository.create(data);
    return await this.auditLogRepository.save(auditLog);
  }

  async getAuditLogs(filters: AuditLogFilters = {}) {
    const {
      action,
      entityType,
      entityId,
      userId,
      startDate,
      endDate,
      search,
      page = 1,
      limit = 50,
    } = filters;

    const query = this.auditLogRepository
      .createQueryBuilder('auditLog')
      .leftJoinAndSelect('auditLog.user', 'user')
      .orderBy('auditLog.createdAt', 'DESC');

    // Apply filters
    if (action) {
      query.andWhere('auditLog.action = :action', { action });
    }

    if (entityType) {
      query.andWhere('auditLog.entityType = :entityType', { entityType });
    }

    if (entityId) {
      query.andWhere('auditLog.entityId = :entityId', { entityId });
    }

    if (userId) {
      query.andWhere('auditLog.userId = :userId', { userId });
    }

    if (startDate) {
      query.andWhere('auditLog.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('auditLog.createdAt <= :endDate', { endDate });
    }

    if (search) {
      query.andWhere(
        '(auditLog.description ILIKE :search OR auditLog.entityName ILIKE :search OR user.fullName ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Pagination
    const offset = (page - 1) * limit;
    query.skip(offset).take(limit);

    const [auditLogs, total] = await query.getManyAndCount();

    return {
      auditLogs,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getAuditLogById(id: string): Promise<AuditLog | null> {
    return await this.auditLogRepository.findOne({
      where: { id },
      relations: ['user'],
    });
  }

  async getEntityAuditLogs(
    entityType: EntityType,
    entityId: string,
  ): Promise<AuditLog[]> {
    return await this.auditLogRepository.find({
      where: { entityType, entityId },
      relations: ['user'],
      order: { createdAt: 'DESC' },
    });
  }

  async getUserAuditLogs(userId: string, limit = 50): Promise<AuditLog[]> {
    return await this.auditLogRepository.find({
      where: { userId },
      relations: ['user'],
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  async getAuditStats() {
    const totalLogs = await this.auditLogRepository.count();

    const actionStats = await this.auditLogRepository
      .createQueryBuilder('auditLog')
      .select('auditLog.action', 'action')
      .addSelect('COUNT(*)', 'count')
      .groupBy('auditLog.action')
      .getRawMany();

    const entityStats = await this.auditLogRepository
      .createQueryBuilder('auditLog')
      .select('auditLog.entityType', 'entityType')
      .addSelect('COUNT(*)', 'count')
      .groupBy('auditLog.entityType')
      .getRawMany();

    const recentActivity = await this.auditLogRepository.find({
      relations: ['user'],
      order: { createdAt: 'DESC' },
      take: 10,
    });

    return {
      totalLogs,
      actionStats,
      entityStats,
      recentActivity,
    };
  }

  // Helper method to log asset operations
  async logAssetOperation(
    action: AuditAction,
    assetId: string,
    assetName: string,
    userId: string,
    description?: string,
    oldValues?: any,
    newValues?: any,
    metadata?: any,
  ) {
    return await this.createAuditLog({
      action,
      entityType: EntityType.ASSET,
      entityId: assetId,
      entityName: assetName,
      description,
      oldValues,
      newValues,
      metadata,
      userId,
    });
  }

  // Helper method to log user operations
  async logUserOperation(
    action: AuditAction,
    userId: string,
    userName: string,
    performedByUserId: string,
    description?: string,
    oldValues?: any,
    newValues?: any,
    ipAddress?: string,
    userAgent?: string,
  ) {
    return await this.createAuditLog({
      action,
      entityType: EntityType.USER,
      entityId: userId,
      entityName: userName,
      description,
      oldValues,
      newValues,
      userId: performedByUserId,
      ipAddress,
      userAgent,
    });
  }

  // Helper method to log system operations
  async logSystemOperation(
    action: AuditAction,
    userId: string,
    description: string,
    metadata?: any,
    ipAddress?: string,
    userAgent?: string,
  ) {
    return await this.createAuditLog({
      action,
      entityType: EntityType.SYSTEM,
      description,
      metadata,
      userId,
      ipAddress,
      userAgent,
    });
  }
}
