import { Repository } from 'typeorm';
import { AuditLog, AuditAction, EntityType } from '../entities/audit-log.entity';
export interface CreateAuditLogDto {
    action: AuditAction;
    entityType: EntityType;
    entityId?: string;
    entityName?: string;
    description?: string;
    oldValues?: any;
    newValues?: any;
    metadata?: any;
    ipAddress?: string;
    userAgent?: string;
    userId?: string;
}
export interface AuditLogFilters {
    action?: AuditAction;
    entityType?: EntityType;
    entityId?: string;
    userId?: string;
    startDate?: Date;
    endDate?: Date;
    search?: string;
    page?: number;
    limit?: number;
}
export declare class AuditLogsService {
    private auditLogRepository;
    constructor(auditLogRepository: Repository<AuditLog>);
    createAuditLog(data: CreateAuditLogDto): Promise<AuditLog>;
    getAuditLogs(filters?: AuditLogFilters): Promise<{
        auditLogs: AuditLog[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getAuditLogById(id: string): Promise<AuditLog | null>;
    getEntityAuditLogs(entityType: EntityType, entityId: string): Promise<AuditLog[]>;
    getUserAuditLogs(userId: string, limit?: number): Promise<AuditLog[]>;
    getAuditStats(): Promise<{
        totalLogs: number;
        actionStats: any[];
        entityStats: any[];
        recentActivity: AuditLog[];
    }>;
    logAssetOperation(action: AuditAction, assetId: string, assetName: string, userId: string, description?: string, oldValues?: any, newValues?: any, metadata?: any): Promise<AuditLog>;
    logUserOperation(action: AuditAction, userId: string, userName: string, performedByUserId: string, description?: string, oldValues?: any, newValues?: any, ipAddress?: string, userAgent?: string): Promise<AuditLog>;
    logSystemOperation(action: AuditAction, userId: string, description: string, metadata?: any, ipAddress?: string, userAgent?: string): Promise<AuditLog>;
}
