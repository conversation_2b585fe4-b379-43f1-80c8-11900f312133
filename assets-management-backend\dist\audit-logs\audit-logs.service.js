"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLogsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const audit_log_entity_1 = require("../entities/audit-log.entity");
let AuditLogsService = class AuditLogsService {
    auditLogRepository;
    constructor(auditLogRepository) {
        this.auditLogRepository = auditLogRepository;
    }
    async createAuditLog(data) {
        const auditLog = this.auditLogRepository.create(data);
        return await this.auditLogRepository.save(auditLog);
    }
    async getAuditLogs(filters = {}) {
        const { action, entityType, entityId, userId, startDate, endDate, search, page = 1, limit = 50, } = filters;
        const query = this.auditLogRepository
            .createQueryBuilder('auditLog')
            .leftJoinAndSelect('auditLog.user', 'user')
            .orderBy('auditLog.createdAt', 'DESC');
        if (action) {
            query.andWhere('auditLog.action = :action', { action });
        }
        if (entityType) {
            query.andWhere('auditLog.entityType = :entityType', { entityType });
        }
        if (entityId) {
            query.andWhere('auditLog.entityId = :entityId', { entityId });
        }
        if (userId) {
            query.andWhere('auditLog.userId = :userId', { userId });
        }
        if (startDate) {
            query.andWhere('auditLog.createdAt >= :startDate', { startDate });
        }
        if (endDate) {
            query.andWhere('auditLog.createdAt <= :endDate', { endDate });
        }
        if (search) {
            query.andWhere('(auditLog.description ILIKE :search OR auditLog.entityName ILIKE :search OR user.fullName ILIKE :search)', { search: `%${search}%` });
        }
        const offset = (page - 1) * limit;
        query.skip(offset).take(limit);
        const [auditLogs, total] = await query.getManyAndCount();
        return {
            auditLogs,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    async getAuditLogById(id) {
        return await this.auditLogRepository.findOne({
            where: { id },
            relations: ['user'],
        });
    }
    async getEntityAuditLogs(entityType, entityId) {
        return await this.auditLogRepository.find({
            where: { entityType, entityId },
            relations: ['user'],
            order: { createdAt: 'DESC' },
        });
    }
    async getUserAuditLogs(userId, limit = 50) {
        return await this.auditLogRepository.find({
            where: { userId },
            relations: ['user'],
            order: { createdAt: 'DESC' },
            take: limit,
        });
    }
    async getAuditStats() {
        const totalLogs = await this.auditLogRepository.count();
        const actionStats = await this.auditLogRepository
            .createQueryBuilder('auditLog')
            .select('auditLog.action', 'action')
            .addSelect('COUNT(*)', 'count')
            .groupBy('auditLog.action')
            .getRawMany();
        const entityStats = await this.auditLogRepository
            .createQueryBuilder('auditLog')
            .select('auditLog.entityType', 'entityType')
            .addSelect('COUNT(*)', 'count')
            .groupBy('auditLog.entityType')
            .getRawMany();
        const recentActivity = await this.auditLogRepository.find({
            relations: ['user'],
            order: { createdAt: 'DESC' },
            take: 10,
        });
        return {
            totalLogs,
            actionStats,
            entityStats,
            recentActivity,
        };
    }
    async logAssetOperation(action, assetId, assetName, userId, description, oldValues, newValues, metadata) {
        return await this.createAuditLog({
            action,
            entityType: audit_log_entity_1.EntityType.ASSET,
            entityId: assetId,
            entityName: assetName,
            description,
            oldValues,
            newValues,
            metadata,
            userId,
        });
    }
    async logUserOperation(action, userId, userName, performedByUserId, description, oldValues, newValues, ipAddress, userAgent) {
        return await this.createAuditLog({
            action,
            entityType: audit_log_entity_1.EntityType.USER,
            entityId: userId,
            entityName: userName,
            description,
            oldValues,
            newValues,
            userId: performedByUserId,
            ipAddress,
            userAgent,
        });
    }
    async logSystemOperation(action, userId, description, metadata, ipAddress, userAgent) {
        return await this.createAuditLog({
            action,
            entityType: audit_log_entity_1.EntityType.SYSTEM,
            description,
            metadata,
            userId,
            ipAddress,
            userAgent,
        });
    }
};
exports.AuditLogsService = AuditLogsService;
exports.AuditLogsService = AuditLogsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(audit_log_entity_1.AuditLog)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AuditLogsService);
//# sourceMappingURL=audit-logs.service.js.map