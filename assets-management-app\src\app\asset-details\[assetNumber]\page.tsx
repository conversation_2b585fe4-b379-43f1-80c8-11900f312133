'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation'
import { useAuth, useRole } from '@/hooks/useAuth'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Grid,
  Avatar,
  Divider,
  Button,
  CircularProgress,
  Alert,
  Container
} from '@mui/material'
import {
  QrCode as QrCodeIcon,
  Category as CategoryIcon,
  LocationOn as LocationIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Info as InfoIcon,
  Print as PrintIcon
} from '@mui/icons-material'
import { assetItemService } from '@/lib/api/assetItemService'
import { assetsService } from '@/lib/api/assets'

interface AssetItemDetails {
  id: string
  assetNumber: string
  status: string
  condition: string
  serialNumber?: string
  purchaseDate?: string
  warrantyDate?: string
  notes?: string
  assignedUser?: {
    id: string
    name: string
    email: string
  }
  asset: {
    id: string
    name: string
    description?: string
    manufacturer?: string
    model?: string
    unitPrice: number
    category: {
      id: string
      name: string
    }
    location: {
      id: string
      name: string
      type: string
    }
  }
}

export default function AssetDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const assetNumber = params.assetNumber as string
  const [assetItem, setAssetItem] = useState<AssetItemDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Authentication hooks
  const { user, isAuthenticated, loading: authLoading } = useAuth()
  const { isAdmin } = useRole()

  // Check authentication first
  useEffect(() => {
    if (!authLoading) {
      if (!isAuthenticated || !isAdmin()) {
        // Redirect to login with return URL
        const currentUrl = `/asset-details/${assetNumber}`
        const returnUrl = encodeURIComponent(currentUrl)
        router.push(`/login?returnUrl=${returnUrl}`)
        return
      }
    }
  }, [authLoading, isAuthenticated, isAdmin, assetNumber, router])

  useEffect(() => {
    const loadAssetDetails = async () => {
      // Don't load data if not authenticated
      if (!isAuthenticated || !isAdmin()) {
        return
      }

      try {
        setLoading(true)
        setError(null)

        console.log('Loading asset details for asset number:', assetNumber)

        // Use the new direct API endpoint
        const assetItemData = await assetItemService.getAssetItemByAssetNumber(assetNumber)
        console.log('Loaded asset item data:', assetItemData)

        setAssetItem(assetItemData as AssetItemDetails)
      } catch (err) {
        console.error('Failed to load asset details:', err)
        setError(`Failed to load asset details: ${err instanceof Error ? err.message : 'Unknown error'}`)
      } finally {
        setLoading(false)
      }
    }

    if (assetNumber && !authLoading && isAuthenticated && isAdmin()) {
      loadAssetDetails()
    }
  }, [assetNumber, authLoading, isAuthenticated, isAdmin])

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'in_stock':
        return 'success'
      case 'in_use':
        return 'primary'
      case 'maintenance':
        return 'warning'
      case 'retired':
        return 'error'
      default:
        return 'default'
    }
  }

  const getConditionColor = (condition: string) => {
    switch (condition.toLowerCase()) {
      case 'excellent':
        return 'success'
      case 'good':
        return 'info'
      case 'fair':
        return 'warning'
      case 'poor':
        return 'error'
      default:
        return 'default'
    }
  }

  const formatStatus = (status: string) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const formatCondition = (condition: string) => {
    return condition.charAt(0).toUpperCase() + condition.slice(1).toLowerCase()
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString()
  }

  if (loading) {
    return (
      <Container maxWidth='md' sx={{ py: 4 }}>
        <Box display='flex' justifyContent='center' alignItems='center' minHeight='50vh'>
          <CircularProgress />
        </Box>
      </Container>
    )
  }

  if (error || !assetItem) {
    return (
      <Container maxWidth='md' sx={{ py: 4 }}>
        <Alert severity='error' sx={{ mb: 2 }}>
          {error || 'Asset not found'}
        </Alert>
        <Button variant='contained' href='/assets'>
          Back to Assets
        </Button>
      </Container>
    )
  }

  return (
    <Container maxWidth='md' sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Avatar sx={{ mx: 'auto', mb: 2, bgcolor: 'primary.main', width: 64, height: 64 }}>
          <QrCodeIcon sx={{ fontSize: 32 }} />
        </Avatar>
        <Typography variant='h4' component='h1' gutterBottom>
          {assetItem.asset.name}
        </Typography>
        <Typography variant='h6' color='text.secondary' gutterBottom>
          Asset Number: {assetItem.assetNumber}
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 2 }}>
          <Chip
            label={formatStatus(assetItem.status)}
            color={getStatusColor(assetItem.status) as any}
            variant='filled'
          />
          <Chip
            label={formatCondition(assetItem.condition)}
            color={getConditionColor(assetItem.condition) as any}
            variant='outlined'
          />
        </Box>
      </Box>

      {/* Asset Information */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant='h6' gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <InfoIcon color='primary' />
            Asset Information
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant='body2' color='text.secondary'>
                Description
              </Typography>
              <Typography variant='body1'>{assetItem.asset.description || 'No description'}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant='body2' color='text.secondary'>
                Unit Price
              </Typography>
              <Typography variant='body1'>{formatCurrency(assetItem.asset.unitPrice)}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant='body2' color='text.secondary'>
                Manufacturer
              </Typography>
              <Typography variant='body1'>{assetItem.asset.manufacturer || 'Not specified'}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant='body2' color='text.secondary'>
                Model
              </Typography>
              <Typography variant='body1'>{assetItem.asset.model || 'Not specified'}</Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Category & Location */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6}>
          <Card>
            <CardContent>
              <Typography variant='h6' gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CategoryIcon color='primary' />
                Category
              </Typography>
              <Typography variant='body1'>{assetItem.asset.category.name}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6}>
          <Card>
            <CardContent>
              <Typography variant='h6' gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LocationIcon color='primary' />
                Location
              </Typography>
              <Typography variant='body1'>{assetItem.asset.location.name}</Typography>
              <Typography variant='body2' color='text.secondary'>
                Type: {assetItem.asset.location.type}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Item Details */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant='h6' gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CalendarIcon color='primary' />
            Item Details
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant='body2' color='text.secondary'>
                Serial Number
              </Typography>
              <Typography variant='body1'>{assetItem.serialNumber || 'Not set'}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant='body2' color='text.secondary'>
                Purchase Date
              </Typography>
              <Typography variant='body1'>{formatDate(assetItem.purchaseDate)}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant='body2' color='text.secondary'>
                Warranty Date
              </Typography>
              <Typography variant='body1'>{formatDate(assetItem.warrantyDate)}</Typography>
            </Grid>
            {assetItem.notes && (
              <Grid item xs={12}>
                <Typography variant='body2' color='text.secondary'>
                  Notes
                </Typography>
                <Typography variant='body1'>{assetItem.notes}</Typography>
              </Grid>
            )}
          </Grid>
        </CardContent>
      </Card>

      {/* Assigned User */}
      {assetItem.assignedUser && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant='h6' gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <PersonIcon color='primary' />
              Assigned To
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar sx={{ bgcolor: 'primary.main' }}>{assetItem.assignedUser.name.charAt(0).toUpperCase()}</Avatar>
              <Box>
                <Typography variant='body1'>{assetItem.assignedUser.name}</Typography>
                <Typography variant='body2' color='text.secondary'>
                  {assetItem.assignedUser.email}
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Actions */}
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        <Button variant='contained' href='/assets' sx={{ mr: 2 }}>
          Back to Assets
        </Button>
        <Button variant='outlined' startIcon={<PrintIcon />} onClick={() => window.print()}>
          Print Details
        </Button>
      </Box>
    </Container>
  )
}
