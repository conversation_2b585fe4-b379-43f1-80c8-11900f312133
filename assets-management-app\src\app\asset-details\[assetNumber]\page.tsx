// 'use client'

// import { useEffect, useState } from 'react'
// import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation'
// import { useAuth } from '@/contexts/AuthContext'
// import {
//   Box,
//   Card,
//   CardContent,
//   Typography,
//   Chip,
//   Grid,
//   Avatar,
//   Divider,
//   Button,
//   CircularProgress,
//   Alert,
//   Container
// } from '@mui/material'
// import {
//   QrCode as QrCodeIcon,
//   Category as CategoryIcon,
//   LocationOn as LocationIcon,
//   Person as PersonIcon,
//   CalendarToday as CalendarIcon,
//   Info as InfoIcon,
//   Print as PrintIcon
// } from '@mui/icons-material'
// import { assetItemService } from '@/lib/api/assetItemService'
// import { assetsService } from '@/lib/api/assets'

// interface AssetItemDetails {
//   id: string
//   assetNumber: string
//   status: string
//   condition: string
//   serialNumber?: string
//   purchaseDate?: string
//   warrantyDate?: string
//   notes?: string
//   assignedUser?: {
//     id: string
//     name: string
//     email: string
//   }
//   asset: {
//     id: string
//     name: string
//     description?: string
//     manufacturer?: string
//     model?: string
//     unitPrice: number
//     category: {
//       id: string
//       name: string
//     }
//     location: {
//       id: string
//       name: string
//       type: string
//     }
//   }
// }

// export default function AssetDetailsPage() {
//   const params = useParams()
//   const router = useRouter()
//   const searchParams = useSearchParams()
//   const assetNumber = params.assetNumber as string
//   const [assetItem, setAssetItem] = useState<AssetItemDetails | null>(null)
//   const [loading, setLoading] = useState(true)
//   const [error, setError] = useState<string | null>(null)

//   // Authentication hooks
//   const { user, isAuthenticated, isLoading: authLoading } = useAuth()

//   // Role checking
//   const isAdmin = () => user?.role === 'admin'

//   // Check authentication first
//   useEffect(() => {
//     if (!authLoading) {
//       if (!isAuthenticated || !isAdmin()) {
//         // Redirect to login with return URL
//         const currentUrl = `/asset-details/${assetNumber}`
//         const returnUrl = encodeURIComponent(currentUrl)
//         router.push(`/login?returnUrl=${returnUrl}`)
//         return
//       }
//     }
//   }, [authLoading, isAuthenticated, assetNumber, router])

//   useEffect(() => {
//     const loadAssetDetails = async () => {
//       // Don't load data if not authenticated
//       if (!isAuthenticated || !isAdmin()) {
//         return
//       }

//       try {
//         setLoading(true)
//         setError(null)

//         console.log('Loading asset details for asset number:', assetNumber)

//         // Use the new direct API endpoint
//         const assetItemData = await assetItemService.getAssetItemByAssetNumber(assetNumber)
//         console.log('Loaded asset item data:', assetItemData)

//         setAssetItem(assetItemData as AssetItemDetails)
//       } catch (err) {
//         console.error('Failed to load asset details:', err)
//         setError(`Failed to load asset details: ${err instanceof Error ? err.message : 'Unknown error'}`)
//       } finally {
//         setLoading(false)
//       }
//     }

//     if (assetNumber && !authLoading && isAuthenticated && isAdmin()) {
//       loadAssetDetails()
//     }
//   }, [assetNumber, authLoading, isAuthenticated])

//   const getStatusColor = (status: string) => {
//     switch (status.toLowerCase()) {
//       case 'in_stock':
//         return 'success'
//       case 'in_use':
//         return 'primary'
//       case 'maintenance':
//         return 'warning'
//       case 'retired':
//         return 'error'
//       default:
//         return 'default'
//     }
//   }

//   const getConditionColor = (condition: string) => {
//     switch (condition.toLowerCase()) {
//       case 'excellent':
//         return 'success'
//       case 'good':
//         return 'info'
//       case 'fair':
//         return 'warning'
//       case 'poor':
//         return 'error'
//       default:
//         return 'default'
//     }
//   }

//   const formatStatus = (status: string) => {
//     return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
//   }

//   const formatCondition = (condition: string) => {
//     return condition.charAt(0).toUpperCase() + condition.slice(1).toLowerCase()
//   }

//   const formatCurrency = (amount: number) => {
//     return `LKR ${amount.toLocaleString()}`
//   }

//   const formatDate = (dateString?: string) => {
//     if (!dateString) return 'Not set'
//     return new Date(dateString).toLocaleDateString()
//   }

//   if (loading) {
//     return (
//       <Container maxWidth='md' sx={{ py: 4 }}>
//         <Box display='flex' justifyContent='center' alignItems='center' minHeight='50vh'>
//           <CircularProgress />
//         </Box>
//       </Container>
//     )
//   }

//   if (error || !assetItem) {
//     return (
//       <Container maxWidth='md' sx={{ py: 4 }}>
//         <Alert severity='error' sx={{ mb: 2 }}>
//           {error || 'Asset not found'}
//         </Alert>
//         <Button variant='contained' href='/assets'>
//           Back to Assets
//         </Button>
//       </Container>
//     )
//   }

//   return (
//     <Container maxWidth='md' sx={{ py: 4 }}>
//       {/* Header */}
//       <Box sx={{ mb: 4, textAlign: 'center' }}>
//         <Avatar sx={{ mx: 'auto', mb: 2, bgcolor: 'primary.main', width: 64, height: 64 }}>
//           <QrCodeIcon sx={{ fontSize: 32 }} />
//         </Avatar>
//         <Typography variant='h4' component='h1' gutterBottom>
//           {assetItem.asset.name}
//         </Typography>
//         <Typography variant='h6' color='text.secondary' gutterBottom>
//           Asset Number: {assetItem.assetNumber}
//         </Typography>
//         <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 2 }}>
//           <Chip
//             label={formatStatus(assetItem.status)}
//             color={getStatusColor(assetItem.status) as any}
//             variant='filled'
//           />
//           <Chip
//             label={formatCondition(assetItem.condition)}
//             color={getConditionColor(assetItem.condition) as any}
//             variant='outlined'
//           />
//         </Box>
//       </Box>

//       {/* Asset Information */}
//       <Card sx={{ mb: 3 }}>
//         <CardContent>
//           <Typography variant='h6' gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
//             <InfoIcon color='primary' />
//             Asset Information
//           </Typography>
//           <Grid container spacing={2}>
//             <Grid item xs={12} sm={6}>
//               <Typography variant='body2' color='text.secondary'>
//                 Description
//               </Typography>
//               <Typography variant='body1'>{assetItem.asset.description || 'No description'}</Typography>
//             </Grid>
//             <Grid item xs={12} sm={6}>
//               <Typography variant='body2' color='text.secondary'>
//                 Unit Price
//               </Typography>
//               <Typography variant='body1'>{formatCurrency(assetItem.asset.unitPrice)}</Typography>
//             </Grid>
//             <Grid item xs={12} sm={6}>
//               <Typography variant='body2' color='text.secondary'>
//                 Manufacturer
//               </Typography>
//               <Typography variant='body1'>{assetItem.asset.manufacturer || 'Not specified'}</Typography>
//             </Grid>
//             <Grid item xs={12} sm={6}>
//               <Typography variant='body2' color='text.secondary'>
//                 Model
//               </Typography>
//               <Typography variant='body1'>{assetItem.asset.model || 'Not specified'}</Typography>
//             </Grid>
//           </Grid>
//         </CardContent>
//       </Card>

//       {/* Category & Location */}
//       <Grid container spacing={2} sx={{ mb: 3 }}>
//         <Grid item xs={12} sm={6}>
//           <Card>
//             <CardContent>
//               <Typography variant='h6' gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
//                 <CategoryIcon color='primary' />
//                 Category
//               </Typography>
//               <Typography variant='body1'>{assetItem.asset.category.name}</Typography>
//             </CardContent>
//           </Card>
//         </Grid>
//         <Grid item xs={12} sm={6}>
//           <Card>
//             <CardContent>
//               <Typography variant='h6' gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
//                 <LocationIcon color='primary' />
//                 Location
//               </Typography>
//               <Typography variant='body1'>{assetItem.asset.location.name}</Typography>
//               <Typography variant='body2' color='text.secondary'>
//                 Type: {assetItem.asset.location.type}
//               </Typography>
//             </CardContent>
//           </Card>
//         </Grid>
//       </Grid>

//       {/* Item Details */}
//       <Card sx={{ mb: 3 }}>
//         <CardContent>
//           <Typography variant='h6' gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
//             <CalendarIcon color='primary' />
//             Item Details
//           </Typography>
//           <Grid container spacing={2}>
//             <Grid item xs={12} sm={6}>
//               <Typography variant='body2' color='text.secondary'>
//                 Serial Number
//               </Typography>
//               <Typography variant='body1'>{assetItem.serialNumber || 'Not set'}</Typography>
//             </Grid>
//             <Grid item xs={12} sm={6}>
//               <Typography variant='body2' color='text.secondary'>
//                 Purchase Date
//               </Typography>
//               <Typography variant='body1'>{formatDate(assetItem.purchaseDate)}</Typography>
//             </Grid>
//             <Grid item xs={12} sm={6}>
//               <Typography variant='body2' color='text.secondary'>
//                 Warranty Date
//               </Typography>
//               <Typography variant='body1'>{formatDate(assetItem.warrantyDate)}</Typography>
//             </Grid>
//             {assetItem.notes && (
//               <Grid item xs={12}>
//                 <Typography variant='body2' color='text.secondary'>
//                   Notes
//                 </Typography>
//                 <Typography variant='body1'>{assetItem.notes}</Typography>
//               </Grid>
//             )}
//           </Grid>
//         </CardContent>
//       </Card>

//       {/* Assigned User */}
//       {assetItem.assignedUser && (
//         <Card sx={{ mb: 3 }}>
//           <CardContent>
//             <Typography variant='h6' gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
//               <PersonIcon color='primary' />
//               Assigned To
//             </Typography>
//             <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
//               <Avatar sx={{ bgcolor: 'primary.main' }}>{assetItem.assignedUser.name.charAt(0).toUpperCase()}</Avatar>
//               <Box>
//                 <Typography variant='body1'>{assetItem.assignedUser.name}</Typography>
//                 <Typography variant='body2' color='text.secondary'>
//                   {assetItem.assignedUser.email}
//                 </Typography>
//               </Box>
//             </Box>
//           </CardContent>
//         </Card>
//       )}

//       {/* Actions */}
//       <Box sx={{ textAlign: 'center', mt: 4 }}>
//         <Button variant='contained' href='/assets' sx={{ mr: 2 }}>
//           Back to Assets
//         </Button>
//         <Button variant='outlined' startIcon={<PrintIcon />} onClick={() => window.print()}>
//           Print Details
//         </Button>
//       </Box>
//     </Container>
//   )
// }

'use client'

import { useEffect, useState } from 'react'
import { useParams, useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Grid,
  Avatar,
  Divider,
  Button,
  CircularProgress,
  Alert,
  Container,
  Paper,
  Stack,
  IconButton,
  Breadcrumbs,
  Link,
  useTheme,
  alpha
} from '@mui/material'
import {
  QrCode as QrCodeIcon,
  Category as CategoryIcon,
  LocationOn as LocationIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Info as InfoIcon,
  Print as PrintIcon,
  ArrowBack as ArrowBackIcon,
  Inventory as InventoryIcon,
  Business as BusinessIcon,
  AttachMoney as MoneyIcon,
  Schedule as ScheduleIcon,
  NavigateNext as NavigateNextIcon
} from '@mui/icons-material'
import { assetItemService } from '@/lib/api/assetItemService'
import { assetsService } from '@/lib/api/assets'

interface AssetItemDetails {
  id: string
  assetNumber: string
  status: string
  condition: string
  serialNumber?: string
  purchaseDate?: string
  warrantyDate?: string
  notes?: string
  assignedUser?: {
    id: string
    name: string
    email: string
  }
  asset: {
    id: string
    name: string
    description?: string
    manufacturer?: string
    model?: string
    unitPrice: number
    category: {
      id: string
      name: string
    }
    location: {
      id: string
      name: string
      type: string
    }
  }
}

export default function AssetDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const theme = useTheme()
  const assetNumber = params.assetNumber as string
  const [assetItem, setAssetItem] = useState<AssetItemDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Authentication hooks
  const { user, isAuthenticated, isLoading: authLoading } = useAuth()

  // Role checking
  const isAdmin = () => user?.role === 'admin'

  // Check authentication first
  useEffect(() => {
    if (!authLoading) {
      if (!isAuthenticated || !isAdmin()) {
        // Redirect to login with return URL
        const currentUrl = `/asset-details/${assetNumber}`
        const returnUrl = encodeURIComponent(currentUrl)
        router.push(`/login?returnUrl=${returnUrl}`)
        return
      }
    }
  }, [authLoading, isAuthenticated, assetNumber, router])

  useEffect(() => {
    const loadAssetDetails = async () => {
      // Don't load data if not authenticated
      if (!isAuthenticated || !isAdmin()) {
        return
      }

      try {
        setLoading(true)
        setError(null)

        console.log('Loading asset details for asset number:', assetNumber)

        // Use the new direct API endpoint
        const assetItemData = await assetItemService.getAssetItemByAssetNumber(assetNumber)
        console.log('Loaded asset item data:', assetItemData)

        setAssetItem(assetItemData as AssetItemDetails)
      } catch (err) {
        console.error('Failed to load asset details:', err)
        setError(`Failed to load asset details: ${err instanceof Error ? err.message : 'Unknown error'}`)
      } finally {
        setLoading(false)
      }
    }

    if (assetNumber && !authLoading && isAuthenticated && isAdmin()) {
      loadAssetDetails()
    }
  }, [assetNumber, authLoading, isAuthenticated])

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'in_stock':
        return 'success'
      case 'in_use':
        return 'primary'
      case 'maintenance':
        return 'warning'
      case 'retired':
        return 'error'
      default:
        return 'default'
    }
  }

  const getConditionColor = (condition: string) => {
    switch (condition.toLowerCase()) {
      case 'excellent':
        return 'success'
      case 'good':
        return 'info'
      case 'fair':
        return 'warning'
      case 'poor':
        return 'error'
      default:
        return 'default'
    }
  }

  const formatStatus = (status: string) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const formatCondition = (condition: string) => {
    return condition.charAt(0).toUpperCase() + condition.slice(1).toLowerCase()
  }

  const formatCurrency = (amount: number) => {
    return `LKR ${amount.toLocaleString()}`
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString()
  }

  if (loading) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
          py: 4
        }}
      >
        <Container maxWidth='lg'>
          <Box display='flex' justifyContent='center' alignItems='center' minHeight='60vh'>
            <Paper
              elevation={0}
              sx={{
                p: 6,
                textAlign: 'center',
                background: 'rgba(255, 255, 255, 0.9)',
                backdropFilter: 'blur(10px)',
                borderRadius: 3
              }}
            >
              <CircularProgress size={48} sx={{ mb: 2 }} />
              <Typography variant='h6' color='text.secondary'>
                Loading asset details...
              </Typography>
            </Paper>
          </Box>
        </Container>
      </Box>
    )
  }

  if (error || !assetItem) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
          py: 4
        }}
      >
        <Container maxWidth='lg'>
          <Paper elevation={0} sx={{ p: 4, borderRadius: 3 }}>
            <Alert severity='error' sx={{ mb: 3, borderRadius: 2 }}>
              {error || 'Asset not found'}
            </Alert>
            <Button
              variant='contained'
              href='/assets'
              size='large'
              startIcon={<ArrowBackIcon />}
              sx={{ borderRadius: 2 }}
            >
              Back to Assets
            </Button>
          </Paper>
        </Container>
      </Box>
    )
  }

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
        py: 4
      }}
    >
      <Container maxWidth='lg'>
        {/* Breadcrumbs */}
        <Paper
          elevation={0}
          sx={{
            p: 2,
            mb: 3,
            borderRadius: 2,
            background: 'rgba(255, 255, 255, 0.7)',
            backdropFilter: 'blur(10px)'
          }}
        >
          <Breadcrumbs separator={<NavigateNextIcon fontSize='small' />} aria-label='breadcrumb'>
            <Link
              underline='hover'
              color='inherit'
              href='/assets'
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <InventoryIcon fontSize='small' />
              Assets
            </Link>
            <Typography color='text.primary' sx={{ display: 'flex', alignItems: 'center', gap: 0.5, fontWeight: 600 }}>
              <QrCodeIcon fontSize='small' />
              {assetItem.assetNumber}
            </Typography>
          </Breadcrumbs>
        </Paper>

        {/* Header Section */}
        <Paper
          elevation={0}
          sx={{
            p: 4,
            mb: 4,
            borderRadius: 3,
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(10px)',
            textAlign: 'center',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* Background decoration */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              width: 200,
              height: 200,
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
              borderRadius: '50%',
              transform: 'translate(50%, -50%)',
              zIndex: 0
            }}
          />

          <Box sx={{ position: 'relative', zIndex: 1 }}>
            <Avatar
              sx={{
                mx: 'auto',
                mb: 3,
                bgcolor: 'primary.main',
                width: 80,
                height: 80,
                boxShadow: `0 8px 32px ${alpha(theme.palette.primary.main, 0.3)}`
              }}
            >
              <QrCodeIcon sx={{ fontSize: 40 }} />
            </Avatar>

            <Typography
              variant='h3'
              component='h1'
              gutterBottom
              sx={{
                fontWeight: 700,
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                color: 'transparent',
                mb: 1
              }}
            >
              {assetItem.asset.name}
            </Typography>

            <Typography variant='h5' color='text.secondary' gutterBottom sx={{ fontWeight: 500, mb: 3 }}>
              Asset #{assetItem.assetNumber}
            </Typography>

            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent='center' alignItems='center'>
              <Chip
                label={formatStatus(assetItem.status)}
                color={getStatusColor(assetItem.status) as any}
                variant='filled'
                size='large'
                sx={{
                  fontWeight: 600,
                  fontSize: '0.875rem',
                  height: 40,
                  borderRadius: 3
                }}
              />
              <Chip
                label={formatCondition(assetItem.condition)}
                color={getConditionColor(assetItem.condition) as any}
                variant='outlined'
                size='large'
                sx={{
                  fontWeight: 600,
                  fontSize: '0.875rem',
                  height: 40,
                  borderRadius: 3,
                  borderWidth: 2
                }}
              />
            </Stack>
          </Box>
        </Paper>

        {/* Main Content Grid */}
        <Grid container spacing={3}>
          {/* Left Column */}
          <Grid item xs={12} lg={8}>
            {/* Asset Information */}
            <Paper
              elevation={0}
              sx={{
                p: 4,
                mb: 3,
                borderRadius: 3,
                background: 'rgba(255, 255, 255, 0.9)',
                backdropFilter: 'blur(10px)'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar
                  sx={{
                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                    color: 'primary.main',
                    mr: 2,
                    width: 48,
                    height: 48
                  }}
                >
                  <InfoIcon />
                </Avatar>
                <Typography variant='h5' sx={{ fontWeight: 600 }}>
                  Asset Information
                </Typography>
              </Box>

              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box sx={{ p: 3, bgcolor: alpha(theme.palette.grey[100], 0.5), borderRadius: 2 }}>
                    <Typography variant='body2' color='text.secondary' sx={{ mb: 1, fontWeight: 600 }}>
                      Description
                    </Typography>
                    <Typography variant='body1' sx={{ lineHeight: 1.6 }}>
                      {assetItem.asset.description || 'No description available'}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box
                    sx={{
                      p: 3,
                      bgcolor: alpha(theme.palette.success.main, 0.05),
                      borderRadius: 2,
                      border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <MoneyIcon sx={{ color: 'success.main', mr: 1, fontSize: 20 }} />
                      <Typography variant='body2' color='text.secondary' sx={{ fontWeight: 600 }}>
                        Unit Price
                      </Typography>
                    </Box>
                    <Typography variant='h6' sx={{ color: 'success.main', fontWeight: 700 }}>
                      {formatCurrency(assetItem.asset.unitPrice)}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box
                    sx={{
                      p: 3,
                      bgcolor: alpha(theme.palette.info.main, 0.05),
                      borderRadius: 2,
                      border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <BusinessIcon sx={{ color: 'info.main', mr: 1, fontSize: 20 }} />
                      <Typography variant='body2' color='text.secondary' sx={{ fontWeight: 600 }}>
                        Manufacturer
                      </Typography>
                    </Box>
                    <Typography variant='body1' sx={{ fontWeight: 600 }}>
                      {assetItem.asset.manufacturer || 'Not specified'}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ p: 3, bgcolor: alpha(theme.palette.grey[100], 0.5), borderRadius: 2 }}>
                    <Typography variant='body2' color='text.secondary' sx={{ mb: 1, fontWeight: 600 }}>
                      Model
                    </Typography>
                    <Typography variant='body1' sx={{ fontWeight: 600 }}>
                      {assetItem.asset.model || 'Not specified'}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ p: 3, bgcolor: alpha(theme.palette.grey[100], 0.5), borderRadius: 2 }}>
                    <Typography variant='body2' color='text.secondary' sx={{ mb: 1, fontWeight: 600 }}>
                      Serial Number
                    </Typography>
                    <Typography variant='body1' sx={{ fontWeight: 600 }}>
                      {assetItem.serialNumber || 'Not set'}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Paper>

            {/* Item Details */}
            <Paper
              elevation={0}
              sx={{
                p: 4,
                mb: 3,
                borderRadius: 3,
                background: 'rgba(255, 255, 255, 0.9)',
                backdropFilter: 'blur(10px)'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar
                  sx={{
                    bgcolor: alpha(theme.palette.secondary.main, 0.1),
                    color: 'secondary.main',
                    mr: 2,
                    width: 48,
                    height: 48
                  }}
                >
                  <ScheduleIcon />
                </Avatar>
                <Typography variant='h5' sx={{ fontWeight: 600 }}>
                  Timeline & Dates
                </Typography>
              </Box>

              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box
                    sx={{
                      p: 3,
                      bgcolor: alpha(theme.palette.primary.main, 0.05),
                      borderRadius: 2,
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <CalendarIcon sx={{ color: 'primary.main', mr: 1, fontSize: 20 }} />
                      <Typography variant='body2' color='text.secondary' sx={{ fontWeight: 600 }}>
                        Purchase Date
                      </Typography>
                    </Box>
                    <Typography variant='body1' sx={{ fontWeight: 600 }}>
                      {formatDate(assetItem.purchaseDate)}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box
                    sx={{
                      p: 3,
                      bgcolor: alpha(theme.palette.warning.main, 0.05),
                      borderRadius: 2,
                      border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <CalendarIcon sx={{ color: 'warning.main', mr: 1, fontSize: 20 }} />
                      <Typography variant='body2' color='text.secondary' sx={{ fontWeight: 600 }}>
                        Warranty Date
                      </Typography>
                    </Box>
                    <Typography variant='body1' sx={{ fontWeight: 600 }}>
                      {formatDate(assetItem.warrantyDate)}
                    </Typography>
                  </Box>
                </Grid>

                {assetItem.notes && (
                  <Grid item xs={12}>
                    <Box sx={{ p: 3, bgcolor: alpha(theme.palette.grey[100], 0.5), borderRadius: 2 }}>
                      <Typography variant='body2' color='text.secondary' sx={{ mb: 1, fontWeight: 600 }}>
                        Notes
                      </Typography>
                      <Typography variant='body1' sx={{ lineHeight: 1.6 }}>
                        {assetItem.notes}
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </Paper>
          </Grid>

          {/* Right Column */}
          <Grid item xs={12} lg={4}>
            {/* Category */}
            <Paper
              elevation={0}
              sx={{
                p: 4,
                mb: 3,
                borderRadius: 3,
                background: 'rgba(255, 255, 255, 0.9)',
                backdropFilter: 'blur(10px)',
                textAlign: 'center'
              }}
            >
              <Avatar
                sx={{
                  mx: 'auto',
                  mb: 2,
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: 'primary.main',
                  width: 56,
                  height: 56
                }}
              >
                <CategoryIcon sx={{ fontSize: 28 }} />
              </Avatar>
              <Typography variant='h6' sx={{ fontWeight: 600, mb: 1 }}>
                Category
              </Typography>
              <Chip
                label={assetItem.asset.category.name}
                variant='filled'
                color='primary'
                size='large'
                sx={{ borderRadius: 2, fontWeight: 600 }}
              />
            </Paper>

            {/* Location */}
            <Paper
              elevation={0}
              sx={{
                p: 4,
                mb: 3,
                borderRadius: 3,
                background: 'rgba(255, 255, 255, 0.9)',
                backdropFilter: 'blur(10px)',
                textAlign: 'center'
              }}
            >
              <Avatar
                sx={{
                  mx: 'auto',
                  mb: 2,
                  bgcolor: alpha(theme.palette.secondary.main, 0.1),
                  color: 'secondary.main',
                  width: 56,
                  height: 56
                }}
              >
                <LocationIcon sx={{ fontSize: 28 }} />
              </Avatar>
              <Typography variant='h6' sx={{ fontWeight: 600, mb: 1 }}>
                Location
              </Typography>
              <Typography variant='body1' sx={{ fontWeight: 600, mb: 1 }}>
                {assetItem.asset.location.name}
              </Typography>
              <Typography variant='body2' color='text.secondary'>
                Type: {assetItem.asset.location.type}
              </Typography>
            </Paper>

            {/* Assigned User */}
            {assetItem.assignedUser && (
              <Paper
                elevation={0}
                sx={{
                  p: 4,
                  mb: 3,
                  borderRadius: 3,
                  background: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(10px)'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    sx={{
                      bgcolor: alpha(theme.palette.success.main, 0.1),
                      color: 'success.main',
                      mr: 2,
                      width: 40,
                      height: 40
                    }}
                  >
                    <PersonIcon />
                  </Avatar>
                  <Typography variant='h6' sx={{ fontWeight: 600 }}>
                    Assigned To
                  </Typography>
                </Box>

                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    p: 3,
                    bgcolor: alpha(theme.palette.success.main, 0.05),
                    borderRadius: 2
                  }}
                >
                  <Avatar
                    sx={{
                      bgcolor: 'success.main',
                      mr: 2,
                      width: 48,
                      height: 48,
                      fontWeight: 700
                    }}
                  >
                    {assetItem.assignedUser.name.charAt(0).toUpperCase()}
                  </Avatar>
                  <Box>
                    <Typography variant='body1' sx={{ fontWeight: 600 }}>
                      {assetItem.assignedUser.name}
                    </Typography>
                    <Typography variant='body2' color='text.secondary'>
                      {assetItem.assignedUser.email}
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            )}
          </Grid>
        </Grid>

        {/* Actions */}
        <Paper
          elevation={0}
          sx={{
            p: 4,
            mt: 4,
            borderRadius: 3,
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(10px)',
            textAlign: 'center'
          }}
        >
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent='center' alignItems='center'>
            <Button
              variant='contained'
              href='/assets'
              size='large'
              startIcon={<ArrowBackIcon />}
              sx={{
                borderRadius: 3,
                px: 4,
                py: 1.5,
                fontSize: '1rem',
                boxShadow: `0 8px 32px ${alpha(theme.palette.primary.main, 0.3)}`
              }}
            >
              Back to Assets
            </Button>
            <Button
              variant='outlined'
              startIcon={<PrintIcon />}
              onClick={() => window.print()}
              size='large'
              sx={{
                borderRadius: 3,
                px: 4,
                py: 1.5,
                fontSize: '1rem',
                borderWidth: 2,
                '&:hover': {
                  borderWidth: 2
                }
              }}
            >
              Print Details
            </Button>
          </Stack>
        </Paper>
      </Container>
    </Box>
  )
}
