import { apiClient } from './client';
import { API_CONFIG } from './config';
import {
  Location,
  CreateLocationRequest,
  UpdateLocationRequest,
  LocationType,
} from '@/types/api';

export class LocationsService {
  async getLocations(): Promise<Location[]> {
    return apiClient.get<Location[]>(API_CONFIG.ENDPOINTS.LOCATIONS.BASE);
  }

  async getActiveLocations(): Promise<Location[]> {
    return apiClient.get<Location[]>(API_CONFIG.ENDPOINTS.LOCATIONS.ACTIVE);
  }

  async getLocationHierarchy(): Promise<Location[]> {
    return apiClient.get<Location[]>(API_CONFIG.ENDPOINTS.LOCATIONS.HIERARCHY);
  }

  async getLocationById(id: string): Promise<Location> {
    return apiClient.get<Location>(API_CONFIG.ENDPOINTS.LOCATIONS.BY_ID(id));
  }

  async createLocation(data: CreateLocationRequest): Promise<Location> {
    return apiClient.post<Location>(API_CONFIG.ENDPOINTS.LOCATIONS.BASE, data);
  }

  async updateLocation(id: string, data: UpdateLocationRequest): Promise<Location> {
    return apiClient.patch<Location>(
      API_CONFIG.ENDPOINTS.LOCATIONS.BY_ID(id),
      data
    );
  }

  async deleteLocation(id: string): Promise<void> {
    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.LOCATIONS.BY_ID(id));
  }

  // Helper method to get locations as options for forms
  async getLocationOptions(): Promise<Array<{ value: string; label: string }>> {
    const locations = await this.getActiveLocations();
    return locations.map(location => ({
      value: location.id,
      label: location.fullPath || location.name,
    }));
  }

  // Get root locations (no parent)
  async getRootLocations(): Promise<Location[]> {
    const locations = await this.getLocations();
    return locations.filter(location => !location.parentId);
  }

  // Get sub-locations of a parent location
  async getSublocations(parentId: string): Promise<Location[]> {
    const locations = await this.getLocations();
    return locations.filter(location => location.parentId === parentId);
  }

  // Get locations by type
  async getLocationsByType(type: LocationType): Promise<Location[]> {
    const locations = await this.getLocations();
    return locations.filter(location => location.type === type);
  }

  // Parse coordinates if they exist
  parseCoordinates(coordinates?: string): { lat: number; lng: number } | null {
    if (!coordinates) return null;
    
    try {
      const parsed = JSON.parse(coordinates);
      if (parsed.lat && parsed.lng) {
        return { lat: parsed.lat, lng: parsed.lng };
      }
    } catch (error) {
      console.error('Failed to parse coordinates:', error);
    }
    
    return null;
  }

  // Format coordinates for storage
  formatCoordinates(lat: number, lng: number): string {
    return JSON.stringify({ lat, lng });
  }
}

export const locationsService = new LocationsService();
