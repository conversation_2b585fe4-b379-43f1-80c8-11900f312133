import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Asset, AssetStatus, AssetCondition } from '../entities/asset.entity';
import { AssetItem } from '../entities/asset-item.entity';
import { User } from '../entities/user.entity';

export interface CreateAssetItemDto {
  assetId: string;
  status?: AssetStatus;
  condition?: AssetCondition;
  serialNumber?: string;
  purchaseDate?: Date;
  warrantyExpiry?: Date;
  notes?: string;
  assignedToId?: string;
}

export interface UpdateAssetItemDto {
  status?: AssetStatus;
  condition?: AssetCondition;
  serialNumber?: string;
  purchaseDate?: Date;
  warrantyExpiry?: Date;
  notes?: string;
  assignedToId?: string;
}

export interface TransferAssetItemDto {
  assetItemId: string;
  fromStatus: AssetStatus;
  toStatus: AssetStatus;
  reason?: string;
  assignedToId?: string;
}

@Injectable()
export class AssetItemService {
  constructor(
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(AssetItem)
    private assetItemRepository: Repository<AssetItem>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private dataSource: DataSource,
  ) {}

  async generateUniqueAssetNumber(assetId: string): Promise<string> {
    const asset = await this.assetRepository.findOne({
      where: { id: assetId },
    });
    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    // Get the count of existing items for this asset
    const itemCount = await this.assetItemRepository.count({
      where: { assetId },
    });

    // Generate format: LOC-CAT-XXXX-NNN (where LOC-CAT-XXXX is from main asset number, NNN is item sequence)
    const itemSequence = String(itemCount + 1).padStart(3, '0');

    return `${asset.assetNumber}-${itemSequence}`;
  }

  async createAssetItem(
    createDto: CreateAssetItemDto,
    userId: string,
  ): Promise<AssetItem> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Validate asset exists
      const asset = await queryRunner.manager.findOne(Asset, {
        where: { id: createDto.assetId },
      });
      if (!asset) {
        throw new NotFoundException('Asset not found');
      }

      // Generate unique asset number
      const assetNumber = await this.generateUniqueAssetNumber(
        createDto.assetId,
      );

      // Create asset item
      const assetItem = queryRunner.manager.create(AssetItem, {
        ...createDto,
        assetNumber,
        lastModifiedById: userId,
      });

      const savedItem = await queryRunner.manager.save(AssetItem, assetItem);

      await queryRunner.commitTransaction();

      return this.getAssetItemById(savedItem.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async createMultipleAssetItems(
    assetId: string,
    quantity: number,
    userId: string,
  ): Promise<AssetItem[]> {
    // Check if asset items already exist for this asset
    const existingItems = await this.getAssetItemsByAssetId(assetId);
    if (existingItems.length > 0) {
      console.log(
        `Asset items already exist for asset ${assetId}. Existing count: ${existingItems.length}`,
      );
      return existingItems;
    }

    const items: AssetItem[] = [];

    for (let i = 0; i < quantity; i++) {
      const item = await this.createAssetItem(
        {
          assetId,
          status: AssetStatus.IN_STOCK,
          condition: AssetCondition.EXCELLENT,
        },
        userId,
      );
      items.push(item);
    }

    console.log(`Created ${items.length} asset items for asset ${assetId}`);
    return items;
  }

  async getAssetItemById(id: string): Promise<AssetItem> {
    const item = await this.assetItemRepository.findOne({
      where: { id },
      relations: ['asset', 'assignedTo', 'lastModifiedBy'],
    });

    if (!item) {
      throw new NotFoundException('Asset item not found');
    }

    return item;
  }

  async getAssetItemsByAssetId(assetId: string): Promise<AssetItem[]> {
    return this.assetItemRepository.find({
      where: { assetId, isActive: true },
      relations: ['asset', 'assignedTo', 'lastModifiedBy'],
      order: { assetNumber: 'ASC' },
    });
  }

  async findByAssetNumber(assetNumber: string): Promise<any> {
    // Find the asset item by asset number
    const assetItem = await this.assetItemRepository.findOne({
      where: { assetNumber, isActive: true },
      relations: ['assignedTo'],
    });

    if (!assetItem) {
      throw new NotFoundException(
        `Asset item with number ${assetNumber} not found`,
      );
    }

    // Get the associated asset with full details
    const asset = await this.assetRepository.findOne({
      where: { id: assetItem.assetId },
      relations: ['category', 'location'],
    });

    if (!asset) {
      throw new NotFoundException(
        `Asset not found for asset item ${assetNumber}`,
      );
    }

    // Return combined data
    return {
      ...assetItem,
      asset: {
        id: asset.id,
        name: asset.name,
        description: asset.description,
        manufacturer: asset.manufacturer,
        model: asset.model,
        unitPrice: asset.unitPrice,
        category: {
          id: asset.category.id,
          name: asset.category.name,
        },
        location: {
          id: asset.location.id,
          name: asset.location.name,
          type: asset.location.type,
        },
      },
    };
  }

  async getAssetItemsByStatus(status: AssetStatus): Promise<AssetItem[]> {
    return this.assetItemRepository.find({
      where: { status, isActive: true },
      relations: ['asset', 'assignedTo', 'lastModifiedBy'],
      order: { assetNumber: 'ASC' },
    });
  }

  async updateAssetItem(
    id: string,
    updateDto: UpdateAssetItemDto,
    userId: string,
  ): Promise<AssetItem> {
    const item = await this.getAssetItemById(id);

    Object.assign(item, updateDto, { lastModifiedById: userId });

    await this.assetItemRepository.save(item);
    return this.getAssetItemById(id);
  }

  async transferAssetItem(
    transferDto: TransferAssetItemDto,
    userId: string,
  ): Promise<AssetItem> {
    const item = await this.getAssetItemById(transferDto.assetItemId);

    if (item.status !== transferDto.fromStatus) {
      throw new BadRequestException(
        `Asset item is not in ${transferDto.fromStatus} status`,
      );
    }

    const updateData: Partial<AssetItem> = {
      status: transferDto.toStatus,
      lastModifiedById: userId,
    };

    // If transferring to IN_USE, assign to user
    if (
      transferDto.toStatus === AssetStatus.IN_USE &&
      transferDto.assignedToId
    ) {
      updateData.assignedToId = transferDto.assignedToId;
      updateData.assignedDate = new Date();
    }

    // If transferring from IN_USE, unassign user
    if (transferDto.fromStatus === AssetStatus.IN_USE) {
      updateData.assignedToId = undefined;
      updateData.assignedDate = undefined;
    }

    Object.assign(item, updateData);
    await this.assetItemRepository.save(item);

    return this.getAssetItemById(transferDto.assetItemId);
  }

  async getAssetQuantitiesByStatus(
    assetId: string,
  ): Promise<Record<AssetStatus, number>> {
    const items = await this.getAssetItemsByAssetId(assetId);

    const quantities: Record<AssetStatus, number> = {
      [AssetStatus.IN_STOCK]: 0,
      [AssetStatus.IN_USE]: 0,
      [AssetStatus.MAINTENANCE]: 0,
      [AssetStatus.RETIRED]: 0,
      [AssetStatus.LOST]: 0,
      [AssetStatus.DAMAGED]: 0,
    };

    items.forEach((item) => {
      quantities[item.status]++;
    });

    return quantities;
  }

  async getAllAssetQuantities(): Promise<
    {
      assetId: string;
      assetName: string;
      quantities: Record<AssetStatus, number>;
      totalItems: number;
    }[]
  > {
    const assets = await this.assetRepository.find({
      where: { isActive: true },
      select: ['id', 'name'],
    });

    const result: {
      assetId: string;
      assetName: string;
      quantities: Record<AssetStatus, number>;
      totalItems: number;
    }[] = [];
    for (const asset of assets) {
      const quantities = await this.getAssetQuantitiesByStatus(asset.id);
      const totalItems = Object.values(quantities).reduce(
        (sum, count) => sum + count,
        0,
      );

      result.push({
        assetId: asset.id,
        assetName: asset.name,
        quantities,
        totalItems,
      });
    }

    return result;
  }

  async deleteAssetItem(id: string): Promise<void> {
    const item = await this.getAssetItemById(id);
    item.isActive = false;
    await this.assetItemRepository.save(item);
  }
}
