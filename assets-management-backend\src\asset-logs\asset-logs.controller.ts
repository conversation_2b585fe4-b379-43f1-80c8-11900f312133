import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  ParseUUI<PERSON>ipe,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { AssetLogsService } from './asset-logs.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../entities/user.entity';
import { LogAction } from '../schemas/asset-log.schema';

@ApiTags('Asset Logs')
@Controller('asset-logs')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class AssetLogsController {
  constructor(private readonly assetLogsService: AssetLogsService) {}

  @Get('recent')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get recent asset logs (Admin/Manager only)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of logs to retrieve (default: 100)' })
  @ApiResponse({
    status: 200,
    description: 'Recent logs retrieved successfully',
  })
  getRecentLogs(@Query('limit', new ParseIntPipe({ optional: true })) limit?: number) {
    return this.assetLogsService.getRecentLogs(limit);
  }

  @Get('asset/:assetId')
  @ApiOperation({ summary: 'Get logs for a specific asset' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of logs to retrieve (default: 50)' })
  @ApiQuery({ name: 'skip', required: false, type: Number, description: 'Number of logs to skip (default: 0)' })
  @ApiResponse({
    status: 200,
    description: 'Asset logs retrieved successfully',
  })
  getAssetLogs(
    @Param('assetId', ParseUUIDPipe) assetId: string,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('skip', new ParseIntPipe({ optional: true })) skip?: number,
  ) {
    return this.assetLogsService.getAssetLogs(assetId, limit, skip);
  }

  @Get('user/:userId')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get activity logs for a specific user (Admin/Manager only)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of logs to retrieve (default: 50)' })
  @ApiQuery({ name: 'skip', required: false, type: Number, description: 'Number of logs to skip (default: 0)' })
  @ApiResponse({
    status: 200,
    description: 'User activity logs retrieved successfully',
  })
  getUserActivityLogs(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('skip', new ParseIntPipe({ optional: true })) skip?: number,
  ) {
    return this.assetLogsService.getUserActivityLogs(userId, limit, skip);
  }

  @Get('action/:action')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get logs by action type (Admin/Manager only)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of logs to retrieve (default: 100)' })
  @ApiResponse({
    status: 200,
    description: 'Logs by action retrieved successfully',
  })
  getLogsByAction(
    @Param('action') action: LogAction,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
  ) {
    return this.assetLogsService.getLogsByAction(action, limit);
  }

  @Get('date-range')
  @Roles(UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get logs by date range (Admin/Manager only)' })
  @ApiQuery({ name: 'startDate', required: true, description: 'Start date (ISO string)' })
  @ApiQuery({ name: 'endDate', required: true, description: 'End date (ISO string)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of logs to retrieve (default: 100)' })
  @ApiResponse({
    status: 200,
    description: 'Logs by date range retrieved successfully',
  })
  getLogsByDateRange(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
  ) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    return this.assetLogsService.getLogsByDateRange(start, end, limit);
  }
}
