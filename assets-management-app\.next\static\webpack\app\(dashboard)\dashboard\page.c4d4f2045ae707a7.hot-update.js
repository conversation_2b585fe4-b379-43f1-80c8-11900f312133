"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx":
/*!************************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AssetDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,LinearProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Category_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Category,LocationOn,Person,Refresh!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Category_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Category,LocationOn,Person,Refresh!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_Category_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Category,LocationOn,Person,Refresh!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _barrel_optimize_names_Category_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Category,LocationOn,Person,Refresh!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Category.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst statusColors = {\n    in_use: \"primary\",\n    in_stock: \"success\",\n    maintenance: \"warning\",\n    retired: \"secondary\",\n    lost: \"error\",\n    damaged: \"error\",\n    unknown: \"default\"\n};\nconst conditionColors = {\n    excellent: \"success\",\n    good: \"info\",\n    fair: \"warning\",\n    poor: \"error\",\n    unknown: \"default\"\n};\nfunction AssetDashboard() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assets, setAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load dashboard data\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Load all data in parallel\n            const [assetsResponse, categoriesData, locationsData, usersData] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.assetsService.getAssets({\n                    limit: 1000\n                }),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.categoriesService.getCategories(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.locationsService.getLocations(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.usersService.getUsers()\n            ]);\n            const allAssets = assetsResponse.assets;\n            setAssets(allAssets);\n            setCategories(categoriesData);\n            setLocations(locationsData);\n            setUsers(usersData);\n            // Calculate statistics\n            const totalAssets = allAssets.length;\n            const statusCounts = allAssets.reduce((acc, asset)=>{\n                const status = asset.status || \"unknown\";\n                acc[status] = (acc[status] || 0) + 1;\n                return acc;\n            }, {});\n            const conditionCounts = allAssets.reduce((acc, asset)=>{\n                const condition = asset.condition || \"unknown\";\n                acc[condition] = (acc[condition] || 0) + 1;\n                return acc;\n            }, {});\n            const categoryCounts = allAssets.reduce((acc, asset)=>{\n                var _asset_category;\n                const categoryName = ((_asset_category = asset.category) === null || _asset_category === void 0 ? void 0 : _asset_category.name) || \"Unknown\";\n                acc[categoryName] = (acc[categoryName] || 0) + 1;\n                return acc;\n            }, {});\n            const locationCounts = allAssets.reduce((acc, asset)=>{\n                var _asset_location;\n                const locationName = ((_asset_location = asset.location) === null || _asset_location === void 0 ? void 0 : _asset_location.name) || \"Unknown\";\n                acc[locationName] = (acc[locationName] || 0) + 1;\n                return acc;\n            }, {});\n            // Create breakdown arrays\n            const categoryBreakdown = Object.entries(categoryCounts).map((param)=>{\n                let [name, count] = param;\n                return {\n                    name,\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const locationBreakdown = Object.entries(locationCounts).map((param)=>{\n                let [name, count] = param;\n                return {\n                    name,\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const statusBreakdown = Object.entries(statusCounts).map((param)=>{\n                let [status, count] = param;\n                return {\n                    status: status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            const conditionBreakdown = Object.entries(conditionCounts).map((param)=>{\n                let [condition, count] = param;\n                return {\n                    condition: condition.charAt(0).toUpperCase() + condition.slice(1),\n                    count,\n                    percentage: Math.round(count / totalAssets * 100)\n                };\n            }).sort((a, b)=>b.count - a.count);\n            // Get recent assets (last 5)\n            const recentAssets = allAssets.sort((a, b)=>new Date(b.createdAt || \"\").getTime() - new Date(a.createdAt || \"\").getTime()).slice(0, 5);\n            const dashboardStats = {\n                totalAssets,\n                inUse: statusCounts.in_use || 0,\n                inStock: statusCounts.in_stock || 0,\n                maintenance: statusCounts.maintenance || 0,\n                retired: statusCounts.retired || 0,\n                lost: statusCounts.lost || 0,\n                damaged: statusCounts.damaged || 0,\n                totalCategories: categoriesData.length,\n                totalLocations: locationsData.length,\n                totalUsers: usersData.length,\n                recentAssets,\n                categoryBreakdown,\n                locationBreakdown,\n                statusBreakdown,\n                conditionBreakdown\n            };\n            setStats(dashboardStats);\n        } catch (err) {\n            console.error(\"Failed to load dashboard data:\", err);\n            setError(\"Failed to load dashboard data. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const StatCard = (param)=>{\n        let { title, value, icon, color, subtitle } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            sx: {\n                background: \"linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)\",\n                backdropFilter: \"blur(10px)\",\n                border: \"1px solid rgba(255,255,255,0.2)\",\n                boxShadow: \"none\",\n                borderColor: \"divider\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    p: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    color: \"text.secondary\",\n                                    gutterBottom: true,\n                                    variant: \"body2\",\n                                    sx: {\n                                        fontWeight: 500\n                                    },\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"h3\",\n                                    component: \"div\",\n                                    color: color,\n                                    sx: {\n                                        fontWeight: 700,\n                                        mb: 0.5\n                                    },\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        fontSize: \"0.875rem\"\n                                    },\n                                    children: subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this),\n                        icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            sx: {\n                                bgcolor: \"\".concat(color, \".main\"),\n                                width: 64,\n                                height: 64,\n                                boxShadow: \"0 4px 14px rgba(0,0,0,0.15)\"\n                            },\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 216,\n            columnNumber: 5\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3,\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"60vh\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                size: 60\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    severity: \"error\",\n                    sx: {\n                        mb: 3\n                    },\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    variant: \"contained\",\n                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 48\n                    }, void 0),\n                    onClick: loadDashboardData,\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, this);\n    }\n    if (!stats) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            sx: {\n                p: 3\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                severity: \"info\",\n                children: \"No data available\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 280,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        sx: {\n            p: 4,\n            backgroundColor: \"grey.50\",\n            minHeight: \"100vh\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"h1\",\n                                gutterBottom: true,\n                                sx: {\n                                    fontWeight: 700,\n                                    color: \"text.primary\"\n                                },\n                                children: \"Asset Management Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"body1\",\n                                color: \"text.secondary\",\n                                children: \"Comprehensive overview of your organization's assets\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        variant: \"outlined\",\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 22\n                        }, void 0),\n                        onClick: loadDashboardData,\n                        sx: {\n                            borderRadius: 2,\n                            textTransform: \"none\",\n                            fontWeight: 600\n                        },\n                        children: \"Refresh Data\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Total Assets\",\n                            value: stats.totalAssets.toLocaleString(),\n                            // icon={<InventoryIcon />}\n                            color: \"primary\",\n                            subtitle: \"All registered assets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Categories\",\n                            value: stats.totalCategories.toLocaleString(),\n                            // icon={<CategoryIcon />}\n                            color: \"info\",\n                            subtitle: \"Asset categories\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Locations\",\n                            value: stats.totalLocations.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"warning\",\n                            subtitle: \"Storage locations\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Users\",\n                            value: stats.totalUsers.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"success\",\n                            subtitle: \"System users\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"In Use\",\n                            value: stats.inUse.toLocaleString(),\n                            color: \"primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Available\",\n                            value: stats.inStock.toLocaleString(),\n                            color: \"success\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Maintenance\",\n                            value: stats.maintenance.toLocaleString(),\n                            color: \"warning\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Retired\",\n                            value: stats.retired.toLocaleString(),\n                            color: \"secondary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Lost\",\n                            value: stats.lost.toLocaleString(),\n                            color: \"error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Damaged\",\n                            value: stats.damaged.toLocaleString(),\n                            color: \"error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 353,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                container: true,\n                spacing: 3,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\",\n                                boxShadow: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Asset Status Distribution\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            mt: 2\n                                        },\n                                        children: stats.statusBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    mb: index === stats.statusBreakdown.length - 1 ? 0 : 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"space-between\",\n                                                            mb: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 500\n                                                                },\n                                                                children: item.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: [\n                                                                    item.count,\n                                                                    \" (\",\n                                                                    item.percentage,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        variant: \"determinate\",\n                                                        value: item.percentage,\n                                                        sx: {\n                                                            height: 8,\n                                                            borderRadius: 4,\n                                                            backgroundColor: \"grey.200\",\n                                                            \"& .MuiLinearProgress-bar\": {\n                                                                borderRadius: 4\n                                                            }\n                                                        },\n                                                        color: item.status.toLowerCase().includes(\"use\") ? \"primary\" : item.status.toLowerCase().includes(\"stock\") ? \"success\" : item.status.toLowerCase().includes(\"maintenance\") ? \"warning\" : \"secondary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.status, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\",\n                                boxShadow: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Assets by Category\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: \"small\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Count\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Percentage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    children: stats.categoryBreakdown.slice(0, 8).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 460,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            category.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    sx: {\n                                                                        fontWeight: 500\n                                                                    },\n                                                                    children: category.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        label: \"\".concat(category.percentage, \"%\"),\n                                                                        size: \"small\",\n                                                                        color: category.percentage > 20 ? \"primary\" : \"default\",\n                                                                        variant: category.percentage > 20 ? \"filled\" : \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, category.name, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\",\n                                boxShadow: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Assets by Location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: \"small\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Count\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                align: \"right\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Percentage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    children: stats.locationBreakdown.slice(0, 8).map((location)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"warning\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 516,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            location.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 515,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    sx: {\n                                                                        fontWeight: 500\n                                                                    },\n                                                                    children: location.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        label: \"\".concat(location.percentage, \"%\"),\n                                                                        size: \"small\",\n                                                                        color: location.percentage > 15 ? \"warning\" : \"default\",\n                                                                        variant: location.percentage > 15 ? \"filled\" : \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 524,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, location.name, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 486,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\",\n                                boxShadow: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Asset Condition Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            mt: 2\n                                        },\n                                        children: stats.conditionBreakdown.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    mb: index === stats.conditionBreakdown.length - 1 ? 0 : 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"space-between\",\n                                                            mb: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 500\n                                                                },\n                                                                children: item.condition\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: [\n                                                                    item.count,\n                                                                    \" (\",\n                                                                    item.percentage,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        variant: \"determinate\",\n                                                        value: item.percentage,\n                                                        sx: {\n                                                            height: 8,\n                                                            borderRadius: 4,\n                                                            backgroundColor: \"grey.200\",\n                                                            \"& .MuiLinearProgress-bar\": {\n                                                                borderRadius: 4\n                                                            }\n                                                        },\n                                                        color: item.condition.toLowerCase() === \"excellent\" ? \"success\" : item.condition.toLowerCase() === \"good\" ? \"info\" : item.condition.toLowerCase() === \"fair\" ? \"warning\" : \"error\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.condition, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            sx: {\n                                // borderRadius: 3,\n                                border: \"1px solid\",\n                                borderColor: \"divider\",\n                                boxShadow: \"none\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                sx: {\n                                    p: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        sx: {\n                                            fontWeight: 600,\n                                            mb: 3\n                                        },\n                                        children: \"Recently Added Assets\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Asset Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Condition\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Assigned To\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                sx: {\n                                                                    fontWeight: 600\n                                                                },\n                                                                children: \"Date Added\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    children: stats.recentAssets.map((asset)=>{\n                                                        var _asset_category, _asset_location, _asset_assignedTo;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        fontWeight: \"medium\",\n                                                                        color: \"primary.main\",\n                                                                        children: asset.assetNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 624,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        fontWeight: \"medium\",\n                                                                        children: asset.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 630,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 629,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 636,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_category = asset.category) === null || _asset_category === void 0 ? void 0 : _asset_category.name) || \"Unknown\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 637,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"warning\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 642,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_location = asset.location) === null || _asset_location === void 0 ? void 0 : _asset_location.name) || \"Unknown\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 643,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 641,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 640,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        label: asset.status ? asset.status.replace(\"_\", \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase()) : \"Unknown\",\n                                                                        color: asset.status ? statusColors[asset.status] : \"default\",\n                                                                        size: \"small\",\n                                                                        variant: \"filled\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 647,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        label: asset.condition ? asset.condition.charAt(0).toUpperCase() + asset.condition.slice(1) : \"Unknown\",\n                                                                        color: asset.condition ? conditionColors[asset.condition] : \"default\",\n                                                                        size: \"small\",\n                                                                        variant: \"outlined\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 659,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        sx: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Category_LocationOn_Person_Refresh_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                fontSize: \"small\",\n                                                                                color: \"action\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 676,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                variant: \"body2\",\n                                                                                children: ((_asset_assignedTo = asset.assignedTo) === null || _asset_assignedTo === void 0 ? void 0 : _asset_assignedTo.fullName) || \"Unassigned\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 677,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        color: \"text.secondary\",\n                                                                        children: asset.createdAt ? new Date(asset.createdAt).toLocaleDateString() : \"Unknown\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 681,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, asset.id, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 15\n                                    }, this),\n                                    stats.recentAssets.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        sx: {\n                                            textAlign: \"center\",\n                                            py: 4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_LinearProgress_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: \"No recent assets found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 595,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 594,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 374,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, this);\n}\n_s(AssetDashboard, \"XHXyvutMjVwFwI//GM0yYOuxtd0=\");\n_c = AssetDashboard;\nvar _c;\n$RefreshReg$(_c, \"AssetDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx\n"));

/***/ })

});