import { useState, useCallback, useMemo } from 'react';
import { assetsService } from '@/lib/api';
import { Asset, AssetSearchParams, CreateAssetRequest, UpdateAssetRequest } from '@/types/api';
import { useApi, useAsyncApi, useMutation } from './useApi';

export function useAssets(params?: AssetSearchParams) {
  const {
    data: assetsData,
    loading,
    error,
    execute: refetch,
  } = useApi(() => assetsService.getAssets(params), {
    immediate: true,
  });

  return {
    assets: assetsData?.assets || [],
    total: assetsData?.total || 0,
    page: assetsData?.page || 1,
    limit: assetsData?.limit || 10,
    totalPages: assetsData?.totalPages || 0,
    loading,
    error,
    refetch,
  };
}

export function useAsset(id: string) {
  return useApi(() => assetsService.getAssetById(id), {
    immediate: !!id,
  });
}

export function useAssetByNumber(assetNumber: string) {
  return useApi(() => assetsService.getAssetByNumber(assetNumber), {
    immediate: !!assetNumber,
  });
}

export function useAssetStats() {
  return useApi(() => assetsService.getAssetStats());
}

export function useAssetMutations() {
  const createAsset = useMutation(
    (data: CreateAssetRequest) => assetsService.createAsset(data)
  );

  const updateAsset = useMutation(
    (id: string, data: UpdateAssetRequest) => assetsService.updateAsset(id, data)
  );

  const deleteAsset = useMutation(
    (id: string) => assetsService.deleteAsset(id)
  );

  const assignAsset = useMutation(
    (id: string, data: { assignedToId: string; notes?: string }) =>
      assetsService.assignAsset(id, data)
  );

  const unassignAsset = useMutation(
    (id: string) => assetsService.unassignAsset(id)
  );

  const uploadImage = useMutation(
    (assetId: string, file: File) => assetsService.uploadAssetImage(assetId, file)
  );

  return {
    createAsset,
    updateAsset,
    deleteAsset,
    assignAsset,
    unassignAsset,
    uploadImage,
  };
}

export function useAssetSearch() {
  const [searchParams, setSearchParams] = useState<AssetSearchParams>({
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'DESC',
  });

  const searchAssets = useAsyncApi(
    (params: AssetSearchParams) => assetsService.getAssets(params)
  );

  const search = useCallback((newParams: Partial<AssetSearchParams>) => {
    const updatedParams = { ...searchParams, ...newParams, page: 1 };
    setSearchParams(updatedParams);
    searchAssets.execute(updatedParams);
  }, [searchParams, searchAssets]);

  const changePage = useCallback((page: number) => {
    const updatedParams = { ...searchParams, page };
    setSearchParams(updatedParams);
    searchAssets.execute(updatedParams);
  }, [searchParams, searchAssets]);

  const changeLimit = useCallback((limit: number) => {
    const updatedParams = { ...searchParams, limit, page: 1 };
    setSearchParams(updatedParams);
    searchAssets.execute(updatedParams);
  }, [searchParams, searchAssets]);

  const sort = useCallback((sortBy: string, sortOrder: 'ASC' | 'DESC' = 'ASC') => {
    const updatedParams = { ...searchParams, sortBy, sortOrder, page: 1 };
    setSearchParams(updatedParams);
    searchAssets.execute(updatedParams);
  }, [searchParams, searchAssets]);

  const reset = useCallback(() => {
    const defaultParams = {
      page: 1,
      limit: 10,
      sortBy: 'createdAt',
      sortOrder: 'DESC' as const,
    };
    setSearchParams(defaultParams);
    searchAssets.execute(defaultParams);
  }, [searchAssets]);

  const results = useMemo(() => {
    if (!searchAssets.data) return null;
    return {
      assets: searchAssets.data.assets,
      total: searchAssets.data.total,
      page: searchAssets.data.page,
      limit: searchAssets.data.limit,
      totalPages: searchAssets.data.totalPages,
    };
  }, [searchAssets.data]);

  return {
    searchParams,
    results,
    loading: searchAssets.loading,
    error: searchAssets.error,
    search,
    changePage,
    changeLimit,
    sort,
    reset,
  };
}

// Hook for getting assets by category
export function useAssetsByCategory(categoryId: string) {
  return useApi(() => assetsService.getAssetsByCategory(categoryId), {
    immediate: !!categoryId,
  });
}

// Hook for getting assets by location
export function useAssetsByLocation(locationId: string) {
  return useApi(() => assetsService.getAssetsByLocation(locationId), {
    immediate: !!locationId,
  });
}

// Hook for getting assets assigned to a user
export function useAssetsByUser(userId: string) {
  return useApi(() => assetsService.getAssetsByUser(userId), {
    immediate: !!userId,
  });
}
