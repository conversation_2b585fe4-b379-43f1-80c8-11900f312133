"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/layout",{

/***/ "(app-pages-browser)/./src/components/layout/shared/UserDropdown.tsx":
/*!*******************************************************!*\
  !*** ./src/components/layout/shared/UserDropdown.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/styles */ \"(app-pages-browser)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _mui_material_Badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/Badge */ \"(app-pages-browser)/./node_modules/@mui/material/Badge/Badge.js\");\n/* harmony import */ var _mui_material_Avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/Avatar */ \"(app-pages-browser)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _mui_material_Popper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/Popper */ \"(app-pages-browser)/./node_modules/@mui/material/Popper/Popper.js\");\n/* harmony import */ var _mui_material_Fade__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/Fade */ \"(app-pages-browser)/./node_modules/@mui/material/Fade/Fade.js\");\n/* harmony import */ var _mui_material_Paper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/Paper */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _mui_material_ClickAwayListener__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/ClickAwayListener */ \"(app-pages-browser)/./node_modules/@mui/material/ClickAwayListener/ClickAwayListener.js\");\n/* harmony import */ var _mui_material_MenuList__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material/MenuList */ \"(app-pages-browser)/./node_modules/@mui/material/MenuList/MenuList.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/Typography */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/Button */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// React Imports\n\n// Next Imports\n\n// Context Imports\n\n// MUI Imports\n\n\n\n\n\n\n\n\n\n\n// Styled component for badge content\nconst BadgeContentSpan = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\"span\")({\n    width: 8,\n    height: 8,\n    borderRadius: \"50%\",\n    cursor: \"pointer\",\n    backgroundColor: \"var(--mui-palette-success-main)\",\n    boxShadow: \"0 0 0 2px var(--mui-palette-background-paper)\"\n});\nconst UserDropdown = ()=>{\n    _s();\n    // States\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Refs\n    const anchorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Hooks\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const handleDropdownOpen = ()=>{\n        !open ? setOpen(true) : setOpen(false);\n    };\n    const handleDropdownClose = (event, url)=>{\n        if (url) {\n            router.push(url);\n        }\n        if (anchorRef.current && anchorRef.current.contains(event === null || event === void 0 ? void 0 : event.target)) {\n            return;\n        }\n        setOpen(false);\n    };\n    const handleLogout = ()=>{\n        logout();\n        setOpen(false);\n    };\n    // Get user initials for avatar\n    const getUserInitials = ()=>{\n        var _user_firstName, _user_lastName;\n        if (!user) return \"U\";\n        return \"\".concat(((_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName[0]) || \"\").concat(((_user_lastName = user.lastName) === null || _user_lastName === void 0 ? void 0 : _user_lastName[0]) || \"\").toUpperCase();\n    };\n    // Get user display name\n    const getUserDisplayName = ()=>{\n        if (!user) return \"User\";\n        return \"\".concat(user.firstName || \"\", \" \").concat(user.lastName || \"\").trim() || user.email;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Badge__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                ref: anchorRef,\n                overlap: \"circular\",\n                badgeContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BadgeContentSpan, {\n                    onClick: handleDropdownOpen\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 23\n                }, void 0),\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: \"right\"\n                },\n                className: \"mis-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Avatar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: anchorRef,\n                    alt: getUserDisplayName(),\n                    onClick: handleDropdownOpen,\n                    className: \"cursor-pointer bs-[38px] is-[38px]\",\n                    sx: {\n                        bgcolor: \"primary.main\",\n                        color: \"primary.contrastText\"\n                    },\n                    children: getUserInitials()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Popper__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                open: open,\n                transition: true,\n                disablePortal: true,\n                placement: \"bottom-end\",\n                anchorEl: anchorRef.current,\n                className: \"min-is-[240px] !mbs-4 z-[1]\",\n                children: (param)=>{\n                    let { TransitionProps, placement } = param;\n                    var _user_role, _user_role1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Fade__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        ...TransitionProps,\n                        style: {\n                            transformOrigin: placement === \"bottom-end\" ? \"right top\" : \"left top\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Paper__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_ClickAwayListener__WEBPACK_IMPORTED_MODULE_10__.ClickAwayListener, {\n                                onClickAway: (e)=>handleDropdownClose(e),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_MenuList__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center plb-2 pli-4 gap-2\",\n                                            tabIndex: -1,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Avatar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    alt: getUserDisplayName(),\n                                                    sx: {\n                                                        bgcolor: \"primary.main\",\n                                                        color: \"primary.contrastText\"\n                                                    },\n                                                    children: getUserInitials()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"font-medium\",\n                                                            color: \"text.primary\",\n                                                            children: getUserDisplayName()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            variant: \"caption\",\n                                                            children: (user === null || user === void 0 ? void 0 : (_user_role = user.role) === null || _user_role === void 0 ? void 0 : _user_role.charAt(0).toUpperCase()) + (user === null || user === void 0 ? void 0 : (_user_role1 = user.role) === null || _user_role1 === void 0 ? void 0 : _user_role1.slice(1)) || \"User\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center plb-2 pli-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                fullWidth: true,\n                                                variant: \"contained\",\n                                                color: \"error\",\n                                                size: \"small\",\n                                                endIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-logout-box-r-line\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                onClick: handleLogout,\n                                                sx: {\n                                                    \"& .MuiButton-endIcon\": {\n                                                        marginInlineStart: 1.5\n                                                    }\n                                                },\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, undefined);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\components\\\\layout\\\\shared\\\\UserDropdown.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(UserDropdown, \"Ki6AwJ+xLhf7pZqDJwJ6otUpJQc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = UserDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UserDropdown);\nvar _c;\n$RefreshReg$(_c, \"UserDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/shared/UserDropdown.tsx\n"));

/***/ })

});