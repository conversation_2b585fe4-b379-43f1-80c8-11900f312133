"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const asset_entity_1 = require("../entities/asset.entity");
const category_entity_1 = require("../entities/category.entity");
const location_entity_1 = require("../entities/location.entity");
const user_entity_1 = require("../entities/user.entity");
const asset_number_service_1 = require("./asset-number.service");
let AssetsService = class AssetsService {
    assetRepository;
    categoryRepository;
    locationRepository;
    userRepository;
    assetNumberService;
    constructor(assetRepository, categoryRepository, locationRepository, userRepository, assetNumberService) {
        this.assetRepository = assetRepository;
        this.categoryRepository = categoryRepository;
        this.locationRepository = locationRepository;
        this.userRepository = userRepository;
        this.assetNumberService = assetNumberService;
    }
    async create(createAssetDto) {
        const category = await this.categoryRepository.findOne({
            where: { id: createAssetDto.categoryId },
        });
        if (!category) {
            throw new common_1.NotFoundException('Category not found');
        }
        const location = await this.locationRepository.findOne({
            where: { id: createAssetDto.locationId },
        });
        if (!location) {
            throw new common_1.NotFoundException('Location not found');
        }
        if (createAssetDto.assignedToId) {
            const user = await this.userRepository.findOne({
                where: { id: createAssetDto.assignedToId },
            });
            if (!user) {
                throw new common_1.NotFoundException('Assigned user not found');
            }
        }
        const assetNumber = await this.assetNumberService.generateAssetNumber();
        const asset = this.assetRepository.create(createAssetDto);
        asset.assetNumber = assetNumber;
        asset.totalValue = asset.unitPrice * asset.quantity;
        return this.assetRepository.save(asset);
    }
    async findAll(options = {}) {
        const { search, categoryId, locationId, status, condition, assignedToId, manufacturer, model, minValue, maxValue, purchaseDateFrom, purchaseDateTo, page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'DESC', } = options;
        const query = this.assetRepository
            .createQueryBuilder('asset')
            .leftJoinAndSelect('asset.category', 'category')
            .leftJoinAndSelect('asset.location', 'location');
        if (search) {
            query.andWhere('(asset.name ILIKE :search OR asset.description ILIKE :search OR asset.assetNumber ILIKE :search OR asset.serialNumber ILIKE :search OR asset.manufacturer ILIKE :search OR asset.model ILIKE :search)', { search: `%${search}%` });
        }
        if (categoryId) {
            query.andWhere('asset.categoryId = :categoryId', { categoryId });
        }
        if (locationId) {
            query.andWhere('asset.locationId = :locationId', { locationId });
        }
        if (status) {
            query.andWhere('asset.status = :status', { status });
        }
        if (condition) {
            query.andWhere('asset.condition = :condition', { condition });
        }
        if (manufacturer) {
            query.andWhere('asset.manufacturer ILIKE :manufacturer', {
                manufacturer: `%${manufacturer}%`,
            });
        }
        if (model) {
            query.andWhere('asset.model ILIKE :model', { model: `%${model}%` });
        }
        if (minValue !== undefined) {
            query.andWhere('asset.totalValue >= :minValue', { minValue });
        }
        if (maxValue !== undefined) {
            query.andWhere('asset.totalValue <= :maxValue', { maxValue });
        }
        if (purchaseDateFrom) {
            query.andWhere('asset.purchaseDate >= :purchaseDateFrom', {
                purchaseDateFrom,
            });
        }
        if (purchaseDateTo) {
            query.andWhere('asset.purchaseDate <= :purchaseDateTo', {
                purchaseDateTo,
            });
        }
        const validSortFields = [
            'name',
            'assetNumber',
            'createdAt',
            'unitPrice',
            'status',
        ];
        const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
        query.orderBy(`asset.${sortField}`, sortOrder);
        const offset = (page - 1) * limit;
        query.skip(offset).take(limit);
        const [assets, total] = await query.getManyAndCount();
        const totalPages = Math.ceil(total / limit);
        return {
            assets,
            total,
            page,
            limit,
            totalPages,
        };
    }
    async findOne(id) {
        const asset = await this.assetRepository.findOne({
            where: { id },
            relations: ['category', 'location'],
        });
        if (!asset) {
            throw new common_1.NotFoundException('Asset not found');
        }
        return asset;
    }
    async findByAssetNumber(assetNumber) {
        const asset = await this.assetRepository.findOne({
            where: { assetNumber },
            relations: ['category', 'location'],
        });
        if (!asset) {
            throw new common_1.NotFoundException('Asset not found');
        }
        return asset;
    }
    async update(id, updateAssetDto) {
        const asset = await this.assetRepository.findOne({ where: { id } });
        if (!asset) {
            throw new common_1.NotFoundException('Asset not found');
        }
        if (updateAssetDto.categoryId) {
            const category = await this.categoryRepository.findOne({
                where: { id: updateAssetDto.categoryId },
            });
            if (!category) {
                throw new common_1.NotFoundException('Category not found');
            }
        }
        if (updateAssetDto.locationId) {
            const location = await this.locationRepository.findOne({
                where: { id: updateAssetDto.locationId },
            });
            if (!location) {
                throw new common_1.NotFoundException('Location not found');
            }
        }
        let totalValue;
        if (updateAssetDto.unitPrice !== undefined ||
            updateAssetDto.quantity !== undefined) {
            const newUnitPrice = updateAssetDto.unitPrice ?? asset.unitPrice;
            const newQuantity = updateAssetDto.quantity ?? asset.quantity;
            totalValue = newUnitPrice * newQuantity;
        }
        await this.assetRepository.update(id, updateAssetDto);
        if (totalValue !== undefined) {
            await this.assetRepository.update(id, { totalValue });
        }
        return this.findOne(id);
    }
    async remove(id) {
        const asset = await this.assetRepository.findOne({ where: { id } });
        if (!asset) {
            throw new common_1.NotFoundException('Asset not found');
        }
        await this.assetRepository.remove(asset);
    }
    async assignAsset(id, assignAssetDto) {
        const asset = await this.assetRepository.findOne({ where: { id } });
        if (!asset) {
            throw new common_1.NotFoundException('Asset not found');
        }
        const user = await this.userRepository.findOne({
            where: { id: assignAssetDto.assignedToId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        await this.assetRepository.update(id, {
            notes: assignAssetDto.notes || asset.notes,
        });
        return this.findOne(id);
    }
    async unassignAsset(id) {
        const asset = await this.assetRepository.findOne({ where: { id } });
        if (!asset) {
            throw new common_1.NotFoundException('Asset not found');
        }
        await this.assetRepository.update(id, {});
        return this.findOne(id);
    }
    async getAssetStats() {
        const assets = await this.assetRepository.find({
            relations: ['category'],
        });
        const totalAssets = assets.length;
        const totalValue = assets.reduce((sum, asset) => sum + (asset.totalValue || 0), 0);
        const statusBreakdown = assets.reduce((acc, asset) => {
            return acc;
        }, {});
        const categoryMap = new Map();
        assets.forEach((asset) => {
            const categoryName = asset.category?.name || 'Uncategorized';
            const existing = categoryMap.get(categoryName) || { count: 0, value: 0 };
            categoryMap.set(categoryName, {
                count: existing.count + 1,
                value: existing.value + (asset.totalValue || 0),
            });
        });
        const categoryBreakdown = Array.from(categoryMap.entries()).map(([categoryName, data]) => ({
            categoryName,
            count: data.count,
            value: data.value,
        }));
        return {
            totalAssets,
            totalValue,
            statusBreakdown,
            categoryBreakdown,
        };
    }
};
exports.AssetsService = AssetsService;
exports.AssetsService = AssetsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(asset_entity_1.Asset)),
    __param(1, (0, typeorm_1.InjectRepository)(category_entity_1.Category)),
    __param(2, (0, typeorm_1.InjectRepository)(location_entity_1.Location)),
    __param(3, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        asset_number_service_1.AssetNumberService])
], AssetsService);
//# sourceMappingURL=assets.service.js.map