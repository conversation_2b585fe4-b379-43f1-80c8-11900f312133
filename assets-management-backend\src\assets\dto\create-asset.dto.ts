import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsUUID,
  IsNumber,
  IsEnum,
  IsDateString,
  IsBoolean,
  Min,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { AssetStatus, AssetCondition } from '../../entities/asset.entity';

export class CreateAssetDto {
  @ApiProperty({
    description: 'Asset name',
    example: 'Dell Laptop XPS 13',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Asset description',
    example: 'High-performance laptop for development work',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Asset model',
    example: 'XPS 13 9310',
    required: false,
  })
  @IsString()
  @IsOptional()
  model?: string;

  @ApiProperty({
    description: 'Asset manufacturer',
    example: 'Dell',
    required: false,
  })
  @IsString()
  @IsOptional()
  manufacturer?: string;

  @ApiProperty({
    description: 'Asset serial number',
    example: 'DL123456789',
    required: false,
  })
  @IsString()
  @IsOptional()
  serialNumber?: string;

  @ApiProperty({
    description: 'Unit price of the asset',
    example: 1299.99,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Type(() => Number)
  unitPrice: number;

  @ApiProperty({
    description: 'Current market/depreciated value of the asset',
    example: 999.99,
    required: false,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Type(() => Number)
  @IsOptional()
  currentValue?: number;

  @ApiProperty({
    description: 'Quantity of assets',
    example: 1,
    default: 1,
  })
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  quantity: number = 1;

  @ApiProperty({
    description: 'Asset status',
    enum: AssetStatus,
    default: AssetStatus.IN_STOCK,
    required: false,
  })
  @IsEnum(AssetStatus)
  @IsOptional()
  status?: AssetStatus;

  @ApiProperty({
    description: 'Asset condition',
    enum: AssetCondition,
    default: AssetCondition.GOOD,
    required: false,
  })
  @IsEnum(AssetCondition)
  @IsOptional()
  condition?: AssetCondition;

  @ApiProperty({
    description: 'Purchase date',
    example: '2024-01-15',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  purchaseDate?: string;

  @ApiProperty({
    description: 'Warranty expiry date',
    example: '2027-01-15',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  warrantyExpiry?: string;

  @ApiProperty({
    description: 'Supplier name',
    example: 'Tech Solutions Inc.',
    required: false,
  })
  @IsString()
  @IsOptional()
  supplier?: string;

  @ApiProperty({
    description: 'Invoice number',
    example: 'INV-2024-001',
    required: false,
  })
  @IsString()
  @IsOptional()
  invoiceNumber?: string;

  @ApiProperty({
    description: 'Additional notes',
    example: 'Configured with additional RAM',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiProperty({
    description: 'Image URL',
    example: 'https://example.com/images/laptop.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  imageUrl?: string;

  @ApiProperty({
    description: 'Category ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  categoryId: string;

  @ApiProperty({
    description: 'Location ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  locationId: string;

  @ApiProperty({
    description: 'Assigned user ID',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  assignedToId?: string;

  @ApiProperty({
    description: 'Whether the asset is active',
    example: true,
    default: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
