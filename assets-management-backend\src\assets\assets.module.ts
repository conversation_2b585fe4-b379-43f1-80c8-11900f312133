import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AssetsService } from './assets.service';
import { AssetsController } from './assets.controller';
import { AssetNumberService } from './asset-number.service';
import { AssetQuantityService } from './asset-quantity.service';
import { AssetItemService } from './asset-item.service';
import { Asset } from '../entities/asset.entity';
import { AssetItem } from '../entities/asset-item.entity';
import { AssetQuantity } from '../entities/asset-quantity.entity';
import { QuantityTransfer } from '../entities/quantity-transfer.entity';
import { Category } from '../entities/category.entity';
import { Location } from '../entities/location.entity';
import { User } from '../entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Asset,
      AssetItem,
      AssetQuantity,
      QuantityTransfer,
      Category,
      Location,
      User,
    ]),
  ],
  controllers: [AssetsController],
  providers: [
    AssetsService,
    AssetNumberService,
    AssetQuantityService,
    AssetItemService,
  ],
  exports: [
    AssetsService,
    AssetNumberService,
    AssetQuantityService,
    AssetItemService,
  ],
})
export class AssetsModule {}
