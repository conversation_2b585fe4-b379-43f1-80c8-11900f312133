import { LocationsService } from './locations.service';
import { CreateLocationDto } from './dto/create-location.dto';
import { UpdateLocationDto } from './dto/update-location.dto';
export declare class LocationsController {
    private readonly locationsService;
    constructor(locationsService: LocationsService);
    create(createLocationDto: CreateLocationDto): Promise<import("../entities").Location>;
    findAll(): Promise<import("../entities").Location[]>;
    findAllActive(): Promise<import("../entities").Location[]>;
    getHierarchy(): Promise<import("../entities").Location[]>;
    findOne(id: string): Promise<import("../entities").Location>;
    update(id: string, updateLocationDto: UpdateLocationDto): Promise<import("../entities").Location>;
    remove(id: string): Promise<void>;
}
