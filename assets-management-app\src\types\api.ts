// User Types
export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  VIEWER = 'viewer'
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  status: UserStatus
  department?: string
  phoneNumber?: string
  lastLoginAt?: string
  createdAt: string
  updatedAt: string
  fullName: string
}

// Asset Types
export enum AssetStatus {
  IN_STOCK = 'in_stock',
  IN_USE = 'in_use',
  MAINTENANCE = 'maintenance',
  RETIRED = 'retired',
  LOST = 'lost',
  DAMAGED = 'damaged'
}

export enum AssetCondition {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor'
}

export interface Asset {
  id: string
  assetNumber: string
  name: string
  description?: string
  model?: string
  manufacturer?: string
  serialNumber?: string
  unitPrice: number
  quantity: number
  totalValue?: number
  status: AssetStatus
  condition: AssetCondition
  purchaseDate?: string
  warrantyExpiry?: string
  supplier?: string
  invoiceNumber?: string
  notes?: string
  imageUrl?: string
  isActive: boolean
  categoryId?: string
  locationId?: string
  assignedToId?: string
  assignedAt?: string
  category?: Category
  location?: Location
  assignedTo?: User
  createdAt: string
  updatedAt: string
}

// Category Types
export interface Category {
  id: string
  name: string
  description?: string
  code?: string
  isActive: boolean
  parentId?: string
  parent?: Category
  children?: Category[]
  createdAt: string
  updatedAt: string
  fullPath: string
}

// Location Types
export enum LocationType {
  REGION = 'region',
  BUILDING = 'building',
  FLOOR = 'floor',
  ROOM = 'room',
  AREA = 'area'
}

export interface Location {
  id: string
  name: string
  description?: string
  code?: string
  type: LocationType
  address?: string
  coordinates?: string
  isActive: boolean
  parentId?: string
  parent?: Location
  children?: Location[]
  createdAt: string
  updatedAt: string
  fullPath: string
}

// Asset Log Types
export enum LogAction {
  CREATED = 'created',
  UPDATED = 'updated',
  DELETED = 'deleted',
  ASSIGNED = 'assigned',
  UNASSIGNED = 'unassigned',
  STATUS_CHANGED = 'status_changed',
  LOCATION_CHANGED = 'location_changed',
  MAINTENANCE = 'maintenance',
  RETIRED = 'retired'
}

export interface AssetLog {
  _id: string
  assetId: string
  assetNumber: string
  action: LogAction
  performedBy: string
  performedByName: string
  previousData?: Record<string, any>
  newData?: Record<string, any>
  description: string
  ipAddress?: string
  userAgent?: string
  timestamp: string
}

// API Request/Response Types
export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  firstName: string
  lastName: string
  password: string
  role?: UserRole
  department?: string
  phoneNumber?: string
}

export interface AuthResponse {
  user: User
  token: string
}

export interface CreateAssetRequest {
  name: string
  description?: string
  model?: string
  manufacturer?: string
  serialNumber?: string
  unitPrice: number
  quantity: number
  status?: AssetStatus
  condition?: AssetCondition
  purchaseDate?: string
  warrantyExpiry?: string
  supplier?: string
  invoiceNumber?: string
  notes?: string
  imageUrl?: string
  categoryId: string
  locationId: string
  assignedToId?: string
  isActive?: boolean
}

export interface UpdateAssetRequest extends Partial<CreateAssetRequest> {}

export interface AssignAssetRequest {
  assignedToId: string
  notes?: string
}

export interface CreateCategoryRequest {
  name: string
  description?: string
  code?: string
  parentId?: string
  isActive?: boolean
}

export interface UpdateCategoryRequest extends Partial<CreateCategoryRequest> {}

export interface CreateLocationRequest {
  name: string
  description?: string
  type: LocationType
  address?: string
  coordinates?: string
  parentId?: string
  isActive?: boolean
}

export interface UpdateLocationRequest extends Partial<CreateLocationRequest> {}

export interface CreateUserRequest {
  email: string
  firstName: string
  lastName: string
  password: string
  role?: UserRole
  status?: UserStatus
  department?: string
  phoneNumber?: string
}

export interface UpdateUserRequest extends Partial<Omit<CreateUserRequest, 'password'>> {}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

// Pagination Types
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface AssetSearchParams {
  search?: string
  categoryId?: string
  locationId?: string
  status?: AssetStatus
  condition?: string
  assignedToId?: string
  manufacturer?: string
  model?: string
  minValue?: number
  maxValue?: number
  purchaseDateFrom?: string
  purchaseDateTo?: string
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'ASC' | 'DESC'
}

export interface AssetStats {
  totalAssets: number
  totalValue: number
  statusBreakdown: Record<AssetStatus, number>
  categoryBreakdown: Array<{
    categoryName: string
    count: number
    value: number
  }>
}

// API Error Types
export interface ApiError {
  message: string
  statusCode: number
  error?: string
}
