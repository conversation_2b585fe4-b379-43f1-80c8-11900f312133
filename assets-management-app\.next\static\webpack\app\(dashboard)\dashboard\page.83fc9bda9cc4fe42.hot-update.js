"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx":
/*!************************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AssetDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/ListItemAvatar/ListItemAvatar.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Chip,Divider,Grid,LinearProgress,List,ListItem,ListItemAvatar,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Inventory,Person!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Inventory.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Inventory,Person!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Inventory,Person!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Inventory,Person!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Mock data for development\nconst mockStats = {\n    totalAssets: 1247,\n    totalValue: 2847593.45,\n    inUse: 892,\n    inStock: 245,\n    maintenance: 67,\n    retired: 43,\n    recentAssets: [\n        {\n            id: \"1\",\n            assetNumber: \"AST-202412-0001\",\n            name: \"Dell Laptop XPS 13\",\n            status: \"In Use\",\n            assignedTo: \"John Doe\",\n            value: 1299.99,\n            date: \"2024-12-01\"\n        },\n        {\n            id: \"2\",\n            assetNumber: \"AST-202412-0002\",\n            name: \"Office Chair Ergonomic\",\n            status: \"In Stock\",\n            assignedTo: null,\n            value: 299.99,\n            date: \"2024-12-01\"\n        },\n        {\n            id: \"3\",\n            assetNumber: \"AST-202412-0003\",\n            name: \"HP Printer LaserJet Pro\",\n            status: \"Maintenance\",\n            assignedTo: null,\n            value: 399.99,\n            date: \"2024-11-30\"\n        }\n    ],\n    categoryBreakdown: [\n        {\n            name: \"IT Equipment\",\n            count: 567,\n            value: 1245678.9,\n            percentage: 65\n        },\n        {\n            name: \"Furniture\",\n            count: 234,\n            value: 456789.12,\n            percentage: 25\n        },\n        {\n            name: \"Vehicles\",\n            count: 45,\n            value: 890123.45,\n            percentage: 8\n        },\n        {\n            name: \"Office Supplies\",\n            count: 401,\n            value: 254901.98,\n            percentage: 2\n        }\n    ],\n    recentActivity: [\n        {\n            id: \"1\",\n            action: \"Asset Created\",\n            description: \"Dell Laptop XPS 13 (AST-202412-0001) was created\",\n            user: \"Admin User\",\n            timestamp: \"2 hours ago\"\n        },\n        {\n            id: \"2\",\n            action: \"Asset Assigned\",\n            description: \"MacBook Pro assigned to Jane Smith\",\n            user: \"Manager User\",\n            timestamp: \"4 hours ago\"\n        },\n        {\n            id: \"3\",\n            action: \"Status Changed\",\n            description: \"Printer moved to maintenance\",\n            user: \"IT Support\",\n            timestamp: \"6 hours ago\"\n        }\n    ]\n};\nconst statusColors = {\n    \"In Use\": \"primary\",\n    \"In Stock\": \"success\",\n    Maintenance: \"warning\",\n    Retired: \"secondary\"\n};\nfunction AssetDashboard() {\n    _s();\n    const [stats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockStats);\n    const StatCard = (param)=>{\n        let { title, value, icon, color, subtitle } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    color: \"text.secondary\",\n                                    gutterBottom: true,\n                                    variant: \"body2\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    variant: \"h4\",\n                                    component: \"div\",\n                                    color: color,\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            sx: {\n                                bgcolor: \"\".concat(color, \".main\"),\n                                width: 56,\n                                height: 56\n                            },\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 121,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        sx: {\n            p: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                variant: \"h4\",\n                component: \"h1\",\n                gutterBottom: true,\n                children: \"Asset Management Dashboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Total Assets\",\n                            value: stats.totalAssets.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"primary\",\n                            subtitle: \"All registered assets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Total Value\",\n                            value: \"$\".concat(stats.totalValue.toLocaleString()),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MoneyIcon, {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"success\",\n                            subtitle: \"Combined asset value\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"In Use\",\n                            value: stats.inUse.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"info\",\n                            subtitle: \"Currently assigned\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Available\",\n                            value: stats.inStock.toLocaleString(),\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 19\n                            }, void 0),\n                            color: \"success\",\n                            subtitle: \"Ready for assignment\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                container: true,\n                spacing: 3,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        children: \"Asset Status Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sx: {\n                                            mt: 2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mb: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: \"In Use\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: stats.inUse\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"determinate\",\n                                                value: stats.inUse / stats.totalAssets * 100,\n                                                sx: {\n                                                    mb: 2,\n                                                    height: 8,\n                                                    borderRadius: 4\n                                                },\n                                                color: \"primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mb: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: \"In Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: stats.inStock\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"determinate\",\n                                                value: stats.inStock / stats.totalAssets * 100,\n                                                sx: {\n                                                    mb: 2,\n                                                    height: 8,\n                                                    borderRadius: 4\n                                                },\n                                                color: \"success\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mb: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: \"Maintenance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: stats.maintenance\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"determinate\",\n                                                value: stats.maintenance / stats.totalAssets * 100,\n                                                sx: {\n                                                    mb: 2,\n                                                    height: 8,\n                                                    borderRadius: 4\n                                                },\n                                                color: \"warning\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    mb: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: \"Retired\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: stats.retired\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"determinate\",\n                                                value: stats.retired / stats.totalAssets * 100,\n                                                sx: {\n                                                    height: 8,\n                                                    borderRadius: 4\n                                                },\n                                                color: \"secondary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        children: \"Assets by Category\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            size: \"small\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                align: \"right\",\n                                                                children: \"Count\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                align: \"right\",\n                                                                children: \"Value\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                align: \"right\",\n                                                                children: \"%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    children: stats.categoryBreakdown.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    children: category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: category.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        category.value.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: [\n                                                                        category.percentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, category.name, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 8,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        children: \"Recent Assets\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                children: \"Asset Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                children: \"Assigned To\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                align: \"right\",\n                                                                children: \"Value\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                children: \"Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    children: stats.recentAssets.map((asset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            hover: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        fontWeight: \"medium\",\n                                                                        children: asset.assetNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    children: asset.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        label: asset.status,\n                                                                        color: statusColors[asset.status],\n                                                                        size: \"small\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    children: asset.assignedTo || \"Unassigned\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    align: \"right\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        asset.value.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    children: asset.date\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, asset.id, true, {\n                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        children: \"Recent Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        children: stats.recentActivity.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        alignItems: \"flex-start\",\n                                                        sx: {\n                                                            px: 0\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    sx: {\n                                                                        bgcolor: \"primary.main\",\n                                                                        width: 32,\n                                                                        height: 32\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Inventory_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        fontSize: \"small\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                primary: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    variant: \"body2\",\n                                                                    fontWeight: \"medium\",\n                                                                    children: activity.action\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 27\n                                                                }, void 0),\n                                                                secondary: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            color: \"text.secondary\",\n                                                                            children: activity.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 350,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            variant: \"caption\",\n                                                                            color: \"text.secondary\",\n                                                                            children: [\n                                                                                \"by \",\n                                                                                activity.user,\n                                                                                \" • \",\n                                                                                activity.timestamp\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 353,\n                                                                            columnNumber: 29\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    index < stats.recentActivity.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Chip_Divider_Grid_LinearProgress_List_ListItem_ListItemAvatar_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, activity.id, true, {\n                                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Invicta\\\\Upcoming Projects\\\\Assets Management System\\\\assets-management-app\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(AssetDashboard, \"T6s30m3sGIT6xM5ahcU7uSQ/boY=\");\n_c = AssetDashboard;\nvar _c;\n$RefreshReg$(_c, \"AssetDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZCkvZGFzaGJvYXJkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBd0JyQjtBQVdNO0FBSTVCLDRCQUE0QjtBQUM1QixNQUFNNEIsWUFBWTtJQUNoQkMsYUFBYTtJQUNiQyxZQUFZO0lBQ1pDLE9BQU87SUFDUEMsU0FBUztJQUNUQyxhQUFhO0lBQ2JDLFNBQVM7SUFDVEMsY0FBYztRQUNaO1lBQ0VDLElBQUk7WUFDSkMsYUFBYTtZQUNiQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsWUFBWTtZQUNaQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBO1lBQ0VOLElBQUk7WUFDSkMsYUFBYTtZQUNiQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsWUFBWTtZQUNaQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBO1lBQ0VOLElBQUk7WUFDSkMsYUFBYTtZQUNiQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsWUFBWTtZQUNaQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtLQUNEO0lBQ0RDLG1CQUFtQjtRQUNqQjtZQUFFTCxNQUFNO1lBQWdCTSxPQUFPO1lBQUtILE9BQU87WUFBV0ksWUFBWTtRQUFHO1FBQ3JFO1lBQUVQLE1BQU07WUFBYU0sT0FBTztZQUFLSCxPQUFPO1lBQVdJLFlBQVk7UUFBRztRQUNsRTtZQUFFUCxNQUFNO1lBQVlNLE9BQU87WUFBSUgsT0FBTztZQUFXSSxZQUFZO1FBQUU7UUFDL0Q7WUFBRVAsTUFBTTtZQUFtQk0sT0FBTztZQUFLSCxPQUFPO1lBQVdJLFlBQVk7UUFBRTtLQUN4RTtJQUNEQyxnQkFBZ0I7UUFDZDtZQUNFVixJQUFJO1lBQ0pXLFFBQVE7WUFDUkMsYUFBYTtZQUNiQyxNQUFNO1lBQ05DLFdBQVc7UUFDYjtRQUNBO1lBQ0VkLElBQUk7WUFDSlcsUUFBUTtZQUNSQyxhQUFhO1lBQ2JDLE1BQU07WUFDTkMsV0FBVztRQUNiO1FBQ0E7WUFDRWQsSUFBSTtZQUNKVyxRQUFRO1lBQ1JDLGFBQWE7WUFDYkMsTUFBTTtZQUNOQyxXQUFXO1FBQ2I7S0FDRDtBQUNIO0FBRUEsTUFBTUMsZUFBZTtJQUNuQixVQUFVO0lBQ1YsWUFBWTtJQUNaQyxhQUFhO0lBQ2JDLFNBQVM7QUFDWDtBQUVlLFNBQVNDOztJQUN0QixNQUFNLENBQUNDLE1BQU0sR0FBR3ZELCtDQUFRQSxDQUFDNEI7SUFFekIsTUFBTTRCLFdBQVc7WUFBQyxFQUFFQyxLQUFLLEVBQUVoQixLQUFLLEVBQUVpQixJQUFJLEVBQUVDLEtBQUssRUFBRUMsUUFBUSxFQUFPOzZCQUM1RCw4REFBQzNELHlQQUFJQTtzQkFDSCw0RUFBQ0MseVBBQVdBOzBCQUNWLDRFQUFDRyx5UEFBR0E7b0JBQUN3RCxJQUFJO3dCQUFFQyxTQUFTO3dCQUFRQyxZQUFZO3dCQUFVQyxnQkFBZ0I7b0JBQWdCOztzQ0FDaEYsOERBQUMzRCx5UEFBR0E7OzhDQUNGLDhEQUFDRix5UEFBVUE7b0NBQUN3RCxPQUFNO29DQUFpQk0sWUFBWTtvQ0FBQ0MsU0FBUTs4Q0FDckRUOzs7Ozs7OENBRUgsOERBQUN0RCx5UEFBVUE7b0NBQUMrRCxTQUFRO29DQUFLQyxXQUFVO29DQUFNUixPQUFPQTs4Q0FDN0NsQjs7Ozs7O2dDQUVGbUIsMEJBQ0MsOERBQUN6RCx5UEFBVUE7b0NBQUMrRCxTQUFRO29DQUFRUCxPQUFNOzhDQUMvQkM7Ozs7Ozs7Ozs7OztzQ0FJUCw4REFBQzlDLHlQQUFNQTs0QkFBQytDLElBQUk7Z0NBQUVPLFNBQVMsR0FBUyxPQUFOVCxPQUFNO2dDQUFRVSxPQUFPO2dDQUFJQyxRQUFROzRCQUFHO3NDQUFJWjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNMUUscUJBQ0UsOERBQUNyRCx5UEFBR0E7UUFBQ3dELElBQUk7WUFBRVUsR0FBRztRQUFFOzswQkFDZCw4REFBQ3BFLHlQQUFVQTtnQkFBQytELFNBQVE7Z0JBQUtDLFdBQVU7Z0JBQUtGLFlBQVk7MEJBQUM7Ozs7OzswQkFLckQsOERBQUM3RCx5UEFBSUE7Z0JBQUNvRSxTQUFTO2dCQUFDQyxTQUFTO2dCQUFHWixJQUFJO29CQUFFYSxJQUFJO2dCQUFFOztrQ0FDdEMsOERBQUN0RSx5UEFBSUE7d0JBQUN1RSxJQUFJO3dCQUFDQyxJQUFJO3dCQUFJQyxJQUFJO3dCQUFHQyxJQUFJO2tDQUM1Qiw0RUFBQ3RCOzRCQUNDQyxPQUFNOzRCQUNOaEIsT0FBT2MsTUFBTTFCLFdBQVcsQ0FBQ2tELGNBQWM7NEJBQ3ZDckIsb0JBQU0sOERBQUNyQyx5SEFBYUE7Ozs7OzRCQUNwQnNDLE9BQU07NEJBQ05DLFVBQVM7Ozs7Ozs7Ozs7O2tDQUdiLDhEQUFDeEQseVBBQUlBO3dCQUFDdUUsSUFBSTt3QkFBQ0MsSUFBSTt3QkFBSUMsSUFBSTt3QkFBR0MsSUFBSTtrQ0FDNUIsNEVBQUN0Qjs0QkFDQ0MsT0FBTTs0QkFDTmhCLE9BQU8sSUFBc0MsT0FBbENjLE1BQU16QixVQUFVLENBQUNpRCxjQUFjOzRCQUMxQ3JCLG9CQUFNLDhEQUFDc0I7Ozs7OzRCQUNQckIsT0FBTTs0QkFDTkMsVUFBUzs7Ozs7Ozs7Ozs7a0NBR2IsOERBQUN4RCx5UEFBSUE7d0JBQUN1RSxJQUFJO3dCQUFDQyxJQUFJO3dCQUFJQyxJQUFJO3dCQUFHQyxJQUFJO2tDQUM1Qiw0RUFBQ3RCOzRCQUNDQyxPQUFNOzRCQUNOaEIsT0FBT2MsTUFBTXhCLEtBQUssQ0FBQ2dELGNBQWM7NEJBQ2pDckIsb0JBQU0sOERBQUNuQyx5SEFBY0E7Ozs7OzRCQUNyQm9DLE9BQU07NEJBQ05DLFVBQVM7Ozs7Ozs7Ozs7O2tDQUdiLDhEQUFDeEQseVBBQUlBO3dCQUFDdUUsSUFBSTt3QkFBQ0MsSUFBSTt3QkFBSUMsSUFBSTt3QkFBR0MsSUFBSTtrQ0FDNUIsNEVBQUN0Qjs0QkFDQ0MsT0FBTTs0QkFDTmhCLE9BQU9jLE1BQU12QixPQUFPLENBQUMrQyxjQUFjOzRCQUNuQ3JCLG9CQUFNLDhEQUFDakMsMEhBQWVBOzs7Ozs0QkFDdEJrQyxPQUFNOzRCQUNOQyxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFLZiw4REFBQ3hELHlQQUFJQTtnQkFBQ29FLFNBQVM7Z0JBQUNDLFNBQVM7O2tDQUV2Qiw4REFBQ3JFLHlQQUFJQTt3QkFBQ3VFLElBQUk7d0JBQUNDLElBQUk7d0JBQUlFLElBQUk7a0NBQ3JCLDRFQUFDN0UseVBBQUlBO3NDQUNILDRFQUFDQyx5UEFBV0E7O2tEQUNWLDhEQUFDQyx5UEFBVUE7d0NBQUMrRCxTQUFRO3dDQUFLRCxZQUFZO2tEQUFDOzs7Ozs7a0RBR3RDLDhEQUFDNUQseVBBQUdBO3dDQUFDd0QsSUFBSTs0Q0FBRW9CLElBQUk7d0NBQUU7OzBEQUNmLDhEQUFDNUUseVBBQUdBO2dEQUFDd0QsSUFBSTtvREFBRUMsU0FBUztvREFBUUUsZ0JBQWdCO29EQUFpQlUsSUFBSTtnREFBRTs7a0VBQ2pFLDhEQUFDdkUseVBBQVVBO3dEQUFDK0QsU0FBUTtrRUFBUTs7Ozs7O2tFQUM1Qiw4REFBQy9ELHlQQUFVQTt3REFBQytELFNBQVE7a0VBQVNYLE1BQU14QixLQUFLOzs7Ozs7Ozs7Ozs7MERBRTFDLDhEQUFDbEIsMFBBQWNBO2dEQUNicUQsU0FBUTtnREFDUnpCLE9BQU8sTUFBT1YsS0FBSyxHQUFHd0IsTUFBTTFCLFdBQVcsR0FBSTtnREFDM0NnQyxJQUFJO29EQUFFYSxJQUFJO29EQUFHSixRQUFRO29EQUFHWSxjQUFjO2dEQUFFO2dEQUN4Q3ZCLE9BQU07Ozs7OzswREFHUiw4REFBQ3RELHlQQUFHQTtnREFBQ3dELElBQUk7b0RBQUVDLFNBQVM7b0RBQVFFLGdCQUFnQjtvREFBaUJVLElBQUk7Z0RBQUU7O2tFQUNqRSw4REFBQ3ZFLHlQQUFVQTt3REFBQytELFNBQVE7a0VBQVE7Ozs7OztrRUFDNUIsOERBQUMvRCx5UEFBVUE7d0RBQUMrRCxTQUFRO2tFQUFTWCxNQUFNdkIsT0FBTzs7Ozs7Ozs7Ozs7OzBEQUU1Qyw4REFBQ25CLDBQQUFjQTtnREFDYnFELFNBQVE7Z0RBQ1J6QixPQUFPLE1BQU9ULE9BQU8sR0FBR3VCLE1BQU0xQixXQUFXLEdBQUk7Z0RBQzdDZ0MsSUFBSTtvREFBRWEsSUFBSTtvREFBR0osUUFBUTtvREFBR1ksY0FBYztnREFBRTtnREFDeEN2QixPQUFNOzs7Ozs7MERBR1IsOERBQUN0RCx5UEFBR0E7Z0RBQUN3RCxJQUFJO29EQUFFQyxTQUFTO29EQUFRRSxnQkFBZ0I7b0RBQWlCVSxJQUFJO2dEQUFFOztrRUFDakUsOERBQUN2RSx5UEFBVUE7d0RBQUMrRCxTQUFRO2tFQUFROzs7Ozs7a0VBQzVCLDhEQUFDL0QseVBBQVVBO3dEQUFDK0QsU0FBUTtrRUFBU1gsTUFBTXRCLFdBQVc7Ozs7Ozs7Ozs7OzswREFFaEQsOERBQUNwQiwwUEFBY0E7Z0RBQ2JxRCxTQUFRO2dEQUNSekIsT0FBTyxNQUFPUixXQUFXLEdBQUdzQixNQUFNMUIsV0FBVyxHQUFJO2dEQUNqRGdDLElBQUk7b0RBQUVhLElBQUk7b0RBQUdKLFFBQVE7b0RBQUdZLGNBQWM7Z0RBQUU7Z0RBQ3hDdkIsT0FBTTs7Ozs7OzBEQUdSLDhEQUFDdEQseVBBQUdBO2dEQUFDd0QsSUFBSTtvREFBRUMsU0FBUztvREFBUUUsZ0JBQWdCO29EQUFpQlUsSUFBSTtnREFBRTs7a0VBQ2pFLDhEQUFDdkUseVBBQVVBO3dEQUFDK0QsU0FBUTtrRUFBUTs7Ozs7O2tFQUM1Qiw4REFBQy9ELHlQQUFVQTt3REFBQytELFNBQVE7a0VBQVNYLE1BQU1yQixPQUFPOzs7Ozs7Ozs7Ozs7MERBRTVDLDhEQUFDckIsMFBBQWNBO2dEQUNicUQsU0FBUTtnREFDUnpCLE9BQU8sTUFBT1AsT0FBTyxHQUFHcUIsTUFBTTFCLFdBQVcsR0FBSTtnREFDN0NnQyxJQUFJO29EQUFFUyxRQUFRO29EQUFHWSxjQUFjO2dEQUFFO2dEQUNqQ3ZCLE9BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUWhCLDhEQUFDdkQseVBBQUlBO3dCQUFDdUUsSUFBSTt3QkFBQ0MsSUFBSTt3QkFBSUUsSUFBSTtrQ0FDckIsNEVBQUM3RSx5UEFBSUE7c0NBQ0gsNEVBQUNDLHlQQUFXQTs7a0RBQ1YsOERBQUNDLHlQQUFVQTt3Q0FBQytELFNBQVE7d0NBQUtELFlBQVk7a0RBQUM7Ozs7OztrREFHdEMsOERBQUN2RCwwUEFBY0E7a0RBQ2IsNEVBQUNILDBQQUFLQTs0Q0FBQzRFLE1BQUs7OzhEQUNWLDhEQUFDeEUsMFBBQVNBOzhEQUNSLDRFQUFDQywwUEFBUUE7OzBFQUNQLDhEQUFDSCwwUEFBU0E7MEVBQUM7Ozs7OzswRUFDWCw4REFBQ0EsMFBBQVNBO2dFQUFDMkUsT0FBTTswRUFBUTs7Ozs7OzBFQUN6Qiw4REFBQzNFLDBQQUFTQTtnRUFBQzJFLE9BQU07MEVBQVE7Ozs7OzswRUFDekIsOERBQUMzRSwwUEFBU0E7Z0VBQUMyRSxPQUFNOzBFQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs4REFHN0IsOERBQUM1RSwwUEFBU0E7OERBQ1ArQyxNQUFNWixpQkFBaUIsQ0FBQzBDLEdBQUcsQ0FBQ0MsQ0FBQUEseUJBQzNCLDhEQUFDMUUsMFBBQVFBOzs4RUFDUCw4REFBQ0gsMFBBQVNBOzhFQUFFNkUsU0FBU2hELElBQUk7Ozs7Ozs4RUFDekIsOERBQUM3QiwwUEFBU0E7b0VBQUMyRSxPQUFNOzhFQUFTRSxTQUFTMUMsS0FBSzs7Ozs7OzhFQUN4Qyw4REFBQ25DLDBQQUFTQTtvRUFBQzJFLE9BQU07O3dFQUFRO3dFQUFFRSxTQUFTN0MsS0FBSyxDQUFDc0MsY0FBYzs7Ozs7Ozs4RUFDeEQsOERBQUN0RSwwUEFBU0E7b0VBQUMyRSxPQUFNOzt3RUFBU0UsU0FBU3pDLFVBQVU7d0VBQUM7Ozs7Ozs7OzJEQUpqQ3lDLFNBQVNoRCxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQWUxQyw4REFBQ2xDLHlQQUFJQTt3QkFBQ3VFLElBQUk7d0JBQUNDLElBQUk7d0JBQUlFLElBQUk7a0NBQ3JCLDRFQUFDN0UseVBBQUlBO3NDQUNILDRFQUFDQyx5UEFBV0E7O2tEQUNWLDhEQUFDQyx5UEFBVUE7d0NBQUMrRCxTQUFRO3dDQUFLRCxZQUFZO2tEQUFDOzs7Ozs7a0RBR3RDLDhEQUFDdkQsMFBBQWNBO2tEQUNiLDRFQUFDSCwwUEFBS0E7OzhEQUNKLDhEQUFDSSwwUEFBU0E7OERBQ1IsNEVBQUNDLDBQQUFRQTs7MEVBQ1AsOERBQUNILDBQQUFTQTswRUFBQzs7Ozs7OzBFQUNYLDhEQUFDQSwwUEFBU0E7MEVBQUM7Ozs7OzswRUFDWCw4REFBQ0EsMFBBQVNBOzBFQUFDOzs7Ozs7MEVBQ1gsOERBQUNBLDBQQUFTQTswRUFBQzs7Ozs7OzBFQUNYLDhEQUFDQSwwUEFBU0E7Z0VBQUMyRSxPQUFNOzBFQUFROzs7Ozs7MEVBQ3pCLDhEQUFDM0UsMFBBQVNBOzBFQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs4REFHZiw4REFBQ0QsMFBBQVNBOzhEQUNQK0MsTUFBTXBCLFlBQVksQ0FBQ2tELEdBQUcsQ0FBQ0UsQ0FBQUEsc0JBQ3RCLDhEQUFDM0UsMFBBQVFBOzREQUFnQjRFLEtBQUs7OzhFQUM1Qiw4REFBQy9FLDBQQUFTQTs4RUFDUiw0RUFBQ04seVBBQVVBO3dFQUFDK0QsU0FBUTt3RUFBUXVCLFlBQVc7a0ZBQ3BDRixNQUFNbEQsV0FBVzs7Ozs7Ozs7Ozs7OEVBR3RCLDhEQUFDNUIsMFBBQVNBOzhFQUFFOEUsTUFBTWpELElBQUk7Ozs7Ozs4RUFDdEIsOERBQUM3QiwwUEFBU0E7OEVBQ1IsNEVBQUNILDBQQUFJQTt3RUFDSG9GLE9BQU9ILE1BQU1oRCxNQUFNO3dFQUNuQm9CLE9BQU9SLFlBQVksQ0FBQ29DLE1BQU1oRCxNQUFNLENBQThCO3dFQUM5RDRDLE1BQUs7Ozs7Ozs7Ozs7OzhFQUdULDhEQUFDMUUsMFBBQVNBOzhFQUFFOEUsTUFBTS9DLFVBQVUsSUFBSTs7Ozs7OzhFQUNoQyw4REFBQy9CLDBQQUFTQTtvRUFBQzJFLE9BQU07O3dFQUFRO3dFQUFFRyxNQUFNOUMsS0FBSyxDQUFDc0MsY0FBYzs7Ozs7Ozs4RUFDckQsOERBQUN0RSwwUEFBU0E7OEVBQUU4RSxNQUFNN0MsSUFBSTs7Ozs7OzsyREFoQlQ2QyxNQUFNbkQsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0EyQnJDLDhEQUFDaEMseVBBQUlBO3dCQUFDdUUsSUFBSTt3QkFBQ0MsSUFBSTt3QkFBSUUsSUFBSTtrQ0FDckIsNEVBQUM3RSx5UEFBSUE7c0NBQ0gsNEVBQUNDLHlQQUFXQTs7a0RBQ1YsOERBQUNDLHlQQUFVQTt3Q0FBQytELFNBQVE7d0NBQUtELFlBQVk7a0RBQUM7Ozs7OztrREFHdEMsOERBQUNsRCwwUEFBSUE7a0RBQ0Z3QyxNQUFNVCxjQUFjLENBQUN1QyxHQUFHLENBQUMsQ0FBQ00sVUFBVUMsc0JBQ25DLDhEQUFDdkYseVBBQUdBOztrRUFDRiw4REFBQ1csMFBBQVFBO3dEQUFDK0MsWUFBVzt3REFBYUYsSUFBSTs0REFBRWdDLElBQUk7d0RBQUU7OzBFQUM1Qyw4REFBQzNFLDBQQUFjQTswRUFDYiw0RUFBQ0oseVBBQU1BO29FQUFDK0MsSUFBSTt3RUFBRU8sU0FBUzt3RUFBZ0JDLE9BQU87d0VBQUlDLFFBQVE7b0VBQUc7OEVBQzNELDRFQUFDM0MsMEhBQVVBO3dFQUFDbUUsVUFBUzs7Ozs7Ozs7Ozs7Ozs7OzswRUFHekIsOERBQUM3RSwwUEFBWUE7Z0VBQ1g4RSx1QkFDRSw4REFBQzVGLHlQQUFVQTtvRUFBQytELFNBQVE7b0VBQVF1QixZQUFXOzhFQUNwQ0UsU0FBUzVDLE1BQU07Ozs7OztnRUFHcEJpRCx5QkFDRSw4REFBQzNGLHlQQUFHQTs7c0ZBQ0YsOERBQUNGLHlQQUFVQTs0RUFBQytELFNBQVE7NEVBQVFQLE9BQU07c0ZBQy9CZ0MsU0FBUzNDLFdBQVc7Ozs7OztzRkFFdkIsOERBQUM3Qyx5UEFBVUE7NEVBQUMrRCxTQUFROzRFQUFVUCxPQUFNOztnRkFBaUI7Z0ZBQy9DZ0MsU0FBUzFDLElBQUk7Z0ZBQUM7Z0ZBQUkwQyxTQUFTekMsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29EQU1qRDBDLFFBQVFyQyxNQUFNVCxjQUFjLENBQUNtRCxNQUFNLEdBQUcsbUJBQUssOERBQUM5RSwwUEFBT0E7Ozs7OzsrQ0F6QjVDd0UsU0FBU3ZELEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBbUN2QztHQTdQd0JrQjtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwLyhkYXNoYm9hcmQpL2Rhc2hib2FyZC9wYWdlLnRzeD9hZTg0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQge1xuICBDYXJkLFxuICBDYXJkQ29udGVudCxcbiAgVHlwb2dyYXBoeSxcbiAgR3JpZCxcbiAgQm94LFxuICBDaGlwLFxuICBUYWJsZSxcbiAgVGFibGVCb2R5LFxuICBUYWJsZUNlbGwsXG4gIFRhYmxlQ29udGFpbmVyLFxuICBUYWJsZUhlYWQsXG4gIFRhYmxlUm93LFxuICBMaW5lYXJQcm9ncmVzcyxcbiAgQXZhdGFyLFxuICBMaXN0LFxuICBMaXN0SXRlbSxcbiAgTGlzdEl0ZW1UZXh0LFxuICBMaXN0SXRlbUF2YXRhcixcbiAgRGl2aWRlcixcbiAgQnV0dG9uLFxuICBDaXJjdWxhclByb2dyZXNzLFxuICBBbGVydFxufSBmcm9tICdAbXVpL21hdGVyaWFsJ1xuaW1wb3J0IHtcbiAgSW52ZW50b3J5IGFzIEludmVudG9yeUljb24sXG4gIEFzc2lnbm1lbnQgYXMgQXNzaWdubWVudEljb24sXG4gIENoZWNrQ2lyY2xlIGFzIENoZWNrQ2lyY2xlSWNvbixcbiAgUGVyc29uIGFzIFBlcnNvbkljb24sXG4gIENhdGVnb3J5IGFzIENhdGVnb3J5SWNvbixcbiAgTG9jYXRpb25PbiBhcyBMb2NhdGlvbkljb24sXG4gIEJ1aWxkIGFzIE1haW50ZW5hbmNlSWNvbixcbiAgV2FybmluZyBhcyBXYXJuaW5nSWNvbixcbiAgUmVmcmVzaCBhcyBSZWZyZXNoSWNvblxufSBmcm9tICdAbXVpL2ljb25zLW1hdGVyaWFsJ1xuaW1wb3J0IHsgYXNzZXRzU2VydmljZSwgY2F0ZWdvcmllc1NlcnZpY2UsIGxvY2F0aW9uc1NlcnZpY2UsIHVzZXJzU2VydmljZSB9IGZyb20gJ0AvbGliL2FwaSdcbmltcG9ydCB7IEFzc2V0LCBDYXRlZ29yeSwgTG9jYXRpb24sIFVzZXIgfSBmcm9tICdAL3R5cGVzL2FwaSdcblxuLy8gTW9jayBkYXRhIGZvciBkZXZlbG9wbWVudFxuY29uc3QgbW9ja1N0YXRzID0ge1xuICB0b3RhbEFzc2V0czogMTI0NyxcbiAgdG90YWxWYWx1ZTogMjg0NzU5My40NSxcbiAgaW5Vc2U6IDg5MixcbiAgaW5TdG9jazogMjQ1LFxuICBtYWludGVuYW5jZTogNjcsXG4gIHJldGlyZWQ6IDQzLFxuICByZWNlbnRBc3NldHM6IFtcbiAgICB7XG4gICAgICBpZDogJzEnLFxuICAgICAgYXNzZXROdW1iZXI6ICdBU1QtMjAyNDEyLTAwMDEnLFxuICAgICAgbmFtZTogJ0RlbGwgTGFwdG9wIFhQUyAxMycsXG4gICAgICBzdGF0dXM6ICdJbiBVc2UnLFxuICAgICAgYXNzaWduZWRUbzogJ0pvaG4gRG9lJyxcbiAgICAgIHZhbHVlOiAxMjk5Ljk5LFxuICAgICAgZGF0ZTogJzIwMjQtMTItMDEnXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJzInLFxuICAgICAgYXNzZXROdW1iZXI6ICdBU1QtMjAyNDEyLTAwMDInLFxuICAgICAgbmFtZTogJ09mZmljZSBDaGFpciBFcmdvbm9taWMnLFxuICAgICAgc3RhdHVzOiAnSW4gU3RvY2snLFxuICAgICAgYXNzaWduZWRUbzogbnVsbCxcbiAgICAgIHZhbHVlOiAyOTkuOTksXG4gICAgICBkYXRlOiAnMjAyNC0xMi0wMSdcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnMycsXG4gICAgICBhc3NldE51bWJlcjogJ0FTVC0yMDI0MTItMDAwMycsXG4gICAgICBuYW1lOiAnSFAgUHJpbnRlciBMYXNlckpldCBQcm8nLFxuICAgICAgc3RhdHVzOiAnTWFpbnRlbmFuY2UnLFxuICAgICAgYXNzaWduZWRUbzogbnVsbCxcbiAgICAgIHZhbHVlOiAzOTkuOTksXG4gICAgICBkYXRlOiAnMjAyNC0xMS0zMCdcbiAgICB9XG4gIF0sXG4gIGNhdGVnb3J5QnJlYWtkb3duOiBbXG4gICAgeyBuYW1lOiAnSVQgRXF1aXBtZW50JywgY291bnQ6IDU2NywgdmFsdWU6IDEyNDU2NzguOSwgcGVyY2VudGFnZTogNjUgfSxcbiAgICB7IG5hbWU6ICdGdXJuaXR1cmUnLCBjb3VudDogMjM0LCB2YWx1ZTogNDU2Nzg5LjEyLCBwZXJjZW50YWdlOiAyNSB9LFxuICAgIHsgbmFtZTogJ1ZlaGljbGVzJywgY291bnQ6IDQ1LCB2YWx1ZTogODkwMTIzLjQ1LCBwZXJjZW50YWdlOiA4IH0sXG4gICAgeyBuYW1lOiAnT2ZmaWNlIFN1cHBsaWVzJywgY291bnQ6IDQwMSwgdmFsdWU6IDI1NDkwMS45OCwgcGVyY2VudGFnZTogMiB9XG4gIF0sXG4gIHJlY2VudEFjdGl2aXR5OiBbXG4gICAge1xuICAgICAgaWQ6ICcxJyxcbiAgICAgIGFjdGlvbjogJ0Fzc2V0IENyZWF0ZWQnLFxuICAgICAgZGVzY3JpcHRpb246ICdEZWxsIExhcHRvcCBYUFMgMTMgKEFTVC0yMDI0MTItMDAwMSkgd2FzIGNyZWF0ZWQnLFxuICAgICAgdXNlcjogJ0FkbWluIFVzZXInLFxuICAgICAgdGltZXN0YW1wOiAnMiBob3VycyBhZ28nXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJzInLFxuICAgICAgYWN0aW9uOiAnQXNzZXQgQXNzaWduZWQnLFxuICAgICAgZGVzY3JpcHRpb246ICdNYWNCb29rIFBybyBhc3NpZ25lZCB0byBKYW5lIFNtaXRoJyxcbiAgICAgIHVzZXI6ICdNYW5hZ2VyIFVzZXInLFxuICAgICAgdGltZXN0YW1wOiAnNCBob3VycyBhZ28nXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJzMnLFxuICAgICAgYWN0aW9uOiAnU3RhdHVzIENoYW5nZWQnLFxuICAgICAgZGVzY3JpcHRpb246ICdQcmludGVyIG1vdmVkIHRvIG1haW50ZW5hbmNlJyxcbiAgICAgIHVzZXI6ICdJVCBTdXBwb3J0JyxcbiAgICAgIHRpbWVzdGFtcDogJzYgaG91cnMgYWdvJ1xuICAgIH1cbiAgXVxufVxuXG5jb25zdCBzdGF0dXNDb2xvcnMgPSB7XG4gICdJbiBVc2UnOiAncHJpbWFyeScsXG4gICdJbiBTdG9jayc6ICdzdWNjZXNzJyxcbiAgTWFpbnRlbmFuY2U6ICd3YXJuaW5nJyxcbiAgUmV0aXJlZDogJ3NlY29uZGFyeSdcbn0gYXMgY29uc3RcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXNzZXREYXNoYm9hcmQoKSB7XG4gIGNvbnN0IFtzdGF0c10gPSB1c2VTdGF0ZShtb2NrU3RhdHMpXG5cbiAgY29uc3QgU3RhdENhcmQgPSAoeyB0aXRsZSwgdmFsdWUsIGljb24sIGNvbG9yLCBzdWJ0aXRsZSB9OiBhbnkpID0+IChcbiAgICA8Q2FyZD5cbiAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgPEJveCBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nIH19PlxuICAgICAgICAgIDxCb3g+XG4gICAgICAgICAgICA8VHlwb2dyYXBoeSBjb2xvcj0ndGV4dC5zZWNvbmRhcnknIGd1dHRlckJvdHRvbSB2YXJpYW50PSdib2R5Mic+XG4gICAgICAgICAgICAgIHt0aXRsZX1cbiAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2g0JyBjb21wb25lbnQ9J2RpdicgY29sb3I9e2NvbG9yfT5cbiAgICAgICAgICAgICAge3ZhbHVlfVxuICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAge3N1YnRpdGxlICYmIChcbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTInIGNvbG9yPSd0ZXh0LnNlY29uZGFyeSc+XG4gICAgICAgICAgICAgICAge3N1YnRpdGxlfVxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvQm94PlxuICAgICAgICAgIDxBdmF0YXIgc3g9e3sgYmdjb2xvcjogYCR7Y29sb3J9Lm1haW5gLCB3aWR0aDogNTYsIGhlaWdodDogNTYgfX0+e2ljb259PC9BdmF0YXI+XG4gICAgICAgIDwvQm94PlxuICAgICAgPC9DYXJkQ29udGVudD5cbiAgICA8L0NhcmQ+XG4gIClcblxuICByZXR1cm4gKFxuICAgIDxCb3ggc3g9e3sgcDogMyB9fT5cbiAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2g0JyBjb21wb25lbnQ9J2gxJyBndXR0ZXJCb3R0b20+XG4gICAgICAgIEFzc2V0IE1hbmFnZW1lbnQgRGFzaGJvYXJkXG4gICAgICA8L1R5cG9ncmFwaHk+XG5cbiAgICAgIHsvKiBLZXkgU3RhdGlzdGljcyAqL31cbiAgICAgIDxHcmlkIGNvbnRhaW5lciBzcGFjaW5nPXszfSBzeD17eyBtYjogNCB9fT5cbiAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fSBtZD17M30+XG4gICAgICAgICAgPFN0YXRDYXJkXG4gICAgICAgICAgICB0aXRsZT0nVG90YWwgQXNzZXRzJ1xuICAgICAgICAgICAgdmFsdWU9e3N0YXRzLnRvdGFsQXNzZXRzLnRvTG9jYWxlU3RyaW5nKCl9XG4gICAgICAgICAgICBpY29uPXs8SW52ZW50b3J5SWNvbiAvPn1cbiAgICAgICAgICAgIGNvbG9yPSdwcmltYXJ5J1xuICAgICAgICAgICAgc3VidGl0bGU9J0FsbCByZWdpc3RlcmVkIGFzc2V0cydcbiAgICAgICAgICAvPlxuICAgICAgICA8L0dyaWQ+XG4gICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17Nn0gbWQ9ezN9PlxuICAgICAgICAgIDxTdGF0Q2FyZFxuICAgICAgICAgICAgdGl0bGU9J1RvdGFsIFZhbHVlJ1xuICAgICAgICAgICAgdmFsdWU9e2AkJHtzdGF0cy50b3RhbFZhbHVlLnRvTG9jYWxlU3RyaW5nKCl9YH1cbiAgICAgICAgICAgIGljb249ezxNb25leUljb24gLz59XG4gICAgICAgICAgICBjb2xvcj0nc3VjY2VzcydcbiAgICAgICAgICAgIHN1YnRpdGxlPSdDb21iaW5lZCBhc3NldCB2YWx1ZSdcbiAgICAgICAgICAvPlxuICAgICAgICA8L0dyaWQ+XG4gICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17Nn0gbWQ9ezN9PlxuICAgICAgICAgIDxTdGF0Q2FyZFxuICAgICAgICAgICAgdGl0bGU9J0luIFVzZSdcbiAgICAgICAgICAgIHZhbHVlPXtzdGF0cy5pblVzZS50b0xvY2FsZVN0cmluZygpfVxuICAgICAgICAgICAgaWNvbj17PEFzc2lnbm1lbnRJY29uIC8+fVxuICAgICAgICAgICAgY29sb3I9J2luZm8nXG4gICAgICAgICAgICBzdWJ0aXRsZT0nQ3VycmVudGx5IGFzc2lnbmVkJ1xuICAgICAgICAgIC8+XG4gICAgICAgIDwvR3JpZD5cbiAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fSBtZD17M30+XG4gICAgICAgICAgPFN0YXRDYXJkXG4gICAgICAgICAgICB0aXRsZT0nQXZhaWxhYmxlJ1xuICAgICAgICAgICAgdmFsdWU9e3N0YXRzLmluU3RvY2sudG9Mb2NhbGVTdHJpbmcoKX1cbiAgICAgICAgICAgIGljb249ezxDaGVja0NpcmNsZUljb24gLz59XG4gICAgICAgICAgICBjb2xvcj0nc3VjY2VzcydcbiAgICAgICAgICAgIHN1YnRpdGxlPSdSZWFkeSBmb3IgYXNzaWdubWVudCdcbiAgICAgICAgICAvPlxuICAgICAgICA8L0dyaWQ+XG4gICAgICA8L0dyaWQ+XG5cbiAgICAgIDxHcmlkIGNvbnRhaW5lciBzcGFjaW5nPXszfT5cbiAgICAgICAgey8qIEFzc2V0IFN0YXR1cyBCcmVha2Rvd24gKi99XG4gICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBtZD17Nn0+XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2g2JyBndXR0ZXJCb3R0b20+XG4gICAgICAgICAgICAgICAgQXNzZXQgU3RhdHVzIE92ZXJ2aWV3XG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPEJveCBzeD17eyBtdDogMiB9fT5cbiAgICAgICAgICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJywgbWI6IDEgfX0+XG4gICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5Mic+SW4gVXNlPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTInPntzdGF0cy5pblVzZX08L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICAgICAgPExpbmVhclByb2dyZXNzXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PSdkZXRlcm1pbmF0ZSdcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXsoc3RhdHMuaW5Vc2UgLyBzdGF0cy50b3RhbEFzc2V0cykgKiAxMDB9XG4gICAgICAgICAgICAgICAgICBzeD17eyBtYjogMiwgaGVpZ2h0OiA4LCBib3JkZXJSYWRpdXM6IDQgfX1cbiAgICAgICAgICAgICAgICAgIGNvbG9yPSdwcmltYXJ5J1xuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJywgbWI6IDEgfX0+XG4gICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5Mic+SW4gU3RvY2s8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5Mic+e3N0YXRzLmluU3RvY2t9PC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgIDwvQm94PlxuICAgICAgICAgICAgICAgIDxMaW5lYXJQcm9ncmVzc1xuICAgICAgICAgICAgICAgICAgdmFyaWFudD0nZGV0ZXJtaW5hdGUnXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17KHN0YXRzLmluU3RvY2sgLyBzdGF0cy50b3RhbEFzc2V0cykgKiAxMDB9XG4gICAgICAgICAgICAgICAgICBzeD17eyBtYjogMiwgaGVpZ2h0OiA4LCBib3JkZXJSYWRpdXM6IDQgfX1cbiAgICAgICAgICAgICAgICAgIGNvbG9yPSdzdWNjZXNzJ1xuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJywgbWI6IDEgfX0+XG4gICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5Mic+TWFpbnRlbmFuY2U8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5Mic+e3N0YXRzLm1haW50ZW5hbmNlfTwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICA8L0JveD5cbiAgICAgICAgICAgICAgICA8TGluZWFyUHJvZ3Jlc3NcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9J2RldGVybWluYXRlJ1xuICAgICAgICAgICAgICAgICAgdmFsdWU9eyhzdGF0cy5tYWludGVuYW5jZSAvIHN0YXRzLnRvdGFsQXNzZXRzKSAqIDEwMH1cbiAgICAgICAgICAgICAgICAgIHN4PXt7IG1iOiAyLCBoZWlnaHQ6IDgsIGJvcmRlclJhZGl1czogNCB9fVxuICAgICAgICAgICAgICAgICAgY29sb3I9J3dhcm5pbmcnXG4gICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgIDxCb3ggc3g9e3sgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLCBtYjogMSB9fT5cbiAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2JvZHkyJz5SZXRpcmVkPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD0nYm9keTInPntzdGF0cy5yZXRpcmVkfTwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICA8L0JveD5cbiAgICAgICAgICAgICAgICA8TGluZWFyUHJvZ3Jlc3NcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9J2RldGVybWluYXRlJ1xuICAgICAgICAgICAgICAgICAgdmFsdWU9eyhzdGF0cy5yZXRpcmVkIC8gc3RhdHMudG90YWxBc3NldHMpICogMTAwfVxuICAgICAgICAgICAgICAgICAgc3g9e3sgaGVpZ2h0OiA4LCBib3JkZXJSYWRpdXM6IDQgfX1cbiAgICAgICAgICAgICAgICAgIGNvbG9yPSdzZWNvbmRhcnknXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9HcmlkPlxuXG4gICAgICAgIHsvKiBDYXRlZ29yeSBCcmVha2Rvd24gKi99XG4gICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBtZD17Nn0+XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2g2JyBndXR0ZXJCb3R0b20+XG4gICAgICAgICAgICAgICAgQXNzZXRzIGJ5IENhdGVnb3J5XG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPFRhYmxlQ29udGFpbmVyPlxuICAgICAgICAgICAgICAgIDxUYWJsZSBzaXplPSdzbWFsbCc+XG4gICAgICAgICAgICAgICAgICA8VGFibGVIZWFkPlxuICAgICAgICAgICAgICAgICAgICA8VGFibGVSb3c+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5DYXRlZ29yeTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgYWxpZ249J3JpZ2h0Jz5Db3VudDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgYWxpZ249J3JpZ2h0Jz5WYWx1ZTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgYWxpZ249J3JpZ2h0Jz4lPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XG4gICAgICAgICAgICAgICAgICA8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUJvZHk+XG4gICAgICAgICAgICAgICAgICAgIHtzdGF0cy5jYXRlZ29yeUJyZWFrZG93bi5tYXAoY2F0ZWdvcnkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZVJvdyBrZXk9e2NhdGVnb3J5Lm5hbWV9PlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD57Y2F0ZWdvcnkubmFtZX08L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgYWxpZ249J3JpZ2h0Jz57Y2F0ZWdvcnkuY291bnR9PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGFsaWduPSdyaWdodCc+JHtjYXRlZ29yeS52YWx1ZS50b0xvY2FsZVN0cmluZygpfTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBhbGlnbj0ncmlnaHQnPntjYXRlZ29yeS5wZXJjZW50YWdlfSU8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvVGFibGVCb2R5PlxuICAgICAgICAgICAgICAgIDwvVGFibGU+XG4gICAgICAgICAgICAgIDwvVGFibGVDb250YWluZXI+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9HcmlkPlxuXG4gICAgICAgIHsvKiBSZWNlbnQgQXNzZXRzICovfVxuICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gbWQ9ezh9PlxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdoNicgZ3V0dGVyQm90dG9tPlxuICAgICAgICAgICAgICAgIFJlY2VudCBBc3NldHNcbiAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICA8VGFibGVDb250YWluZXI+XG4gICAgICAgICAgICAgICAgPFRhYmxlPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlUm93PlxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+QXNzZXQgTnVtYmVyPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5OYW1lPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5TdGF0dXM8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPkFzc2lnbmVkIFRvPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBhbGlnbj0ncmlnaHQnPlZhbHVlPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5EYXRlPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XG4gICAgICAgICAgICAgICAgICA8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUJvZHk+XG4gICAgICAgICAgICAgICAgICAgIHtzdGF0cy5yZWNlbnRBc3NldHMubWFwKGFzc2V0ID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGVSb3cga2V5PXthc3NldC5pZH0gaG92ZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5MicgZm9udFdlaWdodD0nbWVkaXVtJz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXNzZXQuYXNzZXROdW1iZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD57YXNzZXQubmFtZX08L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGlwXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9e2Fzc2V0LnN0YXR1c31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj17c3RhdHVzQ29sb3JzW2Fzc2V0LnN0YXR1cyBhcyBrZXlvZiB0eXBlb2Ygc3RhdHVzQ29sb3JzXX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPSdzbWFsbCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD57YXNzZXQuYXNzaWduZWRUbyB8fCAnVW5hc3NpZ25lZCd9PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGFsaWduPSdyaWdodCc+JHthc3NldC52YWx1ZS50b0xvY2FsZVN0cmluZygpfTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD57YXNzZXQuZGF0ZX08L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvVGFibGVCb2R5PlxuICAgICAgICAgICAgICAgIDwvVGFibGU+XG4gICAgICAgICAgICAgIDwvVGFibGVDb250YWluZXI+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9HcmlkPlxuXG4gICAgICAgIHsvKiBSZWNlbnQgQWN0aXZpdHkgKi99XG4gICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBtZD17NH0+XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2g2JyBndXR0ZXJCb3R0b20+XG4gICAgICAgICAgICAgICAgUmVjZW50IEFjdGl2aXR5XG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPExpc3Q+XG4gICAgICAgICAgICAgICAge3N0YXRzLnJlY2VudEFjdGl2aXR5Lm1hcCgoYWN0aXZpdHksIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8Qm94IGtleT17YWN0aXZpdHkuaWR9PlxuICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW0gYWxpZ25JdGVtcz0nZmxleC1zdGFydCcgc3g9e3sgcHg6IDAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtQXZhdGFyPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhciBzeD17eyBiZ2NvbG9yOiAncHJpbWFyeS5tYWluJywgd2lkdGg6IDMyLCBoZWlnaHQ6IDMyIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8UGVyc29uSWNvbiBmb250U2l6ZT0nc21hbGwnIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhcj5cbiAgICAgICAgICAgICAgICAgICAgICA8L0xpc3RJdGVtQXZhdGFyPlxuICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbVRleHRcbiAgICAgICAgICAgICAgICAgICAgICAgIHByaW1hcnk9e1xuICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdib2R5MicgZm9udFdlaWdodD0nbWVkaXVtJz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YWN0aXZpdHkuYWN0aW9ufVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBzZWNvbmRhcnk9e1xuICAgICAgICAgICAgICAgICAgICAgICAgICA8Qm94PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9J2JvZHkyJyBjb2xvcj0ndGV4dC5zZWNvbmRhcnknPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2FjdGl2aXR5LmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PSdjYXB0aW9uJyBjb2xvcj0ndGV4dC5zZWNvbmRhcnknPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnkge2FjdGl2aXR5LnVzZXJ9IOKAoiB7YWN0aXZpdHkudGltZXN0YW1wfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9MaXN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAge2luZGV4IDwgc3RhdHMucmVjZW50QWN0aXZpdHkubGVuZ3RoIC0gMSAmJiA8RGl2aWRlciAvPn1cbiAgICAgICAgICAgICAgICAgIDwvQm94PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L0xpc3Q+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9HcmlkPlxuICAgICAgPC9HcmlkPlxuICAgIDwvQm94PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJUeXBvZ3JhcGh5IiwiR3JpZCIsIkJveCIsIkNoaXAiLCJUYWJsZSIsIlRhYmxlQm9keSIsIlRhYmxlQ2VsbCIsIlRhYmxlQ29udGFpbmVyIiwiVGFibGVIZWFkIiwiVGFibGVSb3ciLCJMaW5lYXJQcm9ncmVzcyIsIkF2YXRhciIsIkxpc3QiLCJMaXN0SXRlbSIsIkxpc3RJdGVtVGV4dCIsIkxpc3RJdGVtQXZhdGFyIiwiRGl2aWRlciIsIkludmVudG9yeSIsIkludmVudG9yeUljb24iLCJBc3NpZ25tZW50IiwiQXNzaWdubWVudEljb24iLCJDaGVja0NpcmNsZSIsIkNoZWNrQ2lyY2xlSWNvbiIsIlBlcnNvbiIsIlBlcnNvbkljb24iLCJtb2NrU3RhdHMiLCJ0b3RhbEFzc2V0cyIsInRvdGFsVmFsdWUiLCJpblVzZSIsImluU3RvY2siLCJtYWludGVuYW5jZSIsInJldGlyZWQiLCJyZWNlbnRBc3NldHMiLCJpZCIsImFzc2V0TnVtYmVyIiwibmFtZSIsInN0YXR1cyIsImFzc2lnbmVkVG8iLCJ2YWx1ZSIsImRhdGUiLCJjYXRlZ29yeUJyZWFrZG93biIsImNvdW50IiwicGVyY2VudGFnZSIsInJlY2VudEFjdGl2aXR5IiwiYWN0aW9uIiwiZGVzY3JpcHRpb24iLCJ1c2VyIiwidGltZXN0YW1wIiwic3RhdHVzQ29sb3JzIiwiTWFpbnRlbmFuY2UiLCJSZXRpcmVkIiwiQXNzZXREYXNoYm9hcmQiLCJzdGF0cyIsIlN0YXRDYXJkIiwidGl0bGUiLCJpY29uIiwiY29sb3IiLCJzdWJ0aXRsZSIsInN4IiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsImd1dHRlckJvdHRvbSIsInZhcmlhbnQiLCJjb21wb25lbnQiLCJiZ2NvbG9yIiwid2lkdGgiLCJoZWlnaHQiLCJwIiwiY29udGFpbmVyIiwic3BhY2luZyIsIm1iIiwiaXRlbSIsInhzIiwic20iLCJtZCIsInRvTG9jYWxlU3RyaW5nIiwiTW9uZXlJY29uIiwibXQiLCJib3JkZXJSYWRpdXMiLCJzaXplIiwiYWxpZ24iLCJtYXAiLCJjYXRlZ29yeSIsImFzc2V0IiwiaG92ZXIiLCJmb250V2VpZ2h0IiwibGFiZWwiLCJhY3Rpdml0eSIsImluZGV4IiwicHgiLCJmb250U2l6ZSIsInByaW1hcnkiLCJzZWNvbmRhcnkiLCJsZW5ndGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx\n"));

/***/ })

});